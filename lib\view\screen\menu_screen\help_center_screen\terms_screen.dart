// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/core/controller/menu_controller/help_center_controller/help_center_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/help_center_screen/download_documents_screen.dart';
import 'package:waie_app/view/screen/menu_screen/help_center_screen/general_question_screen.dart';
import 'package:waie_app/view/screen/menu_screen/help_center_screen/search_video_tutorial_widget.dart';
import 'package:waie_app/view/screen/menu_screen/help_center_screen/video_tutorials_screen.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../utils/constants.dart';
import '../../../widget/common_appbar_widget.dart';
import 'have_issue_screen.dart';

class TermsScreen extends StatelessWidget {
  TermsScreen({super.key});

  HelpCenterController helpCenterController = Get.put(HelpCenterController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: GestureDetector(
                          onTap: () {
                            Get.back();
                          },
                          child: Container(
                            padding: EdgeInsets.only(
                              left: 16,
                              top: 15,
                              bottom: 15,
                            ),
                            color: AppColor.cBackGround,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                assetSvdImageWidget(
                                    image: DefaultImages.backIcn,
                                    colorFilter: ColorFilter.mode(
                                        AppColor.cDarkBlueFont,
                                        BlendMode.srcIn)),
                                horizontalSpace(10),
                                Text(
                                  "Back".trr,
                                  style: pRegular18.copyWith(
                                      color: AppColor.cDarkBlueFont,
                                      fontSize: 16.5),
                                  textAlign: TextAlign.start,
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    // horizontalSpace(35),
                  ],
                ),
              ),
              // Container(
              //   width: Get.width,
              //   height: 400,
              //   decoration: BoxDecoration(
              //       image: DecorationImage(
              //           image: AssetImage(
              //             DefaultImages.terms,
              //           ),
              //           fit: BoxFit.fill)),
              //   padding: EdgeInsets.all(16),
              // )
              Center(
                child: Text(
                  "Terms And Conditions".trr,
                  style: pBold20,
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(),
                    child: Column(
                      children: [
                        Text(
                            "1. WAIE services are available only at Aldrees fuel stations that are listed in our website."
                                .trr),
                        Text(
                            "2. The customer may cancel this agreement at any time and stop the services on condition that the customer must inform Aldrees Company in writing before 48 hours and handing over all the TAGS and the SMART CARDS. The customer will be refunded the remaining balance in his account."
                                .trr),
                        Text(
                            "3. The customer assumes responsibility for all the information that he entered into the system."
                                .trr),
                        Text(
                            "4. Fuel prices are subject to change by the government and the prices will change automatically in the WAIE system by the same amount."
                                .trr),
                        Text(
                            "5. Upon registration in the WAIE system the customer will get an account number thru which he can manage his account from anywhere in the world with internet access and he will be responsible for all transactions in his account."
                                .trr),
                        Text(
                            "6. The customer will get a 2-year free warranty period against manufacturing defects, starting from the date of installation in the vehicle. The warranty shall not cover any misuse by the users."
                                .trr),
                        Text(
                            "7. Fuel prices in stations outside city limits are according to government regulations."
                                .trr),
                        Text(
                            "8. Upon signing this agreement and acceptance to use WAIE system, the customer declares that he agrees and accepts the terms and conditions of the agreement between him & Aldrees Petroleum & Transport Services Co."
                                .trr),
                        Text(
                            "9. The electronic TAG is considered the property of the customer and under his responsibility and cannot be transferred to another car, and the customer must warn drivers not to tamper with it, and the company is not responsible for compensating the second party for the value of the chip and the amount of consumption resulting from such act."
                                .trr),
                        Text(
                            "10. The company's responsibility towards the customer is limited to installing the TAG and providing the service to the customer in providing him with fuel, and it is not responsible for compensating the customer in the event of the TAG being misused by one of its employees or third parties."
                                .trr),
                        Text(
                            "11. In case the car being broken down, stolen, or the TAG stolen, the customer can de-active the TAG through the website without first consulting the company."
                                .trr),
                        Text(
                            "12. When a dispute arises between the client and the company, it is resolved amicably within 15 days from the date of the client's notification to the company of this dispute by letter. In case that the solution is not possible, it is resorted to the competent Courts."
                                .trr),
                        if (Constants.custRegType == "I")
                          Text(
                              "13. Premium account will charge 10 SAR for each service tags / smartcard (monthly service fees)."
                                  .trr),
                        if (Constants.custRegType == "I")
                          Text(
                              "14. NON-Primmum account will be without monthly service fees."
                                  .trr),
                        if (Constants.custRegType == "C")
                          Text(
                              "13. The validity of the free tag order is 30 days from the date of order. In case it is not installed, the system will be automatically cancel the remain tags not installed from order."
                                  .trr),
                        if (Constants.custRegType == "C")
                          Text(
                              "14. In case of the electronic tag is canceled before 6 months from the date of installation, the value of the tag 100 SAR (not include vat) will be deducted from account balance."
                                  .trr),
                        Gap(16),
                        Center(
                          child: Text(
                            "تسعيرة خدمات نظام واعى وفقا لشرائح خصم الكميات \n WAIE SERVICES PRICES AFTER VOLUME DISCOUNT",
                            style: pBold16,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Gap(16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Volume \n المستويات",
                              style: pRegular12,
                              textAlign: TextAlign.center,
                            ),
                            Text(
                              "Unit Price in SR \n سعر الوحدة بالريال السعودي",
                              style: pRegular12,
                              textAlign: TextAlign.center,
                            )
                          ],
                        ),
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "From \n من \n 1 \n 51 \n 101 \n 201 \n 301 \n 501 \n 801",
                                    style: pRegular12,
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(
                                      height: 160,
                                      child:
                                          VerticalDivider(color: Colors.black)),
                                  Text(
                                    "To \n إلى \n 50 \n 100 \n 200 \n 300 \n 500 \n 800 \n Above",
                                    style: pRegular12,
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                              SizedBox(
                                  height: 160,
                                  child: VerticalDivider(color: Colors.black)),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "E-Tag \n الشريحة الإلكترونية \n 172.50 \n 166.75 \n 161 \n 155.25 \n 149.50 \n 143.75 \n 126.50",
                                    style: pRegular12,
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(
                                      height: 160,
                                      child:
                                          VerticalDivider(color: Colors.black)),
                                  Text(
                                    "Smart Card \n الكرت الذكي \n  \n  \n  \n 10 \n \n \n ",
                                    style: pRegular12,
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(
                                      height: 160,
                                      child:
                                          VerticalDivider(color: Colors.black)),
                                  Text(
                                    "Service charges for petrol vehicle \n رسوم الخدمة الشهرية لمركبات البنزين \n 11.50 \n 10.35 \n 9.20 \n 8.05 \n 6.90 \n 5.75 \n 5.75",
                                    style: pRegular12,
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Gap(16),
                        Center(
                          child: Text(
                            "INSTALLING RFID TAG ON THE VEHICLE'S EXTERNAL TANK"
                                .trr,
                            style: pBold16,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Center(
                          child: Text(
                            "When the vehicle fuel tank is far from the vehicle body, RFID tag must be fixed using a fitting as described by the pictures below :"
                                .trr,
                            style: pRegular12,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Gap(16),
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Center(
                            child: Image.asset(DefaultImages
                                .fitting), // Change 'assets/image.jpg' to your image path
                          ),
                        ),
                        Gap(16),
                        Text(
                            "1. TAGS must be installed in steel fitting which is specially designed for installing of tag as shown in above pictures (1,2)."
                                .trr),
                        Text(
                            "2. Tag will cover by hard plastic protection cap with Lock and glue (3,4,5)."
                                .trr),
                        Text(
                            "3. Extra steel fitting (RING or T-CLAMP) will place near to the tank neck by REVITE. Using of hand drill or manual tool to make holes for REVITE to attach extra fitting on the tank (1,2,3,4,5)."
                                .trr),
                        Text(
                            "4. During installation tanks neck mouth must be closed with cotton or CAP in whichever possible way."
                                .trr),
                        Gap(16),
                        Text(
                          "NOTE".trr,
                          style: pBold16,
                        ),
                        Text(
                            "Service charge volume discount is applicable for petrol vehicles only according to the number of installed tags.\nDiesel vehicles service charge is 11.5 SR per month per vehicle for any quantity.\nAbove prices include VAT."
                                .trr),
                        Gap(16),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
