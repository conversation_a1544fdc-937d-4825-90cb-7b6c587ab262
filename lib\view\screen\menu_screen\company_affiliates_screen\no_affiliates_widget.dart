import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../utils/colors.dart';
import '../../../../utils/images.dart';
import '../../../widget/common_button.dart';
import '../../../widget/icon_and_image.dart';
import 'new_affiliate_screen.dart';

class NoAffiliatesWidget extends StatelessWidget {
  const NoAffiliatesWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 80.0),
          child: Text(
            "You don't have any affilates yet".trr,
            style: pRegular13,
          ),
        ),
        verticalSpace(Get.height * 0.15),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: [
              Center(
                  child: assetSvdImageWidget(
                      image: DefaultImages.affiliatesImage)),
              verticalSpace(32),
              Center(
                  child: Text(
                      "Team up with other companies to get bigger discounts from WAIE"
                          .trr,
                      style: pBold20,
                      textAlign: TextAlign.center)),
              verticalSpace(16),
              Text(
                'Add an affiliate'.trr,
                style: pRegular13,
                textAlign: TextAlign.center,
              )
            ],
          ),
        )
      ],
    );
  }
}
