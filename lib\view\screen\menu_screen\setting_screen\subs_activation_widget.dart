import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/core/controller/menu_controller/setting_controller/subsciption_plan_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/setting_screen/basic_subscription_plan_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class SubsPlanWidget extends StatelessWidget {
  // const SubsPlanWidget({super.key});
  bool premium = false;
  String title = "";
  String other = "";

  SubsPlanWidget(bool premium, {super.key}) {
    this.premium = premium;
    if (premium) {
      title =
          "Are you sure you want to activate your premium subscription plan?";
      other = "10.00 SAR will be paid from your current balance";
    } else {
      title = "Are you sure want to activate your free subscription?";
      other = "";
    }
  }

  Subscription_Plan_Controller subcriptionPlan =
      Get.put(Subscription_Plan_Controller());
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: const EdgeInsets.all(16),
      child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Center(
                          child: assetSvdImageWidget(
                              image: DefaultImages.cancelIcn))),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Center(
                child:
                    Text(title.trr, style: pBold16, textAlign: TextAlign.center),
              ),
            ),
            if (premium)
              Center(
                child: Text(other.trr,
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center),
              ),
            verticalSpace(24),
            Row(
              children: [
                Expanded(
                    child: CommonBorderButton(
                  title: 'No'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  bColor: AppColor.themeDarkBlueColor,
                  textColor: AppColor.cDarkBlueFont,
                )),
                horizontalSpace(8),
                Expanded(
                    child: CommonButton(
                  title: 'Yes'.trr,
                  onPressed: () {
                    Get.back();
                    //Get.off(() => BasicSubscriptionPlanScreen());
                    subcriptionPlan.updateSubsPlan(premium);
                  },
                  btnColor: AppColor.cRedText,
                ))
              ],
            ),
            verticalSpace(16),
          ]),
    );
  }
}
