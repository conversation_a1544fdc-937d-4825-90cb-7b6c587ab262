import 'dart:convert';

LoadProfileDetail loadProfileDetailFromJson(String str) =>
    LoadProfileDetail.fromJson(json.decode(str));

String loadProfileDetailToJson(LoadProfileDetail data) =>
    json.encode(data.toJson());

class LoadProfileDetail {
  dynamic vatDocs;
  dynamic idDoc;
  String custid;
  String emailid;
  dynamic username;
  String mobileno;
  String firstname;
  dynamic acctypeCode;
  String midname;
  String lastname;
  String tel;
  String pobox;
  dynamic zipcode;
  String gps;
  dynamic addr;
  String fuel91;
  String fuel95;
  String diesel;
  dynamic serviceknown;
  String serviceknownCode;
  dynamic regactivated;
  dynamic regvalcode;
  String regactdate;
  dynamic acctstatus;
  dynamic acctactdate;
  String regtypeCode;
  String regtype;
  String accttypeCode;
  dynamic accttype;
  String comptypeCode;
  dynamic balamt;
  dynamic contractno;
  dynamic contractfrom;
  dynamic contractto;
  dynamic sysDate;
  dynamic sysuserid;
  dynamic compcode;
  dynamic loccode;
  dynamic whcode;
  String crno;
  String companyname;
  String contactperson;
  String designation;
  String companytel;
  String companyfax;
  dynamic rfqno;
  dynamic comptype;
  dynamic consumption;
  dynamic payment;
  dynamic bankguarfile;
  dynamic bank;
  dynamic bankamt;
  dynamic bankguarexpiry;
  dynamic bankrefno;
  dynamic firstnamear;
  dynamic middlenamear;
  dynamic lastnamear;
  String street;
  String buildingNo;
  String postalcode;
  dynamic city;
  dynamic district;
  dynamic region;
  dynamic custsubid;
  dynamic guidcode;
  dynamic serviceFee;
  dynamic others;
  dynamic profUpdt;
  String companynamear;
  dynamic fax;
  dynamic directPhone;
  String houseNo;
  dynamic accCustid;
  dynamic salesmanName;
  dynamic b2BBank;
  dynamic b2BIban;
  dynamic b2BBban;
  dynamic b2BClientid;
  dynamic b2BBal;
  String vatNo;
  dynamic motherCompany;
  dynamic cardScFlag;
  dynamic lang;
  dynamic usertype;
  dynamic origin;
  dynamic password;
  dynamic oldpassword;
  dynamic role;
  dynamic custDistricts;
  dynamic otherstn;
  dynamic showtc;
  dynamic empNo;
  dynamic placesAllow;
  dynamic scEffectiveDate;
  dynamic scProcess;
  dynamic otherStnAllow;
  dynamic salesrep;
  dynamic isPartner;
  dynamic otherBal;
  dynamic minusFlag;
  dynamic custType;
  dynamic salesmanList;
  dynamic exproList;
  dynamic idList;
  String idNumber;
  dynamic countryList;
  dynamic regionList;
  dynamic cityList;
  dynamic districtList;
  dynamic languageList;
  dynamic howdidyouknowList;
  dynamic isVerified;
  String selectedcountry;
  String selectedreg;
  String selectedcity;
  String selecteddistrict;
  String selectedexpro;
  String selectedsalesman;
  String selectedidtype;
  dynamic selectedidtypedesc;
  String selectedlang;
  dynamic selectedhduk;
  dynamic treeDataMenuAccess;
  dynamic treeDataOrgLevel;
  bool enableCrno;
  bool txtVatNoEnabled;
  bool salesmanEnabled;
  String premium;
  bool isVatNoValid;
  dynamic profileError;

  LoadProfileDetail({
    required this.vatDocs,
    required this.idDoc,
    required this.custid,
    required this.emailid,
    required this.username,
    required this.mobileno,
    required this.firstname,
    required this.acctypeCode,
    required this.midname,
    required this.lastname,
    required this.tel,
    required this.pobox,
    required this.zipcode,
    required this.gps,
    required this.addr,
    required this.fuel91,
    required this.fuel95,
    required this.diesel,
    required this.serviceknown,
    required this.serviceknownCode,
    required this.regactivated,
    required this.regvalcode,
    required this.regactdate,
    required this.acctstatus,
    required this.acctactdate,
    required this.regtypeCode,
    required this.regtype,
    required this.accttypeCode,
    required this.accttype,
    required this.comptypeCode,
    required this.balamt,
    required this.contractno,
    required this.contractfrom,
    required this.contractto,
    required this.sysDate,
    required this.sysuserid,
    required this.compcode,
    required this.loccode,
    required this.whcode,
    required this.crno,
    required this.companyname,
    required this.contactperson,
    required this.designation,
    required this.companytel,
    required this.companyfax,
    required this.rfqno,
    required this.comptype,
    required this.consumption,
    required this.payment,
    required this.bankguarfile,
    required this.bank,
    required this.bankamt,
    required this.bankguarexpiry,
    required this.bankrefno,
    required this.firstnamear,
    required this.middlenamear,
    required this.lastnamear,
    required this.street,
    required this.buildingNo,
    required this.postalcode,
    required this.city,
    required this.district,
    required this.region,
    required this.custsubid,
    required this.guidcode,
    required this.serviceFee,
    required this.others,
    required this.profUpdt,
    required this.companynamear,
    required this.fax,
    required this.directPhone,
    required this.houseNo,
    required this.accCustid,
    required this.salesmanName,
    required this.b2BBank,
    required this.b2BIban,
    required this.b2BBban,
    required this.b2BClientid,
    required this.b2BBal,
    required this.vatNo,
    required this.motherCompany,
    required this.cardScFlag,
    required this.lang,
    required this.usertype,
    required this.origin,
    required this.password,
    required this.oldpassword,
    required this.role,
    required this.custDistricts,
    required this.otherstn,
    required this.showtc,
    required this.empNo,
    required this.placesAllow,
    required this.scEffectiveDate,
    required this.scProcess,
    required this.otherStnAllow,
    required this.salesrep,
    required this.isPartner,
    required this.otherBal,
    required this.minusFlag,
    required this.custType,
    required this.salesmanList,
    required this.exproList,
    required this.idList,
    required this.idNumber,
    required this.countryList,
    required this.regionList,
    required this.cityList,
    required this.districtList,
    required this.languageList,
    required this.howdidyouknowList,
    required this.isVerified,
    required this.selectedcountry,
    required this.selectedreg,
    required this.selectedcity,
    required this.selecteddistrict,
    required this.selectedexpro,
    required this.selectedsalesman,
    required this.selectedidtype,
    required this.selectedidtypedesc,
    required this.selectedlang,
    required this.selectedhduk,
    required this.treeDataMenuAccess,
    required this.treeDataOrgLevel,
    required this.enableCrno,
    required this.txtVatNoEnabled,
    required this.salesmanEnabled,
    required this.premium,
    required this.isVatNoValid,
    required this.profileError,
  });

  factory LoadProfileDetail.fromJson(Map<String, dynamic> json) =>
      LoadProfileDetail(
        vatDocs: json["VatDocs"],
        idDoc: json["IdDoc"],
        custid: json["CUSTID"],
        emailid: json["EMAILID"],
        username: json["USERNAME"],
        mobileno: json["MOBILENO"],
        firstname: json["FIRSTNAME"],
        acctypeCode: json["ACCTYPE_CODE"],
        midname: json["MIDNAME"],
        lastname: json["LASTNAME"],
        tel: json["TEL"],
        pobox: json["POBOX"],
        zipcode: json["ZIPCODE"],
        gps: json["GPS"],
        addr: json["ADDR"],
        fuel91: json["FUEL91"],
        fuel95: json["FUEL95"],
        diesel: json["DIESEL"],
        serviceknown: json["SERVICEKNOWN"],
        serviceknownCode: json["SERVICEKNOWN_CODE"],
        regactivated: json["REGACTIVATED"],
        regvalcode: json["REGVALCODE"],
        regactdate: json["REGACTDATE"],
        acctstatus: json["ACCTSTATUS"],
        acctactdate: json["ACCTACTDATE"],
        regtypeCode: json["REGTYPE_CODE"],
        regtype: json["REGTYPE"],
        accttypeCode: json["ACCTTYPE_CODE"],
        accttype: json["ACCTTYPE"],
        comptypeCode: json["COMPTYPE_CODE"],
        balamt: json["BALAMT"],
        contractno: json["CONTRACTNO"],
        contractfrom: json["CONTRACTFROM"],
        contractto: json["CONTRACTTO"],
        sysDate: json["SYS_DATE"],
        sysuserid: json["SYSUSERID"],
        compcode: json["COMPCODE"],
        loccode: json["LOCCODE"],
        whcode: json["WHCODE"],
        crno: json["CRNO"],
        companyname: json["COMPANYNAME"],
        contactperson: json["CONTACTPERSON"],
        designation: json["DESIGNATION"],
        companytel: json["COMPANYTEL"],
        companyfax: json["COMPANYFAX"],
        rfqno: json["RFQNO"],
        comptype: json["COMPTYPE"],
        consumption: json["CONSUMPTION"],
        payment: json["PAYMENT"],
        bankguarfile: json["BANKGUARFILE"],
        bank: json["BANK"],
        bankamt: json["BANKAMT"],
        bankguarexpiry: json["BANKGUAREXPIRY"],
        bankrefno: json["BANKREFNO"],
        firstnamear: json["FIRSTNAMEAR"],
        middlenamear: json["MIDDLENAMEAR"],
        lastnamear: json["LASTNAMEAR"],
        street: json["STREET"],
        buildingNo: json["BUILDING_NO"],
        postalcode: json["POSTALCODE"],
        city: json["CITY"],
        district: json["DISTRICT"],
        region: json["REGION"],
        custsubid: json["CUSTSUBID"],
        guidcode: json["GUIDCODE"],
        serviceFee: json["SERVICE_FEE"],
        others: json["OTHERS"],
        profUpdt: json["PROF_UPDT"],
        companynamear: json["COMPANYNAMEAR"],
        fax: json["FAX"],
        directPhone: json["DIRECT_PHONE"],
        houseNo: json["HOUSE_NO"],
        accCustid: json["ACC_CUSTID"],
        salesmanName: json["SALESMAN_NAME"],
        b2BBank: json["B2B_BANK"],
        b2BIban: json["B2B_IBAN"],
        b2BBban: json["B2B_BBAN"],
        b2BClientid: json["B2B_CLIENTID"],
        b2BBal: json["B2B_BAL"],
        vatNo: json["VAT_NO"],
        motherCompany: json["MOTHER_COMPANY"],
        cardScFlag: json["CARD_SC_FLAG"],
        lang: json["LANG"],
        usertype: json["USERTYPE"],
        origin: json["ORIGIN"],
        password: json["PASSWORD"],
        oldpassword: json["OLDPASSWORD"],
        role: json["ROLE"],
        custDistricts: json["CUST_DISTRICTS"],
        otherstn: json["OTHERSTN"],
        showtc: json["SHOWTC"],
        empNo: json["EMP_NO"],
        placesAllow: json["PLACES_ALLOW"],
        scEffectiveDate: json["SC_EFFECTIVE_DATE"],
        scProcess: json["SC_PROCESS"],
        otherStnAllow: json["OTHER_STN_ALLOW"],
        salesrep: json["SALESREP"],
        isPartner: json["IS_PARTNER"],
        otherBal: json["OTHER_BAL"],
        minusFlag: json["MINUS_FLAG"],
        custType: json["CUST_TYPE"],
        salesmanList: json["SALESMAN_LIST"],
        exproList: json["EXPRO_LIST"],
        idList: json["ID_LIST"],
        idNumber: json["ID_NUMBER"],
        countryList: json["COUNTRY_LIST"],
        regionList: json["REGION_LIST"],
        cityList: json["CITY_LIST"],
        districtList: json["DISTRICT_LIST"],
        languageList: json["LANGUAGE_LIST"],
        howdidyouknowList: json["HOWDIDYOUKNOW_LIST"],
        isVerified: json["IS_VERIFIED"],
        selectedcountry: json["SELECTEDCOUNTRY"],
        selectedreg: json["SELECTEDREG"],
        selectedcity: json["SELECTEDCITY"],
        selecteddistrict: json["SELECTEDDISTRICT"],
        selectedexpro: json["SELECTEDEXPRO"],
        selectedsalesman: json["SELECTEDSALESMAN"],
        selectedidtype: json["SELECTEDIDTYPE"],
        selectedidtypedesc: json["SELECTEDIDTYPEDESC"],
        selectedlang: json["SELECTEDLANG"],
        selectedhduk: json["SELECTEDHDUK"],
        treeDataMenuAccess: json["TreeDataMenuAccess"],
        treeDataOrgLevel: json["TreeDataOrgLevel"],
        enableCrno: json["EnableCRNO"],
        txtVatNoEnabled: json["TxtVatNoEnabled"],
        salesmanEnabled: json["SalesmanEnabled"],
        premium: json["Premium"],
        isVatNoValid: json["IsVatNoValid"],
        profileError: json["ProfileError"],
      );

  Map<String, dynamic> toJson() => {
        "VatDocs": vatDocs,
        "IdDoc": idDoc,
        "CUSTID": custid,
        "EMAILID": emailid,
        "USERNAME": username,
        "MOBILENO": mobileno,
        "FIRSTNAME": firstname,
        "ACCTYPE_CODE": acctypeCode,
        "MIDNAME": midname,
        "LASTNAME": lastname,
        "TEL": tel,
        "POBOX": pobox,
        "ZIPCODE": zipcode,
        "GPS": gps,
        "ADDR": addr,
        "FUEL91": fuel91,
        "FUEL95": fuel95,
        "DIESEL": diesel,
        "SERVICEKNOWN": serviceknown,
        "SERVICEKNOWN_CODE": serviceknownCode,
        "REGACTIVATED": regactivated,
        "REGVALCODE": regvalcode,
        "REGACTDATE": regactdate,
        "ACCTSTATUS": acctstatus,
        "ACCTACTDATE": acctactdate,
        "REGTYPE_CODE": regtypeCode,
        "REGTYPE": regtype,
        "ACCTTYPE_CODE": accttypeCode,
        "ACCTTYPE": accttype,
        "COMPTYPE_CODE": comptypeCode,
        "BALAMT": balamt,
        "CONTRACTNO": contractno,
        "CONTRACTFROM": contractfrom,
        "CONTRACTTO": contractto,
        "SYS_DATE": sysDate,
        "SYSUSERID": sysuserid,
        "COMPCODE": compcode,
        "LOCCODE": loccode,
        "WHCODE": whcode,
        "CRNO": crno,
        "COMPANYNAME": companyname,
        "CONTACTPERSON": contactperson,
        "DESIGNATION": designation,
        "COMPANYTEL": companytel,
        "COMPANYFAX": companyfax,
        "RFQNO": rfqno,
        "COMPTYPE": comptype,
        "CONSUMPTION": consumption,
        "PAYMENT": payment,
        "BANKGUARFILE": bankguarfile,
        "BANK": bank,
        "BANKAMT": bankamt,
        "BANKGUAREXPIRY": bankguarexpiry,
        "BANKREFNO": bankrefno,
        "FIRSTNAMEAR": firstnamear,
        "MIDDLENAMEAR": middlenamear,
        "LASTNAMEAR": lastnamear,
        "STREET": street,
        "BUILDING_NO": buildingNo,
        "POSTALCODE": postalcode,
        "CITY": city,
        "DISTRICT": district,
        "REGION": region,
        "CUSTSUBID": custsubid,
        "GUIDCODE": guidcode,
        "SERVICE_FEE": serviceFee,
        "OTHERS": others,
        "PROF_UPDT": profUpdt,
        "COMPANYNAMEAR": companynamear,
        "FAX": fax,
        "DIRECT_PHONE": directPhone,
        "HOUSE_NO": houseNo,
        "ACC_CUSTID": accCustid,
        "SALESMAN_NAME": salesmanName,
        "B2B_BANK": b2BBank,
        "B2B_IBAN": b2BIban,
        "B2B_BBAN": b2BBban,
        "B2B_CLIENTID": b2BClientid,
        "B2B_BAL": b2BBal,
        "VAT_NO": vatNo,
        "MOTHER_COMPANY": motherCompany,
        "CARD_SC_FLAG": cardScFlag,
        "LANG": lang,
        "USERTYPE": usertype,
        "ORIGIN": origin,
        "PASSWORD": password,
        "OLDPASSWORD": oldpassword,
        "ROLE": role,
        "CUST_DISTRICTS": custDistricts,
        "OTHERSTN": otherstn,
        "SHOWTC": showtc,
        "EMP_NO": empNo,
        "PLACES_ALLOW": placesAllow,
        "SC_EFFECTIVE_DATE": scEffectiveDate,
        "SC_PROCESS": scProcess,
        "OTHER_STN_ALLOW": otherStnAllow,
        "SALESREP": salesrep,
        "IS_PARTNER": isPartner,
        "OTHER_BAL": otherBal,
        "MINUS_FLAG": minusFlag,
        "CUST_TYPE": custType,
        "SALESMAN_LIST": salesmanList,
        "EXPRO_LIST": exproList,
        "ID_LIST": idList,
        "ID_NUMBER": idNumber,
        "COUNTRY_LIST": countryList,
        "REGION_LIST": regionList,
        "CITY_LIST": cityList,
        "DISTRICT_LIST": districtList,
        "LANGUAGE_LIST": languageList,
        "HOWDIDYOUKNOW_LIST": howdidyouknowList,
        "IS_VERIFIED": isVerified,
        "SELECTEDCOUNTRY": selectedcountry,
        "SELECTEDREG": selectedreg,
        "SELECTEDCITY": selectedcity,
        "SELECTEDDISTRICT": selecteddistrict,
        "SELECTEDEXPRO": selectedexpro,
        "SELECTEDSALESMAN": selectedsalesman,
        "SELECTEDIDTYPE": selectedidtype,
        "SELECTEDIDTYPEDESC": selectedidtypedesc,
        "SELECTEDLANG": selectedlang,
        "SELECTEDHDUK": selectedhduk,
        "TreeDataMenuAccess": treeDataMenuAccess,
        "TreeDataOrgLevel": treeDataOrgLevel,
        "EnableCRNO": enableCrno,
        "TxtVatNoEnabled": txtVatNoEnabled,
        "SalesmanEnabled": salesmanEnabled,
        "Premium": premium,
        "IsVatNoValid": isVatNoValid,
        "ProfileError": profileError,
      };
}
