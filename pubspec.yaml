name: waie_app
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.8+13

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  quickalert:
  bloc:
  flutter_bloc:

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  #month_picker_dialog: ^2.0.2
  cupertino_icons: ^1.0.2
  carousel_slider: ^5.1.1
  get: ^4.6.5
  fluttertoast: ^8.2.2
  cached_network_image: ^3.2.3
  flutter_svg: ^2.0.7
  intl: ^0.20.2
  html: ^0.15.4
  get_storage: ^2.1.1
  http: ^1.1.0
  qr_flutter: ^4.1.0
  percent_indicator: ^4.2.3
  syncfusion_flutter_charts: ^28.1.38
  url_launcher: ^6.1.12
  image_picker: ^1.0.2
  google_maps_flutter: ^2.2.5
  geolocator: ^12.0.0
  maps_launcher: ^2.2.0
  webview_flutter_plus: ^0.4.5
  chips_choice: ^3.0.0
  draggable_bottom_sheet: ^1.0.2
  intl_phone_number_input: ^0.7.3+1
  shared_preferences: ^2.2.0
  syncfusion_flutter_datepicker: ^28.1.38
  step_progress_indicator: ^1.0.2
  smart_select: ^4.3.3
  json_annotation: ^4.0.2
  gap: ^3.0.1
  file_picker: ^8.0.3
  mime: ^1.0.4
  webview_flutter: ^4.7.0
  webview_flutter_android: ^3.16.1
  path_provider: ^2.1.1
  pay: ^2.0.0
  connectivity_plus: ^6.0.3
  package_info_plus: ^8.0.0
  permission_handler: ^11.0.1
  local_auth: ^2.1.8
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  pinput: ^4.0.0
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^17.2.2
  app_settings: ^5.1.1
  device_info_plus: ^10.1.2
  open_file: ^3.5.4
  in_app_review: ^2.0.10
  audio_session: ^0.1.9+1
dev_dependencies:
  flutter_test:
    sdk: flutter
  hive_generator: ^2.0.1
  build_runner: ^2.1.7
  json_serializable: ^6.5.4
  video_player: ^2.8.6
  flutter_launcher_icons: ^0.11.0
  flutter_spinkit: ^5.2.1
  google_map_dynamic_key: ^1.0.1
  flutter_dotenv: ^5.2.1


flutter_icons:
  android: true
  ios: true
  image_path: "asset/icons/app_icon.png"




  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - asset/image/svg_image/
    - asset/image/image/
    - json/
    - asset/
    - asset/video/
    
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SF Pro Display
      fonts:
        - asset: asset/font/SFPRODISPLAYBLACKITALIC.OTF
        - asset: asset/font/SFPRODISPLAYBOLD.OTF
        - asset: asset/font/SFPRODISPLAYHEAVYITALIC.OTF
        - asset: asset/font/SFPRODISPLAYLIGHTITALIC.OTF
        - asset: asset/font/SFPRODISPLAYMEDIUM.OTF
        - asset: asset/font/SFPRODISPLAYREGULAR.OTF
        - asset: asset/font/SFPRODISPLAYTHINITALIC.OTF
        - asset: asset/font/SFPRODISPLAYULTRALIGHTITALIC.OTF
        - asset: asset/font/SFProDisplay-Semibold.ttf
    - family: Custom
      fonts:
        - asset: asset/font/Custom.ttf
          # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
