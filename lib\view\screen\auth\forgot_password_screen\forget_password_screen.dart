// ignore_for_file: prefer_const_constructors, must_be_immutable, avoid_print

import 'dart:developer';
import 'package:get/get.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/screen/auth/forgot_password_screen/reset_password_otp_screen.dart';
import '../auth_background.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/auth/forgot_password_controller.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  ForgotPasswordController forgotPasswordController =
      Get.put(ForgotPasswordController());

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    forgotPasswordController.emailController.value.clear();
    forgotPasswordController.mobileController.value.clear();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Scaffold(
            backgroundColor: AppColor.cBackGround,
            body: SafeArea(
              child: AuthBackGroundWidget(
                widget: Form(
                  key: formKey,
                  onChanged: () {
                    forgotPasswordController.isValidate.value = false;
                    print("==========${formKey.currentState!.validate()}");
                    if (formKey.currentState!.validate()) {
                      forgotPasswordController.isValidate.value = true;
                    }
                    print(
                        "==========${forgotPasswordController.isValidate.value}");
                  },
                  child: SingleChildScrollView(
                    child: Obx(() {
                      return Padding(
                        padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).viewInsets.bottom),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            verticalSpace(Get.height * 0.02),
                            simpleMyAppBar(
                                title: "",
                                onTap: () {
                                  Get.back();
                                },
                                backString: "Back to Login".trr),
                            verticalSpace(24),
                            Text(
                              "please enter your username".trr,
                              style: pBold28,
                            ),
                            verticalSpace(18),
                            CommonTextField(
                              controller: forgotPasswordController
                                  .useridController.value,
                              //labelText: '${'usr name'.trr} :',
                              labelText: '',
                              hintText: "usr name".trr,
                              filled: true,
                              fillColor: AppColor.cFilled,
                              onChanged: (value) {
                                forgotPasswordController.useridController
                                    .refresh();
                              },
                              validator: (value) {
                                return Validator.validateRequired(value);
                              },
                            ),
                          ],
                        ),
                      );
                    }),
                  ),
                ),
              ),
            ),
            resizeToAvoidBottomInset: false,
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerFloat,
            floatingActionButton: Padding(
              padding: const EdgeInsets.only(left: 24, bottom: 10, right: 24),
              child: Obx(() {
                return CommonButton(
                  title: 'CONFIRM'.trr,
                  onPressed: forgotPasswordController.isValidate.value == true
                      ? () {
                          if (formKey.currentState!.validate()) {
                            log('done');
                            forgotPasswordController.userid.value =
                                forgotPasswordController
                                    .useridController.value.text;
                            forgotPasswordController.forgotPassword();
                            // Get.to(() => ResetPasswordOtpScreen());
                          }
                        }
                      : null,
                  btnColor: forgotPasswordController.isValidate.value == true
                      ? AppColor.themeOrangeColor
                      : AppColor.cLightOrange,
                  // ? AppColor.themeBlueColor
                  // : AppColor.cLightBlueBtn,
                );
              }),
            )),
      ),
    );
  }
}
