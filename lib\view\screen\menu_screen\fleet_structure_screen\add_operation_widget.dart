// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_structure_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';

class AddOperationWidget extends StatelessWidget {
  AddOperationWidget({super.key});

  final FleetStructureController fleetStructureController =
      Get.put(FleetStructureController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Center(
                child: Text(
              "Add a Operation".trr,
              style: pBold20,
            )),
            verticalSpace(21),
            CommonTextField(
              controller: fleetStructureController.branchNameController,
              labelText: 'Operation name'.trr,
              hintText: '',
            ),
            verticalSpace(16),
            Row(
              children: [
                Expanded(
                    child: CommonBorderButton(
                  title: 'Cancel'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  bColor: AppColor.themeDarkBlueColor,
                  textColor: AppColor.cDarkBlueFont,
                )),
                horizontalSpace(8),
                Expanded(
                    child: CommonButton(
                  title: 'Add'.trr,
                  onPressed: () {
                    Get.back();
                    fleetStructureController.fleetStructureList.value =
                        fleetStructureController.dummyFleetStructureList;
                    fleetStructureController.fleetStructureList.refresh();
                  },
                  btnColor: AppColor.themeOrangeColor,
                ))
              ],
            ),
          ],
        ),
      ),
    );
  }
}
