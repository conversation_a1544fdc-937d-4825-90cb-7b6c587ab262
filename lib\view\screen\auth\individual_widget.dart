// ignore_for_file: must_be_immutable, prefer_const_constructors, prefer_const_constructors_in_immutables, body_might_complete_normally_nullable, prefer_interpolation_to_compose_strings

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:waie_app/core/controller/auth/auth_controller.dart';
import 'package:waie_app/core/controller/signup_controller/city_controller.dart';
import 'package:waie_app/core/controller/signup_controller/signup_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class IndividualWidget extends StatefulWidget {
  final AuthController authController;

  IndividualWidget({super.key, required this.authController});

  @override
  State<IndividualWidget> createState() => _IndividualWidgetState();
}

class _IndividualWidgetState extends State<IndividualWidget> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  SignupController signupController = Get.put(SignupController());
  CityController cityController = Get.find();
  //FleetController fleetcontroller = Get.put(FleetController());

  String pattern =
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&-_])[A-Za-z\d@$!%*?&-_]{8,}$';
  RxBool isPatternMatch = false.obs;
  RxBool isPasswordLength = false.obs;

  String errorCity = '';
  String errorWaie = '';
  String errorString = '';

  @override
  Widget build(BuildContext context) {
    print("cityController.cities ====> ${cityController.cities}");
    return Expanded(
      child: Obx(
        () => cityController.cities.isEmpty
            ? Center(child: CircularProgressIndicator())
            : Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Fill you phone number to register as an individual user. We will send you a confirmation code to complete registration."
                            .trr,
                        style: pRegular14.copyWith(fontSize: 13),
                      ),

                      /// Remove || Hide the email field from registration screen
                      /// ///////////////////////////////////////////////////////
                      // verticalSpace(16),
                      // CommonTextField(
                      //   controller: signupController.emailController,
                      //   keyboardType: TextInputType.emailAddress,
                      //   labelText: 'User email ID'.tr,
                      //   hintText: "Email address".tr,
                      //   validator: (value) {
                      //     //return Validator.validateEmail(value);
                      //     return null;
                      //   },
                      // ),

                      /// ///////////////////////////////////////////////////////
                      verticalSpace(16),
                      Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CommonTextField(
                                controller: signupController.fNameController,
                                labelText: 'First name'.trr + '*',
                                hintText: "First name".trr,
                                validator: (value) {
                                  return Validator.validateName(
                                      value, "First name".trr);
                                },
                              ),
                              verticalSpace(16),
                              CommonTextField(
                                controller: signupController.lNameController,
                                labelText: 'Last name'.trr + '*',
                                hintText: "Last name".trr,
                                validator: (value) {
                                  return Validator.validateName(
                                      value, "Last name".trr);
                                },
                              ),
                              verticalSpace(16),
                              CommonTextField(
                                controller: signupController.usernameController,
                                labelText: 'Username'.trr + '*',
                                hintText: "Username".trr,
                                validator: (value) {
                                  return Validator.validateName(
                                      value, "Username".trr);
                                },
                              ),
                              verticalSpace(16),
                              /* CommonTextField(
                                controller: signupController.mobileNoController,
                                keyboardType: TextInputType.number,
                                labelText: 'Mobile number'.trr + "*",
                                hintText: "Mobile number".trr,
                                maxLength: 12,
                                validator: (value) {
                                  return Validator.validateMobile(value);
                                },
                              ),*/
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Mobile number'.trr + "*",
                                    style: pRegular12.copyWith(
                                        color: AppColor.cLabel),
                                    textAlign: TextAlign.start,
                                  ),
                                ],
                              ),
                              verticalSpace(8),
                              Directionality(
                                textDirection: TextDirection.ltr,
                                child: Container(
                                  height: 44,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(6),
                                    //color: AppColor.cBorder,
                                    border: Border.all(
                                      color: AppColor.cBorder,
                                    ),
                                  ),
                                  child: InternationalPhoneNumberInput(
                                    onInputChanged: (PhoneNumber number) {
                                      signupController.isoCode.value =
                                          number.isoCode!;
                                      signupController.mobileNoController.text =
                                          number.phoneNumber
                                              .toString()
                                              .replaceAll("+", "");
                                      print("Entered Number=======" +
                                          number.phoneNumber
                                              .toString()
                                              .replaceAll("+", ""));
                                    },
                                    // maxLength:9,
                                    /* onInputValidated: (bool value) {
                                    print(value);
                                  },*/
                                    cursorColor: AppColor.cHintFont,
                                    selectorConfig: SelectorConfig(
                                      selectorType:
                                          PhoneInputSelectorType.BOTTOM_SHEET,
                                      leadingPadding: 16,
                                      setSelectorButtonAsPrefixIcon: true,
                                    ),
                                    ignoreBlank: false,
                                    // autoValidateMode: AutovalidateMode.disabled,
                                    textStyle: pRegular14.copyWith(
                                      color: AppColor.cLabel,
                                    ),
                                    initialValue: PhoneNumber(
                                        isoCode: signupController.isoCode.value,
                                        dialCode:
                                            signupController.isoCode.value),
                                    inputBorder: OutlineInputBorder(),
                                    keyboardAction: TextInputAction.done,
                                    scrollPadding: EdgeInsets.zero,
                                    selectorTextStyle: pRegular14.copyWith(
                                        color: AppColor.cLabel, fontSize: 14),
                                    textAlign: TextAlign.start,
                                    textAlignVertical: TextAlignVertical.center,
                                    maxLength: 11,
                                    inputDecoration: InputDecoration(
                                      contentPadding:
                                          EdgeInsets.only(left: 16, bottom: 8),
                                      isDense: true,
                                      prefixText: "|  ",
                                      prefixStyle: TextStyle(
                                          fontSize: 30,
                                          color: AppColor.cBorder),
                                      counterText: '',
                                      hintText: " " + 'Please enter here'.trr,
                                      counterStyle:
                                          TextStyle(fontSize: 0, height: 0),
                                      errorStyle:
                                          TextStyle(fontSize: 0, height: 0),
                                      hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont,
                                      ),
                                      border: InputBorder.none,
                                      //filled: true,
                                      //fillColor: AppColor.cFilled,
                                    ),
                                    /*  onSaved: (PhoneNumber number) {
                                  },*/
                                  ),
                                ),
                              ),
                              verticalSpace(8),
                              Center(
                                child: Text(
                                  "We'll send a confirmation code to your phone to finish your registration."
                                      .trr,
                                  style: pRegular12.copyWith(
                                      color: AppColor.cLabel),
                                  textAlign: TextAlign.start,
                                ),
                              ),
                              verticalSpace(16),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  verticalSpace(15),
                                  Text(
                                    "Your password should be...".trr,
                                    style: pSemiBold14,
                                  ),
                                  verticalSpace(8),
                                  validateTextRow(
                                      image: isPasswordLength.value
                                          ? DefaultImages.validateIcn
                                          : DefaultImages.notValidateIcn,
                                      title: "At least 8 characters long.".trr),
                                  validateTextRow(
                                      image: isPatternMatch.value
                                          ? DefaultImages.validateIcn
                                          : DefaultImages.notValidateIcn,
                                      title:
                                          "A combination of uppercase letters, lowercase letters, numbers, and symbols."
                                              .trr),
                                  // Text(
                                  //   "• " +
                                  //       "At least 8 characters long.".trr +
                                  //       "\n• " +
                                  //       "A combination of uppercase letters, lowercase letters, numbers, and symbols."
                                  //           .trr,
                                  //   style: pRegular12,
                                  // ),
                                ],
                              ),
                              verticalSpace(8),
                              CommonTextField(
                                  controller:
                                      signupController.passwordController,
                                  labelText: 'Password'.trr + "*",
                                  hintText: "**********",
                                  obscureText:
                                      widget.authController.isObscure.value,
                                  obscuringCharacter: '*',
                                  validator: (value) {
                                    return Validator.validatePassword(value);
                                  },
                                  onChanged: (value) {
                                    RegExp regex = RegExp(pattern);
                                    if (regex.hasMatch(value)) {
                                      isPatternMatch.value = true;
                                    } else {
                                      isPatternMatch.value = false;
                                    }
                                    if (value.length > 7) {
                                      isPasswordLength.value = true;
                                    } else {
                                      isPasswordLength.value = false;
                                    }
                                  },
                                  suffix: GestureDetector(
                                      onTap: () {
                                        widget.authController.isObscure.value =
                                            !widget
                                                .authController.isObscure.value;
                                      },
                                      child: assetSvdImageWidget(
                                          image: widget.authController.isObscure
                                                  .value
                                              ? DefaultImages.eyeOffIcn
                                              : DefaultImages.eyeIcn))),
                              verticalSpace(16),
                              CommonTextField(
                                controller: widget
                                    .authController.cpConfirmPasswordController,
                                labelText: 'Confirm password'.trr + '*',
                                hintText: "**********",
                                obscureText:
                                    widget.authController.isConfirmPass.value,
                                obscuringCharacter: '*',
                                validator: (value) {
                                  return Validator.validateConfirmPassword(
                                      value,
                                      signupController.passwordController.text);
                                },
                                suffix: GestureDetector(
                                    onTap: () {
                                      widget.authController.isConfirmPass
                                              .value =
                                          !widget.authController.isConfirmPass
                                              .value;
                                    },
                                    child: assetSvdImageWidget(
                                        image: widget.authController
                                                .isConfirmPass.value
                                            ? DefaultImages.eyeOffIcn
                                            : DefaultImages.eyeIcn)),
                              ),
                              verticalSpace(16),
                              // validateTextRow(
                              //     image: isPatternMatch.value
                              //         ? DefaultImages.validateIcn
                              //         : DefaultImages.notValidateIcn,
                              //     title:
                              //         "Be at least 12 characters long, with a combination of uppercase or lowercase letters and symbols"
                              //             .trr),
                              // verticalSpace(16),
                              // CommonHintDropdownWidget(
                              //   labelText: 'City'.trr,
                              //   hint: cityController.cities[0].trr,
                              //   list: cityController.cities,
                              //   value: widget.authController.selectedCity.value,
                              //   onChanged: (value) {
                              //     widget.authController.selectedCity.value = value;
                              //     signupController.cityCodeController.value = value;

                              //     var sampleselectedCity =
                              //         widget.authController.selectedCity.value;
                              //     print("SELECTED CITY =====> $sampleselectedCity");
                              //   },
                              // ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'City'.trr + "*",
                                    style: pRegular12.copyWith(
                                        color: AppColor.cLabel),
                                    textAlign: TextAlign.start,
                                  ),
                                ],
                              ),
                              verticalSpace(10),
                              SizedBox(
                                height: errorCity == "" ? 44 : 50,
                                child: DropdownButtonFormField(
                                  items: cityController.cities.map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    widget.authController.selectedCity.value =
                                        value.toString();
                                    signupController.cityCodeController.text =
                                        widget
                                            .authController.selectedCity.value;
                                  },
                                  validator: (value) {
                                    setState(() {
                                      if (value == null) {
                                        errorCity = 'Please select City'.trr;
                                      } else {
                                        errorCity = '';
                                      }
                                    });
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: '-Select-'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              errorCity == ''
                                  ? SizedBox()
                                  : Text(
                                      errorCity,
                                      style: pRegular12.copyWith(
                                          color: AppColor.cRedText),
                                    ),
                              verticalSpace(16),
                              // CommonHintDropdownWidget(
                              //   labelText: 'How did you know about WAIE?*',
                              //   hint: cityController.serviceknowns[0].trr,
                              //   list: cityController.serviceknowns,
                              //   value: widget.authController.selectedWaie.value,
                              //   onChanged: (value) {
                              //     widget.authController.selectedWaie.value = value;

                              //     var sampleselectedWaie =
                              //         widget.authController.selectedWaie.value;
                              //     print("SELECTED WAIE =====> $sampleselectedWaie");
                              //   },
                              // ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'How did you hear about WAIE?'.trr + "*",
                                    style: pRegular12.copyWith(
                                        color: AppColor.cLabel),
                                    textAlign: TextAlign.start,
                                  ),
                                ],
                              ),
                              verticalSpace(10),
                              SizedBox(
                                height: errorWaie == "" ? 44 : 50,
                                child: DropdownButtonFormField(
                                  // value: authController.selectedWaie.value,
                                  items:
                                      cityController.serviceknowns.map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    widget.authController.selectedWaie.value =
                                        value.toString();
                                    signupController
                                            .serviceKnownController.text =
                                        widget
                                            .authController.selectedWaie.value;
                                  },
                                  validator: (value) {
                                    setState(() {
                                      if (value == null) {
                                        errorWaie =
                                            'Please select waie reference'.trr;
                                      } else {
                                        errorWaie = '';
                                      }
                                    });
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: '-Select-'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              errorWaie == ''
                                  ? SizedBox()
                                  : Text(
                                      errorWaie,
                                      style: pRegular12.copyWith(
                                          color: AppColor.cRedText),
                                    ),
                              verticalSpace(16),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: Checkbox(
                                      value: widget.authController
                                          .isSelectedPolicy.value,
                                      onChanged: (value) {
                                        widget.authController.isSelectedPolicy
                                            .value = value!;
                                      },
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(4)),
                                      activeColor: AppColor.themeBlueColor,
                                    ),
                                  ),
                                  horizontalSpace(10),
                                  Expanded(
                                    child: Text.rich(
                                      TextSpan(
                                        text: 'I agree to the'.trr + ' ',
                                        style: pRegular14,
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: 'Terms and Condition'.trr,
                                            style: pBold14.copyWith(
                                                color: AppColor.cBlueFont),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {
                                                showDialog(
                                                  barrierDismissible: false,
                                                  context: Get.context!,
                                                  builder: (context) {
                                                    return AlertDialog(
                                                      contentPadding:
                                                          const EdgeInsets.all(
                                                              16),
                                                      shape:
                                                          RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          12)),
                                                      insetPadding:
                                                          const EdgeInsets.all(
                                                              25),
                                                      content:
                                                          termsAndCondition(),
                                                    );
                                                  },
                                                );
                                              },
                                          ),
                                          // TextSpan(
                                          //   text: " " + "and".trr + " ",
                                          //   style: pRegular14,
                                          // ),
                                          // TextSpan(
                                          //   text: 'Privacy Policy'.trr,
                                          //   style: pBold14.copyWith(
                                          //       color: AppColor.cBlueFont),
                                          //   recognizer: TapGestureRecognizer()
                                          //     ..onTap = () {},
                                          // ),
                                        ],
                                      ),
                                      maxLines: 2,
                                      softWrap: true,
                                    ),
                                  )
                                ],
                              ),
                              verticalSpace(16),
                              CommonButton(
                                  title: 'Continue'.trr,
                                  onPressed: () {
                                    bool hasError = false;
                                    if (signupController.fNameController.text
                                        .trim()
                                        .isEmpty) {
                                      print('fName is empty');
                                      hasError = true;
                                      commonToast('First Name is Required'.trr);
                                    } else if (signupController
                                        .lNameController.text
                                        .trim()
                                        .isEmpty) {
                                      print('lName is empty');
                                      hasError = true;
                                      commonToast('Last Name is Required'.trr);
                                    } else if (signupController
                                        .usernameController.text
                                        .trim()
                                        .isEmpty) {
                                      print('User Name is empty');
                                      hasError = true;
                                      commonToast('User Name is Required'.trr);
                                    } else if (signupController
                                        .mobileNoController.text
                                        .trim()
                                        .isEmpty) {
                                      print('Mobile Number is empty');
                                      hasError = true;
                                      commonToast(
                                          'Mobile Number is Required'.trr);
                                    } else if (signupController
                                        .passwordController.text
                                        .trim()
                                        .isEmpty) {
                                      print('Password is empty');
                                      hasError = true;
                                      commonToast('Enter your password'.trr);
                                    } else if (signupController
                                        .cityCodeController.text
                                        .trim()
                                        .isEmpty) {
                                      print('City is empty');
                                      hasError = true;
                                      commonToast('Enter your city'.trr);
                                    } else if (signupController
                                        .serviceKnownController.text
                                        .trim()
                                        .isEmpty) {
                                      print('City is empty');
                                      hasError = true;
                                      commonToast(
                                          'kindly select how you heared about us'
                                              .trr);
                                    }
                                    print("continue button pressed");
                                    print(
                                        "${signupController.mobileNoController.text}");
                                    print("form validation");
                                    if (!hasError) {
                                      if (widget.authController.isSelectedPolicy
                                              .value ==
                                          true) {
                                        print(
                                            'policy selected , call the function');
                                        signupController
                                            .registerWithEmail(true);
                                      } else {
                                        print('policy not selected');
                                        commonToast("Please select policy".trr);
                                      }
                                    }
                                  }),
                              // CommonButton(
                              //     title: 'Continue'.trr,
                              //     onPressed: () {
                              //       if (_formKey.currentState!.validate()) {
                              //         if (widget.authController.isSelectedPolicy
                              //                 .value ==
                              //             true) {
                              //           signupController
                              //               .registerWithEmail(true);
                              //         } else {
                              //           commonToast("Please select policy".trr);
                              //         }
                              //       }
                              //     }

                              //     // **** FOR OTP
                              //     // onPressed: () {
                              //     //   Get.to(() => OtpScreen(
                              //     //         authController: widget.authController,
                              //     //         isLogin: true,
                              //     //       ));
                              //     // },

                              //     //**** PREV IMPLEMENTED
                              //     // onPressed: () {
                              //     // if (formKey.currentState!.validate()) {
                              //     //   log('done');
                              //     //   if (widget.authController.isSelectedPolicy.value ==
                              //     //       true) {
                              //     //     Get.to(() => OtpScreen(
                              //     //           authController: widget.authController,
                              //     //           isLogin: true,
                              //     //         ));
                              //     //     widget.authController.clear();
                              //     //   } else {
                              //     //     commonToast("Please select policy".trr);
                              //     //   }
                              //     // }
                              //     // },
                              //     ),
                            ],
                          )),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget validateTextRow({required String image, required String title}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          assetSvdImageWidget(image: image),
          horizontalSpace(13),
          Expanded(
            child: Text(
              title,
              style: pRegular13,
              maxLines: 3,
            ),
          )
        ],
      ),
    );
  }

  Widget termsAndCondition() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
              child: Text(
            "Terms and Condition".trr,
            style: pBold20,
          )),
          verticalSpace(16),
          Text(
            "1. WAIE services are available only at Aldrees fuel stations that are listed in our website."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "2. The customer may cancel this agreement at any time and stop the services on condition that the customer must inform Aldrees Company in writing before 48 hours and handing over all the TAGS and the SMART CARDS. The customer will be refunded the remaining balance in his account."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "3. The customer assumes responsibility for all the information that he entered into the system."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "4. The tags are the property of the customer and under his responsibility and can't be transferred to another vehicle and the customer should instruct his driver not to tamper with the tags."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "5. Fuel prices are subject to change by the government and the prices will change automatically in the WAIE system by the same amount."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "6. Upon registration in the WAIE system the customer will get an account number thru which he can manage his account from anywhere in the world with internet access and he will be responsible for all transactions in his account."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "7. The customer will get a 2-year free warranty period against manufacturing defects, starting from the date of installation in the vehicle. The warranty shall not cover any misuse by the users."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "8. Fuel prices in stations outside city limits are according to government regulations."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "9. Upon signing this agreement and acceptance to use WAIE system, the customer declares that he agrees and accepts the terms and conditions of the agreement between him & Aldrees Petroleum & Transport Services Co."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "10. The Electronic tag is Free, if the balance is charged 300 SAR per tag, noting that the balance can only be used after installing the tag."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "11. In case of the electronic tag is canceled before 6 months from the date of installation, the value of the tag 100 SAR (not include vat) will be deducted from account balance."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "12. In case of the electronic tag not installed within 90 days from the date of payment order, the tag order will be auto cancel."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "13. The price of the replaced tag is 50 SAR (without vat).".trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "14. First Smart Card Free.".trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "15. Smart card price SR 10 for additional smart card, including VAT without monthly fees for normal Account and monthly fees 11.50 SR including VAT for the premium account."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(16),
          CommonButton(
            title: "Close".trr,
            onPressed: () {
              Get.back();
            },
            btnColor: AppColor.themeOrangeColor,
            horizontalPadding: 16,
          ),
        ],
      ),
    );
  }
}
