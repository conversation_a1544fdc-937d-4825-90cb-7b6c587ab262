import 'dart:convert';
import 'dart:developer';

import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/models/load_data.dart';
import 'package:http/http.dart' as http;
import '../../../../utils/api_endpoints.dart';
import '../../../utils/constants.dart';

class FilterController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  final vehicleTypeList = <Load_Data_Model>[].obs;
  final fuelTypeList = <Load_Data_Model>[].obs;

  List<Load_Data_Model> vehicleTypeModelList = [];
  List<Load_Data_Model> fuelTypeModelList = [];
  @override
  void onInit() {
    super.onInit();
    print('FilterController');
    fetchFilters();
    // fetchCityData("");
    // fetchDistrictData("");
  }

  fetchFilters() async {
    var client1 = http.Client();

    var custData = custsData.read('custData');
    try {
      var vehicleTypeResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "VEHTYPE",
            "CustId": custData["CUSTID"],
          });

      var fuelTypeResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "FUELTYPE",
            "CustId": custData["CUSTID"],
          });

      print("vehicleTypeResponse===> ${jsonDecode(vehicleTypeResponse.body)}");

      List vehicleTypeResult = jsonDecode(vehicleTypeResponse.body);

      for (int i = 0; i < vehicleTypeResult.length; i++) {
        Load_Data_Model loadData = Load_Data_Model.fromMap(
            vehicleTypeResult[i] as Map<String, dynamic>);
        vehicleTypeModelList.add(loadData);
        print("vehicleTypeResponse ===============${loadData.TYPEDESC}");
      }

      print("fuelTypeResponse===> ${jsonDecode(fuelTypeResponse.body)}");

      List fuelTypeResult = jsonDecode(fuelTypeResponse.body);

      for (int i = 0; i < fuelTypeResult.length; i++) {
        Load_Data_Model loadData =
            Load_Data_Model.fromMap(fuelTypeResult[i] as Map<String, dynamic>);
        fuelTypeModelList.add(loadData);
        print("fuelTypeResponse ===============${loadData.TYPEDESC}");
      }

      vehicleTypeList.value = vehicleTypeModelList;
      fuelTypeList.value = fuelTypeModelList;

      // return provModelList;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client1.close();
    }
  }
}
