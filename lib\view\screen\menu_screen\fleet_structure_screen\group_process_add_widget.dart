import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_structure_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';

import '../../../../utils/validator.dart';

class GroupProcessAddWidget extends StatefulWidget {
  String name;
  String typeid;
  String parentid;
  GroupProcessAddWidget(
      {super.key,
      required this.name,
      required this.typeid,
      required this.parentid});

  @override
  State<GroupProcessAddWidget> createState() => _GroupProcessAddWidgetState();
}

class _GroupProcessAddWidgetState extends State<GroupProcessAddWidget> {
  final FleetStructureController fleetStructureController =
      Get.put(FleetStructureController());
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                  child: Text(
                "Add a ${widget.name}".trr,
                style: pBold20,
              )),
              verticalSpace(21),
              CommonTextField(
                controller: fleetStructureController.groupProcessNameController,
                labelText: '${widget.name} name'.trr,
                hintText: '',
                validator: (value) {
                  return Validator.validateName(
                      value, "${widget.name} name".trr);
                },
              ),
              verticalSpace(16),
              Row(
                children: [
                  Expanded(
                      child: CommonBorderButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      Get.back();
                    },
                    bColor: AppColor.themeDarkBlueColor,
                    textColor: AppColor.cDarkBlueFont,
                  )),
                  horizontalSpace(8),
                  Expanded(
                      child: CommonButton(
                    title: 'Add'.trr,
                    onPressed: () async {
                      if (_formKey.currentState!.validate()) {
                        if (widget.typeid == "CUSTDIVISION") {
                          fleetStructureController.divisionList.refresh();
                        } else if (widget.typeid == "CUSTBRANCH") {
                          fleetStructureController.branchList.refresh();
                        } else if (widget.typeid == "CUSTDEPT") {
                          fleetStructureController.departmentList.refresh();
                        } else {
                          fleetStructureController.operationList.refresh();
                        }
                        await fleetStructureController.groupingProcessAdd(
                            widget.typeid, widget.parentid);
                      }
                    },
                    btnColor: AppColor.themeOrangeColor,
                  ))
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
