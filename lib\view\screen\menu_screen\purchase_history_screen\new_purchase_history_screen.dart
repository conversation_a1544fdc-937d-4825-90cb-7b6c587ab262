// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/purchase_history_controller/purchase_history_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/new_service_order_screen.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/new_topup_order_screen.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_balance_history_widget.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_order_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_installation/tag_installation_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import 'balance_filter_screen.dart';
import 'balance_topup_history_screen.dart';
import 'order_filter_screen.dart';
import 'order_screen.dart';

class NewPurchaseHistoryScreen extends StatefulWidget {
  const NewPurchaseHistoryScreen({super.key});

  @override
  State<NewPurchaseHistoryScreen> createState() =>
      _NewPurchaseHistoryScreenState();
}

class _NewPurchaseHistoryScreenState extends State<NewPurchaseHistoryScreen> {
  PurchaseHistoryController purchaseHistoryController =
      Get.put(PurchaseHistoryController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      //Get.back();
                      Get.off(() => DashBoardManagerScreen(
                            currantIndex: Constants.TopUpBtn == 'Y' ? 4 : 3,
                          ));
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        top: 15,
                        bottom: 15,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          assetSvdImageWidget(
                              image: DefaultImages.backIcn,
                              colorFilter: ColorFilter.mode(
                                  AppColor.cDarkBlueFont, BlendMode.srcIn)),
                          horizontalSpace(10),
                          Text(
                            // "Menu".trr,
                            "Back".trr,
                            style: pRegular18.copyWith(
                                color: AppColor.cDarkBlueFont, fontSize: 17),
                            textAlign: TextAlign.start,
                          )
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        "Purchase History".trr,
                        style: pBold20,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  companyItemWidget(
                    image: DefaultImages.companyDetailIcn,
                    title: 'Service Order'.trr,
                    onTap: () {
                      Get.to(() => NewServiceOrderScreen());
                    },
                  ),
                  if (Constants.TopUpBtn == "Y")
                    companyItemWidget(
                      image: DefaultImages.addressIcn,
                      title: 'Top up'.trr,
                      onTap: () {
                        Get.to(() => NewTopupOrderScreen());
                      },
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

companyItemWidget({
  required String title,
  required String image,
  required Function() onTap,
  TextStyle? textStyle,
  bool? isIcon,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
      child: Container(
        color: AppColor.cBackGround,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                assetSvdImageWidget(image: image),
                horizontalSpace(16),
                Text(
                  title,
                  style: textStyle ?? pRegular17,
                ),
              ],
            ),
            isIcon == false
                ? SizedBox()
                : assetSvdImageWidget(
                    image: DefaultImages.nextIcn,
                    colorFilter: ColorFilter.mode(
                      AppColor.cText,
                      BlendMode.srcIn,
                    ),
                  ),
          ],
        ),
      ),
    ),
  );
}
