import 'dart:convert';

class Report_Plates {
  final String PLATENOCODE;
  final String PLATENO;

  Report_Plates({
    required this.PLATENOCODE,
    required this.PLATENO,
  });

  Map<String, dynamic> toMap() {
    return {
      'PLATENOCODE': PLATENOCODE,
      'PLATENO': PLATENO,
    };
  }

  factory Report_Plates.fromMap(Map<String, dynamic> map) {
    return Report_Plates(
      PLATENOCODE: map['PLATENOCODE'] ?? '',
      PLATENO: map['PLATENO'] ?? '',
    );
  }
  String toJson() => json.encode(toMap());

  factory Report_Plates.fromJson(String source) =>
      Report_Plates.fromMap(json.decode(source));
}