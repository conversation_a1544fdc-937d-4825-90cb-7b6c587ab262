import 'dart:convert';

List<AuLookupsModel> auLookupsModelFromJson(String str) =>
    List<AuLookupsModel>.from(
        json.decode(str).map((x) => AuLookupsModel.fromJson(x)));

String auLookupsModelToJson(List<AuLookupsModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AuLookupsModel {
  String typeid;
  String typecode;
  String typedesc;
  int orderno;
  dynamic typedescar;
  dynamic custid;
  dynamic whCode;
  dynamic computed;
  int servicecharge;
  dynamic abbr;
  dynamic prodno;
  dynamic parentid;
  dynamic sysDate;

  AuLookupsModel({
    required this.typeid,
    required this.typecode,
    required this.typedesc,
    required this.orderno,
    required this.typedescar,
    required this.custid,
    required this.whCode,
    required this.computed,
    required this.servicecharge,
    required this.abbr,
    required this.prodno,
    required this.parentid,
    required this.sysDate,
  });

  factory AuLookupsModel.fromJson(Map<String, dynamic> json) => AuLookupsModel(
        typeid: json["TYPEID"] ?? "",
        typecode: json["TYPECODE"] ?? "",
        typedesc: json["TYPEDESC"] ?? "",
        orderno: json["ORDERNO"] ?? 0.0,
        typedescar: json["TYPEDESCAR"] ?? "",
        custid: json["CUSTID"] ?? "",
        whCode: json["WH_CODE"] ?? "",
        computed: json["COMPUTED"] ?? "",
        servicecharge: json["SERVICECHARGE"] ?? "",
        abbr: json["ABBR"] ?? "",
        prodno: json["PRODNO"] ?? "",
        parentid: json["PARENTID"] ?? "",
        sysDate: json["SYS_DATE"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "TYPEID": typeid,
        "TYPECODE": typecode,
        "TYPEDESC": typedesc,
        "ORDERNO": orderno,
        "TYPEDESCAR": typedescar,
        "CUSTID": custid,
        "WH_CODE": whCode,
        "COMPUTED": computed,
        "SERVICECHARGE": servicecharge,
        "ABBR": abbr,
        "PRODNO": prodno,
        "PARENTID": parentid,
        "SYS_DATE": sysDate,
      };
}
