import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/driverdc.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../utils/api_endpoints.dart';
import '../../../view/screen/auth/digital_coupon/create_dc_screen.dart';
import '../../../view/screen/auth/login_with_email_screen.dart';
import '../../../view/widget/common_snak_bar_widget.dart';

class DigitalCouponController extends GetxController {
  TextEditingController mobileController = TextEditingController();

  RxString verificationCode = ''.obs;
  RxString isoCode = 'SA'.obs;
  RxBool isShowOtp = false.obs;

  //late Driverdc driverdc;
  RxString OTP = ''.obs;
  RxString serialNumber = ''.obs;
  RxString mobileNo = ''.obs;
  RxString custid = ''.obs;

  //RxString SerialNumber=''.obs;

  @override
  void onInit() {
    super.onInit();
    verificationCode.value = '';
    mobileController.text = '';
  }

  Future<dynamic> VerifyMobileNumber() async {
    Loader.showLoader();
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.verifyDriverMobileNo),
          body: {
            "mobileno": mobileController.text.toString().replaceAll("+", ""),
            "IsAr": Constants.IsAr_App
          });
      print("DC Mobile verify response.body=============" + response.body);

      Map<String, dynamic> parsedJson = jsonDecode(response.body);
      Driverdc driverdc = Driverdc.fromJson(parsedJson);
      Constants.driver.clear();
      Constants.driver.add(Driverdc.fromJson(parsedJson));
      //if (driverdc.custid == "" || driverdc.custid =='') {
      Loader.hideLoader();
      if (driverdc.mobileno.length <=5 || driverdc.custid =='') {
        isShowOtp.value = false;
        loginFailWidget("Mobile is not register with Digital Account");
        commonToast("Mobile is not register with digital coupon account");
      }
      else
        {
          isShowOtp.value = true;
          OTP.value = driverdc.otp.toString();
          serialNumber.value = driverdc.serialid.toString();
          mobileNo.value = driverdc.mobileno.toString();
        }
    } catch (e) {
      log("Digital Coupon Verification Error======" + e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<dynamic> VerifyDriverOTP() async {
    Loader.showLoader();
    print("verificationCode.value======" + verificationCode.value);
    print("OTP.value======" + OTP.value);
    //Loader.showLoader();
    if (verificationCode.value == OTP.value) {
      /*Get.to(() => CreateDCScreen(
        isBack: false,
      ));*/
      var client = http.Client();
      try {
        var response = await client.post(
            Uri.parse(ApiEndPoints.baseUrl +
                ApiEndPoints.authEndpoints.getDriverServicesDetails),
            body: {
              "mobileno": mobileNo.value,
              "serialid": serialNumber.value,
              "tokenid": "",
              "IsAr": Constants.IsAr_App
            });
        //  Loader.hideLoader();
        print(" Service Details response.body===========" + response.body);
        Loader.hideLoader();
        if (response.statusCode == 200) {
          Map<String, dynamic> parsedJson = jsonDecode(response.body);
          Constants.driver.clear();
          Constants.driver.add(Driverdc.fromJson(parsedJson));
          try {
            Get.to(() => CreateDCScreen(
                  isBack: false,
                ));
          } catch (e) {
            log("Digital Coupon Verification Error======" + e.toString());
          }
        } else {
          print("VerifyDriverOTP Error ==========" +
              response.statusCode.toString());
        }
        //Loader.hideLoader();
      } catch (e) {
        Loader.hideLoader();
        // Loader.hideLoader();
        log("Digital Coupon Verification Error======" + e.toString());
        return [];
      } finally {
        // Then finally destroy the client.
        client.close();
      }
    } else {
      Loader.hideLoader();
      print("Invalid OTP");
      commonToast("Invalid OTP");
    }
  }
}
