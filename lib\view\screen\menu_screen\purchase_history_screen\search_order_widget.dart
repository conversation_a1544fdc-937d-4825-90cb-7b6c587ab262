// ignore_for_file: prefer_const_constructors, prefer_const_constructors_in_immutables

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/core/controller/menu_controller/purchase_history_controller/order_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import 'order_screen.dart';

class SearchOrderBottomSheetWidget extends StatefulWidget {
  SearchOrderBottomSheetWidget({super.key});

  @override
  State<SearchOrderBottomSheetWidget> createState() => _SearchOrderBottomSheetWidgetState();
}

class _SearchOrderBottomSheetWidgetState extends State<SearchOrderBottomSheetWidget> {
  OrderController orderController = Get.put(OrderController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    orderController.itemList.clear();
  }

  void filterSearchResults(String query) {
    orderController.itemList.value =
        orderController.orderList.where((item) => item['code'].toLowerCase().contains(query.toLowerCase())).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Get.height - 60,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      padding: EdgeInsets.all(16),
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalSpace(16),
            Row(
              children: [
                Expanded(
                  child: CommonTextField(
                    controller: orderController.searchController.value,
                    labelText: '',
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(12),
                      child: assetSvdImageWidget(image: DefaultImages.searchIcn, width: 24, height: 24),
                    ),
                    hintText: 'Search'.trr,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        orderController.itemList.clear();
                        orderController.itemList.refresh();
                      } else {
                        orderController.searchController.refresh();
                        filterSearchResults(value);
                      }

                    },
                  ),
                ),
                orderController.searchController.value.text.isEmpty
                    ? SizedBox()
                    : cancelButton(
                        () {
                          orderController.searchController.value.clear();
                          orderController.searchController.refresh();
                          orderController.itemList.clear();
                          orderController.itemList.refresh();
                        },
                      )
              ],
            ),
            verticalSpace(16),
            orderController.itemList.isEmpty
                ? Expanded(
                    child: Center(
                        child: Text(
                    "No matches".trr,
                    style: pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                  )))
                : Expanded(
                  child: ListView.builder(
                      itemCount: orderController.itemList.length,
                      shrinkWrap: true,
                      physics: BouncingScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = orderController.itemList[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: orderWidget(
                            code: data['code'],
                            orderDate: data['orderDate'],
                            status: data['status'].toString().trr,
                            orderType: data['orderType'].toString().trr,
                            serviceType: data['SERVICETYPE'].toString().trr,
                            paymentMethod: data['paymentMethod'].toString().trr,
                            paymentMethod2: data['paymentMethod2'].toString().trr,
                           // price: data['price'],
                          //  quantity: data['quantity'],
                            totalValue: data['totalValue'],
                            color: data['status'] == "Pending" ? AppColor.cLightGrey : AppColor.cLightGreen,
                            textColor: data['status'] == "Pending" ? AppColor.cDarkGreyFont : AppColor.cGreen,
                            onTap: () {
                              showModalBottomSheet(
                                context: context,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                                builder: (context) {
                                  return orderActionWidget(
                                    code: data['code'],
                                    isShowCancelOrder: data['status'] == "Claimed",
                                    printOrder: () {},
                                    downloadOrder: () {},
                                    cancelOrder: () {},
                                  );
                                },
                              );
                            },
                          ),
                        );
                      },
                    ),
                ),
          ],
        );
      }),
    );
  }

}

cancelButton(Function()? onTap) {
  return GestureDetector(
    onTap: onTap,
    child: Padding(
      padding: const EdgeInsets.only(left: 8, right: 8),
      child: Container(
          height: 44,
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
          child: Center(
              child: Text(
                "Cancel".trr,
                style: pRegular13,
              ))),
    ),
  );
}