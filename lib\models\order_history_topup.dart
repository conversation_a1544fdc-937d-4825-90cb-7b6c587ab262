import 'dart:convert';

List<OrderHistoryTopupModel> orderHistoryTopupModelFromJson(String str) =>
    List<OrderHistoryTopupModel>.from(
        json.decode(str).map((x) => OrderHistoryTopupModel.fromJson(x)));

String orderHistoryTopupModelToJson(List<OrderHistoryTopupModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OrderHistoryTopupModel {
  int line;
  String reqId;
  String reqMainType;
  String reqSubType;
  String visorEmpno;
  String reqDate;
  String stnId;
  String priority;
  String reqDesc;
  String refcompCode;
  String compCode;
  dynamic acctCode;
  String status;
  String state;
  String approval1;
  dynamic apprBy1;
  dynamic apprDate1;
  dynamic apprRemarks1;
  String approval2;
  dynamic apprBy2;
  dynamic apprDate2;
  dynamic apprRemarks2;
  String approval3;
  dynamic apprBy3;
  dynamic apprDate3;
  dynamic apprRemarks3;
  String cancelledBy;
  String approval4;
  dynamic apprBy4;
  dynamic apprDate4;
  dynamic apprRemarks4;
  String sysUserid;
  DateTime sysDate;
  dynamic pic1;
  dynamic pic2;
  dynamic pic3;
  String reqRemarks;
  dynamic vatAmt;
  String refDocno;
  dynamic info1;
  bool isvalue;

  OrderHistoryTopupModel({
    required this.line,
    required this.reqId,
    required this.reqMainType,
    required this.reqSubType,
    required this.visorEmpno,
    required this.reqDate,
    required this.stnId,
    required this.priority,
    required this.reqDesc,
    required this.refcompCode,
    required this.compCode,
    required this.acctCode,
    required this.status,
    required this.state,
    required this.approval1,
    required this.apprBy1,
    required this.apprDate1,
    required this.apprRemarks1,
    required this.approval2,
    required this.apprBy2,
    required this.apprDate2,
    required this.apprRemarks2,
    required this.approval3,
    required this.apprBy3,
    required this.apprDate3,
    required this.apprRemarks3,
    required this.cancelledBy,
    required this.approval4,
    required this.apprBy4,
    required this.apprDate4,
    required this.apprRemarks4,
    required this.sysUserid,
    required this.sysDate,
    required this.pic1,
    required this.pic2,
    required this.pic3,
    required this.reqRemarks,
    required this.vatAmt,
    required this.refDocno,
    required this.info1,
    required this.isvalue,
  });

  factory OrderHistoryTopupModel.fromJson(Map<String, dynamic> json) =>
      OrderHistoryTopupModel(
        line: json["LINE"] ?? 0,
        reqId: json["REQ_ID"] ?? "",
        reqMainType: json["REQ_MAIN_TYPE"] ?? "",
        reqSubType: json["REQ_SUB_TYPE"] ?? "",
        visorEmpno: json["VISOR_EMPNO"] ?? "",
        reqDate: json["REQ_DATE"] ?? "",
        stnId: json["STN_ID"] ?? "",
        priority: json["PRIORITY"] ?? "",
        reqDesc: json["REQ_DESC"] ?? "",
        refcompCode: json["REFCOMP_CODE"] ?? "",
        compCode: json["COMP_CODE"] ?? "",
        acctCode: json["ACCT_CODE"] ?? "",
        status: json["STATUS"] ?? "",
        state: json["STATE"] ?? "",
        approval1: json["APPROVAL_1"] ?? "",
        apprBy1: json["APPR_BY_1"] ?? "",
        apprDate1: json["APPR_DATE_1"] ?? "",
        apprRemarks1: json["APPR_REMARKS_1"] ?? "",
        approval2: json["APPROVAL_2"] ?? "",
        apprBy2: json["APPR_BY_2"] ?? "",
        apprDate2: json["APPR_DATE_2"] ?? "",
        apprRemarks2: json["APPR_REMARKS_2"] ?? "",
        approval3: json["APPROVAL_3"] ?? "",
        apprBy3: json["APPR_BY_3"] ?? "",
        apprDate3: json["APPR_DATE_3"] ?? "",
        apprRemarks3: json["APPR_REMARKS_3"] ?? "",
        cancelledBy: json["CANCELLED_BY"] ?? "",
        approval4: json["APPROVAL_4"] ?? "",
        apprBy4: json["APPR_BY_4"] ?? "",
        apprDate4: json["APPR_DATE_4"] ?? "",
        apprRemarks4: json["APPR_REMARKS_4"] ?? "",
        sysUserid: json["SYS_USERID"] ?? "",
        sysDate: DateTime.parse(json["SYS_DATE"] ?? ""),
        pic1: json["PIC1"] ?? "",
        pic2: json["PIC2"] ?? "",
        pic3: json["PIC3"] ?? "",
        reqRemarks: json["REQ_REMARKS"] ?? "",
        vatAmt: json["VAT_AMT"] ?? 0,
        refDocno: json["REF_DOCNO"] ?? "",
        info1: json["INFO1"] ?? "",
        isvalue: json["ISVALUE"] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "LINE": line,
        "REQ_ID": reqId,
        "REQ_MAIN_TYPE": reqMainType,
        "REQ_SUB_TYPE": reqSubType,
        "VISOR_EMPNO": visorEmpno,
        "REQ_DATE": reqDate,
        "STN_ID": stnId,
        "PRIORITY": priority,
        "REQ_DESC": reqDesc,
        "REFCOMP_CODE": refcompCode,
        "COMP_CODE": compCode,
        "ACCT_CODE": acctCode,
        "STATUS": status,
        "STATE": state,
        "APPROVAL_1": approval1,
        "APPR_BY_1": apprBy1,
        "APPR_DATE_1": apprDate1,
        "APPR_REMARKS_1": apprRemarks1,
        "APPROVAL_2": approval2,
        "APPR_BY_2": apprBy2,
        "APPR_DATE_2": apprDate2,
        "APPR_REMARKS_2": apprRemarks2,
        "APPROVAL_3": approval3,
        "APPR_BY_3": apprBy3,
        "APPR_DATE_3": apprDate3,
        "APPR_REMARKS_3": apprRemarks3,
        "CANCELLED_BY": cancelledBy,
        "APPROVAL_4": approval4,
        "APPR_BY_4": apprBy4,
        "APPR_DATE_4": apprDate4,
        "APPR_REMARKS_4": apprRemarks4,
        "SYS_USERID": sysUserid,
        "SYS_DATE": sysDate.toIso8601String(),
        "PIC1": pic1,
        "PIC2": pic2,
        "PIC3": pic3,
        "REQ_REMARKS": reqRemarks,
        "VAT_AMT": vatAmt,
        "REF_DOCNO": refDocno,
        "INFO1": info1,
        "ISVALUE": isvalue,
      };
}
