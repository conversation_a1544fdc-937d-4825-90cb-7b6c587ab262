// ignore_for_file: prefer_const_constructors, must_be_immutable, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/tag_installation_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/sign_up_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_installation/history_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_installation/upcoming_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../widget/common_appbar_widget.dart';


class TagInstallationScreen extends StatelessWidget {
  TagInstallationScreen({Key? key}) : super(key: key);
  TagInstallationController tagInstallationController = Get.put(TagInstallationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Obx(() {
              return Expanded(
                child: ListView(
                  padding:
                  EdgeInsets.only(top: 15, left: 16, right: 16, bottom: 16),
                  physics: BouncingScrollPhysics(),
                  shrinkWrap: true,
                  children: [
                    Row(
                      children: [
                        tabWidget(
                          title: 'New'.trr,
                          onTap: () {
                           /* promotionController.isNewPromotion.value = true;
                            promotionController.isHistory.value = false;*/
                          },
                          isSelected:
                          true ,
                        ),
                        tabWidget(
                          title: 'Promotions history'.trr,
                          onTap: () {
                          /*  promotionController.isNewPromotion.value = false;
                            promotionController.isHistory.value = true;*/
                          },
                          //isSelected: promotionController.isHistory.value,
                        ),
                      ],
                    ),
                    /*promotionController.isNewPromotion.value == true
                        ? NewPromotionScreen()
                        : PromotionHistoryScreen(promotionController: promotionController,)*/
                  ],
                ),
              );
            })
          ],
        ),
      ),
    );
  }
}

Widget tabWidget({
  String? title,
  // Color? indicatorColor,
  // Color? fontColor,
  // double? indicatorSize,
  Function()? onTap,
  bool? isSelected,
}) {
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          FittedBox(
            child: Text(
              title!,
              style: pSemiBold17.copyWith(color: isSelected == true ? AppColor.cText : AppColor.cDarkGreyFont),
            ),
          ),
          verticalSpace(8),
          Container(
            // width: Get.width/2,
            height: isSelected == true ? 3 : 1,
            color: isSelected == true ? AppColor.themeOrangeColor : AppColor.cIndicator,
          )
        ],
      ),
    ),
  );
}
