// ignore_for_file: must_be_immutable, avoid_print, prefer_const_constructors, prefer_const_constructors_in_immutables, invalid_use_of_protected_member

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/core/controller/onboarding_controller/onboarding_controller.dart';

class OnBoardingScreen extends StatefulWidget {
  OnBoardingScreen({super.key});

  @override
  State<OnBoardingScreen> createState() => _OnBoardingScreenState();
}

class _OnBoardingScreenState extends State<OnBoardingScreen> {
  OnBoardingController onBoardingController = Get.put(OnBoardingController());

  PageController? pageController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    pageController = PageController(initialPage: 0, keepPage: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Obx(() {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                flex: 2,
                child: PageView(
                  scrollDirection: Axis.horizontal,
                  controller: pageController,
                  onPageChanged: (page) {
                    onBoardingController.currantIndex.value = page;
                    print("====== ${pageController!.initialPage}");
                    print(
                        "====== $page ${onBoardingController.currantIndex.value}");
                  },
                  children: onBoardingController.onBoardingList.value,
                ),
              ),
              Expanded(
                  child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: _buildPageIndicator(),
                    ),
                    verticalSpace(68),
                    CommonIconButton(
                      title: onBoardingController.currantIndex.value == 4
                          ? "Lets start".trr
                          : 'Next'.trr,
                      iconData: onBoardingController.currantIndex.value == 4
                          ? DefaultImages.arrowNextIcn
                          : null,
                      onPressed: () {
                        onBoardingController.currantIndex.value == 4
                            ? Get.offAll(() => DashBoardManagerScreen(currantIndex: 0,))
                            : pageController!.nextPage(
                                duration: Duration(microseconds: 500),
                                curve: Curves.bounceOut);
                      },
                      btnColor: AppColor.themeOrangeColor,
                      width: 140,
                      height: 44,
                    ),
                    verticalSpace(44),
                    TextButton(
                        onPressed: () {
                          Get.offAll(() => DashBoardManagerScreen(currantIndex: 0,));
                        },
                        child: Text(
                          "SKIP".trr,
                          style: pSemiBold12.copyWith(
                              color: AppColor.cDarkGreyFont),
                        ))
                  ],
                ),
              ))
            ],
          );
        }),
      ),
    );
  }

  List<Widget> _buildPageIndicator() {
    List<Widget> list = [];
    for (int i = 0; i < onBoardingController.onBoardingList.length; i++) {
      list.add(i == onBoardingController.currantIndex.value
          ? _indicator(true)
          : _indicator(false));
    }
    return list;
  }
}

Widget _indicator(bool isActive) {
  return SizedBox(
    height: 10,
    child: AnimatedContainer(
      duration: Duration(milliseconds: 150),
      margin: EdgeInsets.symmetric(horizontal: 4.0),
      height: isActive ? 10 : 8,
      width: isActive ? 10 : 8,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isActive ? AppColor.themeOrangeColor : AppColor.cLightBlue,
      ),
    ),
  );
}
