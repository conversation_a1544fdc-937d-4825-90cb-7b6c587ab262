import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/load_profile_detail.dart';
import 'package:waie_app/utils/api_endpoints.dart';

class ProfileController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  LoadProfileDetail? loadProfileDetail;

  fetchProfileDetail() async {
    custsData.remove('custData');
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    print("response.userid *********** $userid");
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.loadProfileDetails),
          body: {
            "custid": userid.toString(),
            "isAR": 'false',
          });
      if (response.statusCode == 200) {
        print("******************************************");
        print("response.statusCode *********** ${response.statusCode}");
        var result = jsonDecode(response.body);
        print("result *********** $result");
        custsData.writeIfNull('custData', result);
        return loadProfileDetail;
      } else {
        print('error fetching data');
      }
    } catch (e) {
      print('error $e');
    }
  }
}
