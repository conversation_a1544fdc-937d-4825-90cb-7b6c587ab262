// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/fleet_structure_screen/fleet_structure_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import 'add_division_widget.dart';
import 'search_fleet_widget.dart';

class NoFleetStructureScreen extends StatelessWidget {
  const NoFleetStructureScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [

        buildAppTitleWidget(searchTap: () {
          showModalBottomSheet(
            context: context,
            barrierColor: AppColor.cBlackOpacity,
            shape: RoundedRectangleBorder(
                borderRadius:
                BorderRadius.vertical(top: Radius.circular(12))),
            isScrollControlled: true,
            builder: (context) {
              return SearchFleetStructureWidget();
            },
          );
        }),
        Container(
          padding: EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "Fleet structure".trr,
                style: pBold20,
                textAlign: TextAlign.center,
              ),
              GestureDetector(
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      barrierColor: AppColor.cBlackOpacity,
                      shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.vertical(top: Radius.circular(12))),
                      isScrollControlled: true,
                      builder: (context) {
                        return SearchFleetStructureWidget();
                      },
                    );
                  },
                  child:
                      assetSvdImageWidget(image: DefaultImages.searchCircleIcn))
            ],
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 25,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                assetSvdImageWidget(image: DefaultImages.noFleetStructureImg),
                verticalSpace(24),
                Text(
                  "Add different organization levels for your fleet".trr,
                  style: pSemiBold17,
                ),
                verticalSpace(16),
                Text(
                    "Easily manage vehicles across divisions, branches, departments and operations."
                        .trr,
                    style: pRegular13,
                    textAlign: TextAlign.center),
                verticalSpace(24),
                CommonIconButton(
                  iconData: DefaultImages.circleAddIcn,
                  title: 'Add Division'.trr,
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.vertical(top: Radius.circular(16))),
                      backgroundColor: AppColor.cBackGround,
                      barrierColor: AppColor.cBlackOpacity,
                      isScrollControlled: true,
                      builder: (context) {
                        return AddDivisionWidget();
                      },
                    );
                  },
                  btnColor: AppColor.themeOrangeColor,
                  width: 220,
                  radius: 6,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
buildAppTitleWidget({Function()? searchTap}) {
  return Container(
    padding: EdgeInsets.only(left: 16, right: 16),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () {
            Get.offAll(() => FleetStructureScreen());
          },
          child: Container(
            padding: EdgeInsets.only(
              top: 15,
              bottom: 15,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                assetSvdImageWidget(
                    image: DefaultImages.backIcn,
                    colorFilter: ColorFilter.mode(
                        AppColor.cDarkBlueFont, BlendMode.srcIn)),
                horizontalSpace(10),
                Text(
                  // "Menu".trr,
                  "Back".trr,
                  style: pRegular18.copyWith(
                      color: AppColor.cDarkBlueFont, fontSize: 17),
                  textAlign: TextAlign.start,
                )
              ],
            ),
          ),
        ),
        Expanded(
          child: Align(
            alignment: Alignment.center,
            child: Text(
              "Fleet structure".trr,
              style: pBold20,
              textAlign: TextAlign.center,
            ),
          ),
        ),
        // GestureDetector(
        //     onTap: searchTap,
        //     child: assetSvdImageWidget(image: DefaultImages.searchCircleIcn))
      ],
    ),
  );
}
