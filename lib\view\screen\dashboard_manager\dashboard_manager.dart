// ignore_for_file: avoid_print, must_be_immutable, prefer_const_constructors_in_immutables, prefer_const_constructors

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/profile_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import '../../../core/controller/dashboard_manager_controller/dashboard_manager_controller.dart';
import '../../../core/controller/location_controller/gas_station_controller.dart';
import '../../../core/controller/menu_controller/order_controller/price_tag_card_controller.dart';
import '../../../core/controller/menu_controller/profile_controller/address_load_controller.dart';
import '../../../core/controller/menu_controller/usermenucontroller.dart';

class DashBoardManagerScreen extends StatefulWidget {
  final int currantIndex;

  DashBoardManagerScreen({super.key, required this.currantIndex});

  @override
  State<DashBoardManagerScreen> createState() => _DashBoardManagerScreenState();
}

class _DashBoardManagerScreenState extends State<DashBoardManagerScreen> {
  DashboardManagerController dashboardManagerController =
      Get.put(DashboardManagerController());
  PriceTagCardController priceTagCardController =
      Get.put(PriceTagCardController());
  Address_Data_Controller addressLoadController =
      Get.put(Address_Data_Controller());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      dashboardManagerController.currantIndex.value = widget.currantIndex;
      dashboardManagerController.currantIndex.refresh();
      dashboardManagerController.naviBarItemList.refresh();
      print('dashboard');
      print(dashboardManagerController.currantIndex.value);
      print('hasCloseAccountRequest');
      print(Constants.hasCloseAccountRequest);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColor.cBackGround,
      child: SafeArea(
        bottom: false,
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Obx(() {
            return Scaffold(
              backgroundColor: AppColor.cBackGround,
              body: dashboardManagerController.naviBarItemList[
                  dashboardManagerController.currantIndex.value]['screen'],
              // floatingActionButton:
              //     dashboardManagerController.currantIndex.value == 0
              //         ? FloatingActionButton(
              //             onPressed: () {},
              //             backgroundColor: AppColor.themeDarkBlueColor,
              //             child:
              //                 assetSvdImageWidget(image: DefaultImages.chatIcn))
              //         : SizedBox(),
              bottomNavigationBar: Obx(() {
                return BottomNavigationBar(
                  backgroundColor: AppColor.cWhite,
                  type: BottomNavigationBarType.fixed,
                  landscapeLayout: BottomNavigationBarLandscapeLayout.centered,
                  selectedItemColor: AppColor.cOrangeFont,
                  unselectedItemColor: AppColor.cDarkGreyFont,
                  showUnselectedLabels: true,
                  showSelectedLabels: true,
                  currentIndex: dashboardManagerController.currantIndex.value,
                  onTap: (index) {
                    int i = Constants.TopUpBtn == 'Y' ? 4 : 3;
                    if (Constants.hasCloseAccountRequest == 'Y') {
                      if (index == 0) {
                        dashboardManagerController.currantIndex.value = index;
                      }
                      if (index == i) {
                        dashboardManagerController.currantIndex.value = i;
                      }
                      if (index != 0 && index != i) {
                        dashboardManagerController.currantIndex.value = 0;
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            backgroundColor: AppColor.cLiteYellow,
                            content: Text(
                                "Your account has pending closing request, it cannot use any facility at this moment.",
                                style: pBold14.copyWith(
                                    color: AppColor.cBlackFont)),
                          ),
                        );
                      }
                    } else if (Constants.custAcctType == "C" &&
                        Constants.custAcctStatus != "A") {
                      if (index == 0) {
                        dashboardManagerController.currantIndex.value = index;
                      }
                      if (index == i) {
                        dashboardManagerController.currantIndex.value = i;
                      }
                      if (index != 0 && index != i) {
                        //dashboardManagerController.currantIndex.value = 0;
                        Get.to(() => ProfileScreen());
                      }
                    } else {
                      dashboardManagerController.currantIndex.value = index;
                    }
                    // else if (Constants.custAcctStatus != "A") {
                    //   Future.delayed(
                    //     const Duration(seconds: 3),
                    //     () => Get.off(() => DashBoardManagerScreen(
                    //           currantIndex: Constants.TopUpBtn == 'Y' ? 4 : 3,
                    //         )),
                    //   );
                    //   ScaffoldMessenger.of(Get.context!).showSnackBar(
                    //     SnackBar(
                    //       backgroundColor: AppColor.themeOrangeColor,
                    //       content: Text("Please update your profile.",
                    //           style:
                    //               pBold14.copyWith(color: AppColor.cWhiteFont)),
                    //     ),
                    //   );
                    // }
                  },
                  selectedLabelStyle: pBold12.copyWith(
                      color: AppColor.cOrangeFont, fontSize: 11),
                  unselectedLabelStyle: pRegular10.copyWith(
                      color: AppColor.cDarkGreyFont, fontSize: 11),
                  items: List.generate(
                      dashboardManagerController.naviBarItemList.length,
                      (index) {
                    var data =
                        dashboardManagerController.naviBarItemList[index];
                    return BottomNavigationBarItem(
                      icon: bottomIconWidget(
                          image: data['icon'],
                          title: data['title'],
                          color:
                              dashboardManagerController.currantIndex.value ==
                                      index
                                  ? AppColor.cOrangeFont
                                  : AppColor.cDarkGreyFont,
                          imageColor:
                              dashboardManagerController.currantIndex.value ==
                                      index
                                  ? AppColor.cOrangeFont
                                  : AppColor.cDarkGreyFont),
                      label: data['title'].toString().trr,
                    );
                  }),
                );
              }),
            );
          }),
        ),
      ),
    );
  }
}

Widget bottomIconWidget(
    {required String image,
    required String title,
    Color? color,
    Color? imageColor}) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8, top: 6),
    child: assetSvdImageWidget(
      image: image,
      width: 33,
      height: 33,
      colorFilter:
          ColorFilter.mode(imageColor ?? AppColor.cOrangeFont, BlendMode.srcIn),
    ),
  );
}
