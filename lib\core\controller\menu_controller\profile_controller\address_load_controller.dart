import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:waie_app/core/controller/menu_controller/profile_controller/new_profile_controller.dart';
import 'package:waie_app/models/load_data.dart';
import 'package:http/http.dart' as http;
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/constants.dart';

class Address_Data_Controller extends GetxController {
  RxList<String> countryList = <String>[].obs;
  NewProfileController newProfileController = Get.put(NewProfileController());

  //RxList<String> provList = <String>[].obs;
  final provList = <Load_Data_Model>[].obs;

  //RxList<String> distList = <String>[].obs;
  final distList = <Load_Data_Model>[].obs;
  RxList<String> distList1 = <String>[].obs;

  //RxList<String> cityList = <String>[].obs;
  final cityList = <Load_Data_Model>[].obs;
  final salesmenList = <Load_Data_Model>[].obs;
  final waieList = <Load_Data_Model>[].obs;
  final cntryList = <Load_Data_Model>[].obs;
  final langList = <Load_Data_Model>[].obs;

  List<Load_Data_Model> distModelList = [];
  List<Load_Data_Model> provModelList = [];
  List<Load_Data_Model> cityModelList = [];
  List<Load_Data_Model> salesmenModeList = [];
  List<Load_Data_Model> waieModeList = [];
  List<Load_Data_Model> countryModeList = [];
  List<Load_Data_Model> langModeList = [];

  @override
  void onInit() {
    super.onInit();
    print('DataLoadController');
    fetchProvData();
    // fetchCityData("");
    // fetchDistrictData("");
  }

  Future<List<Load_Data_Model>> fetchProvData() async {
    var client1 = http.Client();

    try {
      var countryResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "COUNTRY",
            "searchCode": "SA",
            "lookupCode": ""
          });
      var provResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "PROV",
            "searchCode": "",
            "lookupCode": ""
          });

      var cityResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "CITY",
            "searchCode": "",
            "lookupCode": ""
          });

      // var distResponse = await client1.post(
      //     Uri.parse(
      //         ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
      //     body: {
      //       "IsAR": Constants.IsAr_App,
      //       "mode": "DIST",
      //       "searchCode": "",
      //       "lookupCode": ""
      //     });
      var salesmanResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "LOOKUPS",
            "searchCode": "SALESMAN",
            "lookupCode": ""
          });
      var waieResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "LOOKUPS",
            "searchCode": "ORIGIN",
            "lookupCode": ""
          });
      var langResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "LOOKUPS",
            "searchCode": "LANG",
            "lookupCode": ""
          });

      //print("responsegetProv===> ${jsonDecode(provResponse.body)}");

      List countryResult = jsonDecode(countryResponse.body);

      for (int i = 0; i < countryResult.length; i++) {
        Load_Data_Model countryData =
            Load_Data_Model.fromMap(countryResult[i] as Map<String, dynamic>);
        countryModeList.add(countryData);
        //print("responsegetProv ===============${loadData1.TYPEDESC}");
      }

      List provResult = jsonDecode(provResponse.body);

      for (int i = 0; i < provResult.length; i++) {
        Load_Data_Model provData =
            Load_Data_Model.fromMap(provResult[i] as Map<String, dynamic>);
        provModelList.add(provData);
        //print("responsegetProv ===============${loadData1.TYPEDESC}");
      }

      print("responsegetCity===> ${jsonDecode(cityResponse.body)}");

      List cityResult = jsonDecode(cityResponse.body);

      for (int i = 0; i < cityResult.length; i++) {
        Load_Data_Model loadData2 =
            Load_Data_Model.fromMap(cityResult[i] as Map<String, dynamic>);
        cityModelList.add(loadData2);
        //print("cityResult ===============${loadData2.TYPEDESC}");
      }

      //print("responsegetDIST===> ${jsonDecode(distResponse.body)}");

      // List distResult = jsonDecode(distResponse.body);

      // //for (int i = 0; i < distResult.length-1; i++) {
      // for (int i = 0; i < distResult.length; i++) {
      //   Load_Data_Model loadData3 =
      //       Load_Data_Model.fromMap(distResult[i] as Map<String, dynamic>);
      //   distModelList.add(loadData3);
      //   // print("distResult ===============${loadData3.TYPEDESC}");
      // }

      print("responsegetSalesman===> ${jsonDecode(salesmanResponse.body)}");

      List salesManResult = jsonDecode(salesmanResponse.body);

      for (int i = 0; i < salesManResult.length; i++) {
        Load_Data_Model loadData4 =
            Load_Data_Model.fromMap(salesManResult[i] as Map<String, dynamic>);
        salesmenModeList.add(loadData4);
        //print("loadData ===============${loadData4.TYPEDESC}");
      }

      print("responsegetWAIE===> ${jsonDecode(waieResponse.body)}");

      List waieResult = jsonDecode(waieResponse.body);

      for (int i = 0; i < waieResult.length; i++) {
        Load_Data_Model loadData5 =
            Load_Data_Model.fromMap(waieResult[i] as Map<String, dynamic>);
        waieModeList.add(loadData5);
        //print("loadData ===============${loadData5.TYPEDESC}");
      }

      print("langResponse===> ${jsonDecode(langResponse.body)}");

      List langResult = jsonDecode(langResponse.body);

      for (int i = 0; i < langResult.length; i++) {
        Load_Data_Model langData =
            Load_Data_Model.fromMap(langResult[i] as Map<String, dynamic>);
        langModeList.add(langData);
        //print("loadData ===============${loadData5.TYPEDESC}");
      }
      if (newProfileController.selectedCity.value.isNotEmpty &&
          newProfileController.selectedRegion.value.isNotEmpty) {
        var distResponse = await client1.post(
            Uri.parse(ApiEndPoints.baseUrl +
                ApiEndPoints.authEndpoints.loadQueryData),
            body: {
              "IsAR": Constants.IsAr_App,
              "mode": "DIST",
              "searchCode": newProfileController.selectedCity.value,
              "lookupCode": newProfileController.selectedRegion.value
            });

        print("responsegetDIST===> ${jsonDecode(distResponse.body)}");

        List distResult = jsonDecode(distResponse.body);

        //for (int i = 0; i < distResult.length-1; i++) {
        for (int i = 0; i < distResult.length; i++) {
          Load_Data_Model loadData3 =
              Load_Data_Model.fromMap(distResult[i] as Map<String, dynamic>);
          distModelList.add(loadData3);
          // print("distResult ===============${loadData3.TYPEDESC}");
        }
        distList.value = distModelList;
      }

      provList.value = provModelList;
      // distList.value = distModelList;
      cityList.value = cityModelList;
      salesmenList.value = salesmenModeList;
      waieList.value = waieModeList;
      cntryList.value = countryModeList;
      langList.value = langModeList;

      print("provModelList.value===> $provList");

      return provModelList;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client1.close();
    }
  }

  Future<List<Load_Data_Model>> fetchCityData(String regionCode) async {
    var client1 = http.Client();

    try {
      var cityResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "CITY",
            "searchCode": regionCode,
            "lookupCode": ""
          });

      //print("responsegetCity===> ${jsonDecode(cityResponse.body)}");

      List cityResult = jsonDecode(cityResponse.body);

      for (int i = 0; i < cityResult.length; i++) {
        Load_Data_Model loadData2 =
            Load_Data_Model.fromMap(cityResult[i] as Map<String, dynamic>);
        cityModelList.add(loadData2);
        //print("cityResult ===============${loadData2.TYPEDESC}");
      }
      cityList.value = cityModelList;

      print("cityModelList.value===> $cityList");

      return cityModelList;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client1.close();
    }
  }

  Future<List<Load_Data_Model>> fetchDistData(
      String cityCode, String regionCode) async {
    var client1 = http.Client();

    try {
      var distResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "DIST",
            "searchCode": cityCode,
            "lookupCode": regionCode
          });

      //print("responsegetCity===> ${jsonDecode(cityResponse.body)}");

      List distResult = jsonDecode(distResponse.body);

      //for (int i = 0; i < distResult.length-1; i++) {
      for (int i = 0; i < distResult.length; i++) {
        Load_Data_Model loadData3 =
            Load_Data_Model.fromMap(distResult[i] as Map<String, dynamic>);
        distModelList.add(loadData3);
        // print("distResult ===============${loadData3.TYPEDESC}");
      }
      distList.value = distModelList;

      print("distModelList.value===> $distList");

      return distModelList;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client1.close();
    }
  }
}
