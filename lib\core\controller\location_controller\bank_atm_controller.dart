// ignore_for_file: prefer_const_constructors

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:waie_app/models/bankatm_station.dart';
import 'package:waie_app/models/gas_station.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/refundable_service.dart';
import 'dart:math';

class BankAtmController extends GetxController {
  RxBool isGasStations = false.obs;
  RxBool isSalesOffice = false.obs;
  RxBool isInstallationCenters = false.obs;
  RxBool isStations = false.obs;
  RxBool isCarService = false.obs;
  RxBool isMosque = false.obs;
  RxBool isFoodResturant = false.obs;
  RxBool isCarRental = false.obs;
  RxBool isBankAtm = true.obs;
  RxBool isDefaultMap = true.obs;

  RxBool isMap = true.obs;
  RxBool isList = false.obs;
  List<String> saudiArabiaCities = [
    'Riyadh',
    'Jeddah',
    'Mecca',
    'Medina',
    'Dammam',
    'Taif',
    'Tabuk',
    'Al Khobar',
    'Yanbu',
    'Abha',
    // Add more cities as needed
  ];

  RxString selectedCity = ''.obs;

  void setSelectedCity(String city) {
    selectedCity.value = city;
  }

  List<LatLng> latLongList = [
    LatLng(21.**************, 53.***************),
    LatLng(20.***************, 44.**************),
    LatLng(23.***************, 54.***************),
    LatLng(22.***************, 47.***************),
    LatLng(25.***************, 38.***************),
    LatLng(23.***************, 45.**************),
    LatLng(20.***************, 49.**************),
  ];
  List<LatLng> salesOfficeList = [
    LatLng(21.**************, 53.***************),
    LatLng(20.***************, 44.**************),
    LatLng(23.***************, 54.***************),
    LatLng(22.***************, 47.***************),
    LatLng(25.***************, 38.***************),
    LatLng(23.***************, 45.**************),
    LatLng(20.***************, 49.**************),
  ];
  List<LatLng> installationList = [
    LatLng(21.**************, 53.***************),
    LatLng(20.***************, 44.**************),
    LatLng(23.***************, 54.***************),
    LatLng(22.***************, 47.***************),
    LatLng(25.***************, 38.***************),
    LatLng(23.***************, 45.**************),
    LatLng(20.***************, 49.**************),
  ];
  List listData = [
    {
      'data': "Riyadh",
      'list': [
        {
          "title": "Al Reef",
          'subTitle': "RAFB7699، 7699 Riyadh 13314, Saudi Arabia",
        },
        {
          "title": "Asment Exit - 18",
          'subTitle': "REFA7322, 7322 Mahail, 4063",
        },
      ]
    },
    {
      'data': "Al Ahsa",
      'list': [
        {
          "title": "Al Makhaita 2",
          'subTitle':
              "Al Qadisiyah, Al Mubarraz 36422, Saudi Arabia ا,،,لملك سعود",
        },
      ]
    },
    {
      'data': "Duraidah",
      'list': [
        {
          "title": "Al Faizy",
          'subTitle':
              "QBWE7235، 7235 عمر بن الخطاب، 2639، حي النهضة, Buraydah 52388, Saudi Arabia",
        },
      ]
    },
  ];
  RxList itemList = [].obs;

  var bankAtmstationsList = <BankAtmStation>[].obs;

  List<LatLng> bankAtmStats = [];

  getAllBankAtmStations() async {
    bankAtmStats.clear();
    bankAtmstationsList.clear();
    List<BankAtmStation> stations = [];
    var client = http.Client();

    try {
      print("Fetching Stations...");
      var response = await client.get(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnBankAtm));

      //log("getstations API Response: ${jsonDecode(response.body)}");

      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        BankAtmStation station =
            BankAtmStation.fromJson(result[i] as Map<String, dynamic>);

        if (station.latitude.isNotEmpty && station.longitude.isNotEmpty) {
          stations.add(station);
          bankAtmStats.add(LatLng(
              double.parse(station.latitude), double.parse(station.longitude)));
        }
      }

      bankAtmstationsList.value = stations;

      return bankAtmstationsList;
    } catch (e) {
      //log("Error fetching stations: $e");
      return [];
    } finally {
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('GasStationController');
    if (bankAtmstationsList.isEmpty) {
      print("sulod");
      //await getAllBankAtmStations();
    }
    //Navigator.of(Get.context!).pop();
  }

  double? userLatitude;
  double? userLongitude;
  double radiusInKm = 200.0;

  List<BankAtmStation> gasStationListFiltered = [];

  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double R = 6371;
    final double dLat = _degToRad(lat2 - lat1);
    final double dLon = _degToRad(lon2 - lon1);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degToRad(lat1)) *
            cos(_degToRad(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return R * c;
  }

  double _degToRad(double deg) => deg * (pi / 180);

  var allStationsList = <BankAtmStation>[].obs;

  getBankATMStations200kmRadius(
      double userLatitude, double userLongitude, double radiusInKm) async {
    List<BankAtmStation> gasses = [];
    List<BankAtmStation> allStations = [];
    print('yahan aya 200km');
    var client = http.Client();
    bankAtmStats.clear();
    bankAtmstationsList.clear();
    allStationsList.clear();

    try {
      var response = await client.get(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnBankAtm));
      List result = jsonDecode(response.body);
      print('resultofstations $result');
      for (int i = 0; i < result.length; i++) {
        BankAtmStation gas =
            BankAtmStation.fromJson(result[i] as Map<String, dynamic>);

        if (gas.latitude != "" && gas.longitude != "") {
          double stationLatitude = double.parse(gas.latitude);
          double stationLongitude = double.parse(gas.longitude);

          allStations.add(gas);

          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);
          print('distance $distance');
          print('radius $radiusInKm');
          print('distance <= radius ${distance <= radiusInKm}');

          if (distance <= radiusInKm) {
            gasses.add(gas);

            bankAtmStats.add(LatLng(stationLatitude, stationLongitude));
          }
        }
      }

      bankAtmstationsList.value = gasses;

      allStationsList.value = allStations;

      print("Filtered gasStats >>>>> ${jsonEncode(bankAtmStats)}");

      return bankAtmstationsList;
    } catch (e) {
      print("getAllGasStations Error: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }
}
