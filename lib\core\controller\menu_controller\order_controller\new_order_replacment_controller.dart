import 'dart:convert';
import 'dart:developer' as logs;
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:quickalert/quickalert.dart';
import 'package:waie_app/core/controller/review_controller/review_controller.dart';
import 'package:waie_app/models/b2b_bank.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/balancetopupnew.dart';
import 'package:waie_app/models/profile.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/order_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../../view/widget/common_button.dart';
import '../../../../view/widget/common_otp_textfield.dart';

class NewOrderReplacementController extends GetxController {
  TextEditingController otpText = TextEditingController();
  TextEditingController CustIBAN = TextEditingController(
      text: Constants.custB2B_IBAN.isEmpty
          ? "AUTO GENERATED"
          : Constants.custB2B_IBAN);
  GetStorage custsData = GetStorage('custsData');
  RxBool isTag = true.obs;
  RxBool isSmartCard = false.obs;
  RxInt quantity = 0.obs;
  RxString amountValue = "".obs;
  RxString vatAmount = "".obs;
  RxString totalAmount = "".obs;
  RxInt currantIndex = 3.obs;
  RxBool isSubmit = true.obs;
  RxBool isSTCPayValidate = false.obs;
  RxBool isSTCPaySubmit = true.obs;
  RxBool isMokafa = true.obs;
  RxBool isQitaf = false.obs;
  RxBool isANB = false.obs;
  RxBool isALINMA = false.obs;
  RxString isoCode = 'SA'.obs;

  // String paytype = "C";
  RxString paytype = "C".obs;
  String prmType = "";

  RxString prmPayType = "".obs;
  List<B2BBankListModel> bankList = [];
  RxList paymentOptionList = [].obs;

  // List<PriceTagCardModel> prices = [];
  RxString topUpAmount = "0".obs;
  RxString unitPrice = "0".obs;
  RxString servicetype = "0".obs;

  // RxString vatAmount = "0".obs;
  RxString amount = "0".obs;

  // RxString totalAmount = "0".obs;
  RxString subTotal = "0".obs;
  RxString quantty = "0".obs;

  // RxInt quantity = 0.obs;
  RxString srvType = "T".obs;
  RxString useBalanceOTP = "N".obs;
  RxBool isPaymentOpt = false.obs;

  //var priceTagCards = [];

  fetchPriceTagCards() async {
    Loader.showLoader();
    try {
      SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
      var custid = sharedUser2.getString('userid');
      var user = sharedUser2.getString('user');
      Map cardInfo = json.decode(user!);
      Profile userData = Profile.fromJson(cardInfo);
      var client = http.Client();
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.newGetPrice),
          body: {
            "custid": custid,
            "servType": srvType.value.toString() ?? "T",
            "qty": "1",
            "replacement": "true",
          });
      print("Tag Selection Response===========${response.body}");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        print(response.statusCode);
        print(jsonDecode(response.body));

        final Map<String, dynamic> result = jsonDecode(response.body);

        print(result["SERVICETYPE"]);

        topUpAmount.value = result["TOPUPAMT"];
        unitPrice.value = result["UNITPRICE"];
        servicetype.value = result["SERVICETYPE"];
        vatAmount.value = result["VATAMT"];
        amount.value = result["AMT"];
        totalAmount.value = result["TOTAMT"];
        subTotal.value = result["SUBTOTAL"];
        quantty.value = result["QUANTITY"];
        useBalanceOTP.value = result["USEBALANCEOTP"];
        isPaymentOpt.value = result["isPaymentOpt"];
      }
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  void addNewPaymentOption() {
    int i = 0;
    // if (Constants.custIs_partner == "N" && Constants.custBalance != "0")
    {
      RxMap<String, Object> newOption = {
        "id": 0.obs,
        'value': false.obs,
        "balance": Constants.custBalance,
        'title': 'Use my Balance Credits'.tr,
        'label': ''
      }.obs;
      RxMap<String, Object> newOption1 = {
        "id": 1.obs,
        'value': false.obs,
        "balance": "",
        'title': 'Cash',
        'label': 'Cash payment instructions'
      }.obs;
      RxMap<String, Object> newOption3 = {
        "id": 3.obs,
        'value': true.obs,
        "balance": "",
        'title': 'MADA',
        'label': 'MADA Payment Note'
      }.obs;
      RxMap<String, Object> newOption2 = {
        "id": 2.obs,
        'value': false.obs,
        "balance": "",
        'title': 'E. Transfer',
        'label': 'E. Transfer Note'
      }.obs;

      paymentOptionList.insert(0, newOption);
      paymentOptionList.insert(1, newOption1);
      paymentOptionList.insert(2, newOption2);
      paymentOptionList.insert(3, newOption3);
      //if (Constants.custRegType == "I" && Constants.custAcctType == "C") {
      // if (Constants.promoPayment == "Y")
      {
        RxMap<String, Object> newOption4 = {
          "id": 4.obs,
          'value': false.obs,
          "balance": "",
          'title': 'Aldrees promotion'.tr,
          'label': 'Aldrees promotion Note'
        }.obs;
        paymentOptionList.insert(4, newOption4);
      }
      RxMap<String, Object> newOption6 = {
        "id": 5.obs,
        'value': false.obs,
        "balance": "",
        'title': 'Stc pay'.tr,
        'label': 'Stc pay Note'
      }.obs;
      paymentOptionList.insert(5, newOption6);
    }
  }

  TextEditingController emailController = TextEditingController();
  TextEditingController topUpAmtController = TextEditingController();
  TextEditingController serviceTypeController = TextEditingController();
  TextEditingController payTypeController = TextEditingController();
  TextEditingController qtyController = TextEditingController();
  TextEditingController purchaseTotalController = TextEditingController();
  List itemList = [];
  RxString selectedItem = "".obs;

  String phoneNumber = "";
  RxBool isValidate = false.obs;

  // RxBool isSubmit = true.obs;
  RxString verificationCode = ''.obs;
  RxBool isDelivery = true.obs;
  RxBool isPickUp = false.obs;
  RxBool isExpanded = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    qtyController.text = "1";
    payTypeController.text = "B";
    addNewPaymentOption();
    getB2BSender();
    if (Constants.custB2B_BANK == "NCB") {
      selectedItem.value = '(NCB) AL AHLI BANK / البنك الأهلي التجاري';
      itemList = ["(NCB) AL AHLI BANK / البنك الأهلي التجاري"];
    } else if (Constants.custB2B_BANK == "ARB") {
      selectedItem.value = '(ARB) AL-RAJHI BANK / مصرف الراجحي';
      itemList = ["(ARB) AL-RAJHI BANK / مصرف الراجحي"];
    } else {
      selectedItem.value = 'RIYADH BANK / بنك الرياض';
      itemList = ["RIYADH BANK / بنك الرياض"];
    }
    if (Constants.custB2B_BANK != "") {
      isExpanded.value = false;
    } else {
      isExpanded.value = true;
    }

    if (Constants.virtualAccountBTN == "Y" && Constants.custB2B_IBAN == "") {
      CustIBAN.text = "AUTO GENERATED";
    }
  }

  Future<List<B2BBankListModel>> getB2BSender() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    try {
      var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getB2BSENDER),
      );

      print("getB2BSender=====${response.body}");

      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        B2BBankListModel bank =
            B2BBankListModel.fromJson(result[i] as Map<String, dynamic>);
        bankList.add(bank);
      }

      return bankList;
    } catch (e) {
      logs.log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void showErrorMsg(String msg) {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.all(16),
          contentPadding: const EdgeInsets.all(16),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("ERROR".tr, style: pBold20),
              verticalSpace(24),
              Text(
                // "Balance TopUp Failed.".tr,
                msg,
                style: pRegular16,
                textAlign: TextAlign.center,
              ),
              verticalSpace(24),
              TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text("OK"))
            ],
          ),
        );
      },
    );
  }

  Widget cashOrderSuccessWidget({required String code}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Center(
            child: Text(
          "Success".tr,
          style: pBold20,
        )),
        verticalSpace(14),
        Center(
            child: Text(
          "Your order has been successful!.".tr,
          style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          textAlign: TextAlign.center,
        )),
        verticalSpace(8),
        Text.rich(
          TextSpan(
            text:
                'your order has been created, you can print the order details/invoice from your TOPUP history.'
                    .tr,
            style: pRegular13,
            children: <TextSpan>[
              TextSpan(text: ' #$code ', style: pBold12.copyWith(fontSize: 13)),
              TextSpan(
                text: '${'details in'.tr} ',
              ),
              TextSpan(
                  text: 'Order History'.tr,
                  style: pBold12.copyWith(
                      fontSize: 13, color: AppColor.cLinkText)),
            ],
          ),
          textAlign: TextAlign.center,
        ),
        verticalSpace(16),
        successOrderWidget(
            unitPrice: "${qtyController.text} SAR",
            amount: "${topUpAmtController.text} SAR",
            Total: "${purchaseTotalController.text} SAR"),
        verticalSpace(16),
        Container(
          decoration: BoxDecoration(
              color: AppColor.lightOrangeColor,
              borderRadius: BorderRadius.circular(6)),
          padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 20),
          child: Text(
              "Order will be automatically confirmed after successful payment"
                  .tr,
              style: pRegular13,
              textAlign: TextAlign.center),
        ),
        verticalSpace(16),
        buttonRow(
          pdfFun: () {
            Get.back();
          },
          dashboardFun: () {
            Get.offAll(DashBoardManagerScreen(
              currantIndex: 0,
            ));
          },
        ),
      ],
    );
  }

  Widget successOrderWidget(
      {String? unitPrice, String? amount, String? Total}) {
    return Container(
      decoration: BoxDecoration(
          color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          totalsDataWidget(title: "Unit Price".tr, value: unitPrice!),
          verticalSpace(8),
          totalsDataWidget(title: "Amount".tr, value: amount!),
          verticalSpace(8),
          totalsDataWidget(title: "Total".tr, value: Total!),
        ],
      ),
    );
  }

  Widget totalsDataWidget(
      {required String title,
      required String value,
      double? fontSize,
      Color? fontColor}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: pRegular13.copyWith(
              fontSize: fontSize ?? 13, color: fontColor ?? AppColor.cText),
        ),
        Text(
          value,
          style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
        ),
      ],
    );
  }

  Future<List<BalanceTopUpModelNew>> createPromotionOrder() async {
    Loader.showLoader();

    if (payTypeController.text == "B") {
      paytype.value = "B";
    } else if (payTypeController.text == "C") {
      paytype.value = "C";
    } else if (payTypeController.text == "E") {
      paytype.value = "E";
    } else if (payTypeController.text == "D") {
      paytype.value = "D";
    } else {
      paytype.value = "PRM";
      if (isMokafa.value) {
        prmType = "J";
      } else if (isQitaf.value) {
        prmType = "S";
      } else if (isANB.value) {
        prmType = "N";
      } else if (isALINMA.value) {
        prmType = "L";
      }
      //prmType="J";
    }
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);

    Map bbbb = {
      "custid": userid,
      "emailid": userData.auCust?.emailid,
      //"<EMAIL>",
      "regtype": userData.auCust?.regtype,
      //"I",
      "accttype": userData.auCust?.accttype,
      //"C",
      "mobileno": phoneNumber.replaceAll("+", ""),
      //userData.auCust?.mobileno, //"966562467633",
      "paytype": paytype.value,
      "topupamt": purchaseTotalController.text ?? "10",
      // "topupamt": paytype ?? "10",
      "prmtype": prmType,
      //J=Mokafa, S=QITAF, N=ANB
    };

    print("BODY==========$bbbb");
    List<BalanceTopUpModelNew> orders = [];
    try {
      var response = await client.post(
          Uri.parse(
              // ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.createBalOrder),
              ApiEndPoints.baseUrl +
                  ApiEndPoints.authEndpoints.createPromotionOrder),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
          body: {
            "custid": userid,
            "emailid": userData.auCust?.emailid,
            //"<EMAIL>",
            "regtype": userData.auCust?.regtype,
            //"I",
            "accttype": userData.auCust?.accttype,
            //"C",
            "mobileno": phoneNumber.replaceAll("+", ""),
            //userData.auCust?.mobileno, //"966562467633",
            "paytype": paytype.value,
            "topupamt": purchaseTotalController.text ?? "10",
            // "topupamt": paytype ?? "10",
            "prmtype": prmPayType.value,
            //J=Mokafa, S=QITAF, N=ANB
          });

      BalanceTopUpModelNew result =
          BalanceTopUpModelNew.fromJson(jsonDecode(response.body));
      print("result");
      print(jsonDecode(response.body));
      if (result.message != "") {
        // if (true) {
        if (result.returnMessage?.action == 'EXCEPTION') {
          // if (false) {
          //showErrorMsg(result.returnMessage!.message.toString());
          await QuickAlert.show(
            context: Get.context!,
            type: QuickAlertType.error,
            title: result.returnMessage!.message.toString(),
            onCancelBtnTap: () {
              Get.back();
            },
          );
        } else {
          if (prmPayType.value != "J" &&
              prmPayType.value != "S" &&
              prmPayType.value != "N" &&
              prmPayType.value != "L") {
            Loader.hideLoader();
            if (currantIndex.value == 0 || currantIndex.value == 1) {
              await QuickAlert.show(
                  context: Get.context!,
                  type: QuickAlertType.success,
                  title: "Your order has been created successfully!".trr,
                  confirmBtnText: "Continue to Dashboard".trr,
                  //confirmBtnColor: AppColor.themeOrangeColor,
                  onConfirmBtnTap: () {
                    Get.offAll(DashBoardManagerScreen(
                      currantIndex: 0,
                    ));
                    final reviewController = Get.find<ReviewController>();
                    reviewController.triggerReview();
                  });
            }
            // showDialog(
            //   context: Get.context!,
            //   builder: (context) {
            //     return AlertDialog(
            //       contentPadding: const EdgeInsets.all(16),
            //       shape: RoundedRectangleBorder(
            //           borderRadius: BorderRadius.circular(12)),
            //       insetPadding: const EdgeInsets.all(25),
            //       content: currantIndex.value == 0 || currantIndex.value == 1
            //           ? cashOrderSuccessWidget(code: userid.toString())
            //           : cashOrderSuccessWidget(code: userid.toString()),
            //     );
            //   },
            // );
          }
          if (prmPayType.value == 'J' ||
              prmPayType.value == 'S' ||
              prmPayType.value == 'N' ||
              prmPayType.value == 'L') {
            Constants.mobileno = phoneNumber.replaceAll("+", "");
            Constants.PRMAmount = purchaseTotalController.text ?? "0";
            if (prmPayType.value == 'J') {
              Constants.prmAccessToken = result.mOKAFAAccessToken;
              Constants.prmOTPToken = result.mOKAFAOTPToken;
              Constants.prmPayrefNo = result.mOKAFAPayRefNo;
            }
            if (prmPayType.value == 'N') {
              Constants.prmAccessToken = result.aNBAccessToken;
              Constants.prmOTPToken = result.aNBOTPToken;
              Constants.prmPayrefNo = result.aNBPayRefNo;
            }
            if (prmPayType.value == 'S') {
              Constants.prmAccessToken = result.qitafDepositTo;
              Constants.prmOTPToken = result.qitafDepositTo;
              Constants.prmPayrefNo = result.qitafPayRefNo;
            }
            if (prmPayType.value == 'L') {
              Constants.prmAccessToken = result.alinmaAccessToken;
              Constants.prmOTPToken = result.alinmaAccessToken;
              Constants.prmPayrefNo = result.alinmaPaymentRef;
            }
            Loader.hideLoader();
            showOTPDialog();
          }
        }
      } else {
        Loader.hideLoader();
        print("TOPUP ========== failed");
        // FailedDialog().totupFailed();
        showErrorMsg("Failed to Fetch Data");
      }
      orders.add(result);
      return orders;
    } catch (e) {
      Loader.hideLoader();
      logs.log(e.toString());
      return [];
    }
  }

  // void showOTPDialog() {
  //   Get.defaultDialog(
  //     title: 'Enter OTP',
  //     content: TextFormField(
  //       controller: otpText,
  //       maxLength: 4,
  //       keyboardType:
  //           const TextInputType.numberWithOptions(signed: true, decimal: true),
  //       inputFormatters: [FilteringTextInputFormatter.digitsOnly],
  //       decoration: const InputDecoration(
  //         hintText: 'Enter 4-digit OTP',
  //       ),
  //     ),
  //     actions: <Widget>[
  //       // Cancel Button
  //       TextButton(
  //         onPressed: () {
  //           // Just close the dialog
  //           //  Loader.hideLoader();
  //           Get.back();
  //         },
  //         child: const Text('Cancel'),
  //       ),
  //       // Submit Button
  //       TextButton(
  //         onPressed: () {
  //           String enteredOTP = otpText.text;
  //           if (enteredOTP.length == 4) {
  //             print('Entered OTP: $enteredOTP'); // Use the OTP here
  //             // You might want to validate the OTP here
  //             ValidatePRMOTP();
  //             // Optionally close the dialog if OTP is correct inside ValidatePRMOTP
  //           }
  //         },
  //         child: const Text('Submit'),
  //       ),
  //     ],
  //   );
  // }

  //Added By: RYAN VIAEJDOR - 28052025
  void showOTPDialog() {
    //final otpText = TextEditingController();
    final focusNode = FocusNode();

    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        title: Text(
          'Verification Code'.trr,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Please enter the 4-digit code sent to your device'.trr,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: otpText,
              focusNode: focusNode,
              maxLength: 4,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 24,
                letterSpacing: 4,
              ),
              keyboardType: const TextInputType.numberWithOptions(
                signed: true,
                decimal: true,
              ),
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                counterText: '',
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.grey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: Theme.of(Get.context!).primaryColor),
                ),
                hintText: '••••',
                hintStyle: const TextStyle(
                  letterSpacing: 4,
                  color: Colors.grey,
                ),
              ),
              onChanged: (value) {
                if (value.length == 4) {
                  focusNode.unfocus();
                }
              },
            ),
          ],
        ),
        actionsAlignment: MainAxisAlignment.spaceBetween,
        actions: [
          TextButton(
            onPressed: () {
              otpText.clear();
              Get.back();
            },
            child: Text(
              'CANCEL'.trr,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.themeOrangeColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            onPressed: () {
              final enteredOTP = otpText.text;
              if (enteredOTP.length == 4) {
                ValidatePRMOTP();
              }
            },
            child: Text(
              'VERIFY'.trr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );

    // Auto focus the OTP field when dialog appears
    Future.delayed(const Duration(milliseconds: 100), () {
      focusNode.requestFocus();
    });
  }

  Future<dynamic> ValidatePRMOTP() async {
    Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.PRMBalOrder),
          body: {
            "custid": userData.auCust?.custid,
            "mobileno": Constants.mobileno.toString(),
            "userid": userData.auUsers?.emailid,
            "amount": Constants.PRMAmount,
            "accessToken": Constants.prmAccessToken,
            "otptoken": Constants.prmOTPToken,
            "payrefno": Constants.prmPayrefNo,
            "IsAR": Constants.IsAr_App,
            "prmType": prmPayType.value,
            "otp": otpText.text,
          });
      print("PRM OTP Responce==========${response.body}");
      ReturnMessage message = ReturnMessage.fromJson(jsonDecode(response.body));
      if (message.action == 'SUCCESS') {
        Loader.hideLoader();
        await QuickAlert.show(
            context: Get.context!,
            type: QuickAlertType.success,
            title: "Your order has been created successfully!".trr,
            confirmBtnText: "Continue to Dashboard".trr,
            //confirmBtnColor: AppColor.themeOrangeColor,
            onConfirmBtnTap: () {
              Get.offAll(DashBoardManagerScreen(
                currantIndex: 0,
              ));
              final reviewController = Get.find<ReviewController>();
              reviewController.triggerReview();
            });
        // showDialog(
        //   context: Get.context!,
        //   builder: (context) {
        //     return AlertDialog(
        //       contentPadding: const EdgeInsets.all(16),
        //       shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12)),
        //       insetPadding: const EdgeInsets.all(25),
        //       content: currantIndex.value == 0 || currantIndex.value == 1
        //           ? cashOrderSuccessWidget(
        //               code: userData.auCust!.custid.toString())
        //           : cashOrderSuccessWidget(
        //               code: userData.auCust!.custid.toString()),
        //     );
        //   },
        // );
      } else {
        // Get.snackbar('Error', 'Please enter a valid 4-digit OTP',
        //     snackPosition: SnackPosition.BOTTOM);
        Loader.hideLoader();

        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          title: "INVALID OTP PLEASE CHECK THE VALUES AND TRY AGAIN".tr,
          onCancelBtnTap: () {
            Get.back();
          },
        );
        // showDialog(
        //   barrierDismissible: false,
        //   context: Get.context!,
        //   builder: (context) {
        //     return AlertDialog(
        //       insetPadding: const EdgeInsets.all(16),
        //       contentPadding: const EdgeInsets.all(24),
        //       shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12)),
        //       content: Column(
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           Text(
        //             //"${message.message}",
        //             "INVALID OTP PLEASE CHECK THE VALUES AND TRY AGAIN".tr,
        //             style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
        //             textAlign: TextAlign.center,
        //           ),
        //           verticalSpace(24),
        //           CommonButton(
        //             title: "OK".tr,
        //             onPressed: () async {
        //               Get.back();
        //             },
        //             btnColor: AppColor.themeOrangeColor,
        //           )
        //         ],
        //       ),
        //     );
        //   },
        // );
      }
    } catch (e) {
      logs.log(e.toString());
      return [];
    }
  }

  orderContinue() async {
    Loader.showLoader();
    print(
        "Constants.requestSITemp.toString() =========${Constants.requestSITemp}");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    var custData = jsonEncode(custsData.read('custData'));

    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.orderContinue),
          body: {
            "custdata": custData,
            "SERVICETYPE": serviceTypeController.text,
            "QTY": qtyController.text,
            "REPLACEMENT": "true",
            "requestSI": Constants.requestSITemp.toString(),
            "PAYTYPE": payTypeController.text,
          });

      print("Create Order Response===============${response.body}");
      print(
          "Create Order Response===============${jsonDecode(response.body)["ReturnMessage"]["Action"]}");
      if (jsonDecode(response.body)["ReturnMessage"]["Action"] == "POPUP") {
        Constants.requestSITemp = "";
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    jsonDecode(response.body)["ReturnMessage"]["Message"],
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        Loader.hideLoader();
        Map<String, dynamic> parsedJson = jsonDecode(response.body);
        // Accessing the ORDERID value
        String orderId = parsedJson['orderHRD']['ORDERID'];

        showDialog(
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              contentPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              insetPadding: const EdgeInsets.all(25),
              // content: orderErrorWidget(),
              content: payTypeController.text == "B"
                  ? myBalanceSuccessWidget(
                      //code: "",
                      replacement: "true",
                      code: orderId,
                      unitPrice: "",
                      qty: "",
                      subTotal: amountValue.toString(),
                      vat: vatAmount.toString(),
                      purchaseTotal: totalAmount.toString(),
                    )
                  : payTypeController.text == "C"
                      ? cashSuccessWidget(
                          // code: "",
                          replacement: "true",
                          code: orderId,
                          unitPrice: "",
                          qty: "",
                          subTotal: amountValue.toString(),
                          vat: vatAmount.toString(),
                          purchaseTotal: totalAmount.toString(),
                        )
                      : eTransferSuccessWidget(
                          //code: "",
                          replacement: "true",
                          code: orderId,
                          unitPrice: "",
                          qty: "",
                          subTotal: amountValue.toString(),
                          vat: vatAmount.toString(),
                          purchaseTotal: totalAmount.toString(),
                        ),
            );
          },
        );
      }
    } catch (e) {
      showDialog(
          context: Get.context!,
          builder: (context) {
            return SimpleDialog(
              title: const Text('Error'),
              contentPadding: const EdgeInsets.all(20),
              children: [Text(e.toString())],
            );
          });
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
