import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:get_storage/get_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/Services/Notification_Services/notification_services.dart';
import 'package:waie_app/core/controller/session_controller/session_controller.dart';
import 'package:waie_app/core/controller/splash_controller/splash_controller.dart';
import 'package:waie_app/utils/locale_string.dart';
import 'package:waie_app/view/screen/splash_screen/splash_screen.dart';
import 'package:waie_app/waie_app.dart';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';

import 'core/controller/review_controller/review_controller.dart';

NotificationServices notificationServices = NotificationServices();
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(); // Ensure Firebase is initialized
  print("Handling a background message: ${message.notification!.title}");
  print("Handling a background message: ${message.notification!.body}");

  // Handle the background notification here
  NotificationServices notificationServices = NotificationServices();
  await notificationServices.firebaseMessagingBackgroundHandler(message);
}

GetStorage? getStorage;
SharedPreferences? myStorage;

@override
Widget build(BuildContext context) {
  return const MaterialApp(title: 'WaieApp', debugShowCheckedModeBanner: false);
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  Get.put(ReviewController());

  notificationServices.requestNotification();

  var appDir = (await getTemporaryDirectory()).path;
  Directory(appDir).delete(recursive: true);
  SplashController splashController = Get.put(SplashController());

  await GetStorage.init('User');
  await GetStorage.init('usersData');
  await GetStorage.init('custsData');
  await GetStorage.init();
  getStorage = GetStorage();
  await splashController.fetchMobColorsAndImages();
  Get.put(LocaleString());
  await LocaleString.load();
  //debugger();
  myStorage = await SharedPreferences.getInstance();
  SystemChrome.setPreferredOrientations(
      [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
  HttpOverrides.global = MyHttpOverrides();

  // init the hive
  await Hive.initFlutter();

  // open a register box
  await Hive.openBox('isReg_DB');
  // open a rememberMe Box
  await Hive.openBox('isRemember_DB');
  // open a Activate Box
  await Hive.openBox('isActivate_DB');
  Get.put(SessionController());

  runApp(const WaieApp());
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

    // GetStorage userStorage = GetStorage('User');
    // GetStorage usersData = GetStorage('usersData');
    // GetStorage custsData = GetStorage('custsData');
    // final tagOrderRefund = GetStorage();
    // final topupOrderRefund = GetStorage();
    // final vehicle = GetStorage();
    // final getOTPDataVerify = GetStorage();
    // final isPinblock = GetStorage();
    // final isDCBlock = GetStorage();