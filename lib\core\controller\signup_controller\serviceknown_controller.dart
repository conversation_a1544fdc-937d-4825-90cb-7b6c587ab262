import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/city.dart';
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/models/serviceknown.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/view/screen/auth/otp_screen.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/personal_details_screen.dart';

class ServiceKnownController extends GetxController {
  RxList<String> serviceknowns = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    print('ServiceKnownController');
    fetchServiceKnows();
  }

  Future<List<ServiceknownModel>> fetchServiceKnows() async {
    var client = http.Client();
    List<ServiceknownModel> services = [];
    try {
      var response = await client.post(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getServiceKnows));
      print("response===> ${jsonDecode(response.body)}");
      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        ServiceknownModel service =
            ServiceknownModel.fromMap(result[i] as Map<String, dynamic>);
        services.add(service);
      }

      serviceknowns.value = services.map((item) => item.text).toList();
      print("serviceknowns.value===> $serviceknowns");
      print("services===> ${jsonDecode(jsonEncode(services))}");
      return services;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
