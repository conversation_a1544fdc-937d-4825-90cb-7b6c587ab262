import 'dart:convert';
import 'dart:developer';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import 'package:waie_app/models/driverdc.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:http/http.dart' as http;

import '../../../utils/api_endpoints.dart';
import '../../../view/screen/auth/digital_coupon/create_dc_screen.dart';
import '../../../view/screen/auth/digital_coupon/digital_coupon_screen.dart';

class CreateDCController extends GetxController {
  RxBool isFuel = true.obs;
  RxBool isNonFuel = false.obs;
 // List platNoList = ["3948DJA", "4048DJA", "4148DJA"];
  List platNoList = [].obs;
  RxString selectedPlatNo = ''.obs;
  var fillController = TextEditingController().obs;
  //TextEditingController fillController = TextEditingController();
  RxBool disableClick=true.obs;


  Driverdc driver=Constants.driver[0];
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    selectedPlatNo.value=Constants.selectedPlatNo;
    //fillController.value = TextEditingController();
    for(int i=0; i< Constants.driver[0].platenos.length;i++)
      {
        platNoList.add(Constants.driver[0].platenos[i].plateNo);
        print("Constants.driver[0].platenos[i].plateNo========="+Constants.driver[0].platenos[i].plateNo);
      }
  }

  Future<dynamic> ReloadDriverService() async{
    String serialId="";
    for(int i=0; i< Constants.driver[0].platenos.length;i++)
    {
      if(Constants.driver[0].platenos[i].plateNo==selectedPlatNo.value.toString())
        {
          serialId=Constants.driver[0].platenos[i].serialid;
          print("Selected Serial Id=========="+serialId);
        }
    }

    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getDriverServicesDetails),
          body: {
            "mobileno": Constants.driver[0].mobileno.toString(),
            "serialid": serialId,
            "tokenid": "",
            "IsAr": Constants.IsAr_App
          });
      //  Loader.hideLoader();
      print(" Service Details response.body===========" + response.body);
      if (response.statusCode == 200) {
        Map<String, dynamic> parsedJson = jsonDecode(response.body);
        Constants.driver.clear();
        Constants.driver.add(Driverdc.fromJson(parsedJson));
        Get.offAll(CreateDCScreen(isBack: false,));

      } else {
        print("VerifyDriverOTP Error ==========" +
            response.statusCode.toString());
      }
      //Loader.hideLoader();
    } catch (e) {
      // Loader.hideLoader();
      //log("Digital Coupon Verification Error======" + e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }


  Future<dynamic> GenerateQRCode() async{

    //String qty=fillController.value.toString();
    String qty=fillController.value.text;
    print("QTY============="+qty);
    var rng = Random();
    var SecNumber = rng.nextInt(900000) + 100000; // From 100000 to 999999
    //Constants.driver[0].secCode=number.toString();
    //String qty=fillController.value();
    var uuid = Uuid();
    String serialCode = uuid.v4();
   // String originalInput = serialCode + "#" + Constants.driver[0].custid.toString() + "#" + Constants.driver[0].mobileno.toString() + "#" + selectedPlatNo.value.toString() + "#" + "FUEL" + "#" + Constants.driver[0].fueltype.toString() + "#" + qty + "#" + "" + "#" + Constants.driver[0].remquotavalue + "#" + Constants.driver[0].serialid + "#" + Constants.driver[0].stnNo+ "#" + Constants.driver[0].secCode;
    String originalInput = serialCode + "#" + Constants.driver[0].custid.toString() + "#" + Constants.driver[0].mobileno.toString() + "#" + selectedPlatNo.value.toString() + "#" + "FUEL" + "#" + Constants.driver[0].fueltype.toString() + "#" + qty + "#" + "" + "#" + Constants.driver[0].remquotavalue + "#" + Constants.driver[0].serialid + "#" + Constants.driver[0].stnNo+ "#" + SecNumber.toString();

    String encodedString = base64.encode(utf8.encode(originalInput));
    Constants.QRCodeString=encodedString;
    bool isBack=true;
    Get.to(() => DigitalCouponScreen(
      isBack: isBack,
    ));





  }
}
