import 'package:flutter/material.dart';
import 'colors.dart';

///Regular
TextStyle get pRegular8 => TextStyle(
      fontSize: 8,
      fontWeight: FontWeight.w400,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pRegular10 => TextStyle(
      fontSize: 10,
      fontWeight: FontWeight.w400,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pRegular12 => TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pRegular13 => TextStyle(
      fontSize: 13,
      fontWeight: FontWeight.w400,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pRegular14 => TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pRegular16 => TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pRegular17 => TextStyle(
      fontSize: 17,
      fontWeight: FontWeight.w400,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pRegular18 => TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.w400,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pRegular20 => TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w400,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

///Medium
TextStyle get pMedium8 => TextStyle(
      fontSize: 8,
      fontWeight: FontWeight.w500,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pMedium10 => TextStyle(
      fontSize: 10,
      fontWeight: FontWeight.w500,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pMedium12 => TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pMedium14 => TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pMedium16 => TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pMedium18 => TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.w500,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pMedium24 => TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w500,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pMedium36 => TextStyle(
      fontSize: 36,
      fontWeight: FontWeight.w500,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

///SemiBold
TextStyle get pSemiBold8 => TextStyle(
      fontSize: 8,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pSemiBold10 => TextStyle(
      fontSize: 10,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pSemiBold12 => TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pSemiBold14 => TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pSemiBold16 => TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );
TextStyle get pSemiBold17 => TextStyle(
  fontSize: 17,
  fontWeight: FontWeight.w600,
  color: AppColor.cText,
  fontFamily: 'SF Pro Display',
);
TextStyle get pSemiBold18 => TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pSemiBold19 => TextStyle(
      fontSize: 19,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pSemiBold21 => TextStyle(
      fontSize: 21,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pSemiBold23 => TextStyle(
      fontSize: 23,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pSemiBold27 => TextStyle(
      fontSize: 27,
      fontWeight: FontWeight.w600,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

///Bold
TextStyle get pBold12 => TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pBold14 => TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pBold16 => TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pBold17 => TextStyle(
      fontSize: 17,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pBold18 => TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pBold20 => TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pBold24 => TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pBold28 => TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pBold30 => TextStyle(
      fontSize: 30,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );

TextStyle get pBold55 => TextStyle(
      fontSize: 55,
      fontWeight: FontWeight.w700,
      color: AppColor.cText,
      fontFamily: 'SF Pro Display',
    );
