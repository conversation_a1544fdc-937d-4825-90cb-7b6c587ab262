// ignore_for_file: prefer_const_constructors

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/set_quota_limit_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/models/load_data.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/utils/validator.dart';

class SetQuotaLimitsWidget extends StatefulWidget {
  final String serialID;
  const SetQuotaLimitsWidget({
    super.key,
    required this.serialID,
  });

  @override
  State<SetQuotaLimitsWidget> createState() => _SetQuotaLimitsWidgetState();
}

class _SetQuotaLimitsWidgetState extends State<SetQuotaLimitsWidget> {
  GetStorage userStorage = GetStorage('User');
  int selectedClassOption = 0;
  int selectedTypeOption = 158;
  var quotaClass = "0";
  var quotaType = "158";
  final quotaTypeList = <Load_Data_Model>[].obs;
  final quotaClassList = <Load_Data_Model>[].obs;
  List<Load_Data_Model> quotaTypeModelList = [];
  List<Load_Data_Model> quotaClassModelList = [];

  SetQuotaLimitController setQuotaLimitController =
      Get.put(SetQuotaLimitController());
  VehicleController vehicleController = Get.put(VehicleController());

  getQuotaType() async {
    var client = http.Client();
    var custid = userStorage.read('custid');

    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';

      var quotaTypeResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "QUOTYPE",
            "CustId": custid,
          });

      var quotaClassResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "QUOCLASS",
            "CustId": custid,
          });

      print("quotaTypeResponse===> ${jsonDecode(quotaTypeResponse.body)}");

      List quotaTypeResult = jsonDecode(quotaTypeResponse.body);

      for (int i = 0; i < quotaTypeResult.length; i++) {
        Load_Data_Model loadData4 =
            Load_Data_Model.fromMap(quotaTypeResult[i] as Map<String, dynamic>);
        quotaTypeModelList.add(loadData4);
        print("quotaTypeResponse ===============${loadData4.TYPEDESC}");
      }

      quotaTypeList.value = quotaTypeModelList;

      print("quotaClassResponse===> ${jsonDecode(quotaClassResponse.body)}");

      List quotaClassResult = jsonDecode(quotaClassResponse.body);

      for (int i = 0; i < quotaClassResult.length; i++) {
        Load_Data_Model loadData5 = Load_Data_Model.fromMap(
            quotaClassResult[i] as Map<String, dynamic>);
        quotaClassModelList.add(loadData5);
        print("quotaClassResponse ===============${loadData5.TYPEDESC}");
      }

      quotaClassList.value = quotaClassModelList;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 10),
              child: Row(
                children: [
                  GestureDetector(
                      onTap: () {
                        vehicleController.selectedSerialList.clear();
                        vehicleController.selectedVehicleList.clear();
                        vehicleController.selectedFleetList.clear();
                        vehicleController.filterValueList.refresh();
                        vehicleController.selectedVehicleList.refresh();
                        vehicleController.selectedSerialList.refresh();
                        vehicleController.selectedFleetList.refresh();
                        Get.back();
                        // Get.offAll(
                        //   () => DashBoardManagerScreen(
                        //     currantIndex: 0,
                        //   ),
                        //   //preventDuplicates: false,
                        // );
                      },
                      child: CircleAvatar(
                        radius: 20,
                        backgroundColor: AppColor.cLightBlueContainer,
                        child: Center(
                            child: assetSvdImageWidget(
                                image: DefaultImages.backIcn)),
                      )),
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Center(
                        child: Text(
                          "Set quota limits".trr,
                          style: pBold20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            verticalSpace(16),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Quota Class".trr,
                  style: pRegular12,
                ),
                Row(
                  children: [
                    Expanded(
                      child: ListTile(
                        title: Text("MONEY".trr),
                        leading: Radio(
                          value: 0,
                          groupValue: selectedClassOption,
                          onChanged: (value) {
                            setState(() {
                              selectedClassOption = value!;
                              quotaClass = value.toString(); //MONEY
                              print("=== $quotaClass");
                            });
                          },
                        ),
                      ),
                    ),
                    horizontalSpace(1),
                    Expanded(
                      child: ListTile(
                        title: Text("LITER".trr),
                        leading: Radio(
                          value: 1,
                          groupValue: selectedClassOption,
                          onChanged: (value) {
                            setState(() {
                              selectedClassOption = value!;
                              quotaClass = value.toString(); //LITER
                              print("=== $quotaClass");
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            verticalSpace(16),
            Text(
              "Quota Type",
              style: pRegular12,
            ),
            Column(
              children: [
                ListTile(
                  title: Text("DAILY".trr),
                  leading: Radio(
                    value: 158,
                    groupValue: selectedTypeOption,
                    onChanged: (value) {
                      setState(() {
                        selectedTypeOption = value!;
                        quotaType = value.toString();
                        print("=== $quotaType");
                      });
                    },
                  ),
                ),
                ListTile(
                  title: Text("UNLIMITED".trr),
                  leading: Radio(
                    value: 157,
                    groupValue: selectedTypeOption,
                    onChanged: (value) {
                      setState(() {
                        selectedTypeOption = value!;
                        quotaType = value.toString();
                        print("=== $quotaType");
                      });
                    },
                  ),
                ),
                ListTile(
                  title: Text("MONTHLY".trr),
                  leading: Radio(
                    value: 160,
                    groupValue: selectedTypeOption,
                    onChanged: (value) {
                      setState(() {
                        selectedTypeOption = value!;
                        quotaType = value.toString();
                        print("=== $quotaType");
                      });
                    },
                  ),
                ),
                ListTile(
                  title: Text("WEEKLY".trr),
                  leading: Radio(
                    value: 159,
                    groupValue: selectedTypeOption,
                    onChanged: (value) {
                      setState(() {
                        selectedTypeOption = value!;
                        quotaType = value.toString();
                        print("=== $quotaType");
                      });
                    },
                  ),
                ),
              ],
            ),
            verticalSpace(16),
            CommonTextField(
              controller: setQuotaLimitController.quotaValueController,
              labelText: '${'Quota Value'.trr}*',
              hintText: "0-10 000".trr,
              keyboardType:
                  TextInputType.numberWithOptions(signed: true, decimal: true),
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              validator: (value) {
                return Validator.validateName(value, "Quota Value".trr);
              },
            ),
            verticalSpace(16),
            Row(
              children: [
                Expanded(
                  child: CommonBorderButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.back();
                      // Get.offAll(
                      //   () => DashBoardManagerScreen(
                      //     currantIndex: 0,
                      //   ),
                      //   //preventDuplicates: false,
                      // );
                    },
                    textColor: AppColor.cDarkBlueFont,
                    bColor: AppColor.cDarkBlueFont,
                    btnColor: AppColor.cBackGround,
                  ),
                ),
                horizontalSpace(16),
                Expanded(
                  child: CommonButton(
                    title: 'Set Limits'.trr,
                    onPressed: () {
                      print("*************************************");
                      print(
                          'vehicleSerialID  QUOTA--------------- ${widget.serialID}');
                      print("*************************************");
                      setQuotaLimitController.setQuotaLimits(
                          widget.serialID, quotaClass, quotaType);
                      // showDialog(
                      //   context: context,
                      //   builder: (BuildContext context) {
                      //     return AlertDialog(
                      //       contentPadding: EdgeInsets.zero,
                      //       insetPadding: EdgeInsets.all(16),
                      //       shape: RoundedRectangleBorder(
                      //           borderRadius: BorderRadius.circular(12)),
                      //       content: notAddVehicleDialogWidget(),
                      //     );
                      //   },
                      // );
                    },
                    textColor: AppColor.cWhiteFont,
                    btnColor: AppColor.themeOrangeColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Container notAddVehicleDialogWidget() {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      padding: EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: assetSvdImageWidget(image: DefaultImages.cancelIcn)),
            ],
          ),
          verticalSpace(24),
          Center(
              child: Text(
            "You need to do this before you can add new vehicles.".trr,
            style: pBold20,
            textAlign: TextAlign.center,
          )),
          verticalSpace(8),
          Text(
              "Order tags or cards to be able to add vehicles to your fleet".trr,
              style: pRegular13,
              textAlign: TextAlign.center),
          verticalSpace(24),
          CommonIconButton(
            iconData: DefaultImages.circleAddIcn,
            title: 'Order tags or cards'.trr,
            btnColor: AppColor.themeOrangeColor,
            onPressed: () {
              Get.back();
            },
          )
        ],
      ),
    );
  }
}
