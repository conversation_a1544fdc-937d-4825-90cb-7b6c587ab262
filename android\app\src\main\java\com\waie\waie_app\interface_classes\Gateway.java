
package com.waie.aldrees.mobile.interface_classes;

import android.app.Activity;
import android.content.Intent;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.mastercard.gateway.android.sdk.BuildConfig;
import com.mastercard.gateway.android.sdk.Gateway3DSecureActivity;
import com.mastercard.gateway.android.sdk.Gateway3DSecureCallback;
import com.mastercard.gateway.android.sdk.GatewayCallback;
import com.mastercard.gateway.android.sdk.GatewayMap;
import com.waie.aldrees.mobile.MainActivity;
import com.waie.aldrees.mobile.interface_classes.Mada.HostedCheckoutActivity;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

import io.flutter.Log;

/**
 * The public interface to the Gateway SDK.
 * <p>
 * Example set up:
 * <p>
 * <code>
 * Gateway gateway = new Gateway();
 * gateway.setMerchantId("your-merchant-id");
 * gateway.setRegion(Gateway.Region.NORTH_AMERICA);
 * </code>
 */
@SuppressWarnings("unused,WeakerAccess")
public class Gateway {

    /**
     * The available gateway regions
     */
    public enum Region {
        ASIA_PACIFIC("ap."),
        EUROPE("eu."),
        NORTH_AMERICA("na."),
        INDIA("in."),
        CHINA("cn."),
        MTF("mtf.");

        String prefix;

        Region(String prefix) {
            this.prefix = prefix;
        }

        String getPrefix() {
            return prefix;
        }
    }

    // internally supported request methods
    enum Method {
        PUT
    }

    static final int MIN_API_VERSION = 39;
    static final int CONNECTION_TIMEOUT = 15000;
    static final int READ_TIMEOUT = 60000;
    static final int REQUEST_3D_SECURE = 10000;
    static final int REQUEST_GOOGLE_PAY_LOAD_PAYMENT_DATA = 10001;
    static final String API_OPERATION = "UPDATE_PAYER_DATA";
    static final String USER_AGENT = "Gateway-Android-SDK/" + BuildConfig.VERSION_NAME;

    Gson gson = new Gson();
    String merchantId;
    Region region;

    /**
     * Constructs a new instance.
     */
    public Gateway() {
    }

    /**
     * Gets the current Merchant ID
     *
     * @return The current Merchant ID
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * Gets the current {@link Region}
     *
     * @return The region
     */
    public Region getRegion() {
        return region;
    }

    /**
     * Starts the {@link Gateway3DSecureActivity} for result, initializing it with
     * the provided html
     *
     * @param activity The calling activity context
     * @param html     The initial HTML to render in the web view
     */
    public static void start3DSecureActivity(Activity activity, String html) {
        start3DSecureActivity(activity, html, null);
    }

    public static void startHostedCheckoutActivity(Activity activity, String html, JsonObject prepareJson) {
        startHostedCheckoutActivity(activity, html, null);
    }

    /**
     * Starts the {@link Gateway3DSecureActivity} for result, initializing it with
     * the provided html
     *
     * @param activity The calling activity context
     * @param html     The initial HTML to render in the web view
     * @param title    An optional title to render in the toolbar
     */
    public static void start3DSecureActivity(Activity activity, String html, String title) {
        Intent intent = new Intent(activity, Gateway3DSecureActivity.class);
        start3DSecureActivity(activity, html, title, intent);
    }

    public static void startHostedCheckoutActivity(Activity activity, String html, String orderId,
            JsonObject prepareJson) {
        Log.d("startHostedCheckoutActivity ", "Called");
        Intent intent = new Intent(activity, HostedCheckoutActivity.class);
        startHostedCheckoutActivity(activity, html, orderId, prepareJson, intent);
    }

    static void start3DSecureActivity(Activity activity, String html, String title, Intent intent) {
        intent.putExtra(Gateway3DSecureActivity.EXTRA_HTML, html); // required

        if (title != null) {
            intent.putExtra(Gateway3DSecureActivity.EXTRA_TITLE, title);
        }

        activity.startActivityForResult(intent, REQUEST_3D_SECURE);
    }

    // separated for testability
    static void startHostedCheckoutActivity(Activity activity, String html, String orderId, JsonObject prepareJson,
            Intent intent) {
        Log.d("startHostedCheckoutActivity 2", "Called");
        intent.putExtra("HTML", html);
        intent.putExtra("ORDERID", orderId);
        intent.putExtra("PREPAREJSON", prepareJson.toString());
        activity.startActivity(intent);

        // MainActivity.flutterReturn();
    }

    /**
     * A convenience method for handling activity result messages returned from
     * {@link Gateway3DSecureActivity}.
     * This method should be called within the calling Activity's onActivityResult()
     * lifecycle method.
     * This helper only works if the 3-D Secure Activity was launched using the
     * {@link com.mastercard.gateway.android.sdk.Gateway#start3DSecureActivity(Activity, String, String)}
     * method.
     *
     * @param requestCode The request code returning from the activity result
     * @param resultCode  The result code returning from the activity result
     * @param data        The intent data returning from the activity result
     * @param callback    An implementation of {@link Gateway3DSecureCallback}
     * @return True if handled, False otherwise
     * @see com.mastercard.gateway.android.sdk.Gateway#start3DSecureActivity(Activity,
     *      String)
     * @see com.mastercard.gateway.android.sdk.Gateway#start3DSecureActivity(Activity,
     *      String, String)
     */
    public static boolean handle3DSecureResult(int requestCode, int resultCode, Intent data,
            Gateway3DSecureCallback callback) {
        if (callback == null) {
            return false;
        }

        if (requestCode == REQUEST_3D_SECURE) {
            if (resultCode == Activity.RESULT_OK) {
                String acsResultJson = data.getStringExtra(Gateway3DSecureActivity.EXTRA_ACS_RESULT);
                GatewayMap acsResult = new GatewayMap(acsResultJson);

                callback.on3DSecureComplete(acsResult);
            } else {
                callback.on3DSecureCancel();
            }

            return true;
        }

        return false;
    }

    String getApiUrl(String apiVersion) {
        if (Integer.valueOf(apiVersion) < MIN_API_VERSION) {
            throw new IllegalArgumentException("API version must be >= " + MIN_API_VERSION);
        }

        if (region == null) {
            throw new IllegalStateException("You must initialize the the Gateway instance with a Region before use");
        }

        return "https://" + region.getPrefix() + "gateway.mastercard.com/api/rest/version/" + apiVersion;
    }

    String getUpdateSessionUrl(String sessionId, String apiVersion) {
        if (sessionId == null) {
            throw new IllegalArgumentException("Session Id may not be null");
        }

        if (merchantId == null) {
            throw new IllegalStateException(
                    "You must initialize the the Gateway instance with a Merchant Id before use");
        }

        return getApiUrl(apiVersion) + "/merchant/" + merchantId + "/session/" + sessionId;
    }

    // handler callback method when executing a request on a new thread
    @SuppressWarnings("unchecked")
    boolean handleCallbackMessage(GatewayCallback callback, Object arg) {
        if (callback != null) {
            if (arg instanceof Throwable) {
                callback.onError((Throwable) arg);
            } else {
                callback.onSuccess((GatewayMap) arg);
            }
        }
        return true;
    }

    boolean isStatusCodeOk(int statusCode) {
        return (statusCode >= 200 && statusCode < 300);
    }

    String inputStreamToString(InputStream is) throws IOException {
        // get buffered reader from stream
        BufferedReader rd = new BufferedReader(new InputStreamReader(is));

        // read stream into string builder
        StringBuilder total = new StringBuilder();

        String line;
        while ((line = rd.readLine()) != null) {
            total.append(line);
        }

        return total.toString();
    }
}
