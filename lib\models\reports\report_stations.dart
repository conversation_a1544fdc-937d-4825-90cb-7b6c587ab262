import 'dart:convert';

class Report_Stations {
  final String STATIONCODE;
  final String STATION;

  Report_Stations({
    required this.STATIONCODE,
    required this.STATION,
  });

  Map<String, dynamic> toMap() {
    return {
      'STATIONCODE': STATIONCODE,
      'STATION': STATION,
    };
  }

  factory Report_Stations.fromMap(Map<String, dynamic> map) {
    return Report_Stations(
      STATIONCODE: map['STATIONCODE'] ?? '',
      STATION: map['STATION'] ?? '',
    );
  }
  String toJson() => json.encode(toMap());

  factory Report_Stations.fromJson(String source) =>
      Report_Stations.fromMap(json.decode(source));
}