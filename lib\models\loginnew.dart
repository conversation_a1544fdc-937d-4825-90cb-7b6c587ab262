/// users : [{"Item1":"1000000887","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"1000000889","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000039977","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047597","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047598","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047599","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047600","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047601","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047602","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047606","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047619","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047620","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047624","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047626","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047627","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047628","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047629","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047630","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047631","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047632","Item2":"<EMAIL>","Item3":"0.15"},{"Item1":"000047633","Item2":"<EMAIL>","Item3":"0.15"}]
/// response : {"Title":null,"Message":null,"SubMessage":null,"MessageType":"\u0000","ResponseAction":null,"Redirect":null,"ActionParam":null,"Action":"SUCCESS","isValidTransaction":true,"Data":null}

class LoginNew {
  LoginNew({
    List<Users>? users,
    Response? response,
  }) {
    _users = users;
    _response = response;
  }

  LoginNew.fromJson(dynamic json) {
    if (json['users'] != null) {
      _users = [];
      json['users'].forEach((v) {
        _users?.add(Users.fromJson(v));
      });
    }
    _response =
        json['response'] != null ? Response.fromJson(json['response']) : null;
  }

  List<Users>? _users;
  Response? _response;

  LoginNew copyWith({
    List<Users>? users,
    Response? response,
  }) =>
      LoginNew(
        users: users ?? _users,
        response: response ?? _response,
      );

  List<Users>? get users => _users;

  Response? get response => _response;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_users != null) {
      map['users'] = _users?.map((v) => v.toJson()).toList();
    }
    if (_response != null) {
      map['response'] = _response?.toJson();
    }
    return map;
  }
}

class Users {
  Users({
    String? item1,
    String? item2,
    String? item3,
  }) {
    _item1 = item1;
    _item2 = item2;
    _item3 = item3;
  }

  Users.fromJson(dynamic json) {
    _item1 = json['Item1'];
    _item2 = json['Item2'];
    _item3 = json['Item3'];
  }

  String? _item1;
  String? _item2;
  String? _item3;

  Users copyWith({
    String? item1,
    String? item2,
    String? item3,
  }) =>
      Users(
        item1: item1 ?? _item1,
        item2: item2 ?? _item2,
        item3: item3 ?? _item3,
      );

  String? get item1 => _item1;

  String? get item2 => _item2;

  String? get item3 => _item3;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['Item1'] = _item1;
    map['Item2'] = _item2;
    map['Item3'] = _item3;
    return map;
  }
}

class Response {
  Response({
    dynamic title,
    dynamic message,
    dynamic subMessage,
    String? messageType,
    dynamic responseAction,
    dynamic redirect,
    dynamic actionParam,
    String? action,
    bool? isValidTransaction,
    dynamic data,
  }) {
    _title = title;
    _message = message;
    _subMessage = subMessage;
    _messageType = messageType;
    _responseAction = responseAction;
    _redirect = redirect;
    _actionParam = actionParam;
    _action = action;
    _isValidTransaction = isValidTransaction;
    _data = data;
  }

  Response.fromJson(dynamic json) {
    _title = json['Title'];
    _message = json['Message'];
    _subMessage = json['SubMessage'];
    _messageType = json['MessageType'];
    _responseAction = json['ResponseAction'];
    _redirect = json['Redirect'];
    _actionParam = json['ActionParam'];
    _action = json['Action'];
    _isValidTransaction = json['isValidTransaction'];
    _data = json['Data'];
  }

  dynamic _title;
  dynamic _message;
  dynamic _subMessage;
  String? _messageType;
  dynamic _responseAction;
  dynamic _redirect;
  dynamic _actionParam;
  String? _action;
  bool? _isValidTransaction;
  dynamic _data;

  Response copyWith({
    dynamic title,
    dynamic message,
    dynamic subMessage,
    String? messageType,
    dynamic responseAction,
    dynamic redirect,
    dynamic actionParam,
    String? action,
    bool? isValidTransaction,
    dynamic data,
  }) =>
      Response(
        title: title ?? _title,
        message: message ?? _message,
        subMessage: subMessage ?? _subMessage,
        messageType: messageType ?? _messageType,
        responseAction: responseAction ?? _responseAction,
        redirect: redirect ?? _redirect,
        actionParam: actionParam ?? _actionParam,
        action: action ?? _action,
        isValidTransaction: isValidTransaction ?? _isValidTransaction,
        data: data ?? _data,
      );

  dynamic get title => _title;

  dynamic get message => _message;

  dynamic get subMessage => _subMessage;

  String? get messageType => _messageType;

  dynamic get responseAction => _responseAction;

  dynamic get redirect => _redirect;

  dynamic get actionParam => _actionParam;

  String? get action => _action;

  bool? get isValidTransaction => _isValidTransaction;

  dynamic get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['Title'] = _title;
    map['Message'] = _message;
    map['SubMessage'] = _subMessage;
    map['MessageType'] = _messageType;
    map['ResponseAction'] = _responseAction;
    map['Redirect'] = _redirect;
    map['ActionParam'] = _actionParam;
    map['Action'] = _action;
    map['isValidTransaction'] = _isValidTransaction;
    map['Data'] = _data;
    return map;
  }
}
