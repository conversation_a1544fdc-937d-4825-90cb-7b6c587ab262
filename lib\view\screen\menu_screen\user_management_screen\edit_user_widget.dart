// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/user_management_controller/user_management_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class EditUserWidget extends StatelessWidget {
  final String email;
  final String name;

  EditUserWidget({super.key, required this.email, required this.name});

  UserManagementController userManagementController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: EdgeInsets.all(16),
        child: Obx(() {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Edit user".trr,
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Center(child: assetSvdImageWidget(image: DefaultImages.cancelIcn))),
                  // TextButton(
                  //   onPressed: () {
                  //     Get.back();
                  //     showModalBottomSheet(
                  //       context: context,
                  //       shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                  //       backgroundColor: AppColor.cBackGround,
                  //       barrierColor: AppColor.cBlackOpacity,
                  //       isScrollControlled: true,
                  //       builder: (context) {
                  //         return DeleteUserWidget(
                  //           name: name,
                  //         );
                  //       },
                  //     );
                  //   },
                  //   child: Text(
                  //     "delete user".toUpperCase(),
                  //     style: pRegular12.copyWith(decoration: TextDecoration.underline),
                  //     textAlign: TextAlign.center,
                  //   ),
                  // ),
                ],
              ),
              verticalSpace(20),
              CommonTextField(
                controller: userManagementController.editEmailController..text = email,
                hintText: 'Email'.trr,
                labelText: 'Email ID'.trr,
              ),
              verticalSpace(16),
              CommonTextField(
                controller: userManagementController.editNameController..text = name,
                hintText: 'Name'.trr,
                labelText: 'Name'.trr,
              ),
              verticalSpace(24),
              Text(
                "User role".trr,
                style: pRegular12.copyWith(fontSize: 11),
              ),
              verticalSpace(4),
              Row(
                children: userManagementController.userRoleList.value
                    .map((e) => Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: GestureDetector(
                            onTap: () {
                              userManagementController.selectedUserRole.value = e;
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(vertical: 13, horizontal: 14),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  color: userManagementController.selectedUserRole.value == e
                                      ? AppColor.themeOrangeColor
                                      : AppColor.cLightGrey),
                              child: Text(e.toString().trr,
                                  style: pRegular13.copyWith(
                                      color: userManagementController.selectedUserRole.value == e
                                          ? AppColor.cWhiteFont
                                          : AppColor.cText)),
                            ),
                          ),
                        ))
                    .toList(),
              ),
              verticalSpace(40),
              Row(
                children: [
                  Expanded(
                      child: CommonBorderButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      Get.back();
                    },
                    bColor: AppColor.themeDarkBlueColor,
                    textColor: AppColor.cDarkBlueFont,
                  )),
                  horizontalSpace(8),
                  Expanded(
                      child: CommonButton(
                    title: 'Confirm'.trr,
                    onPressed: () {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  ))
                ],
              ),
              verticalSpace(16),
            ],
          );
        }),
      ),
    );
  }
}

class DeleteUserWidget extends StatelessWidget {
  final String name;
  final Function() deleteFun;

  const DeleteUserWidget({super.key, required this.name, required this.deleteFun});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Center(child: assetSvdImageWidget(image: DefaultImages.cancelIcn))),
            ],
          ),
          verticalSpace(17),
          Center(
            child: Text(
             "${"Are you sure you want to delete user".trr} $name?",
              style: pBold20,
              textAlign: TextAlign.center,
            ),
          ),
          verticalSpace(8),
          Text(
            "This action can not be undone.".trr,
            style: pRegular13.copyWith(color: AppColor.cDarkFont),
            textAlign: TextAlign.center,
          ),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonBorderButton(
                title: 'No'.trr,
                onPressed: () {
                  Get.back();
                },
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(8),
              Expanded(
                  child: CommonButton(
                title: 'Yes, Delete'.trr,
                onPressed: deleteFun,
                btnColor: AppColor.cRedText,
                horizontalPadding: 16,
              ))
            ],
          ),
        ],
      ),
    );
  }
}
