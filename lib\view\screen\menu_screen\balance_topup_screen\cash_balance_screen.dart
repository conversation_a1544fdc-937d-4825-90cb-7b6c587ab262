// ignore_for_file: prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../purchase_history_screen/purchse_history_screen.dart';

class CashBalanceScreen extends StatelessWidget {
  const CashBalanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              simpleAppBar(
                  title: "Balance top up".trr,
                  onTap: () {
                    Get.back();
                  },
                  backString: "Back".trr),
              Expanded(
                child: SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                            child: Text(
                          "Balance top-up receipt".trr,
                          style: pBold20,
                        )),
                        verticalSpace(32),
                        Container(
                          decoration: BoxDecoration(border: Border.all(color: AppColor.cBlack)),
                          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "User ID".trr,
                                    style: pRegular14,
                                  ),
                                  Text(
                                    "1-222-A23523",
                                    style: pBold12.copyWith(fontSize: 14),
                                  ),
                                ],
                              ),
                              verticalSpace(20),
                              Image.asset(DefaultImages.barCodeImg)
                            ],
                          ),
                        ),
                        verticalSpace(20),
                        Text(
                          "• "+"Proceed to the Cashier for payment".trr
                              +"\n• "+"Show your top-up receipt or tell your user account ID to the cashier".trr,
                          style: pRegular14,
                        ),
                        verticalSpace(20),
                        CommonIconButton(
                          iconData: DefaultImages.whiteDownloadIcn,
                          title: 'Download pdf receipt'.trr,
                          onPressed: () {},
                          btnColor: AppColor.themeOrangeColor,
                        ),
                        verticalSpace(16),
                        Center(
                          child: Text(
                            "or share this receipt by email".trr,
                            style: pRegular14,
                          ),
                        ),
                        verticalSpace(16),
                        Text('Recepient email'.trr, style: pRegular8.copyWith(fontSize: 11)),
                        verticalSpace(6),
                        Container(
                          height: 44,
                          decoration: BoxDecoration(
                              border: Border.all(color: AppColor.cBorder), borderRadius: BorderRadius.circular(6)),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 3,
                                child: TextFormField(
                                  style: pRegular14,
                                  keyboardType: TextInputType.emailAddress,
                                  cursorColor: AppColor.cBorder,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Enter email'.trr,
                                      hintStyle: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                                      contentPadding: EdgeInsets.only(left: 16, right: 16, bottom: 8)),
                                ),
                              ),
                              sendEmailBtn(() {}),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
        floatingActionButton: purchaseHistoryTextWidget(),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      ),
    );
  }
}

purchaseHistoryTextWidget() {
  return Padding(
    padding: const EdgeInsets.only(bottom: 20),
    child: Text.rich(TextSpan(
        text: 'You can view the details of your order in'.trr+' ',
        style: pRegular12.copyWith(color: AppColor.cDarkGreyFont),
        children: [
          TextSpan(
            text: 'Purchase history'.trr,
            style: pBold12.copyWith(color: AppColor.cDarkGreyFont, decoration: TextDecoration.underline),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                Get.to(PurchaseHistoryScreen());
              },
          )
        ])),
  );
}

sendEmailBtn(Function() onTap) {
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(2),
        child: Container(
          height: 44,
          width: 210,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              border: Border.all(color: AppColor.cBorder),
              color: AppColor.cLightGrey),
          child: Center(child: Text("Send".trr, style: pRegular8.copyWith(fontSize: 11))),
        ),
      ),
    ),
  );
}
