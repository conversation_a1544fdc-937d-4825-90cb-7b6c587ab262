import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/models/tag_transfer_company_detail.dart';
import 'package:waie_app/models/tag_transfer_vehicle_detail.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/incoming_tags_transfer_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class IncomingTagsTransferController extends GetxController {
  VehicleController vehicleController = Get.put(VehicleController());
  final vehicle = GetStorage();
  GetStorage custsData = GetStorage('custsData');
  RxList tagTransferCompanyInfos = [].obs;
  RxList tagTransferVehicelLists = [].obs;
  RxString companyReqID = ''.obs;
  RxString companyCustID = ''.obs;
  RxString companyReqDate = ''.obs;
  RxString companyCustomer = ''.obs;
  String reqID = "";
  String custID = "";
  GetStorage userStorage = GetStorage('User');

  Future<dynamic> fetchTagTransferCompanyInfos() async {
    var client = http.Client();
    var custid = userStorage.read('custid');
    print("===============================================================");
    print("custid>>>>>>> $custid");
    print("===============================================================");

    try {
      print("PASOK1");
      var reasonResponse = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.loadTransferDetails),
          body: {
            "custId": "1000000180", //custid
          });
      print("PASOK2");
      List result = jsonDecode(reasonResponse.body);
      print("===============================================================");
      print("result >>>>> $result");
      print("reasonResponseBody >>>>> ${reasonResponse.body}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        TagTransferCompanyDetailModel companyInfo =
            TagTransferCompanyDetailModel.fromMap(
                result[i] as Map<String, dynamic>);
        print("reqId ================== ${companyInfo.reqId}");
        print("custid ================== ${companyInfo.custid}");
        print("reqDate ================== ${companyInfo.reqDate}");
        print("customer ================== ${companyInfo.customer}");
        reqID = companyInfo.reqId;
        custID = companyInfo.custid;
        companyReqID.value = companyInfo.reqId;
        companyCustID.value = companyInfo.custid;
        companyReqDate.value = companyInfo.reqDate;
        companyCustomer.value = companyInfo.customer;
        print("companyReqID.value ================== ${companyReqID.value}");
        print("companyCustID.value ================== ${companyCustID.value}");
        print(
            "companyReqDate.value ================== ${companyReqDate.value}");
        print(
            "companyCustomer.value ================== ${companyCustomer.value}");
        tagTransferCompanyInfos.add(companyInfo);
      }

      return tagTransferCompanyInfos;
    } catch (e) {
      log(e.toString());
      print("e.toString() >>>>> ${e.toString()}");
      print(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<dynamic> fetchTagTransferVehicleLists(custid, reqid) async {
    var client = http.Client();

    try {
      print("PASOK1");
      var reasonResponse = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getTagTransferVehicleLists),
          body: {
            "custid": custid, //custid
            "top": "0",
            "skip": "0",
            "reqid": reqid,
          });
      print("PASOK2");
      List result = jsonDecode(reasonResponse.body);
      print("===============================================================");
      print("result >>>>> $result");
      print("reasonResponseBody >>>>> ${reasonResponse.body}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        TagTransferVehicleDetailModel vehicleInfo =
            TagTransferVehicleDetailModel.fromMap(
                result[i] as Map<String, dynamic>);
        tagTransferVehicelLists.add(vehicleInfo);
      }

      return tagTransferVehicelLists;
    } catch (e) {
      log(e.toString());
      print("e.toString() >>>>> ${e.toString()}");
      print('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  // @override
  // void onInit() {
  //   super.onInit();
  //   print('IncomingTagsTransferController');
  //   getDatas();
  // }

  getDatas() async {
    // loading circle
    showDialog(
      context: Get.context!,
      builder: (context) {
        return const Center(child: CircularProgressIndicator());
      },
    );

    await fetchTagTransferCompanyInfos();
    print(
        "tagTransferCompanyInfos ${jsonDecode(jsonEncode(tagTransferCompanyInfos))}");
    print("reqID ================== $reqID");
    print("custID ================== $custID");
    await fetchTagTransferVehicleLists(custID, reqID);
    print(
        "tagTransferVehicelLists ${jsonDecode(jsonEncode(tagTransferVehicelLists))}");
    print("companyReqID.value1 ================== $companyReqID");
    print("companyCustID.value1 ================== $companyCustID");
    print("companyReqDate.value1 ================== $companyReqDate");
    print("companyCustomer.value1 ================== $companyCustomer");
    if (tagTransferVehicelLists.isNotEmpty) {
      // pop the loading circle
      Navigator.of(Get.context!).pop();
      await Future.delayed(
        const Duration(seconds: 1),
        () => showModalBottomSheet(
          context: Get.context!,
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
          backgroundColor: AppColor.cBackGround,
          barrierColor: AppColor.cBlackOpacity,
          isScrollControlled: true,
          builder: (context) {
            return IncomingTagsTransferWidget();
          },
        ),
      );
    }
  }

  tagTransferRequest(reqid, isApproved) async {
    print("IncomingTagsTransferController reqid>>>>>>> $reqid");
    print("IncomingTagsTransferController isApproved>>>>>>> $isApproved");

    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = custsData.read('custData');
    print("===============================================================");
    print("IncomingTagsTransferController custData>>>>>>> $custData");
    print("IncomingTagsTransferController custid>>>>>>> $custid");
    print("IncomingTagsTransferController emailid>>>>>>> $emailid");
    print(
        "IncomingTagsTransferController custData['BANKAMT']>>>>>>> ${custData['BANKAMT']}");
    print(
        "IncomingTagsTransferController custData['BANK']>>>>>>> ${custData['BANK']}");
    print(
        "IncomingTagsTransferController custData['BANKREFNO']>>>>>>> ${custData['BANKREFNO']}");
    print(
        "IncomingTagsTransferController custData['B2B_IBAN']>>>>>>> ${custData['B2B_IBAN']}");
    print("===============================================================");
    var client = http.Client();

    try {
      print("HI");
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.tagTransferConfirm),
          body: {
            "reqid": reqid,
            "emailid": custData['EMAILID'],
            "customerID": custData['CUSTID'], //custid
            "bankamt": custData['BANKAMT'],
            "bank": custData['BANK'],
            "bankrefno": custData['BANKREFNO'],
            "b2biban": custData['B2B_IBAN'],
            "isApproved": isApproved,
          });
      print("===============================================================");
      print(
          "IncomingTagsTransferController jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      var result = jsonDecode(response.body);
      if (result == null || result == "") {
        Get.back();
        showDialog(
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    result,
                    style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: 'Continue to Dashboard'.tr,
                    onPressed: () {
                      Get.offAll(() => DashBoardManagerScreen(currantIndex: 0));
                    },
                    btnColor: AppColor.themeOrangeColor,
                  ),
                ],
              ),
            );
          },
        );
      } else {
        Get.back();
        showDialog(
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Request submitted".tr,
                    style: pSemiBold16,
                  ),
                  verticalSpace(24),
                  Text(
                      "You've submitted a request to transfer your tags. We'll review it within 48 hours."
                          .tr,
                      style: pRegular13,
                      textAlign: TextAlign.center),
                  verticalSpace(24),
                  Text.rich(
                    TextSpan(
                      text:
                          '${'You can also track the status of your request in'.tr}  ',
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                      children: [
                        TextSpan(
                          text: "Transfer History".tr,
                          style: pBold12.copyWith(
                            fontSize: 13,
                            color: AppColor.cLinkText,
                          ),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: 'Continue to Dashboard'.tr,
                    onPressed: () {
                      Get.offAll(() => DashBoardManagerScreen(currantIndex: 0));
                    },
                    btnColor: AppColor.themeOrangeColor,
                  ),
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      log(e.toString());
      print(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  tagTransferCancel(reqid) async {
    print("IncomingTagsTransferController reqid>>>>>>> $reqid");

    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = custsData.read('custData');
    print("===============================================================");
    print("IncomingTagsTransferController custData>>>>>>> $custData");
    print("IncomingTagsTransferController custid>>>>>>> $custid");
    print("IncomingTagsTransferController emailid>>>>>>> $emailid");
    print(
        "IncomingTagsTransferController custData['BANKAMT']>>>>>>> ${custData['BANKAMT']}");
    print(
        "IncomingTagsTransferController custData['BANK']>>>>>>> ${custData['BANK']}");
    print(
        "IncomingTagsTransferController custData['BANKREFNO']>>>>>>> ${custData['BANKREFNO']}");
    print(
        "IncomingTagsTransferController custData['B2B_IBAN']>>>>>>> ${custData['B2B_IBAN']}");
    print("===============================================================");
    var client = http.Client();

    try {
      print("HI");
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.tagTransferCancel),
          body: {
            "reqid": reqid,
            "emailid": custData['EMAILID'],
            "customerID": custData['CUSTID'], //custid
            "bankamt": custData['BANKAMT'],
            "bank": custData['BANK'],
            "bankrefno": custData['BANKREFNO'],
            "b2biban": custData['B2B_IBAN'],
            "isARInterface": "false",
          });
      print("===============================================================");
      print(
          "IncomingTagsTransferController jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      var result = jsonDecode(response.body);
      print("result.toString() =======> {$result.toString()}");
    } catch (e) {
      log(e.toString());
      print(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
