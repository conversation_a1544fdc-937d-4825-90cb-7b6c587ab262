// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/widget/common_button.dart';

import '../../../../utils/images.dart';
import '../../../../utils/text_style.dart';
import '../../../widget/common_space_divider_widget.dart';
import '../../../widget/icon_and_image.dart';
import 'no_refund_found_screen.dart';
import 'order_refund_screen.dart';

class RefundHistoryScreen extends StatelessWidget {
  RefundHistoryScreen({super.key});

  RefundsController refundsController = Get.put(RefundsController());

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            refundsController.orderRefundList.isEmpty
                ? NoRefundFoundScreen()
                : ListView.builder(
                    itemCount: refundsController.refundHistoryList.length,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      var data = refundsController.refundHistoryList[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: refundDataWidget(
                          code: data['code'],
                          status: data['status'].toString().trr,
                          orderType: data['orderType'].toString().trr,
                          plate: data['plate'],
                          vehicleType: data['vehicleType'],
                          orderDate: data['orderDate'],
                          amount: data['amount'],
                          vat: data['vat'],
                          textColor: data['status'] == "Confirmed"
                              ? AppColor.cGreen
                              : AppColor.cDarkBlueFont,
                          color: data['status'] == "Confirmed"
                              ? AppColor.cLightGreen
                              : AppColor.cLightBlueContainer,
                          isShowButton:
                              data['status'] == "Confirmed" ? false : true,
                          cancelReqOnTap: () {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12)),
                                  contentPadding: EdgeInsets.all(24),
                                  insetPadding: EdgeInsets.all(16),
                                  content: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          GestureDetector(
                                              onTap: () {
                                                Get.back();
                                              },
                                              child: assetSvdImageWidget(
                                                  image:
                                                      DefaultImages.cancelIcn)),
                                        ],
                                      ),
                                      verticalSpace(24),
                                      Text(
                                         "${"Are you sure you want to cancel refund request".trr} ${data['code']}?",
                                          style: pBold20,
                                          textAlign: TextAlign.center),
                                      verticalSpace(14),
                                      Center(
                                          child: Text(
                                              "You can't undo this. If you still need a problem to be resolved, you'll need to submit a new refund request.".trr,
                                              style: pRegular13,
                                              textAlign: TextAlign.center)),
                                      verticalSpace(24),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: CommonButton(
                                              title: "OK".trr,
                                              onPressed: () {
                                                Get.back();
                                              },
                                              textColor: AppColor.cDarkBlueFont,
                                              btnColor: AppColor.cBackGround,
                                              bColor: AppColor.cDarkBlueFont,
                                            ),
                                          ),
                                          horizontalSpace(16),
                                          Expanded(
                                            child: CommonButton(
                                              title: "Yes, cancel".trr,
                                              onPressed: () {
                                                Get.back();
                                              },
                                              textColor: AppColor.cWhiteFont,
                                              btnColor: AppColor.cRedText,
                                              bColor: AppColor.cTransparent,
                                              horizontalPadding: 16,
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      );
                    },
                  ),
          ],
        ),
      ),
    );
  }
}
