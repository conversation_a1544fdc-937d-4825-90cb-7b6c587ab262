import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/logger_extention/logger_helper.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';

import '../../../models/profile.dart';
import '../../../models/usemenulist.dart';
import '../../../utils/api_endpoints.dart';
import '../../../utils/colors.dart';
import '../../../utils/text_style.dart';
import '../../../view/widget/loading_widget.dart';

class UserMenuController extends GetxController {
  var userMenuList = <UserMenulist>[].obs;

  bool isDownloading = false;
  String downloadStateMessage = "";

  Future<void> downloadPdf(String url, String fileName) async {
    try {
      if (Platform.isIOS||await requestStoragePermission()) {
        isDownloading = true;
        downloadStateMessage = "جارٍ التحميل...";
        update();

        final response = await http.get(Uri.parse(url));

        if (response.statusCode == 200) {
          logSuccess("Good Request with status200");


          Directory? directory ;
          if(Platform.isIOS){
            directory = await getApplicationDocumentsDirectory(); // iOS-safe directory
          } else {
           directory=  await getDownloadsDirectory();
           directory??= await getApplicationDocumentsDirectory();
          }
          logInfo("Saving to: ${directory?.path}");

          if (directory == null) {
            throw Exception("تعذر العثور على مجلد الحفظ!");
          }

          String savePath = "${directory?.path}/$fileName.pdf";
          File file = File(savePath);
          await file.writeAsBytes(response.bodyBytes);

          isDownloading = false;
          downloadStateMessage = "تم التحميل بنجاح: $savePath";
          update();

          commonToast("Download_completed".tr, gravity: ToastGravity.BOTTOM);
          Get.back();

          OpenFile.open(savePath);
        } else {
          logError("error with download ${response.statusCode} ${response.body}");
          throw Exception("فشل التحميل: ${response.statusCode}");
        }
      } else {
        showPermissionDeniedDialog();
        logWarning("PermissionDenied");
        throw Exception("الإذن مرفوض!");
      }
    } catch (e) {
      isDownloading = false;
      downloadStateMessage = "حدث خطأ أثناء التحميل: $e";
      update();

      commonToast("Error_occurred".tr, gravity: ToastGravity.BOTTOM);
      logError("Error_Occurred $e");
      Get.back();
    }
  }

  Future<bool> requestStoragePermission() async {
    if (Platform.isIOS) return true; // iOS does not require storage permission

    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    int sdkInt = androidInfo.version.sdkInt;
    logInfo(sdkInt.toString());

    if (sdkInt >= 33) {
      var permission = await Permission.mediaLibrary.request();
      return permission.isGranted;
    } else {
      var permission = await Permission.storage.request();
      return permission.isGranted;    }

    // if (Platform.isAndroid) {
    //   AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    //   int sdkInt = androidInfo.version.sdkInt;
    //   logInfo("SDK Version: $sdkInt");
    //   if (sdkInt >= 33) {
    //     // Android 13 and above
    //     var mediaPermission = await Permission.mediaLibrary.request();
    //     logInfo("Media Permission: ${mediaPermission.isGranted}");
    //     if (mediaPermission.isGranted) {
    //       return true;
    //     } else {
    //       // If media permission is not granted, request manage external storage permission
    //       var managePermission = await Permission.manageExternalStorage.request();
    //       logInfo("Manage External Storage Permission: ${managePermission.isGranted}");
    //       if (managePermission.isGranted) {
    //         return true;
    //       } else {
    //         // Show dialog asking the user to grant permission in the settings
    //         showPermissionDeniedDialog();
    //         return false;
    //       }
    //     }
    //   } else {
    //     // Android 10 to 12
    //     if (await Permission.storage.request().isGranted) {
    //       return true;
    //     }
    //   }
    // }
    // return false;
  }



  Future<void> showPermissionDeniedDialog() async {
    Get.defaultDialog(
      title: "الإذن مرفوض!",
      middleText: "الرجاء منح الإذن من إعدادات الهاتف لإتمام العملية.",
      actions: [
        TextButton(
          onPressed: () {
            Get.back();
          },
          child: Text('إلغاء'),
        ),
        TextButton(
          onPressed: () {
            openAppSettings();
            Get.back();
          },
          child: Text('فتح الإعدادات'),
        ),
      ],
    );
  }

  void navigateToProfileOrSettings(BuildContext context, Widget Function() page, Widget Function() elsePage) {
    if (Constants.hasCloseAccountRequest == 'Y') {
      showSnackBarIfAccountPending(context);
    } else if (Constants.custAcctType == "C" && Constants.custAcctStatus != "A") {
      Get.to(() => page());  // Invoke the function that returns a widget
    } else {
      Get.to(() => elsePage());  // Invoke the function that returns a widget
    }
  }

  void showSnackBarIfAccountPending(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: AppColor.cLiteYellow,
        content: Text(
          "account_pending".tr,
          style: pBold14.copyWith(color: AppColor.cBlackFont),
        ),
      ),
    );
  }

/*  Future<dynamic> getUserMenu() async {
    Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getUserMenu),
          body: {
            "custId": userid,
            "ACCTTYPE": userData.auCust?.accttype,
            "email": userData.auCust?.emailid,
            "IsAR": Constants.IsAr_App
          });
      print("User Menu List============${response.body}");
      // List <UserMenulist>result2 = UserMenulist.fromJson(jsonDecode(response.body)) as List<UserMenulist>;
      Loader.hideLoader();
      Iterable list = jsonDecode(response.body);
      List<dynamic> orderHistory = List<UserMenulist>.from(
          list.map((model) => UserMenulist.fromJson(model)));
      for (UserMenulist orderHis in orderHistory) {
        //userMenuList.add(orderHis);
        if (orderHis.menuCode == "FINA0") {
          Constants.menu_Finance = true;
          Constants.subMenu_Order = true;
          Constants.subMenu_PurchaseHistory = true;
          Constants.subMenu_Refund = true;
          Constants.subMenu_CompanyAffiliates = true;
        }
        if (orderHis.menuCode == "FLEE0") {
          Constants.menu_Fleet = true;
          Constants.subMenu_FleetStruc = true;
        }
        if (orderHis.menuCode == "REPO0") {
          Constants.menu_Reports = true;
        }
        if (orderHis.menuCode == "PROF0") {
          Constants.menu_profile = true;
          Constants.subMenu_MyCompany = true;
          Constants.subMenu_Users = true;
          Constants.subMenu_Subscription = true;
        }
      }

      *//* Constants.userMenuListCon.clear();
      Constants.userMenuListCon.assignAll(userMenuList);*//*

      //Constants.menu_Finance=true;
      return "";
    } catch (e) {
      log("User Menu List Error $e");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }*/
}
