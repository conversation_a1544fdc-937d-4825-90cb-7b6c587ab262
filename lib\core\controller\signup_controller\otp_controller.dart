import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/login_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../view/screen/auth/login_with_email_screen.dart';

class OTPController extends GetxController {
  final getOTPDataVerify = GetStorage();
  //final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  void signupOTPVerify(code) async {
    Loader.showLoader();
    var client = http.Client();
    var isPrimay = getOTPDataVerify.read('isPrimary');
    var custID = getOTPDataVerify.read('custid');
    var emailID = getOTPDataVerify.read('emailid');
    var otpCode = getOTPDataVerify.read('otpCode');
    print("OTP CONTROLLER ======>$code");
    print("OTP IsPrimay ======>$isPrimay");
    print("OTP CUSTID ======>$custID");
    print("OTP EMAILID ======>$emailID");
    print("OTP otpCode ======>$otpCode");

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    if (code == otpCode) {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.otpVerify),
          body: {
            'isPrimary': isPrimay,
            'custid': custID,
            'emailid': emailID,
            'otpCode': otpCode
          },
          headers: {
            'authorization': basicAuth,
          });

      getOTPDataVerify.remove('isPrimary');
      getOTPDataVerify.remove('custid');
      getOTPDataVerify.remove('emailid');
      getOTPDataVerify.remove('otpCode');
      print("OTP response BODY ======>${jsonDecode(response.body)}");
      Loader.hideLoader();
      return showDialog(
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(16),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("Thanks for registration".tr, style: pBold20),
                verticalSpace(24),
                Text(
                  "We will send for you the confirmation code to your mobile phone."
                      .tr,
                  style: pRegular16,
                  textAlign: TextAlign.center,
                ),
                verticalSpace(24),
                CommonButton(
                  title: "Back to log in".tr,
                  onPressed: () {
                    Get.offAll(() => const LoginWithEmailScreen());
                  },
                )
              ],
            ),
          );
        },
      );
    } else {
      showDialog(
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(16),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("ERROR".tr, style: pBold20),
                verticalSpace(24),
                Text(
                  "OTP is Incorrect.".tr,
                  style: pRegular16,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      );
    }
  }

  signupResendOTP() async {
    Loader.showLoader();
    var client = http.Client();
    var custID = getOTPDataVerify.read('custid');
    var emailID = getOTPDataVerify.read('emailid');
    print("OTP CUSTID ======>$custID");
    print("OTP EMAILID ======>$emailID");

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.signupResendOTP),
          body: {
            "emailid": emailID,
            "custid": custID,
          },
          headers: {
            'authorization': basicAuth,
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      Map<String, dynamic> result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      // print(
      //     "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      // for (int i = 0; i < result.length; i++) {
      //   FleetStructureModel structures =
      //       FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
      //   divisionList.add(structures);
      // }
      // print("===============================================================");
      if (jsonDecode(response.body)["Message"] == "VALIDATIONCODERESEND") {
        Loader.hideLoader();
      } else {
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    jsonDecode(response.body)["Message"],
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }

      // return divisionList;
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
