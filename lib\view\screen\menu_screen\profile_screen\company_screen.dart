// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/menu_controller/profile_controller/profile_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/prefer.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/company_addreess_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/company_details_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/profile_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/profile_controller/address_load_controller.dart';
import '../../../widget/loading_widget.dart';
import '../../auth/login_with_email_screen.dart';
import 'personal_details_screen.dart';

class CompanyScreen extends StatelessWidget {
  CompanyScreen({super.key});

  ProfileController profileController = Get.put(ProfileController());
  Address_Data_Controller addressLoadController =
      Get.put(Address_Data_Controller());

  GetStorage userStorage = GetStorage('User');
  final vehicle = GetStorage();
  GetStorage custsData = GetStorage('custsData');
  GetStorage usersData = GetStorage('usersData');

  @override
  Widget build(BuildContext context) {
    print(
        "profileController >>>>>>>> ${profileController.fetchProfileDetail()}");
    print("userStorage >>>>> ${userStorage.read('custid')}");
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  simpleMyAppBar(
                      title: "My Company".trr,
                      onTap: () async {
                        SharedPreferences sharedProfileDetails =
                            await SharedPreferences.getInstance();
                        SharedPreferences sharedProfileDetail =
                            await SharedPreferences.getInstance();
                        await sharedProfileDetails.clear();
                        await sharedProfileDetail.clear();
                        Get.back();
                      },
                      backString: "Back".trr,
                      horizontalSize: 45),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  companyItemWidget(
                    image: DefaultImages.companyDetailIcn,
                    title: 'Company details'.trr,
                    onTap: () {
                      Get.to(() => CompanyDetailScreen());
                    },
                  ),
                  companyItemWidget(
                    image: DefaultImages.addressIcn,
                    title: 'Company address'.trr,
                    onTap: () {
                      Get.to(() => CompanyAddressScreen());
                    },
                  ),
                  companyItemWidget(
                    image: DefaultImages.userProfileIcn,
                    title: 'Personal information'.trr,
                    onTap: () {
                      Get.to(() => PersonalDetailScreen());
                    },
                  ),

                 /* companyItemWidget(
                      image: DefaultImages.logoutIcn,
                      title: 'Log out'.trr,
                      onTap: () {
                        Prefs.clear();
                        //Get.offAll(() => LoginManagerScreen());
                        vehicle.erase();
                        userStorage.erase();
                        custsData.erase();
                        usersData.erase();
                        Get.offAll(() => LoginWithEmailScreen());
                      },
                      isIcon: false,
                      textStyle: pRegular17.copyWith(color: AppColor.cRedText)),*/
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

companyItemWidget({
  required String title,
  required String image,
  required Function() onTap,
  TextStyle? textStyle,
  bool? isIcon,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
      child: Container(
        color: AppColor.cBackGround,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                assetSvdImageWidget(image: image),
                horizontalSpace(16),
                Text(
                  title,
                  style: textStyle ?? pRegular17,
                ),
              ],
            ),
            isIcon == false
                ? SizedBox()
                : assetSvdImageWidget(
                    image: DefaultImages.nextIcn,
                    colorFilter: ColorFilter.mode(
                      AppColor.cText,
                      BlendMode.srcIn,
                    ),
                  ),
          ],
        ),
      ),
    ),
  );
}
