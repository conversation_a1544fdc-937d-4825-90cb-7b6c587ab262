// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_cards_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_tags_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/refund_history_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/topup_refund_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../location_screen/location_screen.dart';
import 'refund_search_widget.dart';
import 'submit_refund_screen.dart';

class OrderRefundMenuScreen extends StatelessWidget {
  OrderRefundMenuScreen({super.key});

  RefundsController refundsController = Get.put(RefundsController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 16, right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                          top: 15,
                          bottom: 15,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.backIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cDarkBlueFont, BlendMode.srcIn)),
                            horizontalSpace(10),
                            Text(
                              "Back".trr,
                              style: pRegular18.copyWith(
                                  color: AppColor.cDarkBlueFont, fontSize: 17),
                              textAlign: TextAlign.start,
                            )
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "Order Refunds".trr,
                          style: pBold20,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Obx(() {
                  return Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      // shrinkWrap: true,
                      // padding: EdgeInsets.all(16),
                      children: [
                        SizedBox(
                          //height: Get.height * 0.06,
                          child: Row(
                            children: [
                              locationTabWidget(
                                  title: 'Tags'.trr,
                                  width: Get.width / 2.5,
                                  onTap: () {
                                    refundsController.isOrderRefund.value =
                                        true;
                                    refundsController.isTopUpRefund.value =
                                        false;
                                    refundsController.isRefundHistory.value =
                                        false;
                                  },
                                  isSelected:
                                      refundsController.isOrderRefund.value),
                              locationTabWidget(
                                  title: 'Smart Cards'.trr,
                                  width: Get.width / 2.5,
                                  onTap: () {
                                    refundsController.isOrderRefund.value =
                                        false;
                                    refundsController.isTopUpRefund.value =
                                        true;
                                    refundsController.isRefundHistory.value =
                                        false;
                                  },
                                  isSelected:
                                      refundsController.isTopUpRefund.value),
                            ],
                          ),
                        ),
                        refundsController.isOrderRefund.value == true
                            ? OrderRefundTagsScreen()
                            : OrderRefundCardsScreen(),
                      ],
                    ),
                  );
                }),
              )
            ],
          ),
        ),
        bottomNavigationBar: Obx(() {
          return refundsController.selectedOrderRefundList.isEmpty
              ? SizedBox()
              : refundsController.isOrderRefund.value == true
                  ? Container(
                      // padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                      padding: EdgeInsets.fromLTRB(24, 0, 24, 12),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text.rich(
                            TextSpan(
                              text:
                                  '${refundsController.selectedOrderRefundList.length}  ' +
                                      "orders".trr +
                                      " ",
                              style: pBold14,
                              children: <TextSpan>[
                                TextSpan(
                                    text: 'selected'.trr,
                                    style: pRegular14.copyWith(
                                        color: AppColor.cDarkGreyFont)),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              CommonButton(
                                height: 40,
                                width: 130,
                                title: "Refund_selected".trr,
                                horizontalPadding: 8,
                                btnColor: AppColor.themeOrangeColor,
                                onPressed: () {
                                  Get.to(SubmitRefundScreen(routedScreen: "OrderRefundMenuScreen"));
                                },
                              ),
                              horizontalSpace(24),
                              GestureDetector(
                                  onTap: () {
                                    refundsController.selectedOrderRefundList
                                        .clear();
                                    for (var element
                                        in refundsController.orderRefundList) {
                                      print("object $element");
                                      element['value'].value = false;
                                    }
                                    refundsController.orderRefundList.refresh();
                                  },
                                  child: assetSvdImageWidget(
                                      image: DefaultImages.cancelIcn))
                            ],
                          )
                        ],
                      ))
                  : SizedBox();
        }),
      ),
    );
  }
}
