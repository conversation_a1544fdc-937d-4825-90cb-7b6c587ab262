import 'dart:developer';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/auth/auth_controller.dart';
import 'package:waie_app/core/controller/signup_controller/otp_controller.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/auth_background.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:pinput/pinput.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class TFAOTPScreen extends StatefulWidget {
  const TFAOTPScreen({super.key, required this.userName});
  final String userName;

  @override
  State<TFAOTPScreen> createState() => _TFAOTPScreenState();
}

class _TFAOTPScreenState extends State<TFAOTPScreen> {
  AuthController authController = Get.put(AuthController());
  OTPController otpController = Get.put(OTPController());
  final pinController = TextEditingController();
  final focusNode = FocusNode();
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    pinController.dispose();
    focusNode.dispose();
    super.dispose();
    log("username ${widget.userName}");
  }

  bool isLoading = false;

  Future<void> submitOTP(String username, String txtSecurityCode) async {
    isLoading = true;
    setState(() {});
    log("username and otp ${username + txtSecurityCode}");
    final String url =
        ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.authGoogle;

    //'https://devinttest.aldrees.com/api/AUTHGOOGLE/';
    log(url);

    Map<String, String> body = {
      'username': username,
      'txtSecurityCode': txtSecurityCode,
    };
    log(
      json.encode(body),
    );

    final response = await http.post(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: body,
    );

    log(response.statusCode.toString());

    var responseBody = json.decode(response.body);
    log('response of otp $responseBody');
    if (response.statusCode == 200) {
      if (responseBody["Success"] == false) {
        commonToast(responseBody["message"]);
        isLoading = false;
        setState(() {});
      } else if (responseBody["Success"] == true) {
        isLoading = false;
        authController.getUserAccess();
        commonToast(responseBody["message"]);
      }
    } else {
      print('Request failed with status: ${response.statusCode}');
      var responseBody = json.decode(response.body);
      log('Error: ${responseBody['Message']}');
      commonToast('Something Went Wrong'.trr);
    }
  }

  @override
  Widget build(BuildContext context) {
    const focusedBorderColor = Color.fromRGBO(0, 157, 223, 1);
    const fillColor = Color.fromRGBO(243, 246, 249, 0);
    const borderColor = Color.fromRGBO(1, 93, 133, 1);

    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: const TextStyle(
        fontSize: 22,
        color: Color.fromRGBO(30, 60, 87, 1),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(19),
        border: Border.all(color: borderColor),
      ),
    );

    /// Optionally you can use form to validate the Pinput
    return WillPopScope(
      onWillPop: () async => false,
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Scaffold(
          backgroundColor: AppColor.cBackGround,
          body: SafeArea(
            child: AuthBackGroundWidget(
              widget: ListView(
                scrollDirection: Axis.vertical,
                shrinkWrap: true,
                children: [
                  verticalSpace(Get.height * 0.12),
                  Text(
                    "Enter the OTP and Submit".trr,
                    style: pBold28,
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 10),
                    child: Text(
                      "Download and Install Google Authenticator App on your Smartphones To Get OTP"
                          .trr,
                      style: pRegular17,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Form(
                    key: formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        verticalSpace(24),
                        Directionality(
                          // Specify direction if desired
                          textDirection: TextDirection.ltr,
                          child: Pinput(
                            length: 6,
                            controller: pinController,
                            focusNode: focusNode,
                            androidSmsAutofillMethod:
                                AndroidSmsAutofillMethod.smsUserConsentApi,
                            listenForMultipleSmsOnAndroid: true,
                            defaultPinTheme: defaultPinTheme,
                            separatorBuilder: (index) =>
                                const SizedBox(width: 8),
                            // validator: (value) {
                            //   return value == '2222' ? null : 'Pin is incorrect';
                            // },
                            // onClipboardFound: (value) {
                            //   debugPrint('onClipboardFound: $value');
                            //   pinController.setText(value);
                            // },
                            hapticFeedbackType: HapticFeedbackType.lightImpact,
                            onCompleted: (pin) {
                              debugPrint('onCompleted: $pin');
                            },
                            onChanged: (value) {
                              debugPrint('onChanged: $value');
                            },
                            cursor: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(bottom: 9),
                                  width: 22,
                                  height: 1,
                                  color: focusedBorderColor,
                                ),
                              ],
                            ),
                            focusedPinTheme: defaultPinTheme.copyWith(
                              decoration: defaultPinTheme.decoration!.copyWith(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: focusedBorderColor),
                              ),
                            ),
                            submittedPinTheme: defaultPinTheme.copyWith(
                              decoration: defaultPinTheme.decoration!.copyWith(
                                color: fillColor,
                                borderRadius: BorderRadius.circular(19),
                                border: Border.all(color: focusedBorderColor),
                              ),
                            ),
                          ),
                        ),
                        verticalSpace(40),
                        (isLoading)
                            ? const CircularProgressIndicator()
                            : CommonButton(
                                title: 'Submit'.trr,
                                onPressed: () async {
                                  print(pinController.value.text);

                                  if (pinController.text.isEmpty) {
                                    commonToast("Enter OTP".trr);
                                    return;
                                  }
                                  await submitOTP(
                                      widget.userName, pinController.text);

                                  focusNode.unfocus();
                                  formKey.currentState!.validate();
                                },
                              )
                      ],
                    ),
                  ),
                  verticalSpace(20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
