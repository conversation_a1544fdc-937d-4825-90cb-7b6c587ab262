class BalanceTopUpModel {
  BalanceTopUpModel({
    required this.title,
    required this.message,
    required this.subMessage,
    required this.messageType,
    required this.responseAction,
    required this.redirect,
    required this.actionParam,
    required this.action,
    required this.isValidTransaction,
    required this.data,
  });

  final dynamic title;
  final String? message;
  final dynamic subMessage;
  final dynamic messageType;
  final dynamic responseAction;
  final dynamic redirect;
  final dynamic actionParam;
  final String? action;
  final bool? isValidTransaction;
  final dynamic data;

  factory BalanceTopUpModel.fromJson(Map<String, dynamic> json){
    return BalanceTopUpModel(
      title: json["Title"],
      message: json["Message"],
      subMessage: json["SubMessage"],
      messageType: json["MessageType"],
      responseAction: json["ResponseAction"],
      redirect: json["Redirect"],
      actionParam: json["ActionParam"],
      action: json["Action"],
      isValidTransaction: json["isValidTransaction"],
      data: json["Data"],
    );
  }

  Map<String, dynamic> toJson() => {
    "Title": title,
    "Message": message,
    "SubMessage": subMessage,
    "MessageType": messageType,
    "ResponseAction": responseAction,
    "Redirect": redirect,
    "ActionParam": actionParam,
    "Action": action,
    "isValidTransaction": isValidTransaction,
    "Data": data,
  };

}
