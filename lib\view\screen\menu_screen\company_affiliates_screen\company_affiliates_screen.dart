// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:get/get.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'no_affiliates_widget.dart';
import 'company_affiliates_data.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/core/controller/menu_controller/company_affiliate_controller/company_affiliate_controller.dart';

class CompanyAffiliatesScreen extends StatelessWidget {
  CompanyAffiliatesScreen({Key? key}) : super(key: key);
  CompanyAffiliateController companyAffiliateController =
      Get.put(CompanyAffiliateController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Obx(() {
          return Column(
            children: [
              Container(
                padding: EdgeInsets.only(right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                          left: 16,
                          top: 15,
                          bottom: 15,
                        ),
                        color: AppColor.cBackGround,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.backIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cDarkBlueFont, BlendMode.srcIn)),
                            horizontalSpace(10),
                            Text(
                             // "Menu".trr,
                              "Back".trr,
                              style: pRegular18.copyWith(
                                  color: AppColor.cDarkBlueFont, fontSize: 17),
                              textAlign: TextAlign.start,
                            )
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Text(
                        companyAffiliateController.companyAffiliateList.isEmpty
                            ? "Affiliates".trr
                            : "Company affiliates".trr,
                        style: pBold20,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              companyAffiliateController.companyAffiliateList.isEmpty
                  ? Expanded(child: NoAffiliatesWidget())
                  : CompanyAffiliateDataScreen(
                      companyAffiliateController: companyAffiliateController,
                    )
            ],
          );
        }),
      ),
    );
  }
}
