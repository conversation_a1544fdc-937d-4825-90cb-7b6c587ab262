// ignore_for_file: avoid_print, prefer_const_constructors

import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/colors.dart';
import '../../../models/checkupdate.dart';
import '../../../utils/api_endpoints.dart';
import '../../../utils/constant_var.dart';

class SplashController extends GetxController {
  List checkUpdateList = <CheckUpdateModel>[].obs;
  String imagePath = '';
  late final NetworkImage splashImage;
  List<dynamic> fetchMobColorsAndImagesList = [];
  String updateURLANDROID =
      "https://play.google.com/store/apps/details?id=${ConstantVar.appPackageName}";
  String updateURLIOS =
      "https://apps.apple.com/us/app/الدريس-aldrees/id6738043230";

  checkUpdate() async {
    var client = http.Client();
    var verCode = "";
    var verNumber = "";
    var appType = "";
    if (Platform.isIOS) {
      appType = ConstantVar.appTypeIOS;
      verCode = ConstantVar.versionCODEios.toString();
      verNumber = ConstantVar.versionNumberIOS;
    } else {
      appType = ConstantVar.appTypeAndroid;
      verCode = ConstantVar.versionCODEandroid.toString();
      verNumber = ConstantVar.versionNumberAndroid;
    }
    try {
      var response = await client.post(
          // https://devint.aldrees.com/api/COMAPPUPDATE?APPTYPE=ANDROID&APP_NAME=AMA&VER_CODE=50&VER_NUMBER=2.0.2
          Uri.parse(ApiEndPoints.baseUrlLookup +
              ApiEndPoints.authEndpoints.newComAppUpdate),
          body: {
            "APPTYPE": appType,
            "APP_NAME": ConstantVar.appName,
            "VER_CODE": verCode,
            "VER_NUMBER": verNumber,
          });
      print("SplashController result");
      print(appType);
      print(ConstantVar.appName);
      print(verCode);
      print(verNumber);
      print(jsonDecode(response.body));
      List result = jsonDecode(response.body);
      print("SplashController result >>>>> $result");
      print(
          "SplashController URL >>>>> ${jsonDecode(response.body)[0]["URL"]}");
      //ApiEndPoints.baseUrl = 'https://devstage.aldrees.com/api';
      //ApiEndPoints.baseUrl = 'https://devinttest.aldrees.com/api';
      if (jsonDecode(response.body)[0]["RESULT"] != "TERMINATED") {
        if (jsonDecode(response.body)[0]["URL"] != "") {
          ApiEndPoints.baseUrl = jsonDecode(response.body)[0]["URL"];
          //ApiEndPoints.baseUrl = 'https://devstage.aldrees.com/api';
          //ApiEndPoints.baseUrl = 'https://devinttest.aldrees.com/api';
        }
      } else {
        ApiEndPoints.baseUrl = 'https://devstage.aldrees.com/api';
      }
      if (Platform.isIOS) {
        updateURLIOS = jsonDecode(response.body)[0]["LINK"];
      } else {
        updateURLANDROID = jsonDecode(response.body)[0]["LINK"];
      }
      print("===============================================================");
      print(
          "jsonDecode(response.body >>>>> ${jsonDecode(response.body)[0]["RESULT"]}");
      print(
          "jsonDecode(response.body >>>>> ${jsonDecode(response.body)[0]["REMARKS"]}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        CheckUpdateModel chckupdate =
            CheckUpdateModel.fromMap(result[i] as Map<String, dynamic>);
        checkUpdateList.add(chckupdate);
      }
      print(
          "jsonDecode(response.body >>>>> ${jsonDecode(jsonEncode(checkUpdateList))}");
      print("===============================================================");
      return result;
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  checkOldAppTerminate() async {
    var client = http.Client();
    var appType = "";
    if (Platform.isIOS) {
      appType = ConstantVar.appTypeIOS;
    } else {
      appType = ConstantVar.appTypeAndroid;
    }
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrlLookup +
              ApiEndPoints.authEndpoints.appTerminate),
          body: {
            "APPTYPE": appType,
            "APP_NAME": "WAIENOTY",
          });
      print("WAIENOTY result");
      print(appType);
      print("WAIENOTY===");
      print(jsonDecode(response.body));
      List result = jsonDecode(response.body);
      print("appTerminate result >>>>> $result");
      print("appTerminate URL >>>>> ${jsonDecode(response.body)[0]["URL"]}");
      // if (jsonDecode(response.body)[0]["RESULT"] != "TERMINATED") {
      //   if (jsonDecode(response.body)[0]["URL"] != "") {
      //     ApiEndPoints.baseUrl = jsonDecode(response.body)[0]["URL"];
      //   }
      // } else {
      //   ApiEndPoints.baseUrl = 'https://devinttest.aldrees.com/api';
      // }
      if (Platform.isIOS) {
        updateURLIOS = jsonDecode(response.body)[0]["LINK"];
      } else {
        updateURLANDROID = jsonDecode(response.body)[0]["LINK"];
      }
      print("===============================================================");
      print(
          "jsonDecode(response.body >>>>> ${jsonDecode(response.body)[0]["RESULT"]}");
      print(
          "jsonDecode(response.body >>>>> ${jsonDecode(response.body)[0]["REMARKS"]}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        CheckUpdateModel chckupdate =
            CheckUpdateModel.fromMap(result[i] as Map<String, dynamic>);
        checkUpdateList.add(chckupdate);
      }
      print(
          "jsonDecode(response.body >>>>> ${jsonDecode(jsonEncode(checkUpdateList))}");
      print("===============================================================");
      return result;
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  getBaseUrlLookup() async {
    var client = http.Client();
    var verCode = "";
    var verNumber = "";
    var appType = "";
    if (Platform.isIOS) {
      appType = ConstantVar.appTypeIOS;
      verCode = ConstantVar.versionCODEios.toString();
      verNumber = ConstantVar.versionNumberIOS;
    } else {
      appType = ConstantVar.appTypeAndroid;
      verCode = ConstantVar.versionCODEandroid.toString();
      verNumber = ConstantVar.versionNumberAndroid;
    }
    try {
      var response = await client.post(
          // https://devint.aldrees.com/api/COMAPPUPDATE?APPTYPE=ANDROID&APP_NAME=AMA&VER_CODE=50&VER_NUMBER=2.0.2
          Uri.parse(ApiEndPoints.baseUrlLookup +
              ApiEndPoints.authEndpoints.newComAppUpdate),
          body: {
            "APPTYPE": appType,
            "APP_NAME": ConstantVar.appName,
            "VER_CODE": verCode,
            "VER_NUMBER": verNumber,
          });
      print("getBaseUrlLookup result");
      print(appType);
      print(ConstantVar.appName);
      print(verCode);
      print(verNumber);
      print(jsonDecode(response.body));
      List result = jsonDecode(response.body);
      print("getBaseUrlLookup result >>>>> $result");
      print(
          "getBaseUrlLookup URL >>>>> ${jsonDecode(response.body)[0]["URL"]}");
      if (jsonDecode(response.body)[0]["RESULT"] != "TERMINATED") {
        if (jsonDecode(response.body)[0]["URL"] != "") {
          ApiEndPoints.baseUrlLookup = jsonDecode(response.body)[0]["URL"];
        }
      } else {
        ApiEndPoints.baseUrlLookup = 'https://devstage.aldrees.com/api';
      }
      print("===============================================================");
      print(
          "ApiEndPoints.baseUrlLookup >>>>> ${jsonDecode(response.body)[0]["RESULT"]}");
      print(
          "ApiEndPoints.baseUrlLookup >>>>> ${jsonDecode(response.body)[0]["REMARKS"]}");
      print(
          "ApiEndPoints.baseUrlLookup >>>>> ${jsonDecode(response.body)[0]["URL"]}");
      print(
          "ApiEndPoints.baseUrlLookup >>>>> ${jsonDecode(response.body)[0]["LINK"]}");
      print("===============================================================");
      return result;
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  Future<void> fetchMobColorsAndImages() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    //String url = "https://devstage.aldrees.com/api/MOBTHEME/";
    String url = ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getMobTheme;
    log(url);
    final response = await http.post(Uri.parse(url));
    getBaseUrlLookup();

    log(response.body);

    if (response.statusCode == 200) {
      if (response.body != 'null') {
        fetchMobColorsAndImagesList = json.decode(response.body);

        prefs.setString(
            'mobThemeData', json.encode(fetchMobColorsAndImagesList));

        Map<String, String> colorMap = {};

        for (var item in fetchMobColorsAndImagesList) {
          if (item['TYPEID'] == 'MOBCOLORS') {
            colorMap[item['TYPEDESC']] = item['WH_CODE'];
          }
        }

        AppColor.updateColors(colorMap);
      } else {
        log('Response body is null');
        prefs.remove('mobThemeData');
      }
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<String> loadImage(String typeId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? jsonString = prefs.getString('mobThemeData');

    if (jsonString != null) {
      List<dynamic> data = json.decode(jsonString);
      var imageData = data.firstWhere(
        (item) => item['TYPEID'] == typeId,
        orElse: () => {},
      );

      update();

      return imageData['IMG_LOC'] ?? '';
    }

    return '';
  }
}
