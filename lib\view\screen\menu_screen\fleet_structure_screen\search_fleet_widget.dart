// ignore_for_file: prefer_const_constructors, prefer_const_constructors_in_immutables

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_order_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/fleet_structure_controller/fleet_structure_controller.dart';
import 'add_department_widget.dart';
import 'add_operation_widget.dart';
import 'add_sub_elements_widget.dart';
import 'delete_division_widget.dart';
import 'edit_branch_widget.dart';
import 'fleet_structure_screen.dart';

class SearchFleetStructureWidget extends StatefulWidget {
  SearchFleetStructureWidget({super.key});

  @override
  State<SearchFleetStructureWidget> createState() =>
      _SearchFleetStructureWidgetState();
}

class _SearchFleetStructureWidgetState
    extends State<SearchFleetStructureWidget> {
  FleetStructureController fleetStructureController =
      Get.put(FleetStructureController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fleetStructureController.itemList.clear();
  }

  void filterSearchResults(String query) {
    fleetStructureController.itemList.value = fleetStructureController
        .fleetStructureList
        .where(
            (item) => item['title'].toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        height: Get.height - 60,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        padding: EdgeInsets.all(16),
        child: Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              verticalSpace(16),
              Row(
                children: [
                  Expanded(
                    child: CommonTextField(
                      controller:
                          fleetStructureController.searchController.value,
                      labelText: '',
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(12),
                        child: assetSvdImageWidget(
                            image: DefaultImages.searchIcn,
                            width: 24,
                            height: 24),
                      ),
                      hintText: 'Search'.trr,
                      onChanged: (value) {
                        if (value.isEmpty) {
                          fleetStructureController.itemList.clear();
                          fleetStructureController.itemList.refresh();
                        } else {
                          fleetStructureController.searchController.refresh();
                          filterSearchResults(value);
                        }
                      },
                    ),
                  ),
                  fleetStructureController.searchController.value.text.isEmpty
                      ? SizedBox()
                      : cancelButton(
                          () {
                            fleetStructureController.searchController.value
                                .clear();
                            fleetStructureController.searchController.refresh();
                            fleetStructureController.itemList.clear();
                            fleetStructureController.itemList.refresh();
                          },
                        )
                ],
              ),
              verticalSpace(16),
              fleetStructureController.itemList.isEmpty
                  ? Expanded(
                      child: Center(
                          child: Text(
                      "No matches".trr,
                      style:
                          pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                    )))
                  : Expanded(
                      child: ListView.builder(
                        itemCount: fleetStructureController.itemList.length,
                        shrinkWrap: true,
                        physics: BouncingScrollPhysics(),
                        itemBuilder: (context, index) {
                          var data = fleetStructureController.itemList[index];
                          return Obx(() {
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: Container(
                                decoration: BoxDecoration(
                                    border: Border.all(
                                        color: AppColor.cLightBlueContainer),
                                    borderRadius: BorderRadius.circular(4)),
                                child: Column(
                                  children: [
                                    fleetStructureDataWidget(
                                        context: context,
                                        title: data['title'],
                                        status: data['status'].toString().trr,
                                        //dropDownIcn: data['isSelected'].value == true ? DefaultImages.blackArrowDownIcn : DefaultImages.arrowRightIcn,
                                        divisionImage:
                                            DefaultImages.addDivisionIcn,
                                        divisionTitle: data['division_title']
                                            .toString()
                                            .trr,
                                        division: data['division'],
                                        onTap: () {
                                          data['isSelected'].value =
                                              !data['isSelected'].value;
                                          data['branch']['isBranch'].value =
                                              false;
                                          data['department']['isDepartment']
                                              .value = false;
                                        },
                                        tooltipItemList: [
                                          "Edit Division",
                                          "Add Branch",
                                          "Delete Division",
                                        ],
                                        moreOnTap: (String value) {
                                          print(
                                              'You Click on po up menu item $value');
                                          if (value == 'edit division') {
                                            showModalBottomSheet(
                                              context: context,
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.vertical(
                                                          top: Radius.circular(
                                                              16))),
                                              backgroundColor:
                                                  AppColor.cBackGround,
                                              barrierColor:
                                                  AppColor.cBlackOpacity,
                                              isScrollControlled: true,
                                              builder: (context) {
                                                return EditDivisionWidget(
                                                  divisionName: data['title'],
                                                );
                                              },
                                            );
                                          } else if (value == "add branch") {
                                            showModalBottomSheet(
                                              context: context,
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.vertical(
                                                          top: Radius.circular(
                                                              16))),
                                              backgroundColor:
                                                  AppColor.cBackGround,
                                              barrierColor:
                                                  AppColor.cBlackOpacity,
                                              isScrollControlled: true,
                                              builder: (context) {
                                                return AddSubElementsWidget();
                                              },
                                            );
                                          } else {
                                            showDialog(
                                              context: context,
                                              builder: (context) {
                                                return AlertDialog(
                                                  contentPadding:
                                                      EdgeInsets.all(16),
                                                  insetPadding:
                                                      EdgeInsets.all(15),
                                                  shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12)),
                                                  content: DeleteDivisionWidget(
                                                    subTitle: data['title'] +
                                                        " division",
                                                    title: "Delete Division".trr,
                                                    confirmOnTap: () {
                                                      Get.back();
                                                    },
                                                  ),
                                                );
                                              },
                                            );
                                          }
                                        }),
                                    data['isSelected'].value == true
                                        ? Padding(
                                            padding: EdgeInsets.only(
                                                left: 6, top: 5, bottom: 5),
                                            child: fleetStructureDataWidget(
                                              context: context,
                                              title: data['branch']['title'],
                                              status: data['branch']['status']
                                                  .toString()
                                                  .trr,
                                              //dropDownIcn: data['isSelected'].value == true ? DefaultImages.blackArrowDownIcn : DefaultImages.arrowRightIcn,
                                              divisionImage:
                                                  DefaultImages.branchIcn,
                                              divisionTitle: data['branch']
                                                      ['division_title']
                                                  .toString()
                                                  .trr,
                                              division: '',
                                              onTap: () {
                                                data['branch']['isBranch']
                                                    .value = !data['branch']
                                                        ['isBranch']
                                                    .value;
                                              },
                                              bgColor: AppColor
                                                  .cLightOrangeContainer,
                                              textStyle: data['branch']
                                                          ['division_title'] ==
                                                      'Branch'
                                                  ? pSemiBold14
                                                  : pRegular13,
                                              tooltipItemList: [
                                                "Edit Branch",
                                                "Add Department",
                                                "Delete Branch",
                                              ],
                                              moreOnTap: (String value) {
                                                print(
                                                    'You Click on po up menu item $value');
                                                if (value == 'edit branch') {
                                                  showModalBottomSheet(
                                                    context: context,
                                                    shape: RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.vertical(
                                                                top: Radius
                                                                    .circular(
                                                                        16))),
                                                    backgroundColor:
                                                        AppColor.cBackGround,
                                                    barrierColor:
                                                        AppColor.cBlackOpacity,
                                                    isScrollControlled: true,
                                                    builder: (context) {
                                                      return EditBranchWidget();
                                                    },
                                                  );
                                                } else if (value ==
                                                    "add department") {
                                                  showModalBottomSheet(
                                                    context: context,
                                                    shape: RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.vertical(
                                                                top: Radius
                                                                    .circular(
                                                                        16))),
                                                    backgroundColor:
                                                        AppColor.cBackGround,
                                                    barrierColor:
                                                        AppColor.cBlackOpacity,
                                                    isScrollControlled: true,
                                                    builder: (context) {
                                                      return AddDepartmentWidget(
                                                          // isEdit: false,
                                                          // confirmTap: () {
                                                          //   Get.back();
                                                          // },
                                                          );
                                                    },
                                                  );
                                                } else {
                                                  showDialog(
                                                    context: context,
                                                    builder: (context) {
                                                      return AlertDialog(
                                                        contentPadding:
                                                            EdgeInsets.all(16),
                                                        insetPadding:
                                                            EdgeInsets.all(15),
                                                        shape: RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        12)),
                                                        content:
                                                            DeleteDivisionWidget(
                                                          subTitle: data[
                                                                      'branch']
                                                                  ['title'] +
                                                              " branch",
                                                          title: "Delete Branch"
                                                              .trr,
                                                          confirmOnTap: () {
                                                            Get.back();
                                                          },
                                                        ),
                                                      );
                                                    },
                                                  );
                                                }
                                              },
                                            ),
                                          )
                                        : SizedBox(),
                                    data['branch']['isBranch'].value == true
                                        ? Padding(
                                            padding: EdgeInsets.only(
                                                left: 19, top: 5, bottom: 5),
                                            child: fleetStructureDataWidget(
                                              context: context,
                                              title: data['department']
                                                  ['title'],
                                              status: data['department']
                                                      ['status']
                                                  .toString()
                                                  .trr,
                                              //dropDownIcn: data['isSelected'].value == true ? DefaultImages.blackArrowDownIcn : DefaultImages.arrowRightIcn,
                                              divisionImage:
                                                  DefaultImages.departmentIcn,
                                              divisionTitle: data['department']
                                                      ['division_title']
                                                  .toString()
                                                  .trr,
                                              division: '',
                                              onTap: () {
                                                data['department']
                                                        ['isDepartment']
                                                    .value = !data['department']
                                                        ['isDepartment']
                                                    .value;
                                              },
                                              bgColor: AppColor.lightBlueColor,
                                              textStyle: data['department']
                                                          ['division_title'] ==
                                                      'Branch'
                                                  ? pSemiBold14
                                                  : pRegular13,
                                              tooltipItemList: [
                                                "Edit Department",
                                                'Add Operation',
                                                "Delete Department"
                                              ],
                                              moreOnTap: (String value) {
                                                print(
                                                    'You Click on po up menu item $value');
                                                if (value ==
                                                    'edit department') {
                                                  showModalBottomSheet(
                                                    context: context,
                                                    shape: RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.vertical(
                                                                top: Radius
                                                                    .circular(
                                                                        16))),
                                                    backgroundColor:
                                                        AppColor.cBackGround,
                                                    barrierColor:
                                                        AppColor.cBlackOpacity,
                                                    isScrollControlled: true,
                                                    builder: (context) {
                                                      return AddDepartmentWidget(
                                                          // isEdit: true,
                                                          // name: data['department']
                                                          //     ['title'],
                                                          // status:
                                                          //     data['department']
                                                          //             ['status']
                                                          //         .toString()
                                                          //         .trr,
                                                          );
                                                    },
                                                  );
                                                } else if (value ==
                                                    "add operation") {
                                                  showModalBottomSheet(
                                                    context: context,
                                                    shape: RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.vertical(
                                                                top: Radius
                                                                    .circular(
                                                                        16))),
                                                    backgroundColor:
                                                        AppColor.cBackGround,
                                                    barrierColor:
                                                        AppColor.cBlackOpacity,
                                                    isScrollControlled: true,
                                                    builder: (context) {
                                                      return AddOperationWidget(
                                                          // isEdit: false,
                                                          // confirmTap: () {
                                                          //   Get.back();
                                                          // },
                                                          );
                                                    },
                                                  );
                                                } else {
                                                  showDialog(
                                                    context: context,
                                                    builder: (context) {
                                                      return AlertDialog(
                                                        contentPadding:
                                                            EdgeInsets.all(16),
                                                        insetPadding:
                                                            EdgeInsets.all(15),
                                                        shape: RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        12)),
                                                        content:
                                                            DeleteDivisionWidget(
                                                          subTitle:
                                                              data['department']
                                                                      [
                                                                      'title'] +
                                                                  " department",
                                                          title:
                                                              "Delete Department"
                                                                  .trr,
                                                          confirmOnTap: () {
                                                            Get.back();
                                                          },
                                                        ),
                                                      );
                                                    },
                                                  );
                                                }
                                              },
                                            ),
                                          )
                                        : SizedBox(),
                                    data['department']['isDepartment'].value ==
                                            true
                                        ? ListView.builder(
                                            physics:
                                                NeverScrollableScrollPhysics(),
                                            shrinkWrap: true,
                                            itemCount: data['department']
                                                    ['operations']
                                                .length,
                                            itemBuilder: (context, i) {
                                              var operationData =
                                                  data['department']
                                                      ['operations'][i];
                                              return Padding(
                                                padding: EdgeInsets.only(
                                                    left: 50,
                                                    top: 5,
                                                    bottom: 5),
                                                child: fleetStructureDataWidget(
                                                  context: context,
                                                  title: operationData['title'],
                                                  status:
                                                      operationData['status']
                                                          .toString()
                                                          .trr,
                                                  //isDropDown: false,
                                                  divisionImage: DefaultImages
                                                      .operationIcn,
                                                  divisionTitle: operationData[
                                                          'division_title']
                                                      .toString()
                                                      .trr,
                                                  division: '',
                                                  bgColor: AppColor.cBackGround,
                                                  textStyle: operationData[
                                                              'division_title'] ==
                                                          'Branch'
                                                      ? pSemiBold14
                                                      : pRegular13,
                                                  tooltipItemList: [
                                                    "Edit Operation",
                                                    "Delete Operation"
                                                  ],
                                                  moreOnTap: (value) {
                                                    print(
                                                        'You Click on po up menu item $value');
                                                    if (value ==
                                                        'edit operation') {
                                                      showModalBottomSheet(
                                                        context: context,
                                                        shape: RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius.vertical(
                                                                    top: Radius
                                                                        .circular(
                                                                            16))),
                                                        backgroundColor:
                                                            AppColor
                                                                .cBackGround,
                                                        barrierColor: AppColor
                                                            .cBlackOpacity,
                                                        isScrollControlled:
                                                            true,
                                                        builder: (context) {
                                                          return AddOperationWidget(
                                                              // isEdit: true,
                                                              // name: operationData[
                                                              //     'title'],
                                                              // status:
                                                              //     operationData[
                                                              //         'status'],
                                                              );
                                                        },
                                                      );
                                                    } else {
                                                      showDialog(
                                                        context: context,
                                                        builder: (context) {
                                                          return AlertDialog(
                                                            contentPadding:
                                                                EdgeInsets.all(
                                                                    16),
                                                            insetPadding:
                                                                EdgeInsets.all(
                                                                    15),
                                                            shape: RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12)),
                                                            content:
                                                                DeleteDivisionWidget(
                                                              subTitle: operationData[
                                                                      'title'] +
                                                                  " operation",
                                                              title:
                                                                  "Delete Operation"
                                                                      .trr,
                                                              confirmOnTap: () {
                                                                Get.back();
                                                              },
                                                            ),
                                                          );
                                                        },
                                                      );
                                                    }
                                                  },
                                                ),
                                              );
                                            },
                                          )
                                        : SizedBox()
                                  ],
                                ),
                              ),
                            );
                          });
                        },
                      ),
                    ),
            ],
          );
        }),
      ),
    );
  }
}
