import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/auth/auth_controller.dart';
import 'package:waie_app/core/controller/auth/forgot_password_controller.dart';
import 'package:waie_app/core/controller/menu_controller/profile_controller/address_load_controller.dart';
import 'package:waie_app/models/load_profile_detail.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/view/widget/loading_widget.dart';
import 'package:mime/mime.dart';

import '../../../../models/profile.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/text_style.dart';
import '../../../../view/screen/auth/login_with_email_screen.dart';
import '../../../../view/screen/dashboard_manager/dashboard_manager.dart';
import '../../../../view/widget/common_button.dart';
import '../../../../view/widget/common_snak_bar_widget.dart';
import '../../../../view/widget/common_space_divider_widget.dart';

class NewProfileController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  LoadProfileDetail? loadProfileDetail;
  AuthController authController = Get.put(AuthController());
  ForgotPasswordController forgotPasswordController =
      Get.put(ForgotPasswordController());
  TextEditingController englishCompanyController = TextEditingController();
  TextEditingController arabicCompanyController = TextEditingController();
  TextEditingController contactPersonController = TextEditingController();
  TextEditingController designationController = TextEditingController();
  TextEditingController commercialController = TextEditingController();
  TextEditingController vatController = TextEditingController();
  TextEditingController salesmanController = TextEditingController();
  TextEditingController companyTelController = TextEditingController();
  TextEditingController countryController = TextEditingController();
  TextEditingController regionController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController districtController = TextEditingController();
  TextEditingController streetController = TextEditingController();
  TextEditingController buildingNoController = TextEditingController();
  TextEditingController postalCodeController = TextEditingController();
  TextEditingController poBoxController = TextEditingController();
  TextEditingController firstnameController = TextEditingController();
  TextEditingController middlenameController = TextEditingController();
  TextEditingController lastnameController = TextEditingController();
  TextEditingController phoneNoController = TextEditingController();
  TextEditingController companyFaxController = TextEditingController();
  TextEditingController vatNoController = TextEditingController();
  TextEditingController serviceKnownController = TextEditingController();
  TextEditingController langController = TextEditingController();

  RxString vatDocument = ''.obs;
  RxString idDocument = ''.obs;
  List languages = ['English', 'عربى'];
  RxString selectedCountry = ''.obs;
  RxString selectedLanguage = ''.obs;
  RxString selectedRegion = ''.obs;
  RxString selectedCity = ''.obs;
  RxString selectedDistrict = ''.obs;
  List countryList = ['Saudi Arabia'];
  List wList = ['Aldrees Website'];
  RxString selectedSalesmen = ''.obs;
  RxString selectedWaie = ''.obs;

  RxString registrationType = "".obs;
  RxString registrationDate = "".obs;
  RxString accountType = "".obs;
  RxString companyType = "".obs;
  RxString expro = "".obs;
  RxString customerID = "".obs;
  RxString emailID = "".obs;
  RxString regType = "".obs;
  RxString isVerified = "".obs;
  RxString isVat = "".obs;
  RxString gps = "".obs;

  getstoredProfileDetails() async {
    var custData = custsData.read('custData');
    print("NewProfileController custData>>>>>>> $custData");
    registrationType.value =
        custData["REGTYPE"] == "C" ? "Company" : "Individual";
    registrationDate.value = custData["REGACTDATE"];
    accountType.value = custData["ACCTTYPE"] == "C"
        ? "Prepaid"
        : "Postpaid"; //c prepaid D postpaid
    companyType.value = custData["COMPTYPE"] == "P"
        ? "Private"
        : "Government"; //p private g government
    expro.value = custData["EXPRO"];
    customerID.value = custData["CUSTID"];
    emailID.value = custData["EMAILID"];
    firstnameController.text = custData['FIRSTNAME'];
    middlenameController.text = custData['MIDNAME'];
    lastnameController.text = custData['LASTNAME'];
    phoneNoController.text = custData['MOBILENO'];
    countryController.text =
        custData['COUNTRY'] == "SA" ? "Saudi Arabia" : "Saudi Arabia";
    streetController.text = custData['STREET'];
    buildingNoController.text = custData['BUILDING_NO'];
    poBoxController.text = custData['POBOX'];
    postalCodeController.text = custData['ZIPCODE'];
    englishCompanyController.text = custData['COMPANYNAME'];
    arabicCompanyController.text = custData['COMPANYNAMEAR'];
    contactPersonController.text = custData['CONTACTPERSON'];
    designationController.text = custData['DESIGNATION'];
    commercialController.text = custData['CRNO'];
    vatController.text = custData['VAT_NO'];
    //salesmanController.text = custData['SALESMAN'];
    companyFaxController.text = custData['COMPANYFAX'];
    companyTelController.text = custData['COMPANYTEL'];
    selectedCountry.value = custData['COUNTRY'];
    selectedRegion.value = custData['REGION_CODE'];
    selectedCity.value =
        custData['CITY_CODE'] == "" ? "" : custData['CITY_CODE'];
    selectedDistrict.value =
        custData['DISTRICT_CODE'] == "" ? "" : custData['DISTRICT_CODE'];
    selectedSalesmen.value =
        custData['SALESMAN'] == "" ? "00000" : custData['SALESMAN'];
    selectedLanguage.value =
        custData['LANG']; //custData['LANG'] == "A" ? 'عربى' : 'English';
    regType.value = custData['REGTYPE'];
    isVerified.value = custData['IS_VERIFIED'];
    selectedWaie.value = custData['SERVICEKNOWN'];
    gps.value = custData['GPS'];

    if (custData['IS_VERIFIED'] == "Y" || custData['IS_VERIFIED'] == "") {
      isVat.value = "true";
    }
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();

    selectedRegion.refresh();
    selectedCity.refresh();
    selectedDistrict.refresh();
    getstoredProfileDetails();
  }

  updateProfile(invVatdocx, invIddocx) async {
    Loader.showLoader();
    var custData = custsData.read('custData');
    var client = http.Client();
    print("========================================Loader======");
    try {
      var vatDocx = invVatdocx.readStream;
      var idDocx = invIddocx.readStream;

      print("vatDocx $vatDocx");
      print("vatDocx ${invVatdocx.size}");
      print("vatDocx ${invVatdocx.name}");
      print("idDocx $idDocx");
      print("vatDocx ${invIddocx.size}");
      print("vatDocx ${invIddocx.name}");

      var request = http.MultipartRequest(
          'POST',
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.updateProfileDetails));

      request.files.add(http.MultipartFile('vatDocx', vatDocx, invVatdocx.size,
          filename: invVatdocx.name,
          contentType: MediaType('application', 'pdf')));
      vatDocx.cast();

      request.files.add(http.MultipartFile('idDocx', idDocx, invIddocx.size,
          filename: invIddocx.name,
          contentType: MediaType('application', 'pdf')));
      idDocx.cast();

      request.fields['ACTION'] = "cdetail";
      request.fields['CUSTID'] = custData['CUSTID'] ?? "";
      request.fields['EMAILID'] = custData['EMAILID'] ?? "";
      request.fields['COMPANYNAME'] = englishCompanyController.text ?? "";
      request.fields['COMPANYNAMEAR'] = arabicCompanyController.text ?? "";
      request.fields['CONTACTPERSON'] = contactPersonController.text ?? "";
      request.fields['DESIGNATION'] = designationController.text ?? "";
      request.fields['CRNO'] = commercialController.text ?? "";
      request.fields['VAT_NO'] = vatNoController.text ?? "";
      request.fields['SALESMAN__CODE'] = salesmanController.text ?? "";
      request.fields['COMPANYTEL'] =
          companyTelController.text != "" ? companyTelController.text : "";
      request.fields['COMPANYFAX'] = companyFaxController.text ?? "";
      request.fields['COUNTRY_CODE'] = countryController.text == "Saudi Arabia"
          ? "SA"
          : countryController.text;
      request.fields['REGION_CODE'] = regionController.text ?? "";
      request.fields['CITY_CODE'] = cityController.text ?? "";
      request.fields['DISTRICT_CODE'] = districtController.text ?? "";
      request.fields['STREET'] = streetController.text ?? "";
      request.fields['BUILDING_NO'] = buildingNoController.text ?? "";
      request.fields['MOBILENO'] = phoneNoController.text ?? "";
      request.fields['LANG'] = selectedLanguage.value.toString();
      request.fields['SERVICEKNOWN_CODE'] = serviceKnownController.text ?? "";
      request.fields['POSTALCODE'] = postalCodeController.text ?? "";
      request.fields['POBOX'] = poBoxController.text ?? "";
      request.fields['FIRSTNAME'] = firstnameController.text ?? "";
      request.fields['MIDNAME'] = middlenameController.text ?? "";
      request.fields['LASTNAME'] = lastnameController.text ?? "";
      request.fields['HOUSE_NO'] = custData['HOUSE_NO'] ?? "";
      request.fields['ID_TYPE'] = custData['ID_TYPE'] ?? "";
      request.fields['ID_NUMBER'] = custData['ID_NUMBER'] ?? "";
      request.fields['GPS'] = gps.value == "" ? custData['GPS'] : gps.value;
      request.fields['FUEL91'] = custData['FUEL91'] ?? "";
      request.fields['FUEL95'] = custData['FUEL95'] ?? "";
      request.fields['DIESEL'] = custData['DIESEL'] ?? "";
      request.fields['OTHERS'] = custData['OTHERS'] ?? "";
      var res = await request.send();

      return res.stream.bytesToString().asStream().listen((event) {
        var parsedJson = json.decode(event);
        print(
            "===============================================================");
        print(parsedJson);
        print(parsedJson['action']);
        print(parsedJson['message']);
        print(
            "===============================================================");
        if (parsedJson['action'] == "EXCEPTION") {
          Loader.hideLoader();
          //Navigator.of(Get.context!).pop();
          // englishCompanyController.clear();
          // arabicCompanyController.clear();
          // contactPersonController.clear();
          // designationController.clear();
          // commercialController.clear();
          // vatController.clear();
          // salesmanController.clear();
          // companyTelController.clear();
          // countryController.clear();
          // regionController.clear();
          // cityController.clear();
          // districtController.clear();
          // streetController.clear();
          // buildingNoController.clear();
          // postalCodeController.clear();
          // poBoxController.clear();
          // firstnameController.clear();
          // middlenameController.clear();
          // lastnameController.clear();
          // phoneNoController.clear();
          // companyFaxController.clear();
          // vatNoController.clear();
          // vatDocument.value = '';
          // idDocument.value = '';
          // custsData.remove('custData');
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      parsedJson['message'].toString(),
                      style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        // await Get.to(
                        //     () => DashBoardManagerScreen(
                        //           currantIndex: 0,
                        //         ),
                        //     preventDuplicates: false);

                        print("username${parsedJson['password']}");
                        print("password${parsedJson['username']}");
                        forgotPasswordController.verificationCode.value =
                            parsedJson['password'];
                        forgotPasswordController.userid.value =
                            parsedJson['username'];
                        await forgotPasswordController.login();
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
      });
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  updateProfileNoVat() async {
    print("UPDATE PLEASE >>> ${salesmanController.text}");
    Loader.showLoader();
    var custData = custsData.read('custData');
    var client = http.Client();
    print(countryController.text);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.updateProfileDetails),
          body: {
            'ACTION': "personal",
            'CUSTID': custData['CUSTID'] ?? "",
            'EMAILID': custData['EMAILID'] ?? "",
            'COMPANYNAME': englishCompanyController.text ?? "",
            'COMPANYNAMEAR': arabicCompanyController.text ?? "",
            'CONTACTPERSON': contactPersonController.text ?? "",
            'DESIGNATION': designationController.text ?? "",
            'CRNO': commercialController.text ?? "",
            'VAT_NO': vatController.text ?? "",
            'SALESMAN__CODE': salesmanController.text == ""
                ? custData['SALESMAN']
                : salesmanController.text,
            'COMPANYTEL': companyTelController.text != ""
                ? companyTelController.text
                : "",
            'COMPANYFAX': companyFaxController.text ?? "",
            'COUNTRY_CODE': countryController.text == "Saudi Arabia"
                ? "SA"
                : countryController.text,
            'REGION_CODE': regionController.text == ""
                ? selectedRegion.value
                : regionController.text,
            'CITY_CODE': cityController.text == ""
                ? selectedCity.value
                : cityController.text,
            'DISTRICT_CODE': districtController.text == ""
                ? selectedDistrict.value
                : districtController.text,
            'STREET': streetController.text ?? "",
            'BUILDING_NO': buildingNoController.text ?? "",
            'MOBILENO': phoneNoController.text ?? "",
            'LANG': langController.text == ""
                ? selectedLanguage.value.toString()
                : langController.text,
            'SERVICEKNOWN_CODE': serviceKnownController.text == ""
                ? selectedWaie.value == ""
                    ? "A"
                    : selectedWaie.value
                : serviceKnownController.text,
            'POSTALCODE': postalCodeController.text ?? "",
            'POBOX': poBoxController.text ?? "",
            'FIRSTNAME': firstnameController.text ?? "",
            'MIDNAME': middlenameController.text ?? "",
            'LASTNAME': lastnameController.text ?? "",
            'HOUSE_NO': custData['HOUSE_NO'] ?? "",
            'ID_TYPE': custData['ID_TYPE'] ?? "",
            'ID_NUMBER': custData['ID_NUMBER'] ?? "",
            'GPS': gps.value == "" ? custData['GPS'] : gps.value,
            'FUEL91': custData['FUEL91'] ?? "",
            'FUEL95': custData['FUEL95'] ?? "",
            'DIESEL': custData['DIESEL'] ?? "",
            'OTHERS': custData['OTHERS'] ?? "",
            'IsARInterface': Constants.IsAr_App,
          });
      print("++++++++++++++++++++");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");
      print("+++++++++++++++++++");
      if (response.statusCode == 200) {
        // Navigator.of(Get.context!).pop();
        Loader.hideLoader();
        // englishCompanyController.clear();
        // arabicCompanyController.clear();
        // contactPersonController.clear();
        // designationController.clear();
        // commercialController.clear();
        // vatController.clear();
        // salesmanController.clear();
        // companyTelController.clear();
        // countryController.clear();
        // regionController.clear();
        // cityController.clear();
        // districtController.clear();
        // streetController.clear();
        // buildingNoController.clear();
        // postalCodeController.clear();
        // poBoxController.clear();
        // firstnameController.clear();
        // middlenameController.clear();
        // lastnameController.clear();
        // phoneNoController.clear();
        // companyFaxController.clear();
        // vatNoController.clear();
        // custsData.remove('custData');
        if (jsonDecode(response.body)["action"] == "EXCEPTION") {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        Get.back();
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        //Get.back();

                        print(
                            "username${jsonDecode(response.body)['password']}");
                        print(
                            "password${jsonDecode(response.body)['username']}");
                        forgotPasswordController.verificationCode.value =
                            jsonDecode(response.body)['password'];
                        forgotPasswordController.userid.value =
                            jsonDecode(response.body)['username'];
                        await forgotPasswordController.login();
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
          // await Get.to(
          //     () => DashBoardManagerScreen(
          //         currantIndex: Constants.TopUpBtn == 'Y' ? 4 : 3),
          //     preventDuplicates: false);
        }
      } else {
        Loader.hideLoader();
        print('Failed');
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Failed to Update",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
