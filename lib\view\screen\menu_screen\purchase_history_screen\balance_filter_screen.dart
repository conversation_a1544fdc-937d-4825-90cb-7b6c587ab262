// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/purchase_history_controller/purchase_history_filter_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class BalanceHistoryFilterScreen extends StatelessWidget {
  BalanceHistoryFilterScreen({super.key});

  PurchaseHistoryFilterController orderFilterController = Get.put(PurchaseHistoryFilterController());

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text("More filters".trr, style: pBold24.copyWith(fontSize: 22)),
                        horizontalSpace(16),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 4, horizontal: 12),
                          decoration: BoxDecoration(color: AppColor.cLightRed, borderRadius: BorderRadius.circular(12)),
                          child: Text("Clear all".trr, style: pSemiBold12.copyWith(color: AppColor.cRedText)),
                        )
                      ],
                    ),
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(shape: BoxShape.circle, color: AppColor.cLightGrey),
                        child: assetSvdImageWidget(image: DefaultImages.cancelIcn),
                      ),
                    )
                  ],
                ),
              ),
              horizontalDivider(),
              ListView.separated(
                itemCount: orderFilterController.balanceFilterValueList.length,
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  var data = orderFilterController.balanceFilterValueList[index];
                  return Obx(() {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextButton(
                          onPressed: () {
                            for (int i = 0; i < orderFilterController.filterValueList.length; i++) {
                              if (index == i) {
                                data['isExpand'].value = !data['isExpand'].value;
                                // data['isExpand'].value = true;
                              } else {
                                orderFilterController.filterValueList[i]['isExpand'].value = false;
                              }
                              orderFilterController.filterValueList.refresh();
                            }
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                data['title'].toString().trr,
                                style: pSemiBold17.copyWith(color: data['isExpand'].value == true ? AppColor.cText : AppColor.cFont),
                              ),
                              assetSvdImageWidget(
                                  image: data['isExpand'].value == true ? DefaultImages.dropUpIcn : DefaultImages.blueArrowDownIcn, colorFilter: ColorFilter.mode(AppColor.cText, BlendMode.srcIn)),
                            ],
                          ),
                        ),
                        Obx(() {
                          print("${data["subtitle"].length}");
                          return data['isExpand'].value == true
                              ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: data["subtitle"].map<Widget>((name) {
                              print("name $name");
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 10, left: 24),
                                child: GestureDetector(
                                  onTap: () {},
                                  child: Text(
                                    name.toString().trr,
                                    style: pMedium12,
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                              );
                            }).toList(),
                          )
                              : SizedBox();
                        })
                      ],
                    );
                  });
                },
                separatorBuilder: (context, index) => horizontalDivider(),
              ),
            ],
          ),
        ),
        Container(
          color: AppColor.cLightGrey,
          padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: CommonButton(
                  title: 'Cancel'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  textColor: AppColor.cText,
                  btnColor: AppColor.cBackGround,
                ),
              ),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Apply filters'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  textColor: AppColor.cWhiteFont,
                  btnColor: AppColor.themeOrangeColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
