// ignore_for_file: prefer_const_constructors

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:waie_app/models/gas_station.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/refundable_service.dart';
import 'dart:math';

import 'package:waie_app/utils/constants.dart';

class GasStationController extends GetxController {
  RxBool isGasStations = true.obs;
  RxBool isSalesOffice = false.obs;
  RxBool isInstallationCenters = false.obs;
  RxBool isMap = true.obs;
  RxBool isList = false.obs;
  RxBool isDefaultMap = true.obs;
  List<LatLng> latLongList = [
    LatLng(21.44484793949768, 53.295109691390245),
    LatLng(20.582120409829233, 44.96018559757371),
    LatLng(23.899725409740984, 54.046233681296975),
    LatLng(22.302502578679334, 47.422575301577865),
    LatLng(25.169169066706516, 38.833075072755804),
    LatLng(23.359337577784572, 45.01153009917833),
    LatLng(20.950188013577115, 49.39292756943918),
  ];
  List<LatLng> salesOfficeList = [
    LatLng(21.44484793949768, 53.295109691390245),
    LatLng(20.582120409829233, 44.96018559757371),
    LatLng(23.899725409740984, 54.046233681296975),
    LatLng(22.302502578679334, 47.422575301577865),
    LatLng(25.169169066706516, 38.833075072755804),
    LatLng(23.359337577784572, 45.01153009917833),
    LatLng(20.950188013577115, 49.39292756943918),
  ];
  List<LatLng> installationList = [
    LatLng(21.44484793949768, 53.295109691390245),
    LatLng(20.582120409829233, 44.96018559757371),
    LatLng(23.899725409740984, 54.046233681296975),
    LatLng(22.302502578679334, 47.422575301577865),
    LatLng(25.169169066706516, 38.833075072755804),
    LatLng(23.359337577784572, 45.01153009917833),
    LatLng(20.950188013577115, 49.39292756943918),
  ];
  List listData = [
    {
      'data': "Riyadh",
      'list': [
        {
          "title": "Al Reef",
          'subTitle': "RAFB7699، 7699 Riyadh 13314, Saudi Arabia",
        },
        {
          "title": "Asment Exit - 18",
          'subTitle': "REFA7322, 7322 Mahail, 4063",
        },
      ]
    },
    {
      'data': "Al Ahsa",
      'list': [
        {
          "title": "Al Makhaita 2",
          'subTitle':
              "Al Qadisiyah, Al Mubarraz 36422, Saudi Arabia ا,،,لملك سعود",
        },
      ]
    },
    {
      'data': "Duraidah",
      'list': [
        {
          "title": "Al Faizy",
          'subTitle':
              "QBWE7235، 7235 عمر بن الخطاب، 2639، حي النهضة, Buraydah 52388, Saudi Arabia",
        },
      ]
    },
  ];
  RxList itemList = [].obs;

  var gasStationList = <GasStationModel>[].obs;

  List<LatLng> gasStats = [];

  // List<GasStationModel> allGasStations = [];
  // List<LatLng> allGasStats = [];

  // Future<void> fetchAllGasStations() async {
  //   var client = http.Client();
  //   try {
  //     print("Fetching all gas stations...");
  //     var response = await client.post(Uri.parse(
  //         ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnLocation));
  //     List result = jsonDecode(response.body);

  //     for (var item in result) {
  //       GasStationModel gas =
  //           GasStationModel.fromJson(item as Map<String, dynamic>);
  //       if (gas.latitude != "" && gas.longitude != "") {
  //         allGasStations.add(gas);
  //         allGasStats.add(
  //             LatLng(double.parse(gas.latitude), double.parse(gas.longitude)));
  //       }
  //     }
  //     print("Fetched all gas stations: ${allGasStations.length}");

  //     // Initially filter for 200km radius
  //     filterGasStationsWithinRadius();
  //   } catch (e) {
  //     print("Error fetching gas stations: ${e.toString()}");
  //   } finally {
  //     client.close();
  //   }
  // }

  // void filterGasStationsWithinRadius() {
  //   gasStationList.clear();
  //   gasStats.clear();

  //   for (var gas in allGasStations) {
  //     double stationLatitude = double.parse(gas.latitude);
  //     double stationLongitude = double.parse(gas.longitude);
  //     double distance = calculateDistance(
  //         userLatitude, userLongitude, stationLatitude, stationLongitude);

  //     if (distance <= radiusInKm) {
  //       gasStationList.add(gas);
  //       gasStats.add(LatLng(stationLatitude, stationLongitude));
  //     }
  //   }

  //   print("Filtered gas stations within 200km: ${gasStationList.length}");
  // }

  // void showAllGasStations() {
  //   gasStationList.value = allGasStations;
  //   gasStats = List.from(allGasStats);
  //   print("Showing all gas stations: ${gasStationList.length}");
  // }

  getAllGasStations() async {
    List<GasStationModel> gasses = [];
    gasStats.clear();
    gasStationList.clear();

    var client = http.Client();
    try {
      print("getAllGasStations ****************");
      var response = await client.post(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnLocation));
      print(
          "jsonDecode(response.body) result >>>>> ${jsonDecode(response.body)}");
      List result = jsonDecode(response.body);

      //log("api data $result");
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        GasStationModel gas =
            GasStationModel.fromJson(result[i] as Map<String, dynamic>);
        if (gas.latitude != "" && gas.longitude != "") {
          gasses.add(gas);
          gasStats.add(
              LatLng(double.parse(gas.latitude), double.parse(gas.longitude)));
        }
      }

      gasStationList.value = gasses;

      print("gasStats >>>>> ${jsonDecode(jsonEncode(gasStats))}");
      print("===============================================================");
      print(
          "loadgasStationList >>>>> ${jsonDecode(jsonEncode(gasStationList))}");
      //print("gasStats >>>>> $gasStats");
      print("===============================================================");

      return gasStationList;
    } catch (e) {
      print("getAllGasStations +++++++++++++++++++");
      //log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('GasStationController');
    if (gasStationList.isEmpty) {
      print("sulod");
      //await getGasStations200kmRadius();
      // await fetchAllGasStations();
    }
    //Navigator.of(Get.context!).pop();
  }

  double? userLatitude;
  double? userLongitude;
  double radiusInKm = 200.0;

  List<GasStationModel> gasStationListFiltered = [];

  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double R = 6371;
    final double dLat = _degToRad(lat2 - lat1);
    final double dLon = _degToRad(lon2 - lon1);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degToRad(lat1)) *
            cos(_degToRad(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return R * c;
  }

  double _degToRad(double deg) => deg * (pi / 180);

  var allStationsList = <GasStationModel>[].obs;

  getGasStations200kmRadius(
      double userLatitude, double userLongitude, double radiusInKm) async {
    List<GasStationModel> gasses = [];
    List<GasStationModel> allStations = [];
    print('yahan aya 200km');
    var client = http.Client();
    gasStats.clear();
    gasStationList.clear();
    allStationsList.clear();
    print("getGasStations200kmRadius userLatitude ${userLatitude}");
    print("getGasStations200kmRadius userLatitude ${userLongitude}");

    try {
      var response = await client.post(
          Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnLocation,
          ),
          // body: {
          //   "IsAR": Constants.IsAr_App,
          // }
          );
      List result = jsonDecode(response.body);
      print('resultofstations $result');
      for (int i = 0; i < result.length; i++) {
        GasStationModel gas =
            GasStationModel.fromJson(result[i] as Map<String, dynamic>);

        if (gas.latitude != "" && gas.longitude != "") {
          double stationLatitude = double.parse(gas.latitude);
          double stationLongitude = double.parse(gas.longitude);

          allStations.add(gas);

          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);

          if (distance <= radiusInKm) {
            gasses.add(gas);

            gasStats.add(LatLng(stationLatitude, stationLongitude));
          }
        }
      }

      gasStationList.value = gasses;

      allStationsList.value = allStations;

      print("Filtered gasStats >>>>> ${jsonEncode(gasStats)}");

      return gasStationList;
    } catch (e) {
      print("getAllGasStations Error: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }
}
