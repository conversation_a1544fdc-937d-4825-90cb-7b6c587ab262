// ignore_for_file: must_be_immutable, prefer_const_constructors_in_immutables, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/menu_controller/help_center_controller/help_center_controller.dart';

class GeneralQuestionScreen extends StatelessWidget {
  final HelpCenterController helpCenterController;

  GeneralQuestionScreen({Key? key, required this.helpCenterController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "General Questions".trr,
          style: pSemiBold17,
        ),
        verticalSpace(24),
        Column(
          children:helpCenterController.generalQusList.map((data) {
            return ExpansionTile(
              title: ListTile(
                title: Text(data['questions'].toString().trr, style: pSemiBold17,),
              ),
              childrenPadding: EdgeInsets.symmetric(horizontal: 16,vertical: 10),
              tilePadding: EdgeInsets.zero,
              iconColor: AppColor.cText,
              collapsedIconColor: AppColor.cText,
              shape: InputBorder.none,
              collapsedShape: InputBorder.none,
              children: [
                Text(
                 data['answer'].toString().trr,
                  style: pRegular17,
                ),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }
}
