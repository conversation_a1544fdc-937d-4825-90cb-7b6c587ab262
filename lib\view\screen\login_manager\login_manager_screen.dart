// ignore_for_file: must_be_immutable, prefer_const_constructors_in_immutables, prefer_const_constructors

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import '../dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/core/controller/login_manager_controller/login_manager_controller.dart';

class LoginManagerScreen extends StatefulWidget {
  LoginManagerScreen({super.key});

  @override
  State<LoginManagerScreen> createState() => _LoginManagerScreenState();
}

class _LoginManagerScreenState extends State<LoginManagerScreen> {
  LoginManagerController loginManagerController =
      Get.put(LoginManagerController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    loginManagerController.currantIndex.value = 2;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: loginManagerController
                .naviBarItemList[loginManagerController.currantIndex.value]
            ['screen'],
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          landscapeLayout: BottomNavigationBarLandscapeLayout.centered,
          selectedItemColor: AppColor.cOrangeFont,
          unselectedItemColor: AppColor.cDarkGreyFont,
          currentIndex: loginManagerController.currantIndex.value,
          onTap: (index) async {
            loginManagerController.currantIndex.value = index;
            print(index);
          },
          selectedLabelStyle:
              pBold12.copyWith(color: AppColor.cOrangeFont, fontSize: 11),
          unselectedLabelStyle:
              pRegular10.copyWith(color: AppColor.cDarkGreyFont, fontSize: 11),
          items: List.generate(loginManagerController.naviBarItemList.length,
              (index) {
            var data = loginManagerController.naviBarItemList[index];
            return BottomNavigationBarItem(
              icon: bottomIconWidget(
                  image: data['icon'],
                  title: data['title'].toString().trr,
                  color: loginManagerController.currantIndex.value == index
                      ? AppColor.cOrangeFont
                      : AppColor.cDarkGreyFont,
                  imageColor: loginManagerController.currantIndex.value == index
                      ? AppColor.cOrangeFont
                      : AppColor.cDarkGreyFont),
              label: data['title'].toString().trr,
            );
          }),
        ),
      );
    });
  }
}
