// ignore_for_file: avoid_print
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../widget/loading_widget.dart';

class PetroleumServicesWebviewScreen extends StatefulWidget {
  final String url;
  const PetroleumServicesWebviewScreen({
    super.key,
    required this.url,
  });

  @override
  State<PetroleumServicesWebviewScreen> createState() =>
      _PetroleumServicesWebviewScreenState();
}

class _PetroleumServicesWebviewScreenState
    extends State<PetroleumServicesWebviewScreen> {
  //final WebViewController _controler;
  late WebViewController _controler;
  final double _height = 1;
  bool isLoading = true;

  @override
  void initState() {
    _controler = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse('https://www.aldrees.com/petrol'))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (progress) {
            print("progress---> $progress");
            if (progress == 100) {
              setState(() {
                isLoading = false;
              });
            }
          },
        ),
      );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColor.themeBlueColor,
        title: Text(
          "Petroleum Services".trr,
          style: pBold20.copyWith(color: AppColor.cWhiteFont),
          textAlign: TextAlign.center,
        ),
      ),
      body: isLoading
          ? const Center(
              child: LoadingWidget(),
            )
          : WebViewWidget(
              controller: _controler,
            ),
      // child: WebViewPlus(
      //   initialUrl: widget.url,
      //   onWebViewCreated: (controller) {
      //     _controller = controller;
      //     controller.loadUrl(widget.url);
      //   },
      //   onPageFinished: (url) {
      //     _controller.getHeight().then((double height) {
      //       setState(() {
      //         _height = height;
      //       });
      //     });
      //   },
      //   onProgress: (progress) {
      //     print("progress---> $progress");
      //     if (progress == 100) {
      //       setState(() {
      //         isLoading = false;
      //       });
      //     }
      //   },
      //   javascriptMode: JavascriptMode.unrestricted,
      // ),
    );
  }
}
