import 'dart:convert';

import 'package:get/get_rx/src/rx_types/rx_types.dart';

List<OrgLevel> orgLevelFromJson(String str) =>
    List<OrgLevel>.from(json.decode(str).map((x) => OrgLevel.fromJson(x)));

String orgLevelToJson(List<OrgLevel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OrgLevel {
  RxBool value;
  RxBool isSelected = false.obs;
  String divCode;
  String divName;
  List<Orglevel> orglevel;

  OrgLevel({
    required this.value,
    required this.isSelected,
    required this.divCode,
    required this.divName,
    required this.orglevel,
  });

  factory OrgLevel.fromJson(Map<String, dynamic> json) => OrgLevel(
        value: json["value"] ?? false.obs,
        isSelected: json["isSelected"] ?? false.obs,
        divCode: json["DivCode"],
        divName: json["DivName"],
        orglevel: List<Orglevel>.from(
            json["ORGLEVEL"].map((x) => Orglevel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "value": value,
        "isSelected": isSelected,
        "DivCode": divCode,
        "DivName": divName,
        "ORGLEVEL": List<dynamic>.from(orglevel.map((x) => x.toJson())),
      };
}

class Orglevel {
  RxBool value;
  String divCode;
  String brCode;
  String brName;

  Orglevel({
    required this.value,
    required this.divCode,
    required this.brCode,
    required this.brName,
  });

  factory Orglevel.fromJson(Map<String, dynamic> json) => Orglevel(
        value: json["value"] ?? false.obs,
        divCode: json["DivCode"],
        brCode: json["BrCode"],
        brName: json["BrName"],
      );

  Map<String, dynamic> toJson() => {
        "value": value,
        "DivCode": divCode,
        "BrCode": brCode,
        "BrName": brName,
      };
}
