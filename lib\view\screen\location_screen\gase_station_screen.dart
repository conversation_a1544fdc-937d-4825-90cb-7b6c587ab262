// ignore_for_file: prefer_const_constructors

import 'dart:developer';
import 'dart:io';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/models/gas_station.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../../../core/controller/location_controller/gas_station_controller.dart';
import '../../../core/controller/location_controller/location_controller.dart';
import '../menu_screen/user_management_screen/user_management_screen.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import '../../../utils/colors.dart';
import '../../../utils/images.dart';
import 'package:get/get.dart';
import 'location_screen.dart';
import 'dart:ui' as ui;
import 'package:geolocator/geolocator.dart';
import 'package:flutter/material.dart';

class GasStationScreen extends StatefulWidget {
  const GasStationScreen({
    super.key,
  });

  @override
  State<GasStationScreen> createState() => _GasStationScreenState();
}

class _GasStationScreenState extends State<GasStationScreen> {
  GasStationController gasStationController = Get.put(GasStationController());
  GoogleMapController? mapController;
  late CameraPosition cameraPosition;
  final double _defaultLatitude = 24.741305;
  final double _defaultLongitude = 46.805976;
  final LatLng _center = const LatLng(24.741305000000001, 46.805976000000001);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    cameraPosition = CameraPosition(target: _center, zoom: 7.0);
    gasStationController.isDefaultMap.value = true;
    _fetchUserLocation();
  }

  Future<void> _fetchUserLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Please enable location services.")),
      );
      _useDefaultLocation();
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Location permissions are denied.")),
        );
        _useDefaultLocation();
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              "Location permissions are permanently denied. Please enable them in settings."),
          action: SnackBarAction(
            label: 'Settings',
            onPressed: () {
              Geolocator.openAppSettings();
            },
          ),
        ),
      );
      _useDefaultLocation();
      return;
    }

    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      gasStationController.userLatitude = position.latitude;
      gasStationController.userLongitude = position.longitude;

      gasStationController
          .getGasStations200kmRadius(
            gasStationController.userLatitude!,
            gasStationController.userLongitude!,
            gasStationController.radiusInKm,
          )
          .then((_) => _add(gasStationController.gasStationList));

      // gasStationController.filterGasStationsWithinRadius();
      // _add();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Failed to get location: $e")),
      );
      _useDefaultLocation();
    }
  }

  void _useDefaultLocation() {
    gasStationController.userLatitude = _defaultLatitude;
    gasStationController.userLongitude = _defaultLongitude;

    gasStationController
        .getGasStations200kmRadius(
          gasStationController.userLatitude!,
          gasStationController.userLongitude!,
          gasStationController.radiusInKm,
        )
        .then((_) => _add(gasStationController.gasStationList));

    // gasStationController.filterGasStationsWithinRadius();
    // _add();
  }

  void _onMapCreated(GoogleMapController controller) async {
    mapController = controller;
    if (Platform.isIOS) {
      controller.setMapStyle('''
      [
        {
          "featureType": "water",
          "elementType": "labels",
          "stylers": [
            {
              "visibility": "off"
            }
          ]
        }
      ]
      ''');
    }

    //_add();
  }

  Map<MarkerId, Marker> markers = <MarkerId, Marker>{};

  // Future<void> _add(List<GasStationModel> stations) async {
  //   if (stations.isEmpty) {
  //     return;
  //   }

  //   markers.clear();

  //   for (int i = 0; i < stations.length; i++) {
  //     var markerIdVal = "$i";
  //     final MarkerId markerId = MarkerId(markerIdVal);

  //     final Marker marker = Marker(
  //       markerId: markerId,
  //       position: LatLng(
  //         double.parse(stations[i].latitude),
  //         double.parse(stations[i].longitude),
  //       ),
  //       icon: await createMarkerImageFromAsset(
  //           context, DefaultImages.gasStationMarker),
  //       onTap: () {
  //         showModalBottomSheet(
  //           context: context,
  //           barrierColor: AppColor.cBlackOpacity,
  //           shape: RoundedRectangleBorder(
  //               borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
  //           isScrollControlled: true,
  //           builder: (context) {
  //             return gasStationsBottomSheetWidget(
  //               title:
  //                   gasStationController.gasStationList[i].placeDesc.toString(),
  //               subTitle: gasStationController.gasStationList[i].stationName
  //                   .toString(),
  //               status: gasStationController.gasStationList[i].stationStatus,
  //               products: gasStationController.gasStationList[i].products,
  //               latitude: double.parse(
  //                   gasStationController.gasStationList[i].latitude),
  //               longitude: double.parse(
  //                   gasStationController.gasStationList[i].longitude),
  //               coordinates:
  //                   gasStationController.gasStationList[i].stationCoordinates,
  //             );
  //           },
  //         );
  //       },
  //     );

  //     if (mounted) {
  //       setState(() {
  //         markers[markerId] = marker;
  //       });
  //     }
  //   }
  // }

  Future<void> _add(List<GasStationModel> stations) async {
    if (stations.isEmpty) {
      return;
    }

    log('come here add');
    markers.clear();

    for (int i = 0; i < stations.length; i++) {
      var markerIdVal = "$i";
      final MarkerId markerId = MarkerId(markerIdVal);

      final Marker marker = Marker(
        markerId: markerId,
        position: LatLng(
          double.parse(stations[i].latitude),
          double.parse(stations[i].longitude),
        ),
        icon: await createMarkerImageFromAsset(
            context, DefaultImages.gasStationMarker),
        onTap: () {
          showModalBottomSheet(
            context: context,
            barrierColor: AppColor.cBlackOpacity,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
            ),
            isScrollControlled: true,
            builder: (context) {
              // return gasStationsBottomSheetWidget(
              //   title: stations[i].placeDesc.toString(),
              //   subTitle: stations[i].stationName.toString(),
              //   products: stations[i].products,
              //   latitude: double.parse(stations[i].latitude),
              //   longitude: double.parse(stations[i].longitude),
              //   coordinates: '${stations[i].latitude},${stations[i].longitude}',
              //   status: stations[i].stationStatus,
              // );

              return gasStationsBottomSheetWidget(
                title: stations[i].placeDesc.toString(),
                subTitle: stations[i].stationName.toString(),
                status: stations[i].stationStatus,
                products: stations[i].products,
                latitude: double.parse(stations[i].latitude),
                longitude: double.parse(stations[i].longitude),
                coordinates: stations[i].stationCoordinates,
              );
            },
          );
        },
      );

      if (mounted) {
        setState(() {
          markers[markerId] = marker;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => gasStationController.gasStationList.isNotEmpty
          ? Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                        top: 0, right: 16, left: 16, bottom: 16),
                    child: Container(
                      width: Get.width,
                      height: 45,
                      decoration: BoxDecoration(
                          color: AppColor.cLightGrey,
                          borderRadius: BorderRadius.circular(4)),
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            commonLocationTab(
                              onTap: () {
                                gasStationController.isMap.value = true;
                                gasStationController.isList.value = false;
                              },
                              text: "Map".trr,
                              icon: DefaultImages.mapIcn,
                              btnColor: gasStationController.isMap.value == true
                                  ? AppColor.cWhite
                                  : AppColor.cLightGrey,
                              textColor:
                                  gasStationController.isMap.value == true
                                      ? AppColor.cText
                                      : AppColor.cDarkGreyFont,
                            ),
                            commonLocationTab(
                                onTap: () {
                                  gasStationController.isMap.value = false;
                                  gasStationController.isList.value = true;
                                },
                                text: "List".trr,
                                icon: DefaultImages.listIcn,
                                btnColor:
                                    gasStationController.isList.value == true
                                        ? AppColor.cWhite
                                        : AppColor.cLightGrey,
                                textColor:
                                    gasStationController.isList.value == true
                                        ? AppColor.cText
                                        : AppColor.cDarkGreyFont),
                          ]),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            gasStationController.isDefaultMap.value = true;
                            await gasStationController
                                .getGasStations200kmRadius(
                                    gasStationController.userLatitude!,
                                    gasStationController.userLongitude!,
                                    gasStationController.radiusInKm);
                            _add(gasStationController.gasStationList);
                          },
                          child: Container(
                            width: (Get.width - 48) / 2,
                            height: 45,
                            decoration: BoxDecoration(
                              color: gasStationController.isDefaultMap.value
                                  ? AppColor.cWhite
                                  : AppColor.cLightGrey,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black12,
                                  offset: Offset(0, 4),
                                  blurRadius: 6,
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.map_rounded, color: AppColor.cText),
                                SizedBox(width: 6),
                                Text(
                                  "Default".trr,
                                  style: TextStyle(
                                      color: gasStationController
                                              .isDefaultMap.value
                                          ? AppColor.cText
                                          : AppColor.cDarkGreyFont),
                                ),
                              ],
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            gasStationController.isDefaultMap.value = false;
                            _add(gasStationController.allStationsList);
                            // await gasStationController.getAllGasStations(
                            //     // gasStationController.userLatitude!,
                            //     // gasStationController.userLongitude!,
                            //     // gasStationController.radiusInKm

                            //     );
                            // _add(gasStationController.gasStationList);
                          },
                          child: Container(
                            width: (Get.width - 48) / 2,
                            height: 45,
                            decoration: BoxDecoration(
                              color: !gasStationController.isDefaultMap.value
                                  ? AppColor.cWhite
                                  : AppColor.cLightGrey,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black12,
                                  offset: Offset(0, 4),
                                  blurRadius: 6,
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(DefaultImages.mapIcn),
                                SizedBox(width: 6),
                                Text(
                                  "All".trr,
                                  style: TextStyle(
                                      color: !gasStationController
                                              .isDefaultMap.value
                                          ? AppColor.cText
                                          : AppColor.cDarkGreyFont),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  if (gasStationController.isMap.value == true) ...[
                    Expanded(
                      child: Container(
                        height: Get.height / 1.5,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6)),
                        child: GoogleMap(
                          onMapCreated: _onMapCreated,
                          markers: Set<Marker>.of(markers.values),
                          // YOUR MARKS IN MAP
                          initialCameraPosition: cameraPosition,
                          scrollGesturesEnabled: true,
                        ),
                      ),
                    )
                  ] else
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: ListView.builder(
                          scrollDirection: Axis.vertical,
                          shrinkWrap: true,
                          physics: BouncingScrollPhysics(),
                          itemCount: (gasStationController.isDefaultMap == true)
                              ? gasStationController.gasStationList.length
                              : gasStationController.allStationsList.length,
                          itemBuilder: (context, index) {
                            var data = (gasStationController.isDefaultMap ==
                                    true)
                                ? gasStationController.gasStationList[index]
                                : gasStationController.allStationsList[index];

                            return Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(bottom: 8.0),
                                    child: GestureDetector(
                                      onTap: () {
                                        showModalBottomSheet(
                                          context: context,
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.vertical(
                                                      top:
                                                          Radius.circular(12))),
                                          isScrollControlled: true,
                                          builder: (context) {
                                            return gasStationsBottomSheetWidget(
                                              title: data.placeDesc,
                                              subTitle: data.stationName,
                                              status: data.stationStatus,
                                              products: data.products,
                                              latitude:
                                                  double.parse(data.latitude),
                                              longitude:
                                                  double.parse(data.longitude),
                                              coordinates:
                                                  data.stationCoordinates,
                                            );
                                          },
                                        );
                                      },
                                      child: listDataContainer(
                                        title: data.placeDesc,
                                        subTitle: data.stationName,
                                      ),
                                    ),
                                  )
                                  // Text(
                                  //   data['data'],
                                  //   style: pBold20,
                                  // ),
                                  // verticalSpace(11),
                                  // ListView.builder(
                                  //   itemBuilder: (context, i) {
                                  //     var subData = data['list'][i];
                                  //     return Padding(
                                  //       padding:
                                  //           const EdgeInsets.only(bottom: 8.0),
                                  //       child: GestureDetector(
                                  //         onTap: () {
                                  //           showModalBottomSheet(
                                  //             context: context,
                                  //             shape: RoundedRectangleBorder(
                                  //                 borderRadius:
                                  //                     BorderRadius.vertical(
                                  //                         top: Radius.circular(
                                  //                             12))),
                                  //             isScrollControlled: true,
                                  //             builder: (context) {
                                  //               return locationBottomSheetWidget(
                                  //                 title: data['data'],
                                  //                 subTitle: subData['title'],
                                  //                 openNow: "Yes",
                                  //                 workingHour: "24/7",
                                  //                 phoneNo: "966920002667",
                                  //                 latitude: 21.44484793949768,
                                  //                 longitude: 53.295109691390245,
                                  //               );
                                  //             },
                                  //           );
                                  //         },
                                  //         child: listDataContainer(
                                  //           title: subData['title'],
                                  //           subTitle: subData['subTitle'],
                                  //         ),
                                  //       ),
                                  //     );
                                  //   },
                                  //   itemCount: data['list'].length,
                                  //   shrinkWrap: true,
                                  //   physics: NeverScrollableScrollPhysics(),
                                  // )
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                ],
              ),
            )
          : Center(
              child: CircularProgressIndicator(),
            ),
    );
  }

  @override
  void dispose() {
    mapController?.dispose(); // Dispose of the controller
    super.dispose();
  }
}

Widget gasStationsBottomSheetWidget({
  required String title,
  required String subTitle,
  required String status,
  required String products,
  //required String phoneNo,
  required double latitude,
  required double longitude,
  required String coordinates,
}) {
  return Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
    ),
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: pSemiBold16,
        ),
        verticalSpace(6),
        Text(
          subTitle,
          style: pRegular12,
        ),
        verticalSpace(32),
        userDataRowWidget(title: 'Status', value: status),
        verticalSpace(20),
        userDataRowWidget(title: 'Products', value: products),
        //verticalSpace(20),
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //   children: [
        //     userDataRowWidget(title: 'Phone', value: '+$phoneNo'),
        //     GestureDetector(
        //         onTap: () {
        //           Clipboard.setData(ClipboardData(text: phoneNo));
        //         },
        //         child: assetSvdImageWidget(image: DefaultImages.clipboardIcn)),
        //   ],
        // ),
        verticalSpace(28),
        gotoMapButton(onTap: () async {
          //MapsLauncher.launchCoordinates(latitude, longitude);

          final Uri url0 = Uri.parse(coordinates);
          if (!await launchUrl(url0)) {
            // You can show an error message if the URL couldn't be launched
            print('Could not launch $url0');
          }
        }),
        verticalSpace(16),
      ],
    ),
  );
}

Widget locationBottomSheetWidget({
  required String title,
  required String subTitle,
  required String openNow,
  required String workingHour,
  required String phoneNo,
  required double latitude,
  required double longitude,
}) {
  return Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
    ),
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: pSemiBold16,
        ),
        verticalSpace(6),
        Text(
          subTitle,
          style: pRegular12,
        ),
        verticalSpace(32),
        userDataRowWidget(title: 'Open now?', value: openNow),
        verticalSpace(20),
        userDataRowWidget(title: 'Opening hours', value: workingHour),
        verticalSpace(20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            userDataRowWidget(title: 'Phone', value: '+$phoneNo'),
            GestureDetector(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: phoneNo));
                },
                child: assetSvdImageWidget(image: DefaultImages.clipboardIcn)),
          ],
        ),
        verticalSpace(28),
        gotoMapButton(onTap: () {
          MapsLauncher.launchCoordinates(latitude, longitude);
        }),
        verticalSpace(16),
      ],
    ),
  );
}

Widget listDataContainer({required String title, required String subTitle}) {
  return Container(
    decoration: BoxDecoration(
        color: AppColor.lightBlueColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppColor.cLightGrey)),
    width: Get.width,
    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: pSemiBold17,
        ),
        verticalSpace(8),
        Text(
          subTitle,
          style: pRegular13,
        ),
      ],
    ),
  );
}

Widget gotoMapButton({required Function() onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      height: 44,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: AppColor.cBlack, width: 1)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(DefaultImages.googleMapIcn, height: 16, width: 15),
          horizontalSpace(11),
          Text(
            'Show on Google Maps'.trr,
            style: pRegular14.copyWith(color: AppColor.cDarkBlueFont),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}

Future<BitmapDescriptor> createMarkerImageFromAsset(
    BuildContext context, String image) async {
  ByteData data = await rootBundle.load(image);
  ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
      targetWidth: 150);
  ui.FrameInfo fi = await codec.getNextFrame();
  (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
      .buffer
      .asUint8List();
  return BitmapDescriptor.fromBytes(
      (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
          .buffer
          .asUint8List());
}
