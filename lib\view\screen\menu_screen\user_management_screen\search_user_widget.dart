// ignore_for_file: prefer_const_constructors, prefer_const_constructors_in_immutables

import 'package:get/get.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'edit_user_widget.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_order_widget.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/user_management_screen.dart';
import 'package:waie_app/core/controller/menu_controller/user_management_controller/user_management_controller.dart';


class SearchUserWidget extends StatefulWidget {
  SearchUserWidget({super.key});

  @override
  State<SearchUserWidget> createState() => _SearchUserWidgetState();
}

class _SearchUserWidgetState extends State<SearchUserWidget> {
  UserManagementController userManagementController = Get.find();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    userManagementController.itemList.clear();
  }

  void filterSearchResults(String query) {
    userManagementController.itemList.value = userManagementController.userDataList
        .where((item) => item['name'].toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        height: Get.height - 60,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        padding: EdgeInsets.all(16),
        child: Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              verticalSpace(16),
              Row(
                children: [
                  Expanded(
                    child: CommonTextField(
                      controller: userManagementController.searchController.value,
                      labelText: '',
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(12),
                        child: assetSvdImageWidget(image: DefaultImages.searchIcn, width: 24, height: 24),
                      ),
                      hintText: 'Search'.trr,
                      onChanged: (value) {
                        if (value.isEmpty) {
                          userManagementController.itemList.clear();
                          userManagementController.itemList.refresh();
                        } else {
                          userManagementController.searchController.refresh();
                          filterSearchResults(value);
                        }
                      },
                    ),
                  ),
                  userManagementController.searchController.value.text.isEmpty
                      ? SizedBox()
                      : cancelButton(
                          () {
                            userManagementController.searchController.value.clear();
                            userManagementController.searchController.refresh();
                            userManagementController.itemList.clear();
                            userManagementController.itemList.refresh();
                          },
                        )
                ],
              ),
              verticalSpace(16),
              userManagementController.itemList.isEmpty
                  ? Expanded(
                      child: Center(
                          child: Text(
                      "No matches".trr,
                      style: pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                    )))
                  : Expanded(
                      child: ListView.builder(
                        itemCount: userManagementController.itemList.length,
                        shrinkWrap: true,
                        physics: BouncingScrollPhysics(),
                        itemBuilder: (context, index) {
                          var data = userManagementController.itemList[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8.0),
                            child: userDetailWidget(
                              name: data['name'],
                              status: data['status'].toString().trr,
                              vehiclesAssigned: data['vehiclesAssigned'],
                              email: data['email'],
                              id: data['id'],
                              created: data['created'],
                              lastVisit: data['lastVisit'].toString().trr,
                              isShow: data['name'].contains("You") ? false : true,
                              onTap: () {
                                showModalBottomSheet(
                                  context: context,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                                  backgroundColor: AppColor.cBackGround,
                                  barrierColor: AppColor.cBlackOpacity,
                                  isScrollControlled: true,
                                  builder: (context) {
                                    return userActionWidget(
                                      name: data['name'],
                                      editFunction: () {
                                        Get.back();
                                        showModalBottomSheet(
                                          context: context,
                                          shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                                          backgroundColor: AppColor.cBackGround,
                                          barrierColor: AppColor.cBlackOpacity,
                                          isScrollControlled: true,
                                          builder: (context) {
                                            return EditUserWidget(
                                              email: data['email'],
                                              name: data['name'],
                                            );
                                          },
                                        );
                                      },
                                      deleteFunction: () {
                                        Get.back();
                                        showDialog(
                                          context: context,
                                          builder: (context) {
                                            return AlertDialog(
                                              insetPadding: EdgeInsets.all(16),
                                              contentPadding: EdgeInsets.all(16),
                                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                              content: DeleteUserWidget(
                                                  name: data['name'],
                                                  deleteFun: () {
                                                    Get.back();
                                                  }),
                                            );
                                          },
                                        );
                                      },
                                    );
                                  },
                                );
                                // if (data['status'] == "Active") {
                                //   showModalBottomSheet(
                                //     context: context,
                                //     shape: RoundedRectangleBorder(
                                //         borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                                //     backgroundColor: AppColor.cBackGround,
                                //     barrierColor: AppColor.cBlackOpacity,
                                //     isScrollControlled: true,
                                //     builder: (context) {
                                //       return InviteUserWidget(
                                //         email: data['email'],
                                //       );
                                //     },
                                //   );
                                // } else {
                                //   showModalBottomSheet(
                                //     context: context,
                                //     shape: RoundedRectangleBorder(
                                //         borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                                //     backgroundColor: AppColor.cBackGround,
                                //     barrierColor: AppColor.cBlackOpacity,
                                //     isScrollControlled: true,
                                //     builder: (context) {
                                //       return EditUserWidget(
                                //         email: data['email'],
                                //         name: data['name'],
                                //       );
                                //     },
                                //   );
                                // }
                              },
                            ),
                          );
                        },
                      ),
                    ),
            ],
          );
        }),
      ),
    );
  }
}
