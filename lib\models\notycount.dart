import 'dart:convert';

class NotyCount {
  NotyCount({
    required this.genCount,
    required this.compCount,
  });

  final String? genCount;
  final String? compCount;

  Map<String, dynamic> toMap() {
    return {
      'genCount': genCount,
      'compCount': compCount,
    };
  }

  factory NotyCount.fromMap(Map<String, dynamic> map) {
    return NotyCount(
      genCount: map['genCount'] ?? '',
      compCount: map['compCount'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory NotyCount.fromJson(String source) =>
      NotyCount.fromMap(json.decode(source));

  /*factory NotyCount.fromJson(Map<String, dynamic> json){
    return NotyCount(
      genCount: json["genCount"],
      compCount: json["compCount"],
    );
  }*/

}