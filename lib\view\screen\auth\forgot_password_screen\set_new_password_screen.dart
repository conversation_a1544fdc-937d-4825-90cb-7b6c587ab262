// ignore_for_file: prefer_const_constructors_in_immutables

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/auth/forgot_password_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/screen/auth/auth_background.dart';
import 'package:waie_app/view/screen/auth/login_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class SetNewPasswordScreen extends StatefulWidget {
  SetNewPasswordScreen({super.key});

  @override
  State<SetNewPasswordScreen> createState() => _SetNewPasswordScreenState();
}

class _SetNewPasswordScreenState extends State<SetNewPasswordScreen> {
  ForgotPasswordController forgotPasswordController =
      Get.put(ForgotPasswordController());

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    forgotPasswordController.passwordController.clear();
    forgotPasswordController.confirmPasswordController.clear();
    forgotPasswordController.isPatternMatch.value = false;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
          backgroundColor: AppColor.cBackGround,
          body: SafeArea(
            child: AuthBackGroundWidget(
              widget: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  child: Obx(() {
                    return Form(
                      key: formKey,
                      autovalidateMode: AutovalidateMode.disabled,
                      onChanged: () {
                        if (formKey.currentState!.validate()) {
                          forgotPasswordController.isReSetPassword.value = true;
                        }
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          verticalSpace(Get.height * 0.02),
                          simpleMyAppBar(
                              title: "",
                              onTap: () {
                                Get.back();
                              },
                              backString: "Login".trr),
                          verticalSpace(23),
                          Center(
                            child: Text(
                              "Choose a new password".trr,
                              style: pBold28,
                            ),
                          ),
                          verticalSpace(32),
                          CommonTextField(
                              controller:
                                  forgotPasswordController.passwordController,
                              labelText: '',
                              hintText: "Enter your new password".trr,
                              filled: true,
                              fillColor: AppColor.cFilled,
                              obscuringCharacter: '*',
                              obscureText:
                                  forgotPasswordController.isPassword.value,
                              validator: (value) {
                                return Validator.validatePassword(value);
                              },
                              onChanged: (value) {
                                RegExp regex =
                                    RegExp(forgotPasswordController.pattern);
                                if (regex.hasMatch(value)) {
                                  forgotPasswordController
                                      .isPatternMatch.value = true;
                                } else {
                                  forgotPasswordController
                                      .isPatternMatch.value = false;
                                }
                              },
                              suffix: GestureDetector(
                                  onTap: () {
                                    forgotPasswordController.isPassword.value =
                                        !forgotPasswordController
                                            .isPassword.value;
                                  },
                                  child: assetSvdImageWidget(
                                      image: forgotPasswordController
                                              .isPassword.value
                                          ? DefaultImages.eyeOffIcn
                                          : DefaultImages.eyeIcn))),
                          verticalSpace(8),
                          CommonTextField(
                              controller: forgotPasswordController
                                  .confirmPasswordController,
                              labelText: '',
                              hintText: "Enter your new password again".trr,
                              obscuringCharacter: '*',
                              filled: true,
                              fillColor: AppColor.cFilled,
                              obscureText:
                                  forgotPasswordController.isConfirmPass.value,
                              validator: (value) {
                                return Validator.validateConfirmPassword(
                                    value,
                                    forgotPasswordController
                                        .passwordController.text);
                              },
                              suffix: GestureDetector(
                                  onTap: () {
                                    forgotPasswordController
                                            .isConfirmPass.value =
                                        !forgotPasswordController
                                            .isConfirmPass.value;
                                  },
                                  child: assetSvdImageWidget(
                                      image: forgotPasswordController
                                              .isConfirmPass.value
                                          ? DefaultImages.eyeOffIcn
                                          : DefaultImages.eyeIcn))),
                          verticalSpace(35),
                          Text("Your password should be...".trr,style:pSemiBold12,),
                          verticalSpace(16),
                          validateTextRow(
                              image:
                                  forgotPasswordController.isPatternMatch.value
                                      ? DefaultImages.validateIcn
                                      : DefaultImages.notValidateIcn,
                              title:
                                  "Be at least 12 characters long, with a combination of uppercase or lowercase letters and symbols".trr),
                          // validateTextRow(
                          //     image: (forgotPasswordController
                          //                     .passwordController.text.length ==
                          //                 12 ||
                          //             forgotPasswordController
                          //                     .passwordController.text.length >=
                          //                 14)
                          //         ? DefaultImages.validateIcn
                          //         : DefaultImages.notValidateIcn,
                          //     title:
                          //         "At least 12 characters long but 14 or more is better.".trr),
                          // validateTextRow(
                          //     image: DefaultImages.notValidateIcn,
                          //     title:
                          //         "Not a word that can be found in a dictionary or the name of a person, character, product, or organization.".trr),
                        ],
                      ),
                    );
                  }),
                ),
              ),
            ),
          ),
          resizeToAvoidBottomInset: false,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
          floatingActionButton: Padding(
            padding: const EdgeInsets.only(left: 24, bottom: 10, right: 24),
            child: Obx(() {
              return CommonButton(
                title: 'Submit'.trr,
                onPressed:
                    forgotPasswordController.isReSetPassword.value == true
                        ? () {
                            if (formKey.currentState!.validate()) {
                              log('done');
                              Get.offAll(() => LoginScreen());
                            }
                          }
                        : null,
                btnColor: forgotPasswordController.isReSetPassword.value == true
                    ? AppColor.themeBlueColor
                    : AppColor.cLightBlueBtn,
              );
            }),
          )),
    );
  }

  Widget validateTextRow({required String image, required String title}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          assetSvdImageWidget(image: image),
          horizontalSpace(13),
          Expanded(
            child: Text(
              title,
              style: pRegular13,
              maxLines: 3,
            ),
          )
        ],
      ),
    );
  }
}
