// ignore_for_file: avoid_print

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../widget/loading_widget.dart';

class LogisticsTransportWebviewScreen extends StatefulWidget {
  final String url;
  const LogisticsTransportWebviewScreen({
    super.key,
    required this.url,
  });

  @override
  State<LogisticsTransportWebviewScreen> createState() =>
      _LogisticsTransportWebviewScreenState();
}

class _LogisticsTransportWebviewScreenState
    extends State<LogisticsTransportWebviewScreen> {
  late WebViewController _controler;
  final double _height = 1;
  bool isLoading = true;

  @override
  void initState() {
    _controler = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse('https://www.aldrees.com/transport'))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (progress) {
            print("progress---> $progress");
            if (progress == 100) {
              setState(() {
                isLoading = false;
              });
            }
          },
        ),
      );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColor.themeBlueColor,
        title: Text(
          "Logistics Transport".trr,
          style: pBold20.copyWith(color: AppColor.cWhiteFont),
          textAlign: TextAlign.center,
        ),
      ),
      body: isLoading
          ? const Center(
              child: LoadingWidget(),
            )
          : WebViewWidget(
              controller: _controler,
            ),
    );
  }
}
