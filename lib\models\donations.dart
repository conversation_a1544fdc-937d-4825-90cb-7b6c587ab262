import 'dart:convert';

class Donations {
  final String TYPEID;
  final String IMG_LOC;
  final String TYPEDESCEN;
  final String TYPEDESCAR;
  final String LINK;
  final String SYS_DATE;
  final String MODIFYBY;

  Donations(
      {required this.TYPEID,
      required this.IMG_LOC,
      required this.TYPEDESCEN,
      required this.TYPEDESCAR,
      required this.LINK,
      required this.SYS_DATE,
      required this.MODIFYBY});

  Map<String, dynamic> toMap() {
    return {
      'TYPEID': TYPEID,
      'IMG_LOC': IMG_LOC,
      'TYPEDESCEN': TYPEDESCEN,
      'TYPEDESCAR': TYPEDESCAR,
      'LINK': LINK,
      'SYS_DATE': SYS_DATE,
      'MODIFYBY': MODIFYBY,
    };
  }

  factory Donations.fromMap(Map<String, dynamic> map) {
    return Donations(
      TYPEID: map['TYPEID'] ?? '',
      IMG_LOC: map['IMG_LOC'] ?? '',
      TYPEDESCEN: map['TYPEDESCEN'] ?? '',
      TYPEDESCAR: map['TYPEDESCAR'] ?? '',
      LINK: map['LINK'] ?? '',
      SYS_DATE: map['SYS_DATE'] ?? '',
      MODIFYBY: map['MODIFYBY'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Donations.fromJson(String source) =>
      Donations.fromMap(json.decode(source));
}
