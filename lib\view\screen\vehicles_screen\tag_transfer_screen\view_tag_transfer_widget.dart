// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/incoming_tags_transfer_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/tag_transfer_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/view_tag_transfer_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class ViewTagTransferWidget extends StatelessWidget {
  ViewTagTransferWidget({super.key});
  ViewTagsTransferController viewTagsTransferController =
      Get.put(ViewTagsTransferController());

  String REQID = '';
  String isApproved = '';

  // @override
  // void initState() {
  //   // TODO: implement initState
  //   super.initState();
  //   viewTagsTransferController.tagTransferVehicelLists();
  // }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Obx(
        () => viewTagsTransferController.tagTransferVehicelLists.isNotEmpty
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        children: [
                          GestureDetector(
                              onTap: () {
                                Get.back();
                              },
                              child: CircleAvatar(
                                radius: 20,
                                backgroundColor: AppColor.cLightBlueContainer,
                                child: Center(
                                    child: assetSvdImageWidget(
                                        image: DefaultImages.backIcn)),
                              )),
                          Expanded(
                            child: Align(
                              alignment: Alignment.center,
                              child: Center(
                                child: Text(
                                  "Transfer Details",
                                  style: pSemiBold17,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    verticalSpace(25),
                    tagTitleWidget(),
                    // ListView.builder(
                    //     shrinkWrap: true,
                    //     itemCount: tagTransferController.incomingTagDataList.length,
                    //     physics: BouncingScrollPhysics(),
                    //     itemBuilder: (context, index) {
                    //       var data = tagTransferController.incomingTagDataList[index];
                    //       return tagDataWidget(
                    //         id: data['id'],
                    //         plat: data['plat'],
                    //         make: data['make'],
                    //         model: data['model'],
                    //       );
                    //     }),

                    Obx(
                      () => viewTagsTransferController
                              .tagTransferVehicelLists.isNotEmpty
                          ? ListView.builder(
                              scrollDirection: Axis.vertical,
                              shrinkWrap: true,
                              itemCount: viewTagsTransferController
                                  .tagTransferVehicelLists.length,
                              itemBuilder: (context, index) {
                                final data = viewTagsTransferController
                                    .tagTransferVehicelLists
                                    .elementAt(index);
                                print(
                                    'data12312312 ${viewTagsTransferController.tagTransferVehicelLists.length}');
                                print(
                                    'data12312312 ${jsonDecode(jsonEncode(data))}');
                                return tagDataWidget(
                                  id: data.rowNumber.toString(),
                                  plat: data.plateno,
                                  type: data.vehicletypeDisp,
                                  fuel: data.fueltypeDisp,
                                  status: data.servicestatus,
                                  textColor: data.servicestatus == "A" //active
                                      ? AppColor.cGreen
                                      : data.servicestatus == "N" //new
                                          ? AppColor.cDarkBlueFont
                                          : data.servicestatus ==
                                                  "T" //terminated
                                              ? AppColor.cRedText
                                              : data.servicestatus ==
                                                      "I" //inactive
                                                  ? AppColor.cYellow
                                                  : AppColor.cGreen,
                                  color: data.servicestatus == "A" //active
                                      ? AppColor.cLightGreen
                                      : data.servicestatus == "N" //new
                                          ? AppColor.cLightBlueContainer
                                          : data.servicestatus ==
                                                  "T" //terminated
                                              ? AppColor.cLightRedContainer
                                              : data.servicestatus ==
                                                      "I" //inactive
                                                  ? AppColor.cLightYellow
                                                  : AppColor.cGreen,
                                );
                              })
                          : const CircularProgressIndicator(),
                    ),
                  ])
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: const [
                  Center(child: CircularProgressIndicator()),
                ],
              ),
      ),
    );
  }

  Widget successDialogWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "Request submitted".trr,
          style: pSemiBold16,
        ),
        verticalSpace(24),
        Text(
            "You've submitted a request to transfer your tags. We'll review it within 48 hours."
                .trr,
            style: pRegular13,
            textAlign: TextAlign.center),
        verticalSpace(24),
        Text.rich(
          TextSpan(
            text: '${'You can also track the status of your request in'.trr}  ',
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
            children: [
              TextSpan(
                text: "Transfer History".trr,
                style: pBold12.copyWith(
                  fontSize: 13,
                  color: AppColor.cLinkText,
                ),
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
        verticalSpace(24),
        CommonButton(
          title: 'Continue to Dashboard'.trr,
          onPressed: () {
            Get.offAll(() => DashBoardManagerScreen(currantIndex: 0));
          },
          btnColor: AppColor.themeOrangeColor,
        ),
      ],
    );
  }

  Container tagDataWidget({
    String? id,
    String? plat,
    String? type,
    String? fuel,
    String? status,
    Color? textColor,
    Color? color,
  }) {
    return Container(
      // padding: EdgeInsets.symmetric(vertical: 18, horizontal: 16),
      padding: EdgeInsets.fromLTRB(16, 18, 12, 18),
      decoration: BoxDecoration(
          color: AppColor.cBackGround,
          border: Border(bottom: BorderSide(color: AppColor.cBorder))),
      child: Row(children: [
        SizedBox(width: 50, child: Text(id!, style: pRegular12)),
        Expanded(
            flex: 1,
            child: Text(
              plat!,
              style: pRegular12,
              textAlign: TextAlign.start,
              overflow: TextOverflow.ellipsis,
            )),
        Expanded(
            flex: 1,
            child: Text(
              type!,
              style: pRegular12,
              textAlign: TextAlign.start,
              overflow: TextOverflow.ellipsis,
            )),
        Expanded(
            flex: 1,
            child: Text(
              fuel!,
              style: pRegular12,
              textAlign: TextAlign.start,
              overflow: TextOverflow.ellipsis,
            )),
        SizedBox(
          child: statusWidget(
            text: status,
            textColor: textColor,
            color: color,
          ),
        ),
      ]),
    );
  }

  Container tagTitleWidget() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 18, horizontal: 16),
      decoration: BoxDecoration(
          color: AppColor.cFilled,
          border: Border(bottom: BorderSide(color: AppColor.cBorder))),
      child: Row(children: [
        SizedBox(
          width: 50,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text("#", style: pSemiBold12),
              //horizontalSpace(8),
              //assetSvdImageWidget(image: DefaultImages.upDownIcn),
            ],
          ),
        ),
        Expanded(
            flex: 1,
            child: Text(
              "${"Plate".trr}#",
              style: pMedium12,
            )),
        Expanded(
            flex: 1,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "Vehicle Type".trr,
                  style: pMedium12,
                ),
                // horizontalSpace(8),
                // assetSvdImageWidget(image: DefaultImages.upDownIcn),
              ],
            )),
        Expanded(
            flex: 1,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "Fuel".trr,
                  style: pMedium12,
                ),
                // horizontalSpace(8),
                // assetSvdImageWidget(image: DefaultImages.upDownIcn),
              ],
            )),
        SizedBox(
          child: Text(
            "Status".trr,
            style: pMedium12,
          ),
        ),
      ]),
    );
  }

  Widget statusWidget(
      {String? text,
      Color? textColor,
      Color? color,
      double? horizontalSpace,
      double? left}) {
    return Container(
      height: 24,
      decoration: BoxDecoration(
          color: color ?? AppColor.cLightGreen,
          borderRadius: BorderRadius.circular(4)),
      padding: EdgeInsets.all(5),
      child: Text(
        text == "A" //active
            ? "ACTIVE"
            : text == "N" //new
                ? "NEW"
                : text == "T" //terminated
                    ? "TERMINATED"
                    : text == "I" //inactive
                        ? "INACTIVE"
                        : "ACTIVE",
        style: pSemiBold12.copyWith(color: textColor ?? AppColor.cGreen),
        textAlign: TextAlign.start,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
