import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/regDB.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../../models/profile.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/locale_string.dart';
import '../../../../utils/prefer.dart';
import '../../../../utils/text_style.dart';
import '../../../../view/screen/auth/login_with_email_screen.dart';
import '../../../../view/screen/dashboard_manager/dashboard_manager.dart';
import '../../../../view/screen/menu_screen/setting_screen/privacy_security_screen.dart';
import '../../../../view/widget/common_button.dart';
import '../../../../view/widget/common_snak_bar_widget.dart';
import '../../../../view/widget/common_space_divider_widget.dart';

class PrivacySecurityController extends GetxController {
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController oldPasswordController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController oldEmailController = TextEditingController();
  TextEditingController newEmailController = TextEditingController();
  TextEditingController checkPasswordController = TextEditingController();
  RxBool isObSecure = true.obs;
  RxBool isOldObSecure = true.obs;
  RxBool isNewObSecure = true.obs;
  RxBool isCheckPass = true.obs;
  GetStorage custsData = GetStorage('custsData');
  GetStorage userStorage = GetStorage('User');
  final vehicle = GetStorage();
  GetStorage usersData = GetStorage('usersData');
  RegisterDatabase db = RegisterDatabase();

  RxBool isAuth = false.obs;
  RxBool isOnTap = false.obs;
  RxBool isMatch = false.obs;

  checkUserPassword(bool active) async {
    //Loader.showLoader();
    print("checkUserPassword");
    var client = http.Client();
    var custData = custsData.read('custData');
    var jsonCustData = jsonEncode(custsData.read('custData'));

    print("custid>>>>>>> ${custData['CUSTID']}");
    print(
        "checkPasswordController.text>>>>>>> ${checkPasswordController.text}");
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.checkPassword),
          body: {
            "currentpass": checkPasswordController.text,
            "custdata": jsonCustData,
          });
      print("===============================================================");
      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      String message = "Credentials are created Successfully";
      checkPasswordController.clear();
      if (jsonDecode(response.body)["MessageType"] == "success") {
        Get.back();
        isAuth.value = active;
        String message = "Credentials are created Successfully";
        checkPasswordController.clear();
        Loader.hideLoader();
        print(
            "===============================================================");
        //db.createUserInfoData("viajedorryan", "V1ajedor", "Y");
        db.createUserInfoData(jsonDecode(response.body)["username"],
            jsonDecode(response.body)["password"], "Y");
        print(
            "===============================================================");
        db.loadData();
        print(
            "===============================================================");
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Activate Successful'.tr,
                    style: pBold20,
                  ),
                  verticalSpace(24),
                  Text(
                    message.toString(),
                    style: pRegular16,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: 'CONTINUE'.tr,
                    onPressed: () {
                      Get.back();
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        Loader.hideLoader();
        isAuth.value = false;
        commonToast(jsonDecode(response.body)["message"]);
        Get.back();
      }
    } catch (e) {
      log("authentication error: $e");
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  changeUserPass() async {
    Loader.showLoader();
    print("changeUserPass");
    var client = http.Client();
    var custData = jsonEncode(custsData.read('custData'));
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.changePassword),
          body: {
            "currentpass": oldPasswordController.text,
            "newpass": newPasswordController.text,
            "custdata": custData,
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      Map<String, dynamic> result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      // print(
      //     "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      // for (int i = 0; i < result.length; i++) {
      //   FleetStructureModel structures =
      //       FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
      //   divisionList.add(structures);
      // }
      // print("===============================================================");
      if (jsonDecode(response.body)["MessageType"] == "success") {
        Loader.hideLoader();
        db.deleteDatabase();
        String message = "Password Change Successfully";
        String title = "SUCCESS";
        oldPasswordController.clear();
        newPasswordController.clear();
        showDialog(
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(title.tr, style: pBold20),
                  verticalSpace(24),
                  Text(
                    message.toString(),
                    style: pRegular16,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: 'Continue to Dashboard'.tr,
                    onPressed: () {
                      Prefs.clear();
                      vehicle.erase();
                      userStorage.erase();
                      custsData.erase();
                      usersData.erase();
                      login(jsonDecode(response.body)["username"],
                          jsonDecode(response.body)["password"]);
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text("ERROR".tr, style: pBold20),
                  verticalSpace(24),
                  Text(
                    jsonDecode(response.body)["message"],
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  changeUserEmail() async {
    Loader.showLoader();
    print("changeUserEmail");
    var client = http.Client();
    var custData = jsonEncode(custsData.read('custData'));
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.changeEmail),
          body: {
            "newemailid": newEmailController.text,
            "custdata": custData,
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      Map<String, dynamic> result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      // print(
      //     "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      // for (int i = 0; i < result.length; i++) {
      //   FleetStructureModel structures =
      //       FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
      //   divisionList.add(structures);
      // }
      // print("===============================================================");
      if (jsonDecode(response.body)["MessageType"] == "success") {
        Loader.hideLoader();
        String message = "Email Change Successfully";
        String title = "SUCCESS";
        newEmailController.clear();
        showDialog(
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(title.tr, style: pBold20),
                  verticalSpace(24),
                  Text(
                    message.toString(),
                    style: pRegular16,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: 'Continue to Dashboard'.tr,
                    onPressed: () {
                      Prefs.clear();
                      vehicle.erase();
                      userStorage.erase();
                      custsData.erase();
                      usersData.erase();
                      login(jsonDecode(response.body)["username"],
                          jsonDecode(response.body)["password"]);
                      //Get.offAll(() => DashBoardManagerScreen(currantIndex: 0));
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text("ERROR".tr, style: pBold20),
                  verticalSpace(24),
                  Text(
                    jsonDecode(response.body)["message"],
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  login(usrname, pass) async {
    Loader.showLoader();
    clearStorage();
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');

    try {
      var client = http.Client();
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      print("username$usrname");
      print("password$pass");
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.signInEmail),
          body: {
            "username": usrname,
            "password": pass,
          });
      print(response.statusCode);
      print(response.body);
      // print(basicAuth);
      //Loader.hideLoader();
      print('Loader loads');
      if (response.statusCode == 200) {
        print('Suucessfully Fetch');

        Map cardInfo = json.decode(response.body);
        print("json.decode(response.body) ${json.decode(response.body)}");
        print('1');
        print(cardInfo);
        Profile userData = Profile.fromJson(cardInfo);
        print('2');

        if (userData.returnMessage!.isValidTransaction!) {
          if (userData.returnMessage!.action == 'POPUP') {
            Loader.hideLoader();
            //loginFailWidget(userData.returnMessage!.message);
            commonToast(userData.returnMessage!.message);
          } else {
            print(userData.auUsers?.custid);
            var custData = jsonDecode(jsonEncode(userData.auCust));
            var usrData = jsonDecode(jsonEncode(userData.auUsers));
            print(custData);
            if (custData['IS_VERIFIED'] == 'Y') {
              Constants.custIsVerified == "Y";
            }
            Constants.custAcctStatus = userData.auCust!.acctstatus!;

            print(custData['MOBILENO']);
            print(usrData['CUSTID']);
            print(usrData['EMAILID']);
            print(usrData['USERNAME']);
            print("usrData $usrData");
            print(userData.auUsers!.password);
            SharedPreferences sharedUser =
                await SharedPreferences.getInstance();
            // Map decode_options = jsonDecode(jsonString);
            String user = jsonEncode(cardInfo);
            sharedUser.setString('user', user);
            sharedUser.setString('userid', userData.auCust!.custid!);
            userStorage.writeIfNull('custid', userData.auCust!.custid!);
            userStorage.writeIfNull('emailid', userData.auCust!.emailid!);
            userStorage.writeIfNull('accttype', userData.auCust?.accttype);
            usersData.writeIfNull('usrData', usrData);
            custsData.writeIfNull('custData', custData);
            /* Get.to(() => DashBoardManagerScreen(
                currantIndex: 0,
              ));*/
            authController.getUserAccess();
          }
        } else {
          print("Invalid ID Password");
          //loginFailWidget("Invalid ID or password");
          //commonToast("Invalid ID or password");
          if (userData.returnMessage!.message!
              .toString()
              .contains("ACCTLOCKEDMSG")) {
            Loader.hideLoader();
            commonToast("Your Account is lock, try after 5 minutes");
          } else if (userData.returnMessage!.message!
              .toString()
              .contains("INVALIDPASSWORD")) {
            Loader.hideLoader();
            commonToast("Invalid ID Password");
          } else {
            Loader.hideLoader();
            commonToast(userData.returnMessage!.message!.toString());
          }
        }
        return userData;
      } else {
        print('Login Fail');
        commonToast('Login Fail');
        print(response.statusCode.toString());
        loginFailWidget(response.statusCode.toString());
      }
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  clearStorage() {
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');
    final tagOrderRefund = GetStorage();
    final topupOrderRefund = GetStorage();
    final vehicle = GetStorage();
    final getOTPDataVerify = GetStorage();
    final isPinblock = GetStorage();
    final isDCBlock = GetStorage();
    Prefs.clear();
    //Get.offAll(() => LoginManagerScreen());
    vehicle.erase();
    tagOrderRefund.erase();
    topupOrderRefund.erase();
    getOTPDataVerify.erase();
    isPinblock.erase();
    isDCBlock.erase();
    userStorage.erase();
    custsData.erase();
    usersData.erase();
  }
}
