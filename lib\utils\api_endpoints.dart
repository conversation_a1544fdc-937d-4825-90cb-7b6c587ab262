class ApiEndPoints {
  // Local Setup
  //static const String baseUrl = 'https://localhost:44319/api';

  // Test Live Setup

  //static const String baseUrl = 'https://devinttest.aldrees.com/api';

  // Live Setup
  static String baseUrl = 'https://devint.aldrees.com/api';

  // Live TEST
  static String baseUrlLookup = 'https://devint.aldrees.com/api';

  static const String username = 'aldrees';
  static const String password = 'testpass';

  static AuthEndPoints authEndpoints = AuthEndPoints();
}

class AuthEndPoints {
  //UPDATE
  final String comAppUpdate = '/COMAPPUPDATE';
  final String newComAppUpdate = '/NEWCOMAPPUPDATE';
  final String appTerminate = '/APPTERMINATE';

  //LOGIN
  final String signInEmail = '/CUSTLOGIN';
  final String custMobileValidate = '/CUSTMOBILEVALID';
  final String loginWithMobile = '/CUSTMOBILELOGIN';
  final String loginMobileUsers = '/CUSTMOBILELOGINUSER';
  final String forgotPassword = '/RESETPASSWORD';
  final String resetMobileOTP = '/RESETMOBILEOTP';
  final String getNotifCount = '/GETNOTIFYCOUNT';

  //FLEET
  final String addFleet = '/ADDFLEET';
  final String getFleets = '/FLEETLIST';
  final String loadPlaces = '/LOADSTNPLACES';
  final String setServiceStatus = '/SETSERVICESTATUS';
  final String setServiceStatusByRFID = '/SETSERVICESTATUSBYRFID';
  final String updateQuotaLimits = '/UPDATEQUOTALIMITS';
  final String getComplaintReason = '/COMPLAINTREASON';
  final String getComplaintCityList = '/CENTERPLACES';
  final String getComplaintCenterList = '/ACTIVECENTER';
  final String fileComplaint = '/SUBMITCOMPLAINT';
  final String cancelComplaint = '/CANCELCOMPLAINT';
  final String requestChangePlate = '/REQUESTCHANGEPLATE';
  final String cancelChangePlate = '/CANCELCHANGEPLATE';
  final String editVehicle = '/EDITVEHICLE';

  //TAG TRANSFER
  final String tagTransferProcessRequest = '/TRANSFERPROCESSREQUEST';
  final String loadTransferDetails = '/LOADTRANSFERCONFIRM';
  final String getTagTransferVehicleLists = '/GETLOADTRANSFERDETAILS';
  final String tagTransferConfirm = '/TRANSFERPROCESSCONFIRM';
  final String getLoadTransferHistory = '/GETLOADTRANSFERHISTORY';
  final String tagTransferCancel = '/TRANSFERPROCESSCANCEL';

  //REFUND
  final String getAllRefundablesService = '/LOADREFUNDABLESSERVICE';
  final String getRefundablesTopUp = '/LOADREFUNDABLESTOPUP';
  final String loadOrderHistoryTopup = '/LOADHISTORYTOPUP';
  final String loadOrderHistoryService = '/LOADHISTORYSERVICE';
  final String submitRefundFormService = '/SUBMITREFUNDFORMSERVICE';

  final String submitRefundFormTopup = '/SUBMITREFUNDFORMTOPUP';
  final String getReservedTopup = '/GETRESERVEDTOPUPBALANCE';
  final String getCurrentTopup = '/GETCURRENTTOPUPBALANCE';
  final String cancelOrderHistory = '/CANCELORDERHISTORY';
  final String cancelTopupHistory = '/CANCELTOPUPHISTORY';

  //PURCHASE HISTORY
  final String getPurchaseHistory = '/PURCHASEHISTORY';
  final String cancelPurchaseHistory = '/CANCELRESCODES';

  //AFFILIATE
  final String addAffiliates = '/ADDAFFILIATE';
  final String cancelAffiliates = '/CANCELAFFILIATE';
  final String getCurrentAffiliates = '/GETCURRENTAFFILIATE';
  final String getAffiliateRequestHistory = '/GETAFFILIATEHISTORY';

  //DASHBOARD
  final String getBalances = '/BALANCECOUNT';
  final String getServiceCount = '/SERVICECOUNT';
  final String getNews = '/GETNEWS';
  final String getGasStnCount = '/GASSTATIONSCOUNT';
  final String getImageSlide = '/SLIDES';
  final String getGasStnNews = '/STATIONSLIDES/';

  //B2B
  final String getB2BSENDER = '/B2BSENDER/';

  //BALANCE
  final String createBalOrder = '/CREATEBALORDER';
  final String PRMBalOrder = '/VALIDATEBALOTP';

  //REGISTRATION
  final String registerEmail = '/CUSTSIGNUP';
  final String getCities = '/GETCITIES';
  final String getServiceKnows = '/GETSERVICEKNOWN';
  final String otpVerify = '/OTPVERIFY';
  final String signupResendOTP = '/SIGNUPRESENDOTP';

  //ORDER
  final String getPriceTagCard = '/GETPRICE';
  final String orderContinue = '/ORDERCONTINUE';
  final String newServiceOrder = '/NEWSERVICEORDER';
  final String newBalOrder = '/NEWBALORDER';
  final String validateSTCPaySubmit = '/VALIDATESTCPAYSUBMIT';
  final String createCheckoutSession = '/CREATECHECKOUTSESSION';
  final String prepareMADA = '/PREPAREMADA';
  final String confirmOrder = '/CONFIRMORDER';
  final String createOrderMADA = '/CREATEORDER';
  final String createPromotionOrder = '/CREATEPROMOTIONLORDER';
  final String newGetPrice = '/GETQTYPRICE';
  final String createSingleMADAOrder = '/CONFIRMREALTIMEMADA';
  final String generateVirtualAccount = '/GENERATEVIRTUALACCOUNT';
  final String sendAlrajhiApplePayReq = '/SENDALRAJHIAPPLEPAYREQ';

  //PROFILE
  final String loadQueryData = '/LOADQUERYDATA';
  final String updateProfileDetails = '/PROFILEUPDATE';
  final String loadProfileDetails = '/LOADPROFILEDETAIL';

  //PROMO CODE
  final String activatePromoCode = '/ACTIVATE_PROMO_CODE';
  final String PromoHistory = '/PROMO_HISTORY';

  //DONATION
  final String getDonations = '/GETDONATIONS';

  //ORDER HISTORY
  final String getOrderHistory = "/ORDERHISTORY";

  //ACCOUNT SETTING
  final String changeEmail = '/CHANGEEMAIL';
  final String changePassword = '/CHANGEPASSWORD';
  final String checkPassword = '/CHECKPASSWORD';

  // EMail Notification
  final String getEmailNotyList = '/LOADEMAILNOTYLIST';
  final String getCustEmailNotyList = '/LOADECUSTMAILNOTY';
  final String updateCustNoty = '/UPDATEEMAILNOTY';
  final String saveDeviceToken = '/SAVEDEVICETOKEN';

  // User Manu List
  final String getUserMenu = '/MENUNODES';
  final String getUserAccess = '/USERACCESS';
  final String getMenuNodes = '/GETMENUNODES';
  final String getOrgLevelNodes = '/GETORGLEVELNODES';
  final String saveUser = '/SAVEUSER';
  final String getUserList = '/GETUSERLIST';
  final String submitCloseAccountRequest = '/SUBMITCLOSEACCOUNTREQUEST';

  //Subscription
  final String hasSubscrition = '/HASSERVICEFEE';
  final String activatePremiumSubs = '/ENABLEDISABLEPREMIUM';

  //Locations
  final String getStnLocation = '/LOADSTNLOCATION/';
  final String getWAIEStnLocation = '/LOADWAIESTN';
  final String getCenterLocation = '/LOADINSTLOCATION';
  final String getStnWaie = '/GETSTNWAIE';
  final String getStnCarService = '/GETSTNCARSERVICE';
  final String getStnMosque = '/GETSTNMOSQUE';
  final String getStnFoodRes = '/GETSTNFOODRES';
  final String getStnCarRental = '/GETSTNCARRENT';
  final String getStnBankAtm = '/GETSTNATM';
  final String getStationsByFuelType = '/GetStationsByFuelType';

  // Notification
  final String getGenNotification = '/GETNOTIFICATION';
  final String getNotyCount = '/NOTYCOUNT';

  //Digital Coupon
  final String verifyDriverMobileNo = '/VERIFYDRIVERMOBILE';
  final String getDriverServicesDetails = '/SERVICEDETAILS';

  //WS APPOINTMENT
  final String loadOrderList = '/LOADORDERLIST';
  final String loadSelectedOrderList = '/ORDERDETAILLIST';

  // FLEET STRUCTURE
  final String loadDivision = '/LOADDIVISION';
  final String loadBranch = '/LOADBRANCH';
  final String loadDepartment = '/LOADDEPARTMENT';
  final String loadOperation = '/LOADOPERATION';
  final String groupingProcessAdd = '/GROUPINGPROCESSADD';
  final String groupingProcessEdit = '/GROUPINGPROCESSEDIT';
  final String groupingProcessDelete = '/GROUPINGPROCESSDELETE';
  final String isCustHasHoldRefill = '/ISCUSTHASHOLDREFILL';
  final String holdingProcess = '/HOLDINGPROCESS';
  final String activatePinblock = '/ACTIVATEPINBLOCK';

  // Filter
  final String getAULookups = '/GETAULOOKUPS';

  final String getDCStatus = '/ISDCACTIVE';

  //REPORTS
  final String reportReqSubmit = '/REPORTSUBMIT';
  final String reportLoadPlaces = '/REPORTLOADWAIEPLACES';
  final String reportLoadProducts = '/REPORTLOADPRODUCTTYPE';
  final String reportLoadDivision = '/REPORTLOADCUSTDIV';
  final String reportLoadBranch = '/REPORTLOADCUSTBRANCH';
  final String reportLoadDept = '/REPORTLOADCUSTDEPT';
  final String reportLoadOperation = '/REPORTLOADCUSTOPERATION';
  final String reportLoadStation = '/REPORTLOADCUSTSTATIONS';
  final String reportLoadDriver = '/REPORTLOADCUSTDRIVER';
  final String reportLoadVehicle = '/REPORTLOADCUSTVEHICLE';
  final String reportLoadPlates = '/REPORTLOADCUSTPLATES';
  final String getDictionary = '/GETDICTIONARY';
  final String getMobTheme = '/MOBTHEME';
  final String authGoogle = '/AUTHGOOGLE';

  //In AppReview State
  final String inAppReviewState = "/INAPPREVIEW/";
  final String insertDictionary = '/INSERTDICTIONARY';
}
