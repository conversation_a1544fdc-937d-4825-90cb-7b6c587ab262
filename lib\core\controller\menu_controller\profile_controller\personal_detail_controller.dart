import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';

class PersonalDetailController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  TextEditingController fNameController = TextEditingController();
  TextEditingController mNameController = TextEditingController();
  TextEditingController lNameController = TextEditingController();
  TextEditingController mobileNoController = TextEditingController();
  List waieList = ['Aldrees Website'];
  RxString selectedWaie = 'Aldrees Website'.obs;

  getstoredProfileDetails() async {
    var custData = custsData.read('custData');
    print("PersonalDetailController custData>>>>>>> $custData");
    fNameController.text = custData['FIRSTNAME'];
    mNameController.text = custData['MIDNAME'];
    lNameController.text = custData['LASTNAME'];
    mobileNoController.text = custData['MOBILENO'];
  }

  Future<void> updateProfileDetail() async {
    showDialog(
      barrierDismissible: false,
      context: Get.context!,
      builder: (context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
    var custData = custsData.read('custData');
    print("cancelCompanyAffiliateRequest custData>>>>>>> $custData");
    var client = http.Client();
    print("fNameController.text >>>>>>>>> ${fNameController.text}");
    print("mNameController.text >>>>>>>>> ${mNameController.text}");
    print("lNameController.text >>>>>>>>> ${lNameController.text}");
    print("mobileNoController.text >>>>>>>>> ${mobileNoController.text}");
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.updateProfileDetails),
          body: {
            "ACTION": "personal",
            "CUSTID": custData['CUSTID'] ?? "",
            "EMAILID": custData['EMAILID'] ?? "",
            "COMPANYNAME": custData['COMPANYNAME'] ?? "",
            "COMPANYNAMEAR": custData['COMPANYNAMEAR'] ?? "",
            "CONTACTPERSON": custData['CONTACTPERSON'] ?? "",
            "DESIGNATION": custData['DESIGNATION'] ?? "",
            "CRNO": custData['CRNO'] ?? "",
            "VAT_NO": custData['VAT_NO'] ?? "",
            "SALESMAN__CODE": custData['SELECTEDSALESMAN'] ?? "",
            "COUNTRY_CODE": custData['SELECTEDCOUNTRY'] ?? "",
            "REGION_CODE": custData['SELECTEDREG'] ?? "",
            "CITY_CODE": custData['SELECTEDCITY'] ?? "",
            "DISTRICT_CODE": custData['SELECTEDDISTRICT'] ?? "",
            "STREET": custData['STREET'] ?? "",
            "BUILDING_NO": custData['BUILDING_NO'] ?? "",
            "MOBILENO": mobileNoController.text,
            "LANG": custData['SELECTEDLANG'] ?? "",
            "SERVICEKNOWN_CODE": custData['SERVICEKNOWN_CODE'] ?? "",
            "POSTALCODE": custData['POSTALCODE'] ?? "",
            "POBOX": custData['POBOX'] ?? "",
            "FIRSTNAME": fNameController.text,
            "MIDNAME": mNameController.text,
            "LASTNAME": lNameController.text,
            "COMPANYTEL": custData['COMPANYTEL'] ?? "",
            "COMPANYFAX": custData['COMPANYFAX'] ?? "",
            "HOUSE_NO": custData['HOUSE_NO'] ?? "",
            "ID_TYPE": custData['SELECTEDIDTYPE'] ?? "",
            "ID_NUMBER": custData['ID_NUMBER'] ?? "",
            "GPS": custData['GPS'] ?? "",
            "FUEL91": custData['FUEL91'] ?? "",
            "FUEL95": custData['FUEL95'] ?? "",
            "DIESEL": custData['DIESEL'] ?? "",
            "OTHERS": custData['OTHERS'] ?? "",
          });
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Navigator.of(Get.context!).pop();
        print(
            "cancelCompanyAffiliateRequest response.statusCode>>>>>>> ${response.statusCode}");
        print("cancelCompanyAffiliateRequest custData>>>>>>> $custData");
        mobileNoController.clear();
        fNameController.clear();
        mNameController.clear();
        lNameController.clear();
        custsData.remove('custData');
        await Get.to(
            () => DashBoardManagerScreen(
                  currantIndex: 0,
                ),
            preventDuplicates: false);
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
