import 'dart:convert';

List<LoadPlaces> loadPlacesFromJson(String str) =>
    List<LoadPlaces>.from(json.decode(str).map((x) => LoadPlaces.fromJson(x)));

String loadPlacesToJson(List<LoadPlaces> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class LoadPlaces {
  String placeCode;
  bool selectedPlace;
  String placeName;
  List<Station> stations;

  LoadPlaces({
    required this.placeCode,
    required this.selectedPlace,
    required this.placeName,
    required this.stations,
  });

  factory LoadPlaces.fromJson(Map<String, dynamic> json) => LoadPlaces(
        placeCode: json["PlaceCode"],
        selectedPlace: json["SelectedPlace"] ?? false,
        placeName: json["PlaceName"],
        stations: List<Station>.from(
            json["Stations"].map((x) => Station.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "PlaceCode": placeCode,
        "PlaceName": placeName,
        "Stations": List<dynamic>.from(stations.map((x) => x.toJson())),
      };
}

class Station {
  String placeCode;
  bool selectedStation;
  String stationNo;
  String stationName;

  Station({
    required this.placeCode,
    required this.selectedStation,
    required this.stationNo,
    required this.stationName,
  });

  factory Station.fromJson(Map<String, dynamic> json) => Station(
        placeCode: json["PlaceCode"],
        selectedStation: json["SelectedStation"] ?? false,
        stationNo: json["StationNo"],
        stationName: json["StationName"],
      );

  Map<String, dynamic> toJson() => {
        "PlaceCode": placeCode,
        "StationNo": stationNo,
        "StationName": stationName,
      };
}
