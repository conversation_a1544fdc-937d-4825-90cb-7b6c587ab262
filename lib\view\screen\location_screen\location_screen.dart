// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'dart:io';

import 'package:flutter/material.dart';
//import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/location_controller/gas_station_controller.dart';
import 'package:waie_app/core/controller/location_controller/sales_office_controller.dart';
import 'package:waie_app/core/controller/splash_controller/splash_controller.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/location_screen/bank_atm_screen.dart';
import 'package:waie_app/view/screen/location_screen/car_rental_screen.dart';
import 'package:waie_app/view/screen/location_screen/car_service_screen.dart';
import 'package:waie_app/view/screen/location_screen/food_resturant_screen.dart';
import 'package:waie_app/view/screen/location_screen/installation_center_screen.dart';
import 'package:waie_app/view/screen/location_screen/mosque_screen.dart';
import 'package:waie_app/view/screen/location_screen/sales_office_screen.dart';
import 'package:waie_app/view/screen/location_screen/stations_screen.dart';
import '../../../core/controller/location_controller/installation_center_controller.dart';
import '../../../core/controller/location_controller/location_controller.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/location_screen/gase_station_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import '../../../utils/colors.dart';
import '../../../utils/images.dart';
import 'search_location_widget.dart';

class LocationScreen extends StatefulWidget {
  const LocationScreen({super.key});

  @override
  State<LocationScreen> createState() => _LocationScreenState();
}

class _LocationScreenState extends State<LocationScreen> {
  LocationController locationController = Get.put(LocationController());
  GasStationController gasStationController = Get.put(GasStationController());
  SalesOfficeController salesOfficeController =
      Get.put(SalesOfficeController());
  InstallationCenterController installationCenterController =
      Get.put(InstallationCenterController());

  var isActive = "";

  @override
  void initState() {
    super.initState();
    _loadHomeImage();
  }

  SplashController splashController = Get.put(SplashController());
  String homeLogo = '';
  Future<void> _loadHomeImage() async {
    String path = await splashController.loadImage('MOBLOGOIMGS');
    setState(() {
      homeLogo = path;
    });
  }
  // Future<void> checkPermission(
  //     Permission permission, BuildContext context) async {
  //   final status = await permission.request();

  //   if (status.isGranted) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(content: Text("Location Permission is Granted")));
  //   } else {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(content: Text("Location Permission is Denied")));
  //   }
  // }

  // @override
  // void initState() {
  //   // TODO: implement initState
  //   super.initState();
  //   if (Platform.isIOS) {
  //     checkPermission(Permission.location, context);
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalSpace(7),
            // Center(
            //   child: Image.asset(
            //     'asset/image/image/loginnationaldaylogo.jpg',
            //     height: 35,
            //     fit: BoxFit.cover,
            //   ),
            // ),

            Center(
              child: homeLogo.isNotEmpty
                  ? Image.network(
                      homeLogo,
                      height: 35,
                      fit: BoxFit.cover,
                    )
                  : Image.asset(
                      'asset/image/image/logotransparent.png',
                      height: 35,
                      fit: BoxFit.cover,
                    ),
            ),
            Container(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.offAll(() => DashBoardManagerScreen(currantIndex: 0));
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        top: 15,
                        bottom: 16,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          assetSvdImageWidget(
                              image: DefaultImages.backIcn,
                              colorFilter: ColorFilter.mode(
                                  AppColor.cDarkBlueFont, BlendMode.srcIn)),
                          horizontalSpace(10),
                          Text(
                            "Back".trr,
                            style: pRegular18.copyWith(
                                color: AppColor.cDarkBlueFont, fontSize: 17),
                            textAlign: TextAlign.start,
                          )
                        ],
                      ),
                    ),
                  ),
                  // horizontalSpace(35),
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        "Locations".trr,
                        style: pBold20,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          barrierColor: AppColor.cBlackOpacity,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(12))),
                          isScrollControlled: true,
                          builder: (context) {
                            return SearchLocationWidget(
                              isActive: isActive,
                            );
                          },
                        );
                      },
                      child: assetSvdImageWidget(
                          image: DefaultImages.searchCircleIcn))
                ],
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Obx(() {
                  return Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: SizedBox(
                          height: Get.height * 0.06,
                          child: ListView(
                            shrinkWrap: true,
                            scrollDirection: Axis.horizontal,
                            children: [
                              locationTabWidget(
                                title: 'Aldrees Gas stations'.trr,
                                onTap: () {
                                  locationController.isGasStations.value = true;
                                  locationController.isSalesOffice.value =
                                      false;
                                  locationController
                                      .isInstallationCenters.value = false;
                                  locationController.isStations.value = false;
                                  locationController.isCarService.value = false;
                                  locationController.isMosque.value = false;
                                  locationController.isFoodResturant.value =
                                      false;
                                  locationController.isCarRental.value = false;
                                  locationController.isBankAtm.value = false;

                                  setState(() {
                                    isActive = "isGasStations";
                                  });
                                },
                                isSelected:
                                    locationController.isGasStations.value,
                              ),
                              locationTabWidget(
                                  title: 'WAIE Sales Offices'.trr,
                                  onTap: () {
                                    locationController.isGasStations.value =
                                        false;
                                    locationController.isSalesOffice.value =
                                        true;
                                    locationController
                                        .isInstallationCenters.value = false;
                                    locationController.isStations.value = false;
                                    locationController.isCarService.value =
                                        false;
                                    locationController.isMosque.value = false;
                                    locationController.isFoodResturant.value =
                                        false;
                                    locationController.isCarRental.value =
                                        false;
                                    locationController.isBankAtm.value = false;

                                    setState(() {
                                      isActive = "isSalesOffice";
                                    });
                                  },
                                  isSelected:
                                      locationController.isSalesOffice.value),
                              locationTabWidget(
                                title: 'Installation centers'.trr,
                                onTap: () {
                                  locationController.isGasStations.value =
                                      false;
                                  locationController.isSalesOffice.value =
                                      false;

                                  locationController
                                      .isInstallationCenters.value = true;
                                  locationController.isStations.value = false;
                                  locationController.isCarService.value = false;
                                  locationController.isMosque.value = false;
                                  locationController.isFoodResturant.value =
                                      false;
                                  locationController.isCarRental.value = false;
                                  locationController.isBankAtm.value = false;

                                  setState(() {
                                    isActive = "isInstallationCenters";
                                  });
                                },
                                isSelected: locationController
                                    .isInstallationCenters.value,
                              ),
                              if (Constants.isSTNWAIE == "Y")
                                locationTabWidget(
                                  title: 'Stations'.trr,
                                  onTap: () {
                                    locationController.isGasStations.value =
                                        false;
                                    locationController.isSalesOffice.value =
                                        false;
                                    locationController
                                        .isInstallationCenters.value = false;
                                    locationController.isStations.value = true;
                                    locationController.isCarService.value =
                                        false;
                                    locationController.isMosque.value = false;
                                    locationController.isFoodResturant.value =
                                        false;
                                    locationController.isCarRental.value =
                                        false;
                                    locationController.isBankAtm.value = false;

                                    setState(() {
                                      isActive = "isStations";
                                    });
                                  },
                                  isSelected:
                                      locationController.isStations.value,
                                ),
                              if (Constants.isSTNCARSERVICE == "Y")
                                locationTabWidget(
                                  title: 'Car Service'.trr,
                                  onTap: () {
                                    locationController.isGasStations.value =
                                        false;
                                    locationController.isSalesOffice.value =
                                        false;
                                    locationController
                                        .isInstallationCenters.value = false;
                                    locationController.isStations.value = false;
                                    locationController.isCarService.value =
                                        true;
                                    locationController.isMosque.value = false;
                                    locationController.isFoodResturant.value =
                                        false;
                                    locationController.isCarRental.value =
                                        false;
                                    locationController.isBankAtm.value = false;

                                    setState(() {
                                      isActive = "isCarService";
                                    });
                                  },
                                  isSelected:
                                      locationController.isCarService.value,
                                ),
                              if (Constants.isSTNMOSQ == "Y")
                                locationTabWidget(
                                  title: 'Mosque'.trr,
                                  onTap: () {
                                    locationController.isGasStations.value =
                                        false;
                                    locationController.isSalesOffice.value =
                                        false;
                                    locationController
                                        .isInstallationCenters.value = false;
                                    locationController.isStations.value = false;
                                    locationController.isCarService.value =
                                        false;
                                    locationController.isMosque.value = true;
                                    locationController.isFoodResturant.value =
                                        false;
                                    locationController.isCarRental.value =
                                        false;
                                    locationController.isBankAtm.value = false;

                                    setState(() {
                                      isActive = "isMosque";
                                    });
                                  },
                                  isSelected: locationController.isMosque.value,
                                ),
                              if (Constants.isSTNFOODRES == "Y")
                                locationTabWidget(
                                  title: 'Food Resturants'.trr,
                                  onTap: () {
                                    locationController.isGasStations.value =
                                        false;
                                    locationController.isSalesOffice.value =
                                        false;
                                    locationController
                                        .isInstallationCenters.value = false;
                                    locationController.isStations.value = false;
                                    locationController.isCarService.value =
                                        false;
                                    locationController.isMosque.value = false;
                                    locationController.isFoodResturant.value =
                                        true;
                                    locationController.isCarRental.value =
                                        false;
                                    locationController.isBankAtm.value = false;

                                    setState(() {
                                      isActive = "isFoodResturant";
                                    });
                                  },
                                  isSelected:
                                      locationController.isFoodResturant.value,
                                ),
                              if (Constants.isSTNCARRENT == "Y")
                                locationTabWidget(
                                  title: 'Car Rental'.trr,
                                  onTap: () {
                                    locationController.isGasStations.value =
                                        false;
                                    locationController.isSalesOffice.value =
                                        false;
                                    locationController
                                        .isInstallationCenters.value = false;
                                    locationController.isStations.value = false;
                                    locationController.isCarService.value =
                                        false;
                                    locationController.isMosque.value = false;
                                    locationController.isFoodResturant.value =
                                        false;
                                    locationController.isCarRental.value = true;
                                    locationController.isBankAtm.value = false;

                                    setState(() {
                                      isActive = "isCarRental";
                                    });
                                  },
                                  isSelected:
                                      locationController.isCarRental.value,
                                ),
                              if (Constants.isSTNATM == "Y")
                                locationTabWidget(
                                  title: 'Bank ATM'.trr,
                                  onTap: () {
                                    locationController.isGasStations.value =
                                        false;
                                    locationController.isSalesOffice.value =
                                        false;
                                    locationController
                                        .isInstallationCenters.value = false;
                                    locationController.isStations.value = false;
                                    locationController.isCarService.value =
                                        false;
                                    locationController.isMosque.value = false;
                                    locationController.isFoodResturant.value =
                                        false;
                                    locationController.isCarRental.value =
                                        false;
                                    locationController.isBankAtm.value = true;

                                    setState(() {
                                      isActive = "isBankAtm";
                                    });
                                  },
                                  isSelected:
                                      locationController.isBankAtm.value,
                                ),
                            ],
                          ),
                        ),
                      ),
                      // locationController.isGasStations.value == true
                      //     ? GasStationScreen()
                      //     : locationController.isSalesOffice.value == true
                      //         ? SalesOfficeScreen()
                      //         : InstallationCenterScreen()

                      locationController.isGasStations.value
                          ? GasStationScreen()
                          : locationController.isSalesOffice.value
                              ? SalesOfficeScreen()
                              : locationController.isInstallationCenters.value
                                  ? InstallationCenterScreen()
                                  : locationController.isStations.value
                                      ? StationsScreen()
                                      : locationController.isCarService.value
                                          ? CarServiceScreen()
                                          : locationController.isMosque.value
                                              ? MosqueScreen()
                                              : locationController
                                                      .isFoodResturant.value
                                                  ? FoodResturantScreen()
                                                  : locationController
                                                          .isCarRental.value
                                                      ? CarRentalScreen()
                                                      : locationController
                                                              .isBankAtm.value
                                                          ? BankAtmScreen()
                                                          : Container()
                    ],
                  );
                }),
              ),
            )
          ],
        ),
      ),
    );
  }
}

Widget locationTabWidget({
  String? title,
  double? width,
  bool? isSelected,
  Function()? onTap,
}) {
  return SizedBox(
    width: width ?? Get.width / 2.3,
    child: GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Text(
            title!,
            style: pSemiBold17.copyWith(
              color:
                  isSelected == true ? AppColor.cText : AppColor.cDarkGreyFont,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          verticalSpace(8),
          Container(
            // width: Get.width/2,
            height: isSelected == true ? 3 : 1,
            color: isSelected == true
                ? AppColor.themeOrangeColor
                : AppColor.cIndicator,
          )
        ],
      ),
    ),
  );
}

Widget commonLocationTab(
    {required Function() onTap,
    required String text,
    required String icon,
    required Color btnColor,
    required Color textColor}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width / 2.5,
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
          color: btnColor, borderRadius: BorderRadius.circular(4)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          assetSvdImageWidget(image: icon),
          horizontalSpace(8),
          Text(
            text,
            style: pRegular14.copyWith(fontSize: 15, color: textColor),
          ),
        ],
      ),
    ),
  );
}
