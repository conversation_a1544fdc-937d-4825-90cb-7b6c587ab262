// ignore_for_file: prefer_const_constructors, must_be_immutable, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/current_balance_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/submit_order_refund_service_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/submit_order_refund_topup_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/new_affiliate_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/bank_ac_topup_detail.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import '../../auth/digital_coupon/create_dc_screen.dart';
import 'bank_ac_detail.dart';

class SubmitTopupScreen extends StatelessWidget {
  SubmitTopupScreen({super.key});
  final topupOrderRefund = GetStorage();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  RefundsController refundsController = Get.put(RefundsController());
  SubmitOrderRefundTopupController submitOrderRefundTopupController =
      Get.put(SubmitOrderRefundTopupController());

  //CurrentBalanceController currentBalanceController = Get.find();

  @override
  Widget build(BuildContext context) {
    var chkRES = topupOrderRefund.read('chkRES');
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 16, right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                          top: 15,
                          bottom: 15,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.backIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cDarkBlueFont, BlendMode.srcIn)),
                            horizontalSpace(10),
                            Text(
                              "Back".trr,
                              style: pRegular18.copyWith(
                                  color: AppColor.cDarkBlueFont, fontSize: 17),
                              textAlign: TextAlign.start,
                            )
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "Order Refund Details".trr,
                          style: pBold20,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Obx(() {
                  return ListView(
                    scrollDirection: Axis.vertical,
                    shrinkWrap: true,
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    children: [
                      verticalSpace(24),
                      Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: AppColor.cLightGrey),
                            color: AppColor.lightBlueColor),
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          children: [
                            // Gap(12),
                            // Row(
                            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //   children: [
                            //     Text(
                            //       'Current Balance'.trr + ": ",
                            //       style: pSemiBold17,
                            //     ),
                            //     Text(
                            //       chkRES == "true"
                            //           ? "${currentBalanceController.reservedBalance} SAR"
                            //           : "${currentBalanceController.currentBalance} SAR",
                            //       style: pSemiBold17,
                            //     ),
                            //   ],
                            // ),
                            Gap(12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Total Amount'.trr + ": ",
                                  style: pSemiBold17,
                                ),
                                Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      assetSvdImageWidget(
                                          image: DefaultImages.saudiRiyal,
                                          width: 16,
                                          height: 16),
                                      Gap(4),
                                      Text(
                                          topupOrderRefund
                                              .read('topupTotaltAmt')
                                              .toString(),
                                          style: pSemiBold17),
                                    ],
                                  ),
                                ),
                                // Text(
                                //   "${topupOrderRefund.read('topupTotaltAmt').toString()} SAR",
                                //   style: pSemiBold17,
                                // ),
                              ],
                            ),
                            Gap(12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'VAT'.trr + ": ",
                                  style: pSemiBold17,
                                ),
                                Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      assetSvdImageWidget(
                                          image: DefaultImages.saudiRiyal,
                                          width: 16,
                                          height: 16),
                                      Gap(4),
                                      Text(
                                          topupOrderRefund
                                              .read('topupTotaltVAT')
                                              .toString(),
                                          style: pSemiBold17),
                                    ],
                                  ),
                                ),
                                // Text(
                                //   "${topupOrderRefund.read('topupTotaltVAT').toString()} SAR",
                                //   style: pSemiBold17,
                                // ),
                              ],
                            ),
                            horizontalDivider(),
                            Gap(12),
                          ],
                        ),
                      ),
                      verticalSpace(24),
                      Row(
                        children: [
                          /* Expanded(
                            flex: 1,
                            child: selectRadioWidget(
                                onTap: () {
                                  refundsController.isBalance.value = true;
                                  refundsController.isDeposit.value = false;
                                },
                                icon: refundsController.isBalance.value == true
                                    ? DefaultImages.checkCircleIcn
                                    : DefaultImages.circleIcn,
                                title: "Convert to balance".trr,
                                borderColor:
                                    refundsController.isBalance.value == true
                                        ? AppColor.themeDarkBlueColor
                                        : AppColor.cBorder),
                          ),
                          horizontalSpace(16),*/
                          Expanded(
                            flex: 1,
                            child: selectRadioWidget(
                                onTap: () {
                                  refundsController.isBalance.value = false;
                                  refundsController.isDeposit.value = true;
                                },
                                icon: refundsController.isDeposit.value == true
                                    ? DefaultImages.checkCircleIcn
                                    : DefaultImages.circleIcn,
                                title: "Debit Note".trr,
                                borderColor:
                                    refundsController.isDeposit.value == true
                                        ? AppColor.themeDarkBlueColor
                                        : AppColor.cBorder),
                          ),
                        ],
                      ),
                      verticalSpace(24),
                      refundsController.isBalance.value == true
                          ? convertToBalanceWidget(context)
                          : BankAcTopupDetailWidget(
                              isDebit: refundsController.isDeposit.value),
                    ],
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget convertToBalanceWidget(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          CommonTextField(
            controller: submitOrderRefundTopupController.reasonRefundController,
            labelText: "Reason for refund".trr,
            hintText: "Let us know why you need a refund".trr,
            maxLines: 5,
          ),
          verticalSpace(24),
          Container(
            padding: EdgeInsets.symmetric(vertical: 7, horizontal: 16),
            decoration: BoxDecoration(
                color: AppColor.lightOrangeColor,
                borderRadius: BorderRadius.circular(6)),
            child: Text(
                "!  " +
                    "Refunded orders don't count towards your bulk discount on your next order."
                        .trr,
                style: pRegular13.copyWith(color: AppColor.cDarkOrangeText)),
          ),
          verticalSpace(24),
          CommonButton(
            title: "Submit".trr,
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                if (refundsController.isBalance.value == true) {
                  submitOrderRefundTopupController.rblRefundOPT.value = "B";
                }
                submitOrderRefundTopupController.submitOrderRefundFormB();
              }
              // Get.back();
              // showDialog(
              //   context: context,
              //   builder: (context) {
              //     return AlertDialog(
              //       shape: RoundedRectangleBorder(
              //           borderRadius: BorderRadius.circular(12)),
              //       insetPadding: EdgeInsets.all(16),
              //       contentPadding: EdgeInsets.all(24),
              //       content: successDialogWidget(
              //           title: "You've submitted a refund request".trr,
              //           subTitle:
              //               "We'll let you know when it's been approved.".trr, () {
              //         Get.back();
              //       }, isBorderBtn: true),
              //     );
              //   },
              // );
            },
            btnColor: AppColor.themeOrangeColor,
          ),
        ],
      ),
    );
  }
}
