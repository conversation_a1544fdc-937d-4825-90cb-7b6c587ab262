import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class ChangePlateWidget extends StatefulWidget {
  const ChangePlateWidget({super.key});

  @override
  State<ChangePlateWidget> createState() => _ChangePlateWidgetState();
}

class _ChangePlateWidgetState extends State<ChangePlateWidget> {
  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
