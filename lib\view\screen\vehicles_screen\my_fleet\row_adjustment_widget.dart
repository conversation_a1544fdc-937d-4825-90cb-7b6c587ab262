// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/new_user_screen.dart';

class RowAdjustmentWidget extends StatelessWidget {
  RowAdjustmentWidget({super.key});

  VehicleController vehicleController = Get.find();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Rows adjustment".trr,
              style: pBold20,
            ),
            verticalSpace(21),
            ListView.builder(
              physics: NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: vehicleController.rowAdjustmentList.length,
              itemBuilder: (context, index) {
                var data = vehicleController.rowAdjustmentList[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4.0),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Obx(() {
                      return Expanded(
                        child: Row(
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.arrangementIcn),
                            horizontalSpace(12),
                            CustomSwitch(
                              value: data['isStatus'].value,
                              onChanged: (value) {
                                data['isStatus'].value = value;
                              },
                            ),
                            horizontalSpace(8),
                            Text(
                              data['title'].toString().trr,
                              style: pRegular13,
                            )
                          ],
                        ),
                      );
                    }),
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
