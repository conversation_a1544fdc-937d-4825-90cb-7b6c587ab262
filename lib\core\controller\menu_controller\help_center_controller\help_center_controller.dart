import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/images.dart';

class HelpCenterController extends GetxController {
  TextEditingController controller = TextEditingController();
  List helpValueList = ['General Questions', 'Download Documents', 'Video Tutorials'];
  RxString helpValue = 'General Questions'.obs;
  List generalQusList = [
    {
      "questions": "What is Waie system?".tr,
      "answer":
          "It is an electronic system that replaces paper transactions and invoices and controls monthly fuel expenses for companies and individuals.".tr
    },
    {
      "questions": "How does Waie system work?".tr,
      "answer":
          "It is an electronic fuel refueling system, in which communication takes place between the nozzle reader and the electronic tag within a short range, through which authorization is given for filling according to the data recorded in the system.".tr
    },
    {"questions": "What are the advantages of WAIE system?".tr, "answer": "answer3"},
    {"questions": "What types of services are offered".tr, "answer": "answer4"},
    {"questions": "How can one benefit from the WAIE system?".tr, "answer": "You can benefit from the system by opening an account, requesting a card or tag, and top-up the account.".tr},
  ];
  List videoList = [
    {"image": DefaultImages.videoImage, "title": "WAIE Registration , Service Purchase and TOPUP"},
    {"image": DefaultImages.videoImage, "title": "WAIE Registration , Service Purchase and TOPUP"},
    {"image": DefaultImages.videoImage, "title": "WAIE Registration , Service Purchase and TOPUP"},
  ];
  RxString isoCode = 'SA'.obs;
  RxList itemList = [].obs;
  var searchController = TextEditingController().obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    searchController.value = TextEditingController();
  }
}
