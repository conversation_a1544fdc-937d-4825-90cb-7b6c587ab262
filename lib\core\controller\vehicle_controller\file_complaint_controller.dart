import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/models/complaint_reason.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:quickalert/quickalert.dart';

import '../../../utils/constants.dart';
import '../../../view/widget/loading_widget.dart';

class FileComplaintController extends GetxController {
  VehicleController vehicleController = Get.put(VehicleController());
  GetStorage userStorage = GetStorage('User');
  final vehicle = GetStorage();
  final isPinblock = GetStorage();
  final isDCBlock = GetStorage();
  TextEditingController carController = TextEditingController();
  TextEditingController problemController = TextEditingController();
  TextEditingController reasonController = TextEditingController();
  TextEditingController complaintCityController = TextEditingController();
  TextEditingController complaintCenterController = TextEditingController();
  List reasonList = ['Tag issue', 'File issue'];
  RxString reasonValue = 'Tag issue'.obs;
  var reasonLists = <ComplaintReasonModel>[].obs;
  RxString selectedReason = ''.obs;
  List<String> cityList = [
    'Al-Darb',
    'Deira',
    'Bur Dubai',
    'Jumeirah',
    'Dubai Marina',
    'Downtown Dubai',
    'Palm Jumeirah',
    'Al Barsha',
    'Business Bay',
    'Jumeirah Lakes Towers (JLT)',
    'Dubai Silicon Oasis',
  ];
  RxString selectedCity = ''.obs;
  List centerList = [
    'NO CENTER FOUND',
    //'Bur Dubai',
  ];
  RxString centerValue = ''.obs;

  submitComplaint(serialid) async {
    // showDialog(
    //     context: Get.context!,
    //     builder: (context) {
    //       return const Center(
    //         child: CircularProgressIndicator(),
    //       );
    //     });
    Loader.showLoader();
    print("===============================================================");
    print("problemController.text >>>>> ${problemController.text}");
    print("reasonController.text >>>>> ${reasonController.text}");
    print("serialid >>>>> $serialid");
    print("===============================================================");

    var client = http.Client();

    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.fileComplaint),
          body: {
            "serialid": serialid,
            "reason": reasonController.text,
            "problem": problemController.text,
          });
      print("===============================================================");
      print("submitComplaint response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        problemController.clear();
        reasonController.clear();
        vehicle.remove('vehicleID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('complaintJobID');
        isPinblock.remove('isPinActivate');
        isDCBlock.remove('isdcBlock');
        vehicleController.selectedSerialList.clear();
        vehicleController.selectedVehicleList.clear();
        vehicleController.selectedFleetList.clear();
        vehicleController.filterValueList.refresh();
        vehicleController.selectedVehicleList.refresh();
        vehicleController.selectedSerialList.refresh();
        vehicleController.selectedFleetList.refresh();

        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.success,
          text:
              'Your complaint has been sent.\nPlease bear with us while we resolve the matter.',
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
      } else {
        //throw "COMPLAINT ALREADY EXISTS FOR THIS SERVICE!";
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          title: 'Error',
          text: 'Complaint already exists for this service!.',
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
      }
    } catch (e) {
      log(e.toString());
      showDialog(
          context: Get.context!,
          builder: (context) {
            return SimpleDialog(
              title: const Text('Error'),
              contentPadding: const EdgeInsets.all(20),
              children: [Text(e.toString())],
            );
          });
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  cancelComplaint(serialid) async {
    // showDialog(
    //     context: Get.context!,
    //     builder: (context) {
    //       return const Center(
    //         child: CircularProgressIndicator(),
    //       );
    //     });
    Loader.showLoader();
    print("===============================================================");
    print("serialid >>>>> $serialid");
    print("===============================================================");

    var client = http.Client();

    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.cancelComplaint),
          body: {
            "serialid": serialid,
          });
      print("===============================================================");
      print("cancelComplaint response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        problemController.clear();
        reasonController.clear();
        vehicle.remove('vehicleID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('complaintJobID');
        isPinblock.remove('isPinActivate');
        isDCBlock.remove('isdcBlock');
        vehicleController.selectedSerialList.clear();
        vehicleController.selectedVehicleList.clear();
        vehicleController.selectedFleetList.clear();
        vehicleController.filterValueList.refresh();
        vehicleController.selectedVehicleList.refresh();
        vehicleController.selectedSerialList.refresh();
        vehicleController.selectedFleetList.refresh();

        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.success,
          text: 'Your complaint has been cancelled.',
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
      } else {
        //throw "REQUEST NO LONGER AVAILABLE, PLEASE REFRESH!";
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          title: 'Error',
          text: 'Request no longer available, Please Refresh!.',
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
      }
    } catch (e) {
      log(e.toString());
      showDialog(
          context: Get.context!,
          builder: (context) {
            return SimpleDialog(
              title: const Text('Error'),
              contentPadding: const EdgeInsets.all(20),
              children: [Text(e.toString())],
            );
          });
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
