// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:get/get_connect/http/src/request/request.dart';
import 'package:http_parser/http_parser.dart';

import 'package:file_picker/src/file_picker_result.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:mime/mime.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_service_menu_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../utils/constants.dart';
import '../../../../view/screen/dashboard_manager/dashboard_manager.dart';

class SubmitOrderRefundServiceController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  var refundablesServices = <RefundableService>[].obs;

  Future<List<RefundableService>> getAllRefundablesService() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    print("OrderRefundsController custid>>>>>>> $custid");
    print("OrderRefundsController emailid>>>>>>> $emailid");
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getAllRefundablesService),
          body: {
            "custid": custid,
            "IsAR": Constants.IsAr_App,
          });
      List result = jsonDecode(response.body);

      print("OrderRefundsController result >>>>> $result");
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        RefundableService order =
            RefundableService.fromJson(result[i] as Map<String, dynamic>);
        refundablesServices.add(order);
      }
      print("===============================================================");
      print(
          "loadPlacesloadPlacesloadPlacesloadPlaces >>>>> ${jsonDecode(jsonEncode(refundablesServices))}");
      print("===============================================================");

      return refundablesServices;
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  submitFile(bankLetter, companyRegistration) async {
    var custData = jsonEncode(custsData.read('custData'));
    print("submitFile bankLetter $bankLetter");
    print("submitFile bankLetter ${bankLetter.name}");
    print("submitFile bankLetter ${bankLetter.readStream}");
    print("submitFile companyRegistration $companyRegistration");
    print("submitFile companyRegistration ${companyRegistration.name}");
    print("submitFile companyRegistration ${companyRegistration.readStream}");
    //submitFile bankLetter PlatformFile(, name: bank_letter.pdf, bytes: null, readStream: Instance of '_ControllerStream<List<int>>', size: 13264)
    // for (var element in bankLetter!.files) {
    //   print(element.name);
    //   print(element.identifier);
    //   print("submitFile bankLetter ${bankLetter.files.first.size}");
    // }
    final file = bankLetter;
    final mimeType = lookupMimeType(file.name) ?? '';
    // for (var element in companyRegistration!.files) {
    //   print(element.name);
    //   print(
    //       "submitFile companyRegistration ${companyRegistration.files.first.size}");
    // }

    var client = http.Client();
    try {
      // var response = await client.post(
      //     Uri.parse(
      //         ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.submitFile),
      //     body: {
      //       "custid": "000000054A",
      //       "IsAR": Constants.IsAr_App,
      //     });

      var bankletter = bankLetter.readStream;
      var companyregistration = companyRegistration.readStream;

      print("bankletter $bankletter");
      print("companyrvegistration $companyregistration");

      var request = http.MultipartRequest(
          'POST',
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getAllRefundablesService));
      var headers = {'Content-Type': 'application/pdf; charset=UTF-8'};

      request.files.add(http.MultipartFile(
          'bankletter', bankletter!, bankLetter.size,
          filename: bankLetter.name,
          contentType: MediaType('application', 'pdf')));

      bankletter.cast();

      request.files.add(http.MultipartFile(
          'companyregistration', companyregistration!, companyRegistration.size,
          filename: companyRegistration.name,
          contentType: MediaType('application', 'pdf')));
      companyregistration.cast();
      request.headers.addAll(headers);
      request.fields['serialids'] = '****************';
      request.fields['custdata'] = custData;
      request.fields['chkRes'] = 'false';
      request.fields['rblRefundOptions'] = 'Q';
      request.fields['txtReason'] = 'sample';
      request.fields['orderids'] = '2211900';
      request.fields['tAmt'] = '16.50';
      request.fields['tVAT'] = '16.50';
      request.fields['nationIdNo'] = '**********';
      request.fields['accountHolder'] = 'Sample Client';
      request.fields['ibanNo'] = '*****************';
      request.fields['selectedBankType'] = 'SA';
      request.fields['stype'] = 'T';
      request.fields['otype'] = 'S';
      var res = await request.send();
      // print("===============================================================");
      // print("res >>>>> ${jsonDecode(jsonEncode(res))}");
      // print("===============================================================");
      // List result = jsonDecode(response.body);

      // print("OrderRefundsController result >>>>> $result");
      // print("===============================================================");
      // print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      // print("===============================================================");

      // for (int i = 0; i < result.length; i++) {
      //   RefundableService order =
      //       RefundableService.fromJson(result[i] as Map<String, dynamic>);
      //   refundablesServices.add(order);
      // }
      // print("===============================================================");
      // print(
      //     "loadPlacesloadPlacesloadPlacesloadPlaces >>>>> ${jsonDecode(jsonEncode(refundablesServices))}");
      // print("===============================================================");
      // var response = await http.Response.fromStream(res);
      // print("===============================================================");
      // print("response >>>>> ${jsonDecode(response.body)}");
      // print("===============================================================");

      return res.stream.bytesToString().asStream().listen((event) {
        var parsedJson = json.decode(event);
        print(
            "===============================================================");
        print(parsedJson['response']['Action']);
        print(parsedJson['response']['Message']);
        print(
            "===============================================================");
        if (parsedJson['response']['Action'] == 'EXCEPTION') {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.end,
                    //   children: [
                    //     GestureDetector(
                    //         onTap: () {
                    //           Get.back();
                    //         },
                    //         child: assetSvdImageWidget(
                    //             image: DefaultImages.cancelIcn)),
                    //   ],
                    // ),
                    // verticalSpace(24),
                    Text(
                      parsedJson['response']['Message'].toString(),
                      style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                      textAlign: TextAlign.center,
                    ),
                    // verticalSpace(24),
                    // Text(
                    //   "Give us 48 hours to review your request. We’ll send you an email once it’s reviewed.".tr,
                    //   style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    //   textAlign: TextAlign.center,
                    // ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        //Get.offAll(() => OrderRefundServiceMenuScreen());
                        await Get.to(
                            () => DashBoardManagerScreen(
                                  currantIndex: 0,
                                ),
                            preventDuplicates: false);
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
      });
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
