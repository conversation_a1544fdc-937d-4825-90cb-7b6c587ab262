
import 'Check3DSecureEnrollmentCallback.dart';
import 'CompleteSessionCallback.dart';
import 'CreateSessionCallback.dart';


abstract class IMadaApiService {
 /* Future<void> createSession(
      String amount, String orderId, CreateSessionCallback callback);

  Future<void> updateSession(
      String sessionId, String apiVersion, GatewayMap request, GatewayCallback gatewayCallback);
  Future<void> completeSession(
      String jsonText, String orderId, String transactionId, String threeDSecureId, CompleteSessionCallback callback);

  Future<void> check3dsEnrollment(
      String sessionId, String amount, String currency, String threeDSecureId, Check3DSecureEnrollmentCallback enrollmentCallback);

  String checkTransaction(String orderId);

  String getRequestText();

  String getResponseText();*/
}