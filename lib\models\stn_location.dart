import 'package:flutter/foundation.dart';

class StnLocation
{
  int srNo;
  int stnNo;
  String stationName;
  String placeDesc;
  String areaName;
  String stationStatus;
  String district;
  dynamic stnOwners;
  String stnAllCustFlag;
  String stationAddress;
  String stationFrom;
  String stationTo;
  dynamic isOffice;
  String latitude;
  String longitude;
  String stnMosque;
  String stationCoordinates;
  String placeCode;
  String mainbranch;
  String branchName;
  String products;
  String prods;
  String stnCarService;
  String stnFoodRest;
  String stnCarRent;
  String stnAtm;

  StnLocation({
    required this.srNo,
    required this.stnNo,
    required this.stationName,
    required this.placeDesc,
    required this.areaName,
    required this.stationStatus,
    required this.district,
    required this.stnOwners,
    required this.stnAllCustFlag,
    required this.stationAddress,
    required this.stationFrom,
    required this.stationTo,
    required this.isOffice,
    required this.latitude,
    required this.longitude,
    required this.stnMosque,
    required this.stationCoordinates,
    required this.placeCode,
    required this.mainbranch,
    required this.branchName,
    required this.products,
    required this.prods,
    required this.stnCarService,
    required this.stnFoodRest,
    required this.stnCarRent,
    required this.stnAtm,
  });
  factory StnLocation.fromJson(Map<String, dynamic> json) {
    return StnLocation(
        srNo: json['srNo'],
        stnNo: json['stnNo'],
        stationName: json['stationName'],
        placeDesc: json['placeDesc'],
        areaName: json['areaName'],
        stationStatus: json['stationStatus'],
        district: json['district'],
        stnOwners: json['stnOwners'],
        stnAllCustFlag: json['stnAllCustFlag'],
        stationAddress: json['stationAddress'],
        stationFrom: json['stationFrom'],
        stationTo: json['stationTo'],
        isOffice: json['isOffice'],
        latitude: json['latitude'],
        longitude: json['longitude'],
        stnMosque: json['stnMosque'],
        stationCoordinates: json['stationCoordinates'],
        placeCode: json['placeCode'],
        mainbranch: json['mainbranch'],
        branchName: json['branchName'],
        products: json['products'],
        prods: json['prods'],
        stnCarService: json['stnCarService'],
        stnFoodRest: json['stnFoodRest'],
        stnCarRent: json['stnCarRent'],
        stnAtm: json['stnAtm']
    );
  }




}