import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ShowTagController extends GetxController {
  RxInt quantity = 1.obs;
  RxInt currantIndex = 0.obs;
  RxList paymentOptionList = [
    {'value': true.obs, "balance": "176721.69", 'title': 'Use my Balance Credits', 'label': ''}.obs,
    {'value': false.obs, "balance": "", 'title': 'Cash', 'label': 'Cash payment instructions'}.obs,
    {'value': false.obs, "balance": "", 'title': 'E. Transfer', 'label': 'E. Transfer Note'}.obs,
    {'value': false.obs, "balance": "", 'title': 'MADA', 'label': 'MADA Payment Note'}.obs,
    {'value': false.obs, "balance": "", 'title': 'Aldrees promotion', 'label': 'Aldrees promotion Note'}.obs,
  ].obs;
  TextEditingController emailController = TextEditingController();
  List itemList = ['item1', 'item2'];
  RxString selectedItem = 'item1'.obs;
  RxBool isUser1 = true.obs;
  RxBool isUser2 = false.obs;
  RxBool isValidate = false.obs;
  RxBool isSubmit = true.obs;
  RxString verificationCode = ''.obs;
  RxString isoCode = 'SA'.obs;
  RxString expireMonth = 'MM'.obs;
  RxString expireYear = 'YY'.obs;
}
