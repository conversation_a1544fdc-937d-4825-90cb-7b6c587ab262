// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/transfer_tags_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../tag_installation/schedule_installation_widget.dart';
import 'change_status_widget.dart';
import 'choose_gas_stations_widget.dart';
import 'set_quota_limits_widget.dart';

class BulkActionsWidget extends StatefulWidget {
  const BulkActionsWidget({super.key});

  @override
  State<BulkActionsWidget> createState() => _BulkActionsWidgetState();
}

class _BulkActionsWidgetState extends State<BulkActionsWidget> {
  VehicleController vehicleController = Get.put(VehicleController());
  final vehicle = GetStorage();
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              children: [
                GestureDetector(
                    onTap: () {
                      vehicle.remove('vehicleID');
                      vehicle.remove('vehicleSerialID');
                      vehicle.remove('vehicleSerialID');
                      vehicle.remove('complaintJobID');
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.offAll(
                        () => DashBoardManagerScreen(
                          currantIndex: 0,
                        ),
                        //preventDuplicates: false,
                      );
                    },
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: AppColor.cLightBlueContainer,
                      child: Center(
                          child: assetSvdImageWidget(
                              image: DefaultImages.backIcn)),
                    )),
                Expanded(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(
                        "Bulk actions".trr,
                        style: pBold20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          verticalSpace(22),
          // bulkActionWidget(
          //   title: "Change status".trr,
          //   onTap: () {
          //     Get.back();
          //     showModalBottomSheet(
          //       isDismissible: false,
          //       context: context,
          //       shape: RoundedRectangleBorder(
          //           borderRadius:
          //               BorderRadius.vertical(top: Radius.circular(16))),
          //       backgroundColor: AppColor.cBackGround,
          //       barrierColor: AppColor.cBlackOpacity,
          //       isScrollControlled: true,
          //       builder: (context) {
          //         return ChangeStatusWidget();
          //       },
          //     );
          //   },
          // ),
          bulkActionWidget(
            title: "Set quota limits".trr,
            onTap: () {
              Get.back();
              showModalBottomSheet(
                context: context,
                shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16))),
                backgroundColor: AppColor.cBackGround,
                barrierColor: AppColor.cBlackOpacity,
                isScrollControlled: true,
                builder: (context) {
                  return Text("test");
                  // return SetQuotaLimitsWidget();
                },
              );
            },
          ),
          // bulkActionWidget(
          //   title: "Terminate".trr,
          //   onTap: () {},
          // ),
          /* bulkActionWidget(
            title: "Book tag installation".trr,
            onTap: () {
              Get.back();
              Get.to(ScheduleInstallationWidget());
            },
          ),*/
          bulkActionWidget(
            title: "Choose gas stations".trr,
            onTap: () {
              Get.back();
              showModalBottomSheet(
                context: context,
                shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16))),
                backgroundColor: AppColor.cBackGround,
                barrierColor: AppColor.cBlackOpacity,
                isScrollControlled: true,
                builder: (context) {
                  return ChooseGasStationsWidget();
                },
              );
            },
          ),
          bulkActionWidget(
            title: "Transfer tag".trr,
            onTap: () {
              Get.back();
              showModalBottomSheet(
                context: context,
                shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16))),
                backgroundColor: AppColor.cBackGround,
                barrierColor: AppColor.cBlackOpacity,
                isScrollControlled: true,
                builder: (context) {
                  return Text("test");
                  // return TransferTagsWidget();
                },
              );
            },
          ),
        ],
      ),
    );
  }
}

bulkActionWidget(
    {required String title, required Function() onTap, Color? textColor}) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8.0),
    // child: GestureDetector(
    //   onTap: onTap,
    //   child: Container(
    //     padding: EdgeInsets.symmetric(vertical: 11, horizontal: 16),
    //     decoration: BoxDecoration(color: AppColor.lightBlueColor, borderRadius: BorderRadius.circular(6)),
    //     child: Align(alignment: Alignment.centerLeft, child: Text(title, style: pRegular16.copyWith(color: textColor))),
    //   ),
    // ),
    child: SizedBox(
      height: 44,
      child: Center(
        child: ListTile(
          onTap: onTap,
          title: Text(title, style: pRegular16.copyWith(color: textColor)),
          contentPadding: EdgeInsets.only(left: 16, right: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
          tileColor: AppColor.lightBlueColor,
          // tileColor: AppColor.cGreen,
          subtitle: SizedBox(),
          isThreeLine: true,
          minVerticalPadding: 0,
          horizontalTitleGap: 0,
          splashColor: AppColor.cLightBlueContainer,
          selectedColor: AppColor.cLightBlueContainer,
          focusColor: AppColor.cLightBlueContainer,
          hoverColor: AppColor.cLightBlueContainer,
          selectedTileColor: AppColor.lightBlueColor,
          selected: true,
          enabled: true,
        ),
      ),
    ),
  );
}
