import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../core/controller/auth/auth_controller.dart';

import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

AuthController authController = Get.put(AuthController());

// class LocaleString extends Translations {
//   static final Map<String, Map<String, String>> _localizedStrings = {};

//   static Future<void> load() async {
//     Map<String, Map<String, String>> languages = {};

//     LocaleString localeString = LocaleString();
//     await localeString.fetchLanguage();
//     languages = await localeString.loadTranslationData();
//     log(languages.toString());
//     if (languages.isEmpty) {
//       for (var languageModel in authController.languageList) {
//         String jsonStringValues = await rootBundle
//             .loadString('json/${languageModel['languageCode']}.json');
//         log("+++---------->${languageModel['languageCode']}");

//         Map<String, dynamic> mappedJson = json.decode(jsonStringValues);
//         Map<String, String> loadJson = {};

//         mappedJson.forEach((key, value) {
//           loadJson[key] = value.toString();
//         });
//         languages[
//                 '${languageModel['languageCode']}_${languageModel['countryCode']}'] =
//             loadJson;
//       }
//     }

//     _localizedStrings.addAll(languages);
//   }

//   @override
//   Map<String, Map<String, String>> get keys {
//     return _localizedStrings;
//   }

//   Future<void> fetchLanguage() async {
//     final response = await http
//         .get(Uri.parse('https://devint.aldrees.com/api/GETDICTIONARY/'));

//     if (response.statusCode == 200) {
//       try {
//         String cleanedResponse = response.body
//             .replaceAll(r'\"', '"')
//             .replaceAll(r'\\', '')
//             .replaceAll(r'\\n', '')
//             .replaceAll(r'\\r', '')
//             .replaceAll(r'\[|\]', '');

//         List<String> data = cleanedResponse.split('","');

//         List<Map<String, String>> matchedLanguageList = [
//           {"key": "arabic value"}
//         ];
//         List<Map<String, String>> unmatchedLanguageList = [
//           {"key": "english value"}
//         ];

//         for (int i = 0; i < data.length - 1; i += 2) {
//           String englishText = data[i].replaceAll('"', '').trim();
//           String arabicText = data[i + 1].replaceAll('"', '').trim();

//           englishText = englishText.replaceAll('*', '').trim();
//           arabicText = arabicText.replaceAll('*', '').trim();

//           englishText = englishText.replaceAll(RegExp(r'\s+'), ' ').trim();

//           if (arabicText.isNotEmpty) {
//             matchedLanguageList.add({englishText: arabicText});
//           } else {
//             unmatchedLanguageList.add({englishText: englishText});
//           }
//         }

//         // Convert lists to JSON strings
//         String matchedLanguageListJson = jsonEncode(matchedLanguageList);
//         String unmatchedLanguageListJson = jsonEncode(unmatchedLanguageList);

//         // Save to device storage
//         await saveApiDataLocally('ar_dynamic.json', matchedLanguageListJson);
//         await saveApiDataLocally('en_dynamic.json', unmatchedLanguageListJson);
//       } catch (e) {
//         log('Error parsing response: $e');
//       }
//     } else {
//       throw Exception('Failed to load data');
//     }
//   }

//   Future<void> saveApiDataLocally(String fileName, String jsonData) async {
//     try {
//       final directory = await getApplicationDocumentsDirectory();
//       final file = File('${directory.path}/$fileName');
//       await file.writeAsString(jsonData);
//       log('Data saved to ${file.path}');
//     } catch (e) {
//       log('Failed to save data: $e');
//     }
//   }

//   Future<Map<String, Map<String, String>>> loadTranslationData() async {
//     try {
//       final directory = await getApplicationDocumentsDirectory();

//       final arDynamicFilePath = '${directory.path}/ar_dynamic.json';
//       final arDynamicFile = File(arDynamicFilePath);

//       Map<String, String> matchedLanguageList = {};
//       if (await arDynamicFile.exists()) {
//         final jsonString = await arDynamicFile.readAsString();
//         List<dynamic> jsonList = jsonDecode(jsonString);
//         for (var item in jsonList) {
//           matchedLanguageList.addAll(Map<String, String>.from(item));
//         }
//       } else {
//         throw Exception('ar_dynamic.json file not found');
//       }

//       final enDynamicFilePath = '${directory.path}/en_dynamic.json';
//       final enDynamicFile = File(enDynamicFilePath);

//       Map<String, String> unmatchedLanguageList = {};
//       if (await enDynamicFile.exists()) {
//         final jsonString = await enDynamicFile.readAsString();
//         List<dynamic> jsonList = jsonDecode(jsonString);
//         for (var item in jsonList) {
//           unmatchedLanguageList.addAll(Map<String, String>.from(item));
//         }
//       } else {
//         throw Exception('en_dynamic.json file not found');
//       }

//       return {
//         'matchedLanguageList': matchedLanguageList,
//         'unmatchedLanguageList': unmatchedLanguageList,
//       };
//     } catch (e) {
//       print('Error loading translation data: $e');
//       return {
//         'matchedLanguageList': {},
//         'unmatchedLanguageList': {},
//       };
//     }
//   }
// }

class LocaleString extends Translations {
  static final Map<String, Map<String, String>> localizedStrings = {};

  static Future<void> load() async {
    LocaleString localeString = LocaleString();

    SharedPreferences prefs = await SharedPreferences.getInstance();

    String? previousDate = prefs.getString('loginDate');
    log('date check $previousDate');
    String currentDate = DateFormat('yyyy/MM/dd').format(DateTime.now());
    previousDate ??= currentDate;

    await localeString.fetchLanguage(previousDate);

    await prefs.setString('loginDate', currentDate);

    Map<String, Map<String, String>> loadedLanguages =
        await loadTranslationData();

    localizedStrings.addAll(loadedLanguages);

    // log('Final Localized Strings: $_localizedStrings');
  }

  @override
  Map<String, Map<String, String>> get keys => localizedStrings;

  Future<void> saveApiDataLocally(String fileName, String jsonData) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(jsonData);
      log('Data saved to ${file.path}');
    } catch (e) {
      log('Failed to save data: $e');
    }
  }

  // Future<bool> isDataChanged(String fileName, String newData) async {
  //   try {
  //     final directory = await getApplicationDocumentsDirectory();
  //     final file = File('${directory.path}/$fileName');

  //     if (await file.exists()) {
  //       final existingData = await file.readAsString();
  //       log("compare both ${existingData == newData}");
  //       return existingData != newData;
  //     } else {
  //       return true;
  //     }
  //   } catch (e) {
  //     log('Failed to compare data: $e');
  //     return true;
  //   }
  // }

  Future<void> fetchLanguage(String date) async {
    final String url =
        "${ApiEndPoints.baseUrl}${ApiEndPoints.authEndpoints.getDictionary}";

    //"https://devinttest.aldrees.com/api/GETDICTIONARY";

    final response = await http.get(
      Uri.parse("$url?date=$date"),
    );
    log("$url?date=$date");
    //log("response of dictionary ${response.body}");

    if (response.statusCode == 200) {
      if (response.body == "{}") {
        log('No new data available. Skipping processing.');
        return;
      }
      try {
        String cleanedResponse = response.body
            .replaceAll(r'\"', '"')
            .replaceAll(r'\\', '')
            .replaceAll(r'\\n', '')
            .replaceAll(r'\\r', '')
            .replaceAll(r'\[|\]', '');

        List<String> data = cleanedResponse.split('","');

        Map<String, String> matchedLanguageList = {"key": "arabic value"};
        Map<String, String> unmatchedLanguageList = {"key": "english value"};

        for (int i = 0; i < data.length - 1; i += 2) {
          String englishText = data[i].replaceAll('"', '').trim();
          String arabicText = data[i + 1].replaceAll('"', '').trim();

          englishText = englishText.replaceAll('*', '').trim();
          arabicText = arabicText.replaceAll('*', '').trim();
          englishText = englishText.replaceAll(RegExp(r'\s+'), ' ').trim();

          if (arabicText.isNotEmpty &&
              !arabicText.contains("{") &&
              !englishText.contains("{") &&
              arabicText != englishText) {
            matchedLanguageList[englishText] = arabicText;
          } else if (englishText.isNotEmpty && arabicText.isEmpty) {
            unmatchedLanguageList[englishText] = englishText;
          } else if (arabicText.isEmpty || englishText.isEmpty) {
            continue;
          }
        }

        String matchedLanguageListJson = jsonEncode(matchedLanguageList);
        String unmatchedLanguageListJson = jsonEncode(unmatchedLanguageList);
        await saveApiDataLocally('ar_dynamic.json', matchedLanguageListJson);
        await saveApiDataLocally('en_dynamic.json', unmatchedLanguageListJson);

        // bool isArChanged =
        //     await isDataChanged('ar_dynamic.json', matchedLanguageListJson);
        // bool isEnChanged =
        //     await isDataChanged('en_dynamic.json', unmatchedLanguageListJson);

        // if (isArChanged) {
        //   await saveApiDataLocally('ar_dynamic.json', matchedLanguageListJson);
        // }
        // if (isEnChanged) {
        //   await saveApiDataLocally(
        //       'en_dynamic.json', unmatchedLanguageListJson);
        // }
      } catch (e) {
        log('Error parsing response: $e');
      }
    } else {
      throw Exception('Failed to load data');
    }
  }

  static Future<Map<String, Map<String, String>>> loadTranslationData() async {
    try {
      final directory = await getApplicationDocumentsDirectory();

      final arDynamicFilePath = '${directory.path}/ar_dynamic.json';
      final arDynamicFile = File(arDynamicFilePath);

      Map<String, String> matchedLanguageList = {};
      if (await arDynamicFile.exists()) {
        final jsonString = await arDynamicFile.readAsString();
        matchedLanguageList = Map<String, String>.from(jsonDecode(jsonString));
      } else {
        throw Exception('ar_dynamic.json file not found');
      }

      final enDynamicFilePath = '${directory.path}/en_dynamic.json';
      final enDynamicFile = File(enDynamicFilePath);

      Map<String, String> unmatchedLanguageList = {};
      if (await enDynamicFile.exists()) {
        final jsonString = await enDynamicFile.readAsString();
        unmatchedLanguageList =
            Map<String, String>.from(jsonDecode(jsonString));
      } else {
        throw Exception('en_dynamic.json file not found');
      }

      return {
        'en_US': unmatchedLanguageList,
        'ar_AR': matchedLanguageList,
      };
    } catch (e) {
      log('Error loading translation data: $e');
      return {
        'en_US': {},
        'ar_AR': {},
      };
    }
  }
}
