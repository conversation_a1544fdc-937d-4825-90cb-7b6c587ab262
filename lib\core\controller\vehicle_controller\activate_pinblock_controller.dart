import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';

import '../../../utils/colors.dart';
import '../../../utils/text_style.dart';
import '../../../view/widget/common_button.dart';
import '../../../view/widget/common_space_divider_widget.dart';
import '../../../view/widget/loading_widget.dart';

class ActivatePinblockController extends GetxController {
  VehicleController vehicleController = Get.put(VehicleController());
  RxBool isActivate = false.obs;
  TextEditingController pinblockController = TextEditingController();
  GetStorage custsData = GetStorage('custsData');
  final isPinblock = GetStorage();
  final vehicle = GetStorage();
  final isDCBlock = GetStorage();

  activatePinblock(serialid) async {
    Loader.showLoader();
    print("===============================================================");
    print("serialid>>>>>>> $serialid");
    print("pinblockController>>>>>>> ${pinblockController.text}");
    print("===============================================================");

    var custData = jsonEncode(custsData.read('custData'));
    var client = http.Client();

    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.activatePinblock),
          body: {
            "custdata": custData,
            "pinblock": pinblockController.text,
            "serialid": serialid,
          });
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        isPinblock.remove('isPinActivate');
        vehicle.remove('vehicleID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('complaintJobID');
        isPinblock.remove('isPinActivate');
        isDCBlock.remove('isdcBlock');
        vehicleController.selectedSerialList.clear();
        vehicleController.selectedVehicleList.clear();
        vehicleController.selectedFleetList.clear();
        vehicleController.filterValueList.refresh();
        vehicleController.selectedVehicleList.refresh();
        vehicleController.selectedSerialList.refresh();
        vehicleController.selectedFleetList.refresh();
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: 0,
          ),
          //preventDuplicates: false,
        );
      } else {
        Navigator.of(Get.context!).pop();
        print('Failed');
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    jsonDecode(response.body),
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
