import 'ReturnMessage.dart';

class BalanceTopUpModelNew {
  BalanceTopUpModelNew({
    dynamic orderHRD,
    dynamic orderTRL,
    dynamic message,
    dynamic reference,
    ReturnMessage? returnMessage,
    dynamic orderType,
    dynamic sessionId,
    String? getPromotion,
    dynamic mOKAFAAccessToken,
    dynamic mOKAFAOTPToken,
    dynamic mOKAFAPayRefNo,
    dynamic aNBAccessToken,
    dynamic aNBOTPToken,
    dynamic aNBPayRefNo,
    dynamic aNBAmount,
    dynamic qitafPayRefNo,
    dynamic qitafDepositTo,
    dynamic stcPayOtpReference,
    dynamic stcPayPmtReference,
    dynamic stcPayExpiryDuration,
    dynamic alinmaAccessToken,
    dynamic alinmaPaymentRef,
  }) {
    _orderHRD = orderHRD;
    _orderTRL = orderTRL;
    _message = message;
    _reference = reference;
    _returnMessage = returnMessage;
    _orderType = orderType;
    _sessionId = sessionId;
    _getPromotion = getPromotion;
    _mOKAFAAccessToken = mOKAFAAccessToken;
    _mOKAFAOTPToken = mOKAFAOTPToken;
    _mOKAFAPayRefNo = mOKAFAPayRefNo;
    _aNBAccessToken = aNBAccessToken;
    _aNBOTPToken = aNBOTPToken;
    _aNBPayRefNo = aNBPayRefNo;
    _aNBAmount = aNBAmount;
    _qitafPayRefNo = qitafPayRefNo;
    _qitafDepositTo = qitafDepositTo;
    _stcPayOtpReference = stcPayOtpReference;
    _stcPayPmtReference = stcPayPmtReference;
    _stcPayExpiryDuration = stcPayExpiryDuration;
    _alinmaAccessToken = alinmaAccessToken;
    _alinmaPaymentRef = alinmaPaymentRef;
  }

  BalanceTopUpModelNew.fromJson(dynamic json) {
    _orderHRD = json['orderHRD'] ?? "";
    _orderTRL = json['orderTRL'] ?? "";
    _message = json['message'] ?? "";
    _reference = json['reference'] ?? "";
    _returnMessage = (json['ReturnMessage'] != null
        ? ReturnMessage.fromJson(json['ReturnMessage'])
        : "") as ReturnMessage?;
    _orderType = json['orderType'] ?? "";
    _sessionId = json['sessionId'] ?? "";
    _getPromotion = json['getPromotion'];
    _mOKAFAAccessToken = json['MOKAFAAccessToken'] ?? "";
    _mOKAFAOTPToken = json['MOKAFAOTPToken'] ?? "";
    _mOKAFAPayRefNo = json['MOKAFAPayRefNo'] ?? "";
    _aNBAccessToken = json['ANBAccessToken'] ?? "";
    _aNBOTPToken = json['ANBOTPToken'] ?? "";
    _aNBPayRefNo = json['ANBPayRefNo'] ?? "";
    _aNBAmount = json['ANBAmount'] ?? "";
    _qitafPayRefNo = json['QitafPayRefNo'] ?? "";
    _qitafDepositTo = json['QitafDepositTo'] ?? "";
    _stcPayOtpReference = json['STCPayOtpReference'] ?? "";
    _stcPayPmtReference = json['STCPayPmtReference'] ?? "";
    _stcPayExpiryDuration = json['STCPayExpiryDuration'] ?? "";
    _alinmaAccessToken = json['ALINMAAccessToken'] ?? "";
    _alinmaPaymentRef = json['ALINMAPaymentRef'] ?? "";
  }
  dynamic _orderHRD;
  dynamic _orderTRL;
  dynamic _message;
  dynamic _reference;
  ReturnMessage? _returnMessage;
  dynamic _orderType;
  dynamic _sessionId;
  String? _getPromotion;
  dynamic _mOKAFAAccessToken;
  dynamic _mOKAFAOTPToken;
  dynamic _mOKAFAPayRefNo;
  dynamic _aNBAccessToken;
  dynamic _aNBOTPToken;
  dynamic _aNBPayRefNo;
  dynamic _aNBAmount;
  dynamic _qitafPayRefNo;
  dynamic _qitafDepositTo;
  dynamic _stcPayOtpReference;
  dynamic _stcPayPmtReference;
  dynamic _stcPayExpiryDuration;
  dynamic _alinmaAccessToken;
  dynamic _alinmaPaymentRef;
  BalanceTopUpModelNew copyWith({
    dynamic orderHRD,
    dynamic orderTRL,
    dynamic message,
    dynamic reference,
    ReturnMessage? returnMessage,
    dynamic orderType,
    dynamic sessionId,
    String? getPromotion,
    dynamic mOKAFAAccessToken,
    dynamic mOKAFAOTPToken,
    dynamic mOKAFAPayRefNo,
    dynamic aNBAccessToken,
    dynamic aNBOTPToken,
    dynamic aNBPayRefNo,
    dynamic aNBAmount,
    dynamic qitafPayRefNo,
    dynamic qitafDepositTo,
    dynamic stcPayOtpReference,
    dynamic stcPayPmtReference,
    dynamic stcPayExpiryDuration,
    dynamic alinmaAccessToken,
    dynamic alinmaPaymentRef,
  }) =>
      BalanceTopUpModelNew(
        orderHRD: orderHRD ?? _orderHRD,
        orderTRL: orderTRL ?? _orderTRL,
        message: message ?? _message,
        reference: reference ?? _reference,
        returnMessage: returnMessage ?? _returnMessage,
        orderType: orderType ?? _orderType,
        sessionId: sessionId ?? _sessionId,
        getPromotion: getPromotion ?? _getPromotion,
        mOKAFAAccessToken: mOKAFAAccessToken ?? _mOKAFAAccessToken,
        mOKAFAOTPToken: mOKAFAOTPToken ?? _mOKAFAOTPToken,
        mOKAFAPayRefNo: mOKAFAPayRefNo ?? _mOKAFAPayRefNo,
        aNBAccessToken: aNBAccessToken ?? _aNBAccessToken,
        aNBOTPToken: aNBOTPToken ?? _aNBOTPToken,
        aNBPayRefNo: aNBPayRefNo ?? _aNBPayRefNo,
        aNBAmount: aNBAmount ?? _aNBAmount,
        qitafPayRefNo: qitafPayRefNo ?? _qitafPayRefNo,
        qitafDepositTo: qitafDepositTo ?? _qitafDepositTo,
        stcPayOtpReference: stcPayOtpReference ?? _stcPayOtpReference,
        stcPayPmtReference: stcPayPmtReference ?? _stcPayPmtReference,
        stcPayExpiryDuration: stcPayExpiryDuration ?? _stcPayExpiryDuration,
        alinmaAccessToken: alinmaAccessToken ?? _alinmaAccessToken,
        alinmaPaymentRef: alinmaPaymentRef ?? _alinmaPaymentRef,
      );
  dynamic get orderHRD => _orderHRD;
  dynamic get orderTRL => _orderTRL;
  dynamic get message => _message;
  dynamic get reference => _reference;
  ReturnMessage? get returnMessage => _returnMessage;
  dynamic get orderType => _orderType;
  dynamic get sessionId => _sessionId;
  String? get getPromotion => _getPromotion;
  dynamic get mOKAFAAccessToken => _mOKAFAAccessToken;
  dynamic get mOKAFAOTPToken => _mOKAFAOTPToken;
  dynamic get mOKAFAPayRefNo => _mOKAFAPayRefNo;
  dynamic get aNBAccessToken => _aNBAccessToken;
  dynamic get aNBOTPToken => _aNBOTPToken;
  dynamic get aNBPayRefNo => _aNBPayRefNo;
  dynamic get aNBAmount => _aNBAmount;
  dynamic get qitafPayRefNo => _qitafPayRefNo;
  dynamic get qitafDepositTo => _qitafDepositTo;
  dynamic get stcPayOtpReference => _stcPayOtpReference;
  dynamic get stcPayPmtReference => _stcPayPmtReference;
  dynamic get stcPayExpiryDuration => _stcPayExpiryDuration;
  dynamic get alinmaAccessToken => _alinmaAccessToken;
  dynamic get alinmaPaymentRef => _alinmaPaymentRef;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['orderHRD'] = _orderHRD;
    map['orderTRL'] = _orderTRL;
    map['message'] = _message;
    map['reference'] = _reference;
    if (_returnMessage != null) {
      map['ReturnMessage'] = _returnMessage?.toJson();
    } else {
      map['ReturnMessage'] = "";
    }
    map['orderType'] = _orderType;
    map['sessionId'] = _sessionId;
    map['getPromotion'] = _getPromotion;
    map['MOKAFAAccessToken'] = _mOKAFAAccessToken;
    map['MOKAFAOTPToken'] = _mOKAFAOTPToken;
    map['MOKAFAPayRefNo'] = _mOKAFAPayRefNo;
    map['ANBAccessToken'] = _aNBAccessToken;
    map['ANBOTPToken'] = _aNBOTPToken;
    map['ANBPayRefNo'] = _aNBPayRefNo;
    map['ANBAmount'] = _aNBAmount;
    map['QitafPayRefNo'] = _qitafPayRefNo;
    map['QitafDepositTo'] = _qitafDepositTo;
    map['STCPayOtpReference'] = _stcPayOtpReference;
    map['STCPayPmtReference'] = _stcPayPmtReference;
    map['STCPayExpiryDuration'] = _stcPayExpiryDuration;
    map['ALINMAAccessToken'] = _alinmaAccessToken;
    map['ALINMAPaymentRef'] = _alinmaPaymentRef;
    return map;
  }
}
