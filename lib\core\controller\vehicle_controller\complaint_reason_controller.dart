import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/complain_city.dart';
import 'package:waie_app/models/complaint_reason.dart';
import 'package:waie_app/models/load_data.dart';
import 'package:waie_app/utils/api_endpoints.dart';

class ComplaintReasonController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  TextEditingController carController = TextEditingController();
  TextEditingController problemController = TextEditingController();
  List reasonList = ['Tag issue', 'File issue'];
  RxString reasonValue = 'Tag issue'.obs;
  var reasonLists = <ComplaintReasonModel>[].obs;
  var cityLists = <ComplaintCityModel>[].obs;
  var centerLists = <Load_Data_Model>[].obs;
  List<Load_Data_Model> centerModelList = [];
  RxString selectedReason = ''.obs;
  RxString selectedCity = ''.obs;
  RxString selectedCenter = ''.obs;

  @override
  void onInit() {
    super.onInit();
    print('ComplaintReasonController');
    fetchComplaintReasons();
  }

  Future<List<ComplaintReasonModel>?> fetchComplaintReasons() async {
    List<ComplaintReasonModel> reasons = [];
    List<ComplaintCityModel> cities = [];
    var client = http.Client();
    var custid = userStorage.read('custid');
    print("custid>>>>>>> $custid");

    try {
      var reasonResponse = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getComplaintReason),
          body: {
            "custid": custid, //custid
          });
      List reasonResult = jsonDecode(reasonResponse.body);
      print("===============================================================");
      print("reasonResponse >>>>> ${reasonResponse.body}");
      print("===============================================================");

      for (int i = 0; i < reasonResult.length; i++) {
        ComplaintReasonModel reason = ComplaintReasonModel.fromJson(
            reasonResult[i] as Map<String, dynamic>);
        reasons.add(reason);
      }

      var cityResponse = await client.post(Uri.parse(ApiEndPoints.baseUrl +
          ApiEndPoints.authEndpoints.getComplaintCityList));
      List cityResult = jsonDecode(cityResponse.body);
      print("===============================================================");
      print("cityResponse >>>>> ${cityResponse.body}");
      print("===============================================================");

      for (int i = 0; i < cityResult.length; i++) {
        ComplaintCityModel city =
            ComplaintCityModel.fromJson(cityResult[i] as Map<String, dynamic>);

        cities.add(city);
      }

      cityLists.value = cities;

      reasonLists.value = reasons;

      // if (selectedReason.value.isNotEmpty) {
      //   selectedCity.value = cityLists[1].placeCode;
      // }

      // selectedReason.value = reasonLists[0].typecode;
      // selectedCity.value = cityLists[0].placeCode;
      // print("selectedReason.value >>>>> ${selectedReason.value}");
      // print("selectedCity.value >>>>> ${selectedCity.value}");

      // if (selectedCity.value.isNotEmpty) {
      //   var centerResponse = await client.post(
      //     Uri.parse(ApiEndPoints.baseUrl +
      //         ApiEndPoints.authEndpoints.getComplaintCenterList),
      //     body: {"placeCode": selectedCity.value},
      //   );
      //   List centerResult = jsonDecode(centerResponse.body);
      //   print(
      //       "===============================================================");
      //   print("centerResponse >>>>> ${centerResponse.body}");
      //   print(
      //       "===============================================================");

      //   for (int i = 0; i < centerResult.length; i++) {
      //     Load_Data_Model center =
      //         Load_Data_Model.fromMap(centerResult[i] as Map<String, dynamic>);
      //     print("center.TYPECODE >>>>> ${center.TYPECODE}");
      //     print("center.TYPEDESC >>>>> ${center.TYPEDESC}");
      //     centerModelList.add(center);
      //   }
      //   print(
      //       "center.centerLists >>>>> ${jsonDecode(jsonEncode(centerModelList))}");
      //   if (centerModelList.isNotEmpty) {
      //     centerLists.value = centerModelList;
      //     selectedCenter.value = centerLists[0].TYPECODE;
      //   }
      // }

      return reasonLists;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
    return null;
  }

  Future<List<Load_Data_Model>?> fetchCenterData(String placeCode) async {
    var client = http.Client();

    try {
      var centerResponse = await client.post(
        Uri.parse(ApiEndPoints.baseUrl +
            ApiEndPoints.authEndpoints.getComplaintCenterList),
        body: {"placeCode": placeCode},
      );
      List centerResult = jsonDecode(centerResponse.body);
      print("===============================================================");
      print("centerResponse >>>>> ${centerResponse.body}");
      print("===============================================================");

      centerLists.clear();
      centerLists.refresh();
      for (int i = 0; i < centerResult.length; i++) {
        Load_Data_Model center =
            Load_Data_Model.fromMap(centerResult[i] as Map<String, dynamic>);
        print("center.TYPECODE >>>>> ${center.TYPECODE}");
        print("center.TYPEDESC >>>>> ${center.TYPEDESC}");
        centerModelList.add(center);
      }
      print(
          "center.centerLists >>>>> ${jsonDecode(jsonEncode(centerModelList))}");

      centerLists.value = centerModelList;

      return centerLists;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
    return null;
  }
}
