/// Flutter icons Custom
/// Copyright (C) 2024 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  Custom
///      fonts:
///       - asset: fonts/Custom.ttf
///
/// 
/// * Font Awesome 5, Copyright (C) 2016 by <PERSON>
///         Author:    <PERSON>
///         License:   SIL (https://github.com/FortAwesome/Font-Awesome/blob/master/LICENSE.txt)
///         Homepage:  http://fortawesome.github.com/Font-Awesome/
///
import 'package:flutter/widgets.dart';

class Custom {
  Custom._();

  static const _kFontFam = 'Custom';
  static const String? _kFontPkg = null;

  static const IconData apple_pay = IconData(0xf415, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cc_apple_pay = IconData(0xf416, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
