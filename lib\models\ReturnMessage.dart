/// Title : null
/// Message : "MOKAFA Error: {\"MESSAGE\":\"GENERAL API ERROR\",\"STATUS\":500,\"ERRORCODE\":\"500\",\"REQUESTID\":\"5FAC307F-B0C8-4F8C-8FE9-7C068A001C4D\"}The remote server returned an error: (500) Internal Server Error. PLEASE CHECK THE VALUES AND TRY AGAIN ... "
/// SubMessage : null
/// MessageType : "\u0000"
/// ResponseAction : null
/// Redirect : null
/// ActionParam : null
/// Action : "EXCEPTION"
/// isValidTransaction : true
/// Data : null

class ReturnMessage {
  ReturnMessage({
      dynamic title, 
      String? message, 
      dynamic subMessage, 
      String? messageType, 
      dynamic responseAction, 
      dynamic redirect, 
      dynamic actionParam, 
      String? action, 
      bool? isValidTransaction, 
      dynamic data,}){
    _title = title;
    _message = message;
    _subMessage = subMessage;
    _messageType = messageType;
    _responseAction = responseAction;
    _redirect = redirect;
    _actionParam = actionParam;
    _action = action;
    _isValidTransaction = isValidTransaction;
    _data = data;
}

  ReturnMessage.fromJson(dynamic json) {
    _title = json['Title'];
    _message = json['Message'];
    _subMessage = json['SubMessage'];
    _messageType = json['MessageType'];
    _responseAction = json['ResponseAction'];
    _redirect = json['Redirect'];
    _actionParam = json['ActionParam'];
    _action = json['Action'];
    _isValidTransaction = json['isValidTransaction'];
    _data = json['Data'];
  }
  dynamic _title;
  String? _message;
  dynamic _subMessage;
  String? _messageType;
  dynamic _responseAction;
  dynamic _redirect;
  dynamic _actionParam;
  String? _action;
  bool? _isValidTransaction;
  dynamic _data;
ReturnMessage copyWith({  dynamic title,
  String? message,
  dynamic subMessage,
  String? messageType,
  dynamic responseAction,
  dynamic redirect,
  dynamic actionParam,
  String? action,
  bool? isValidTransaction,
  dynamic data,
}) => ReturnMessage(  title: title ?? _title,
  message: message ?? _message,
  subMessage: subMessage ?? _subMessage,
  messageType: messageType ?? _messageType,
  responseAction: responseAction ?? _responseAction,
  redirect: redirect ?? _redirect,
  actionParam: actionParam ?? _actionParam,
  action: action ?? _action,
  isValidTransaction: isValidTransaction ?? _isValidTransaction,
  data: data ?? _data,
);
  dynamic get title => _title;
  String? get message => _message;
  dynamic get subMessage => _subMessage;
  String? get messageType => _messageType;
  dynamic get responseAction => _responseAction;
  dynamic get redirect => _redirect;
  dynamic get actionParam => _actionParam;
  String? get action => _action;
  bool? get isValidTransaction => _isValidTransaction;
  dynamic get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['Title'] = _title;
    map['Message'] = _message;
    map['SubMessage'] = _subMessage;
    map['MessageType'] = _messageType;
    map['ResponseAction'] = _responseAction;
    map['Redirect'] = _redirect;
    map['ActionParam'] = _actionParam;
    map['Action'] = _action;
    map['isValidTransaction'] = _isValidTransaction;
    map['Data'] = _data;
    return map;
  }

}