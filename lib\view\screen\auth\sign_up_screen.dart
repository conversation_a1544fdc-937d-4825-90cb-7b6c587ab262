// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/core/controller/signup_controller/city_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/company_widget.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/screen/auth/individual_widget.dart';
import 'package:waie_app/core/controller/auth/auth_controller.dart';

class CreateAccountScreen extends StatelessWidget {
  CreateAccountScreen({super.key});

  AuthController authController = Get.put(AuthController());
  CityController cityController = Get.put(CityController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Obx(() {
            return Column(
              children: [
                simpleMyAppBar(
                    title: "Create account".trr,
                    backString: "Back".trr,
                    onTap: () {
                      Get.back();
                    },
                    backColor: AppColor.cBlueFont),
                Padding(
                  padding: const EdgeInsets.only(top: 24, right: 16, left: 16),
                  child: Container(
                    width: Get.width,
                    height: 3, //45,
                    decoration: BoxDecoration(
                        color: AppColor.cWhite, //cLightGrey,
                        borderRadius: BorderRadius.circular(4)),
                    padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          commonAuthTab(
                              onTap: () {
                                authController.isIndividual.value = true;
                                authController.isCompany.value = false;
                                print("I ${authController.isIndividual.value}");
                                print("I ${authController.isCompany.value}");
                              },
                              //text: "Individual".trr,
                              text: "",
                              btnColor:
                                  authController.isIndividual.value == true
                                      ? AppColor.cWhite
                                      : AppColor.cLightGrey,
                              textColor:
                                  authController.isIndividual.value == true
                                      ? AppColor.cText
                                      : AppColor.cDarkGreyFont),
                          // commonAuthTab(
                          //     onTap: () {
                          //       authController.isIndividual.value = false;
                          //       authController.isCompany.value = true;
                          //       print("C ${authController.isIndividual.value}");
                          //       print("C ${authController.isCompany.value}");
                          //     },
                          //     text: "Company".trr,
                          //     btnColor: authController.isCompany.value == true
                          //         ? AppColor.cWhite
                          //         : AppColor.cLightGrey,
                          //     textColor: authController.isCompany.value == true
                          //         ? AppColor.cText
                          //         : AppColor.cDarkGreyFont),
                        ]),
                  ),
                ),
                authController.isIndividual.value == true
                    ? IndividualWidget(
                        authController: authController,
                      )
                    : SizedBox()
                // : CompanyWidget(
                //     authController: authController,
                //   )
              ],
            );
          }),
        ),
      ),
    );
  }
}

Widget commonAuthTab(
    {required Function() onTap,
    required String text,
    required Color btnColor,
    required Color textColor}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width / 2.5,
      decoration: BoxDecoration(
          color: btnColor, borderRadius: BorderRadius.circular(4)),
      child: Center(
        child: Text(
          text,
          style: pRegular14.copyWith(fontSize: 15, color: textColor),
        ),
      ),
    ),
  );
}
