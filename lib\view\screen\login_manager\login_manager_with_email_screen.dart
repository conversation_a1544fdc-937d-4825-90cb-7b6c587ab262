// ignore_for_file: must_be_immutable, prefer_const_constructors_in_immutables, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';

import '../../../core/controller/login_manager_controller/login_manager_with_email_controller.dart';
import '../dashboard_manager/dashboard_manager.dart';

class LoginManagerWithEmailScreen extends StatefulWidget {
  const LoginManagerWithEmailScreen({super.key});

  @override
  State<LoginManagerWithEmailScreen> createState() =>
      _LoginManagerWithEmailScreenState();
}

class _LoginManagerWithEmailScreenState
    extends State<LoginManagerWithEmailScreen> {
  LoginManagerWithEmailController loginManagerWithEmailController =
      Get.put(LoginManagerWithEmailController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    loginManagerWithEmailController.currantIndex.value = 2;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Obx(() {
        return Scaffold(
          backgroundColor: AppColor.cBackGround,
          body: loginManagerWithEmailController.naviBarItemList[
              loginManagerWithEmailController.currantIndex.value]['screen'],
          // body: LoginScreen(),
          bottomNavigationBar: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            landscapeLayout: BottomNavigationBarLandscapeLayout.centered,
            selectedItemColor: AppColor.cOrangeFont,
            unselectedItemColor: AppColor.cDarkGreyFont,
            selectedFontSize: 14,
            unselectedFontSize: 14,
            currentIndex: loginManagerWithEmailController.currantIndex.value,
            onTap: (index) async {
              loginManagerWithEmailController.currantIndex.value = index;
              print(index);
            },
            selectedLabelStyle:
                pBold12.copyWith(color: AppColor.cOrangeFont, fontSize: 11),
            unselectedLabelStyle: pRegular10.copyWith(
                color: AppColor.cDarkGreyFont, fontSize: 11),
            items: List.generate(
                loginManagerWithEmailController.naviBarItemList.length,
                (index) {
              var data = loginManagerWithEmailController.naviBarItemList[index];
              return BottomNavigationBarItem(
                icon: bottomIconWidget(
                    image: data['icon'],
                    title: data['title'].toString().trr,
                    color: loginManagerWithEmailController.currantIndex.value ==
                            index
                        ? AppColor.cOrangeFont
                        : AppColor.cDarkGreyFont,
                    imageColor:
                        loginManagerWithEmailController.currantIndex.value ==
                                index
                            ? AppColor.cOrangeFont
                            : AppColor.cDarkGreyFont),
                label: data['title'].toString().trr,
              );
            }),
          ),
        );
      }),
    );
  }
}
