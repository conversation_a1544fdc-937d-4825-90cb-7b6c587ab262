// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/order_current_topup.dart';
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../../view/widget/common_snak_bar_widget.dart';

class OrderRefundReservedController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  var refundableReservedTopups = <OrderCurrentTopupModel>[].obs;
double topupTotaltAmt = 0.0;
  double topupTotaltVAT = 0.0;
  getRefundablesTopUp() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = jsonEncode(custsData.read('custData'));
    print("OrderRefundCurrentController custid>>>>>>> $custid");
    print("OrderRefundCurrentController emailid>>>>>>> $emailid");
    var client = http.Client();
    try {
      if (refundableReservedTopups.isEmpty) {
        var response = await client.post(
            Uri.parse(ApiEndPoints.baseUrl +
                ApiEndPoints.authEndpoints.getRefundablesTopUp),
            body: {
              "custdata": custData,
              "chkRes": "true",
              "orderId": "",
              "rblRefundOptions": "",
            });
        List result = jsonDecode(response.body);
        print("OrderRefundReservedController response >>>>> $response");
        print(
            "OrderRefundReservedController STATUS >>>>> ${response.statusCode}");

        print("OrderRefundReservedController result >>>>> $result");
        print("OrderRefundReservedController COUNT >>>>> ${result.length}");
        print(
            "===============================================================");
        print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
        print(
            "===============================================================");

        if (result.isEmpty) {
          commonToast("No Data Found, Please Check Internet Connection.");
        } else {
          for (int i = 0; i < result.length; i++) {
            OrderCurrentTopupModel order = OrderCurrentTopupModel.fromJson(
                result[i] as Map<String, dynamic>);
            refundableReservedTopups.add(order);
          }
        }

        print(
            "===============================================================");
        print(
            "OrderRefundReservedController >>>>> ${jsonDecode(jsonEncode(refundableReservedTopups))}");
        print(
            "===============================================================");

        return refundableReservedTopups;
      }
      print("ERROR: NO DATA");
      return [];
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('OrderRefundReservedController');
    print(jsonDecode(jsonEncode(refundableReservedTopups)));
    if (refundableReservedTopups.isEmpty) {
      print("sulod");
      await getRefundablesTopUp();
    }
    //Navigator.of(Get.context!).pop();
  }
}
