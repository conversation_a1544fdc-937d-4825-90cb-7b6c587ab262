import 'dart:convert';

import 'package:get/get_rx/src/rx_types/rx_types.dart';

List<MenuAccess> menuAccessFromJson(String str) =>
    List<MenuAccess>.from(json.decode(str).map((x) => MenuAccess.fromJson(x)));

String menuAccessToJson(List<MenuAccess> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class MenuAccess {
  RxBool value;
  RxBool isSelected = false.obs;
  String menuCode;
  String menuName;
  List<Menu> menu;

  MenuAccess({
    required this.value,
    required this.isSelected,
    required this.menuCode,
    required this.menuName,
    required this.menu,
  });

  factory MenuAccess.fromJson(Map<String, dynamic> json) => MenuAccess(
        value: json["value"] ?? false.obs,
        isSelected: json["isSelected"] ?? false.obs,
        menuCode: json["MenuCode"],
        menuName: json["MenuName"],
        menu: List<Menu>.from(json["Menu"].map((x) => Menu.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "value": value,
        "isSelected": isSelected,
        "MenuCode": menuCode,
        "MenuName": menuName,
        "Menu": List<dynamic>.from(menu.map((x) => x.toJson())),
      };

  addAll(Map<String, RxBool> map) {}
}

class Menu {
  RxBool value;
  RxBool isSelected = false.obs;
  String menuCode;
  String controlCode;
  String controlName;
  List<Menu>? subCont;

  Menu({
    required this.value,
    required this.isSelected,
    required this.menuCode,
    required this.controlCode,
    required this.controlName,
    this.subCont,
  });

  factory Menu.fromJson(Map<String, dynamic> json) => Menu(
        value: json["value"] ?? false.obs,
        isSelected: json["isSelected"] ?? false.obs,
        menuCode: json["MenuCode"],
        controlCode: json["ControlCode"],
        controlName: json["ControlName"],
        subCont: json["SubCont"] == null
            ? []
            : List<Menu>.from(json["SubCont"]!.map((x) => Menu.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "value": value,
        "isSelected": isSelected,
        "MenuCode": menuCode,
        "ControlCode": controlCode,
        "ControlName": controlName,
        "SubCont": subCont == null
            ? []
            : List<dynamic>.from(subCont!.map((x) => x.toJson())),
      };
}

class SubCont {
  RxBool value;
  String menuCode;
  String controlCode;
  String controlName;

  SubCont({
    required this.value,
    required this.menuCode,
    required this.controlCode,
    required this.controlName,
  });

  factory SubCont.fromJson(Map<String, dynamic> json) => SubCont(
        value: json["value"] ?? false.obs,
        menuCode: json["MenuCode"],
        controlCode: json["ControlCode"],
        controlName: json["ControlName"],
      );

  Map<String, dynamic> toJson() => {
        "value": value,
        "MenuCode": menuCode,
        "ControlCode": controlCode,
        "ControlName": controlName,
      };
}
