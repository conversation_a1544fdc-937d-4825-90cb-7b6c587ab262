import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/complaint_reason_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/file_complaint_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class FileComplaintScreen extends StatefulWidget {
  final String code;
  String serialid;

  FileComplaintScreen({
    super.key,
    required this.code,
    required this.serialid,
  });

  @override
  State<FileComplaintScreen> createState() => _FileComplaintScreenState();
}

class _FileComplaintScreenState extends State<FileComplaintScreen> {
  FileComplaintController fileComplaintController =
      Get.put(FileComplaintController());

  ComplaintReasonController complaintReasonController =
      Get.put(ComplaintReasonController());

  VehicleController vehicleController = Get.find();

  String errorString = '';

  bool _isCenterDropdownEnabled = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fileComplaintController.carController.text = widget.code;
    print("widget.code >>> ${widget.code}");
    if (fileComplaintController.complaintCityController.text.isNotEmpty) {
      print("fileComplaintController.complaintCityController.text");
      print(fileComplaintController.complaintCityController.text);
      _isCenterDropdownEnabled = true;
      complaintReasonController.fetchCenterData(
          fileComplaintController.complaintCityController.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    //print("vehicleSerialID >>>>> ${vehicle.read('vehicleSerialID')}");
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              simpleAppBar(
                  title: "New complaint".trr,
                  onTap: () {
                    vehicleController.selectedSerialList.clear();
                    vehicleController.selectedVehicleList.clear();
                    vehicleController.selectedFleetList.clear();
                    vehicleController.filterValueList.refresh();
                    vehicleController.selectedVehicleList.refresh();
                    vehicleController.selectedSerialList.refresh();
                    vehicleController.selectedFleetList.refresh();
                    // Get.offAll(
                    //   () => DashBoardManagerScreen(
                    //     currantIndex: 0,
                    //   ),
                    //   //preventDuplicates: false,
                    // );
                    Get.back();
                  },
                  backString: "Back".trr),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Fill in the form to file a complaint. Give us a few days to review your inquiry. We’ll notify you about any updates."
                              .trr,
                          style: pRegular13,
                        ),
                        verticalSpace(12),
                        CommonTextField(
                          controller: fileComplaintController.carController,
                          labelText: 'Plate No'.trr,
                          hintText: '0797DDG',
                          fillColor: AppColor.lightBlueColor,
                          filled: true,
                        ),
                        verticalSpace(15),
                        // CommonDropdownButtonWidget(
                        //   labelText: 'Reason for complaint'.trr,
                        //   value: fileComplaintController.reasonValue.value,
                        //   list: fileComplaintController.reasonList,
                        //   onChanged: (value) {
                        //     fileComplaintController.reasonValue.value = value;
                        //   },
                        // ),
                        Text(
                          "Reason".trr,
                          style: pRegular13,
                        ),
                        verticalSpace(10),
                        SizedBox(
                          height: errorString == "" ? 44 : 50,
                          child: DropdownButtonFormField(
                            isExpanded: true,
                            // value:
                            //     complaintReasonController.selectedReason.value,
                            items: complaintReasonController.reasonLists
                                .map((data) {
                              return DropdownMenuItem(
                                value: data.typecode,
                                child: Text(
                                  data.typedesc,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              complaintReasonController.selectedReason.value =
                                  value.toString();
                              print(
                                  "value.toString() >>>> ${value.toString()}");
                              fileComplaintController.reasonController.text =
                                  complaintReasonController
                                      .selectedReason.value;
                            },
                            validator: (value) {
                              setState(() {
                                if (value == null) {
                                  errorString = 'Please Choose a reason'.trr;
                                } else {
                                  errorString = '';
                                }
                              });
                              return null;
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Choose a reason'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  const EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                        ),
                        errorString == ''
                            ? const SizedBox()
                            : Text(
                                errorString,
                                style: pRegular12.copyWith(
                                    color: AppColor.cRedText),
                              ),
                        verticalSpace(15),
                        // CommonHintDropdownWidget(
                        //   labelText: 'Place'.trr,
                        //   hint: 'Choose a place'.trr,
                        //   list: fileComplaintController.cityList,
                        //   value: fileComplaintController.selectedCity.value,
                        //   onChanged: (value) {
                        //     fileComplaintController.selectedCity.value = value;
                        //   },
                        // ),
                        Text(
                          "Place".trr,
                          style: pRegular13,
                        ),
                        verticalSpace(10),
                        SizedBox(
                          height: errorString == "" ? 44 : 50,
                          child: DropdownButtonFormField(
                            isExpanded: true,
                            // value: complaintReasonController
                            //         .selectedCity.value.isEmpty
                            //     ? null
                            //     : complaintReasonController.selectedCity.value,
                            items:
                                complaintReasonController.cityLists.map((data) {
                              return DropdownMenuItem(
                                value: data.placeCode,
                                child: Text(
                                  data.place,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              complaintReasonController.selectedCity.value =
                                  value.toString();
                              print(
                                  "value.toString() >>>> ${value.toString()}");
                              fileComplaintController
                                      .complaintCityController.text =
                                  complaintReasonController.selectedCity.value;
                              complaintReasonController.centerLists.clear();
                              complaintReasonController.centerLists.refresh();
                              setState(() {
                                _isCenterDropdownEnabled = true;
                                print(
                                    "_isCenterDropdownEnabled >>>> $_isCenterDropdownEnabled");
                                complaintReasonController
                                    .fetchCenterData(value.toString());
                              });
                            },
                            validator: (value) {
                              setState(() {
                                if (value == null) {
                                  errorString = 'Please Choose a place'.trr;
                                } else {
                                  errorString = '';
                                }
                              });
                              return null;
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Choose a place'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  const EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                        ),
                        errorString == ''
                            ? const SizedBox()
                            : Text(
                                errorString,
                                style: pRegular12.copyWith(
                                    color: AppColor.cRedText),
                              ),
                        verticalSpace(15),
                        if (complaintReasonController.centerLists.isEmpty)
                          CommonHintDropdownWidget(
                            labelText: 'Center'.trr,
                            hint: 'Choose a center'.trr,
                            list: fileComplaintController.centerList,
                            value: fileComplaintController.centerValue.value,
                            onChanged: (value) {
                              fileComplaintController.centerValue.value = value;
                            },
                          ),
                        complaintReasonController.centerLists.isNotEmpty
                            ? Text(
                                "Center".trr,
                                style: pRegular13,
                              )
                            : const SizedBox(),

                        complaintReasonController.centerLists.isNotEmpty
                            ? verticalSpace(10)
                            : const SizedBox(),

                        complaintReasonController.centerLists.isNotEmpty
                            ? SizedBox(
                                height: errorString == "" ? 44 : 50,
                                child: DropdownButtonFormField(
                                  items: complaintReasonController.centerLists
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: _isCenterDropdownEnabled
                                      ? (value) {
                                          complaintReasonController
                                              .selectedCenter
                                              .value = value.toString();
                                          print(
                                              "value.toString() >>>> ${value.toString()}");
                                          fileComplaintController
                                                  .complaintCenterController
                                                  .text =
                                              complaintReasonController
                                                  .selectedCenter.value;
                                        }
                                      : null,
                                  validator: (value) {
                                    setState(() {
                                      if (value == null) {
                                        errorString =
                                            'Please Choose a center'.trr;
                                      } else {
                                        errorString = '';
                                      }
                                    });
                                    return null;
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'Choose a center'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            : const SizedBox(),
                        errorString == ''
                            ? const SizedBox()
                            : Text(
                                errorString,
                                style: pRegular12.copyWith(
                                    color: AppColor.cRedText),
                              ),
                        verticalSpace(15),
                        CommonTextField(
                          controller: fileComplaintController.problemController,
                          labelText: 'Your problem'.trr,
                          hintText: 'Tell us whats wrong'.trr,
                          maxLines: 5,
                        ),
                        verticalSpace(24),
                        CommonButton(
                          title: 'Submit complaint'.trr,
                          btnColor: AppColor.themeOrangeColor,
                          onPressed: () {
                            // vehicleController.myFleetList.refresh();
                            // final serialid = vehicle.read('vehicleSerialID');
                            // print("serialid >>>>> $serialid");
                            fileComplaintController
                                .submitComplaint(widget.serialid);
                          },
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
