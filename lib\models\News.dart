import 'dart:convert';

/// ID : "NEWS1"
/// SUBJECT : "New Gas stations"
/// SUMMARY : "We are happy to announce the opening of new modern gas stations in Riyadh since.."
/// DETAILS : "https://waiemvc.aldrees.com/"
/// DATE : "2023-11-25T19:26:44.0833333"

class News {
  String? subject;
  String? summary;
  String? details;
  String? date;
  String? subject_ar;
  String? summary_ar;

  News({
    required this.subject,
    required this.summary,
    required this.details,
    required this.date,
    required this.subject_ar,
    required this.summary_ar,
  });

  Map<String, dynamic> toMap() {
    return {
      'SUBJECT': subject,
      'SUMMARY': summary,
      'DETAILS': details,
      'DATE': date,
      'SUMMARY_AR': summary_ar,
      'SUBJECT_AR': subject_ar,
    };
  }

  factory News.fromMap(Map<String, dynamic> map) {
    return News(
      subject: map['SUBJECT'] ?? '',
      summary: map['SUMMARY'] ?? '',
      details: map['DETAILS'] ?? '',
      date: map['DATE'] ?? '',
      summary_ar: map['SUMMARY_AR'] ?? '',
      subject_ar: map['SUBJECT_AR'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory News.fromJson(String source) =>
      News.fromMap(json.decode(source));
}