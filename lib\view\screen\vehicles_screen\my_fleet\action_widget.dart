// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/change_plate_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/change_plate_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/file_complaint_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/activate_pinblock_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/plate_change_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/vehicle_controller/vehicle_controller.dart';
import '../tag_transfer_screen/transfer_tags_widget.dart';
import 'bulk_actions_widget.dart';
import 'change_status_widget.dart';
import 'choose_gas_stations_widget.dart';
import 'file_complaint_screen.dart';
import 'set_quota_limits_widget.dart';

class ActionWidget extends StatelessWidget {
  final String code;
  final bool isComplaint;
  final String serviceStatus;

  ActionWidget(
      {super.key,
      required this.code,
      required this.isComplaint,
      required this.serviceStatus});

  VehicleController vehicleController = Get.find();
  FileComplaintController fileComplaintController =
      Get.put(FileComplaintController());
  ChangePlateController changePlateController =
      Get.put(ChangePlateController());
  final vehicle = GetStorage();
  final isPinblock = GetStorage();
  final isDCBlock = GetStorage();

  @override
  Widget build(BuildContext context) {
    final serialid = vehicle.read('vehicleSerialID');
    final isPin = isPinblock.read('isPinActivate');
    final isdcBlock = isDCBlock.read('isdcBlock');
    print("isPin +++++++++ $isPin");
    print("isdcBlock +++++++++ $isdcBlock");
    print("code +++++++++ $code");
    print("serialid +++++++++ $serialid");
    print("isComplaint +++++++++ $isComplaint");
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              children: [
                GestureDetector(
                    onTap: () {
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.back();
                      // Get.offAll(
                      //   () => DashBoardManagerScreen(
                      //     currantIndex: 0,
                      //   ),
                      //   //preventDuplicates: false,
                      // );
                    },
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: AppColor.cLightBlueContainer,
                      child: Center(
                          child: assetSvdImageWidget(
                              image: DefaultImages.backIcn)),
                    )),
                Expanded(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(
                        "Actions".trr,
                        style: pBold20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          verticalSpace(22),
          if (isPin == true && isdcBlock == false)
            bulkActionWidget(
              title: "PINBLOCK".trr,
              onTap: () {
                Get.back();
                showModalBottomSheet(
                  isDismissible: false,
                  context: context,
                  shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16))),
                  backgroundColor: AppColor.cBackGround,
                  barrierColor: AppColor.cBlackOpacity,
                  isScrollControlled: true,
                  builder: (context) {
                    return Text("test");
                    // return ActivatePinblockWidget(
                    //   serialid: serialid,
                    // );
                  },
                );
              },
            ),
          bulkActionWidget(
            title: "Change status".trr,
            onTap: () {
              Get.back();
              showModalBottomSheet(
                isDismissible: false,
                context: context,
                shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16))),
                backgroundColor: AppColor.cBackGround,
                barrierColor: AppColor.cBlackOpacity,
                isScrollControlled: true,
                builder: (context) {
                  return Text("test");
                  //return ChangeStatusWidget(serviceStatus: serviceStatus);
                },
              );
            },
          ),
          // bulkActionWidget(
          //   title: "Terminate".trr,
          //   onTap: () {},
          // ),
          bulkActionWidget(
            title: "Set quota limits".trr,
            onTap: () {
              Get.back();
              showModalBottomSheet(
                isDismissible: false,
                context: context,
                shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16))),
                backgroundColor: AppColor.cBackGround,
                barrierColor: AppColor.cBlackOpacity,
                isScrollControlled: true,
                builder: (context) {
                  return Text("test");
                  //return SetQuotaLimitsWidget();
                },
              );
            },
          ),
          if (isdcBlock == false)
            bulkActionWidget(
              title: "Change Plate".trr,
              onTap: () {
                Get.back();
                showModalBottomSheet(
                  isDismissible: false,
                  context: context,
                  shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16))),
                  backgroundColor: AppColor.cBackGround,
                  barrierColor: AppColor.cBlackOpacity,
                  isScrollControlled: true,
                  builder: (context) {
                    print("PlateChangeWidget code +++++++++ $code");
                    return PlateChangeWidget(
                      code: code,
                      serialid: serialid,
                    );
                  },
                );
                // showDialog(
                //   barrierDismissible: false,
                //   context: context,
                //   builder: (context) {
                //     return AlertDialog(
                //       insetPadding: EdgeInsets.all(16),
                //       contentPadding: EdgeInsets.all(24),
                //       shape: RoundedRectangleBorder(
                //           borderRadius: BorderRadius.circular(12)),
                //       content: changePlateWidget(
                //         code: code,
                //         onTap: () {
                //           final serialid = vehicle.read('singleVehicleSerialID');
                //           print("vehicleSerialID >>>>> $serialid");
                //           print("Old Plate No +++++++++ $code");
                //           changePlateController.changePlate(serialid, code);

                //           // vehicleController.myFleetList.refresh();
                //           // fileComplaintController.cancelComplaint(serialid);
                //         },
                //       ),
                //     );
                //   },
                // );
              },
            ),
          // bulkActionWidget(
          //   title: "Choose gas stations".trr,
          //   onTap: () {
          //     Get.back();
          //     showModalBottomSheet(
          //       context: context,
          //       shape: RoundedRectangleBorder(
          //           borderRadius:
          //               BorderRadius.vertical(top: Radius.circular(16))),
          //       backgroundColor: AppColor.cBackGround,
          //       barrierColor: AppColor.cBlackOpacity,
          //       isScrollControlled: true,
          //       builder: (context) {
          //         return ChooseGasStationsWidget();
          //       },
          //     );
          //   },
          // ),
          if (isdcBlock == false)
            bulkActionWidget(
              title: "Transfer tag".trr,
              onTap: () {
                Get.back();
                showModalBottomSheet(
                  context: context,
                  shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16))),
                  backgroundColor: AppColor.cBackGround,
                  barrierColor: AppColor.cBlackOpacity,
                  isScrollControlled: true,
                  builder: (context) {
                    return Text("test");
                    // return TransferTagsWidget();
                  },
                );
              },
            ),
          if (isdcBlock == false)
            bulkActionWidget(
              title: isComplaint == true
                  ? "File complaint".trr
                  : "Cancel complaint".trr,
              onTap: () {
                Get.back();
                if (isComplaint == true) {
                  Get.to(() => FileComplaintScreen(
                        code: code, serialid: '',
                      ));
                } else {
                  showDialog(
                    barrierDismissible: false,
                    context: context,
                    builder: (context) {
                      return AlertDialog(
                        insetPadding: EdgeInsets.all(16),
                        contentPadding: EdgeInsets.all(24),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        content: cancelComplaintWidget(
                          code: code,
                          onTap: () {
                            final serialid =
                                vehicle.read('singleVehicleSerialID');
                            print("singleVehicleSerialID >>>>> $serialid");

                            vehicleController.myFleetList.refresh();
                            fileComplaintController.cancelComplaint(serialid);
                            // Get.back();
                            // for (var element in vehicleController.myFleetList) {
                            //   if (element['code'] == code) {
                            //     element['status'] = 'Active';
                            //   }
                            // }
                            // vehicleController.myFleetList.refresh();
                          },
                        ),
                      );
                    },
                  );
                }
              },
            ),
        ],
      ),
    );
  }

  cancelComplaintWidget({required String code, required Function() onTap}) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              vehicleController.selectedSerialList.clear();
              vehicleController.selectedVehicleList.clear();
              vehicleController.selectedFleetList.clear();
              vehicleController.filterValueList.refresh();
              vehicleController.selectedVehicleList.refresh();
              vehicleController.selectedSerialList.refresh();
              vehicleController.selectedFleetList.refresh();
              Get.back();
              // Get.offAll(
              //   () => DashBoardManagerScreen(
              //     currantIndex: 0,
              //   ),
              //   //preventDuplicates: false,
              // );
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [assetSvdImageWidget(image: DefaultImages.cancelIcn)],
            ),
          ),
          verticalSpace(24),
          Text.rich(
              TextSpan(
                  text: 'Do you want to cancel your complaint for'.trr,
                  style: pRegular17,
                  children: [TextSpan(text: '  $code?', style: pSemiBold17)]),
              textAlign: TextAlign.center),
          verticalSpace(8),
          Text(
              "You can't undo this. You'll need to submit another complaint if the issue hasn't been resolved."
                  .trr,
              style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
              textAlign: TextAlign.center),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonButton(
                title: 'Back'.trr,
                onPressed: () {
                  vehicleController.selectedSerialList.clear();
                  vehicleController.selectedVehicleList.clear();
                  vehicleController.selectedFleetList.clear();
                  vehicleController.filterValueList.refresh();
                  vehicleController.selectedVehicleList.refresh();
                  vehicleController.selectedSerialList.refresh();
                  vehicleController.selectedFleetList.refresh();
                  Get.back();
                  // Get.offAll(
                  //   () => DashBoardManagerScreen(
                  //     currantIndex: 0,
                  //   ),
                  //   //preventDuplicates: false,
                  // );
                },
                btnColor: AppColor.cBackGround,
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Cancel complaint'.trr,
                  onPressed: onTap,
                  btnColor: AppColor.themeOrangeColor,
                  horizontalPadding: 16,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  changePlateWidget({required String code, required Function() onTap}) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              vehicleController.selectedSerialList.clear();
              vehicleController.selectedVehicleList.clear();
              vehicleController.selectedFleetList.clear();
              vehicleController.filterValueList.refresh();
              vehicleController.selectedVehicleList.refresh();
              vehicleController.selectedSerialList.refresh();
              vehicleController.selectedFleetList.refresh();
              Get.back();
              // Get.offAll(
              //   () => DashBoardManagerScreen(
              //     currantIndex: 0,
              //   ),
              //   //preventDuplicates: false,
              // );
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [assetSvdImageWidget(image: DefaultImages.cancelIcn)],
            ),
          ),
          verticalSpace(24),
          Text("Change Plate".trr,
              style: pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
              textAlign: TextAlign.center),
          verticalSpace(8),
          Text("This process will require approval from Aldrees.".trr,
              style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
              textAlign: TextAlign.center),
          verticalSpace(24),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonTextField(
                controller: changePlateController.oldPlateController,
                labelText: 'Old Plate'.trr,
                hintText: code,
                fillColor: AppColor.lightBlueColor,
                readOnly: true,
              ),
              verticalSpace(15),
              CommonTextField(
                controller: changePlateController.newPlateController,
                labelText: 'New Plate'.trr,
              ),
            ],
          ),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonButton(
                title: 'Back'.trr,
                onPressed: () {
                  vehicleController.selectedSerialList.clear();
                  vehicleController.selectedVehicleList.clear();
                  vehicleController.selectedFleetList.clear();
                  vehicleController.filterValueList.refresh();
                  vehicleController.selectedVehicleList.refresh();
                  vehicleController.selectedSerialList.refresh();
                  vehicleController.selectedFleetList.refresh();
                  Get.back();
                  // Get.offAll(
                  //   () => DashBoardManagerScreen(
                  //     currantIndex: 0,
                  //   ),
                  //   //preventDuplicates: false,
                  // );
                },
                btnColor: AppColor.cBackGround,
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Submit'.trr,
                  onPressed: onTap,
                  btnColor: AppColor.themeOrangeColor,
                  horizontalPadding: 16,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
