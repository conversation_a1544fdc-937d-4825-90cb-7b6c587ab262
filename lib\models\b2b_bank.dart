import 'dart:convert';

List<B2BBankListModel> b2bbankListModelFromJson(String str) =>
    List<B2BBankListModel>.from(
        json.decode(str).map((x) => B2BBankListModel.fromJson(x)));

String b2bbankListModelToJson(List<B2BBankListModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class B2BBankListModel {
  String bankcode;
  String bankdesc;
  String bankdescar;
  int vas;
  String computed;

  B2BBankListModel({
    required this.bankcode,
    required this.bankdesc,
    required this.bankdescar,
    required this.vas,
    required this.computed,
  });

  factory B2BBankListModel.fromJson(Map<String, dynamic> json) =>
      B2BBankListModel(
        bankcode: json["BANKCODE"],
        bankdesc: json["BANKDESC"],
        bankdescar: json["BANKDESCAR"],
        vas: json["VAS"],
        computed: json["COMPUTED"],
      );

  Map<String, dynamic> toJson() => {
        "BANKCODE": bankcode,
        "BANKDESC": bankdesc,
        "BANKDESCAR": bankdescar,
        "VAS": vas,
        "COMPUTED": computed,
      };

  Map<String, dynamic> toMap() {
    return {
      'BANKCODE': bankcode,
      'BANKDESC': bankdesc,
      'BANKDESCAR': bankdescar,
      'VAS': vas,
      'COMPUTED': computed,
    };
  }

  factory B2BBankListModel.fromMap(Map<String, dynamic> map) {
    return B2BBankListModel(
      bankcode: map['BANKCODE'] ?? '',
      bankdesc: map['BANKDESC'] ?? '',
      bankdescar: map['BANKDESCAR'] ?? '',
      vas: map['VAS'] ?? '',
      computed: map['COMPUTED'] ?? '',
    );
  }
}
