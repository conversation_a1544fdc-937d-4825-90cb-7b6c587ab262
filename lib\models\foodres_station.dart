import 'dart:convert';

List<FoodResStation> foodResStationFromJson(String str) =>
    List<FoodResStation>.from(
        json.decode(str).map((x) => FoodResStation.fromJson(x)));

String foodResStationToJson(List<FoodResStation> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class FoodResStation {
  double stationCode;
  String stnNameE;
  String stnNameA;
  String latitude;
  String longitude;
  String districtE;
  String districtA;
  String cityE;
  String cityA;
  String areaE;
  String areaA;
  String services;
  double stnPetrol91;
  double stnPetrol95;
  double stnDiesel;

  FoodResStation({
    required this.stationCode,
    required this.stnNameE,
    required this.stnNameA,
    required this.latitude,
    required this.longitude,
    required this.districtE,
    required this.districtA,
    required this.cityE,
    required this.cityA,
    required this.areaE,
    required this.areaA,
    required this.services,
    required this.stnPetrol91,
    required this.stnPetrol95,
    required this.stnDiesel,
  });

  factory FoodResStation.fromJson(Map<String, dynamic> json) => FoodResStation(
        stationCode: json["STATION_CODE"]?.toDouble() ?? 0.0,
        stnNameE: json["STN_NAME_E"] ?? "",
        stnNameA: json["STN_NAME_A"] ?? "",
        latitude: json["LATITUDE"]?.trim() ?? "",
        longitude: json["LONGITUDE"]?.trim() ?? "",
        districtE: json["DISTRICT_E"] ?? "",
        districtA: json["DISTRICT_A"] ?? "",
        cityE: json["CITY_E"] ?? "",
        cityA: json["CITY_A"] ?? "",
        areaE: json["AREA_E"] ?? "",
        areaA: json["AREA_A"] ?? "",
        services: json["SERVICES"] ?? "",
        stnPetrol91: json["STN_PETROL91"]?.toDouble() ?? 0.0,
        stnPetrol95: json["STN_PETROL95"]?.toDouble() ?? 0.0,
        stnDiesel: json["STN_DIESEL"]?.toDouble() ?? 0.0,
      );

  Map<String, dynamic> toJson() => {
        "STATION_CODE": stationCode,
        "STN_NAME_E": stnNameE,
        "STN_NAME_A": stnNameA,
        "LATITUDE": latitude,
        "LONGITUDE": longitude,
        "DISTRICT_E": districtE,
        "DISTRICT_A": districtA,
        "CITY_E": cityE,
        "CITY_A": cityA,
        "AREA_E": areaE,
        "AREA_A": areaA,
        "SERVICES": services,
        "STN_PETROL91": stnPetrol91,
        "STN_PETROL95": stnPetrol95,
        "STN_DIESEL": stnDiesel,
      };
}
