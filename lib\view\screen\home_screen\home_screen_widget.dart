// ignore_for_file: prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

Widget aldreesNewsWidget({String? title, String? subTitle, Function()? onTap}) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8.0),
    child: Container(
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: AppColor.lightBlueColor),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title!,
            style: pBold12.copyWith(fontSize: 13),
          ),
          verticalSpace(8),
          Text(
            subTitle!,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(16),
          GestureDetector(
            onTap: onTap,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  "Learn more".trr,
                  style: pRegular14.copyWith(color: AppColor.cDarkBlueFont),
                ),
                horizontalSpace(10),
                assetSvdImageWidget(image: DefaultImages.nextIcn),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

/*Widget aldreesNewsWidget({String? title, String? subTitle, Function()? onTap}) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8.0),
    child: Container(
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: AppColor.lightBlueColor),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title!,
            style: pBold12.copyWith(fontSize: 13),
          ),
          verticalSpace(8),
          Text(
            subTitle!,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(16),
          GestureDetector(
            onTap: onTap, // This uses the onTap function passed to the widget
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  "Learn more".trr,
                  style: pRegular14.copyWith(color: AppColor.cDarkBlueFont),
                ),
                horizontalSpace(10),
                assetSvdImageWidget(image: DefaultImages.nextIcn),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}*/

Widget bestSellerWidget({
  String? image,
  //String? title,
  List? list,
  int? currantIndex,
}) {
  return Container(
    // width: Get.width,
    decoration: BoxDecoration(
        image: DecorationImage(image: AssetImage(image!), fit: BoxFit.fill)),
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        /* FittedBox(
          child: Text(
            //title!,
            style: pBold24.copyWith(color: AppColor.cWhiteFont, fontSize: 22),
          ),
        ),*/
        verticalSpace(12),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: _buildPageIndicator(list!, currantIndex),
        )
      ],
    ),
  );
}

/*
Widget bestSellerWidget({
  Widget? image, // Change the type to Widget
  String? title,
  List? list,
  int? currantIndex,
}) {
  return Container(
    // Your container properties
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Other widgets
        image ?? Container(), // Use the provided Image widget directly, or a placeholder if null
        // Other widgets
        Text(title ?? ''), // Display the title
        // Other widgets
      ],
    ),
  );
}
*/

List<Widget> _buildPageIndicator(List list, currantIndex) {
  List<Widget> itemList = [];
  for (int i = 0; i < list.length; i++) {
    itemList.add(i == currantIndex ? _indicator(true) : _indicator(false));
  }
  return itemList;
}

Widget _indicator(bool isActive) {
  return AnimatedContainer(
    duration: Duration(milliseconds: 150),
    margin: EdgeInsets.symmetric(horizontal: 4),
    height: 8,
    width: 8,
    decoration: BoxDecoration(
      color: isActive ? AppColor.cWhite : AppColor.cTransparent,
      border: Border.all(color: AppColor.cWhite),
      shape: BoxShape.circle,
    ),
  );
}

Widget tagWidget({
  required String title,
  required String subTitle,
}) {
  return Expanded(
      child: Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(10),
    ),
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style:
              pRegular10.copyWith(fontSize: 11, color: AppColor.cDarkGreyFont),
        ),
        verticalSpace(8),
        Text(
          subTitle,
          style: pBold24.copyWith(fontSize: 22),
        )
      ],
    ),
  ));
}

Widget tagDashboardWidget({
  required String title,
  required String subTitle,
}) {
  return Expanded(
      child: Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(10),
    ),
    padding: EdgeInsets.only(
      top: 8,
      bottom: 8,
      right: 16,
      left: 16,
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style:
              pRegular10.copyWith(fontSize: 11, color: AppColor.cDarkGreyFont),
        ),
        Text(
          subTitle,
          style: pBold20,
        )
      ],
    ),
  ));
}

Widget balanceWidget(
    {required String title, required String balance, required String srs}) {
  return Container(
    decoration: BoxDecoration(
        color: AppColor.lightBlueColor,
        borderRadius: BorderRadius.circular(10)),
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          title,
          style:
              pRegular10.copyWith(fontSize: 11, color: AppColor.cDarkGreyFont),
        ),
        SizedBox(height: 18),
        Directionality(
          textDirection: TextDirection.ltr,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center, // Centers horizontally
            crossAxisAlignment: CrossAxisAlignment.center, // Centers vertically
            children: [
              assetSvdImageWidget(
                  image: DefaultImages.saudiRiyal, width: 20, height: 20),
              Gap(4),
              Text(balance,
                  style: pBold24.copyWith(fontSize: 30, color: AppColor.cLabel),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1),
            ],
          ),
        ),
        // Text(balance,
        //     style: pBold24.copyWith(fontSize: 22, color: AppColor.cLabel),
        //     overflow: TextOverflow.ellipsis,
        //     maxLines: 1),
        // SizedBox(height: 4),
        // Text(
        //   srs,
        //   style: pRegular10.copyWith(fontSize: 11, color: AppColor.cLabel),
        // ),
      ],
    ),
  );
}

// Widget balanceWidget(
//     {required String title, required String balance, required String srs}) {
//   return Expanded(
//     child: Container(
//       decoration: BoxDecoration(
//           color: AppColor.lightBlueColor,
//           borderRadius: BorderRadius.circular(10)),
//       padding: EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             title,
//             style: pRegular10.copyWith(
//                 fontSize: 11, color: AppColor.cDarkGreyFont),
//           ),
//           verticalSpace(8),
//           Text(
//             balance,
//             style: pBold24.copyWith(fontSize: 22, color: AppColor.cLabel),
//             overflow: TextOverflow.ellipsis,
//           ),
//           verticalSpace(4),
//           Text(
//             srs,
//             style: pRegular10.copyWith(fontSize: 11, color: AppColor.cLabel),
//           ),
//         ],
//       ),
//     ),
//   );
// }

Widget balanceOptionalWidget(
    {required String title, required String balance, required String srs}) {
  return Container(
    width: 230,
    decoration: BoxDecoration(
        color: AppColor.lightBlueColor,
        borderRadius: BorderRadius.circular(10)),
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style:
              pRegular10.copyWith(fontSize: 11, color: AppColor.cDarkGreyFont),
        ),
        verticalSpace(8),
        Text(
          balance,
          style: pBold24.copyWith(fontSize: 22, color: AppColor.cLabel),
          overflow: TextOverflow.ellipsis,
        ),
        verticalSpace(4),
        Text(
          srs,
          style: pRegular10.copyWith(fontSize: 11, color: AppColor.cLabel),
        ),
      ],
    ),
  );
}

Widget profileWidget(
    {required String name,
    required String userId,
    required String emailID,
    required String notification,
    Function()? notificationTap}) {
  return ListTile(
    contentPadding: EdgeInsets.zero,
    title: Text(
      name,
      style: pBold18.copyWith(
        color: AppColor.themeOrangeColor,
        fontSize: 18,
      ),
    ),
    subtitle: Text.rich(
      TextSpan(
        text: '',
        style: pRegular16.copyWith(
          color: AppColor.themeOrangeColor,
          fontSize: 16,
        ),
        children: <TextSpan>[
          TextSpan(
            text: "$userId\n$emailID",
            style: pRegular16.copyWith(
              color: AppColor.themeOrangeColor,
              fontSize: 16,
            ),
          ),
        ],
      ),
    ),
    trailing: GestureDetector(
      onTap: notificationTap,
      child: SizedBox(
          height: 44,
          width: 44,
          child: totalNotificationWidget(totalNotification: notification)),
    ),
  );
}

Widget totalNotificationWidget({required String totalNotification}) {
  return Stack(
    alignment: Alignment.topRight,
    children: [
      assetSvdImageWidget(
          image: DefaultImages.bellIcn, backgroundColor: Colors.black),
      Align(
        alignment: Alignment.topRight - Alignment(.6, .3),
        child: CircleAvatar(
          radius: 7,
          backgroundColor: AppColor.themeOrangeColor,
          child: Center(
            child: Text(
              totalNotification,
              style: pSemiBold10.copyWith(
                color: AppColor.cWhiteFont,
                fontSize: 11,
              ),
            ),
          ),
        ),
      )
    ],
  );
}
