// ignore_for_file: prefer_const_constructors

import 'dart:developer';

import 'package:flutter/material.dart';

class AppColor {
  static Color cBlack = Colors.black;
  static Color cBlackOpacity = Colors.black.withOpacity(0.6);
  static Color cWhite = Color(0xffFFFFFF);
  static Color cBorder = Color(0xffC5CBD5);
  static Color cBackGround = Color(0xffFFFFFF);
  static Color cTransparent = Colors.transparent;
  static Color themeBlueColor = Color(0xff009DDF);
  static Color themeOrangeColor = Color(0xffFC6423);
  static Color themeDarkBlueColor = Color(0xff2457B6);
  static Color lightDarkBlueColor = Color(0x1A2457B6);
  static Color lightBlueColor = Color(0xffF7F9FC);
  static Color cLightBlue = Color(0xffC5CBD5);
  static Color cLightOrange = Color(0xffFEDCCD);
  static Color cLightRed = Color(0xffFEE2E2);
  static Color lightOrangeColor = Color(0xffFEEDE6);
  static Color cGreen = Color(0xff008A37);
  static Color cLightGreen = Color(0xffE1EFE3);
  static Color cLiteYellow = Color(0xffffc266);
  static Color cLightGreenBarLine = Color(0xffA1CEA6);
  static Color cBarBlueLine = Color(0xff2297D4);
  static Color cYellow = Color(0xffC2990A);
  static Color cLightYellow = Color(0xffF6EBC2);
  static Color cFilled = Color(0xffF8F9FD);
  static Color cLightGrey = Color(0xffEEF1F6);
  static Color cLightBlueBtn = Color.fromRGBO(0, 157, 223, 0.50);
  static Color cFocusedTextField = Color.fromRGBO(36, 87, 182, 0.25);
  static Color cLightBlueBorder = Color(0xff99B5EB);
  static Color cDarkBlueBorder = Color(0xff183977);
  static Color cLightBlueContainer = Color(0xffEAF0FB);
  static Color cLightOrangeContainer = Color(0xffFFF4F0);
  static Color cMediumGreyContainer = Color.fromARGB(255, 157, 160, 165);

  ///text color
  static Color cWhiteFont = Color(0xffffffff);
  static Color cBlackFont = Colors.black;
  static Color cFont = Color(0xff13141C);
  static Color cDarkBlueFont = Color(0xff2457B6);
  static Color cDarkBlueText = Color(0xff1E4999);
  static Color cLinkText = Color(0xff296FF5);
  static Color cBlueFont = Color(0xff009DDF);
  static Color cDarkFont = Color(0xff0F0F0F);
  static Color cOrangeFont = Color(0xffFC6423);
  static Color cLightBlueFont = Color(0xffC5CBD5);
  // static Color cText = Color(0xff020202);
  static Color cText = Color(0xff030303);
  static Color cDarkGreyFont = Color(0xff686F7A);
  static Color cLightGreyFont = Color.fromRGBO(255, 255, 255, 0.70);
  static Color cHintFont = Color(0xff686F7A);
  static Color cLabel = Color(0xff161616);
  static Color cRedText = Color(0xffEB0909);
  static Color cLightRedContainer = Color.fromARGB(255, 251, 234, 234);
  static Color cDarkOrangeText = Color(0xff9F3000);
  static Color cIndicator = Color(0xff949DAB);
  static Color cShadow = Color.fromRGBO(0, 0, 0, 0.04);
  static Color cDivider = Color(0xffC5CBD5);
  static Color cEhsan = Color(0xff183650);

  static void updateColors(Map<String, String> colorMap) {
    log('update color dynamically $colorMap');
    if (colorMap.containsKey('themeBlueColor')) {
      themeBlueColor = Color(int.parse(colorMap['themeBlueColor']!));
    }
    if (colorMap.containsKey('themeOrangeColor')) {
      themeOrangeColor = Color(int.parse(colorMap['themeOrangeColor']!));
    }
    if (colorMap.containsKey('themeDarkBlueColor')) {
      themeDarkBlueColor = Color(int.parse(colorMap['themeDarkBlueColor']!));
    }
    if (colorMap.containsKey('cBlueFont')) {
      cBlueFont = Color(int.parse(colorMap['cBlueFont']!));
    }
    if (colorMap.containsKey('cDarkBlueFont')) {
      cDarkBlueFont = Color(int.parse(colorMap['cDarkBlueFont']!));
    }
    if (colorMap.containsKey('cDarkBlueText')) {
      cDarkBlueText = Color(int.parse(colorMap['cDarkBlueText']!));
    }
    if (colorMap.containsKey('cLinkText')) {
      cLinkText = Color(int.parse(colorMap['cLinkText']!));
    }
    if (colorMap.containsKey('cOrangeFont')) {
      cOrangeFont = Color(int.parse(colorMap['cOrangeFont']!));
    }
  }
}
