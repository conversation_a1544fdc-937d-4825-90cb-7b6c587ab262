// ignore_for_file: prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/bank_ac_detail.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';

import '../../../../utils/images.dart';
import '../../../widget/common_button.dart';

class TopUpRefundScreen extends StatelessWidget {
  const TopUpRefundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Container(
                decoration: BoxDecoration(
                  color: AppColor.lightBlueColor,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: AppColor.cLightGrey),
                ),
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonIconButton(
                      title: "Current Balance".trr + ':  176000.00 SAR',
                      iconData: DefaultImages.checkIcn,
                      btnColor: AppColor.themeDarkBlueColor,
                      textColor: AppColor.cWhiteFont,
                      onPressed: () {},
                      textStyle: pBold12.copyWith(
                          fontSize: 13, color: AppColor.cWhiteFont),
                    ),
                    verticalSpace(16),
                    Container(
                      width: Get.width,
                      decoration: BoxDecoration(
                        color: AppColor.cLightGrey,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: AppColor.cLightGrey),
                      ),
                      padding:
                          EdgeInsets.symmetric(vertical: 11, horizontal: 16),
                      child: Center(
                          child: Text(
                              "Unallocated balance".trr + ":  1100.00 SAR",
                              style: pBold12.copyWith(fontSize: 13))),
                    ),
                    verticalSpace(16),
                    horizontalDivider(),
                    verticalSpace(16),
                    Text(
                      "Amount to refund".trr + ":",
                      style: pSemiBold14,
                    ),
                    verticalSpace(8),
                    CommonTextField(
                      labelText: '',
                      hintText: "SAR | Enter amount".trr,
                      keyboardType: TextInputType.numberWithOptions(
                          signed: true, decimal: true),
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    )
                  ],
                )),
            verticalSpace(32),
            //BankAcDetailWidget(),
          ],
        ),
      ),
    );
  }
}
