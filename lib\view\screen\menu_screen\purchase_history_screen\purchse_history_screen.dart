// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/purchase_history_controller/purchase_history_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_balance_history_widget.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_order_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_installation/tag_installation_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import 'balance_filter_screen.dart';
import 'balance_topup_history_screen.dart';
import 'order_filter_screen.dart';
import 'order_screen.dart';

class PurchaseHistoryScreen extends StatelessWidget {
  PurchaseHistoryScreen({super.key});

  PurchaseHistoryController purchaseHistoryController =
      Get.put(PurchaseHistoryController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(right: 16, left: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      //Get.back();
                      Get.off(() => DashBoardManagerScreen(
                            currantIndex: Constants.TopUpBtn == 'Y' ? 4 : 3,
                          ));
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        top: 15,
                        bottom: 16,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          assetSvdImageWidget(
                              image: DefaultImages.backIcn,
                              colorFilter: ColorFilter.mode(
                                  AppColor.cDarkBlueFont, BlendMode.srcIn)),
                          horizontalSpace(10),
                          Text(
                            "Back".trr,
                            style: pRegular18.copyWith(
                                color: AppColor.cDarkBlueFont, fontSize: 17),
                            textAlign: TextAlign.start,
                          )
                        ],
                      ),
                    ),
                  ),
                  // horizontalSpace(35),
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        "Purchase History".trr,
                        style: pBold20,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  // GestureDetector(
                  //     onTap: () {
                  //       showModalBottomSheet(
                  //         context: context,
                  //         barrierColor: AppColor.cBlackOpacity,
                  //         shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
                  //         isScrollControlled: true,
                  //         builder: (context) {
                  //           return purchaseHistoryController.isOrder.value == true
                  //               ? SearchOrderBottomSheetWidget()
                  //               : SearchBalanceHistoryWidget();
                  //         },
                  //       );
                  //     },
                  //     child: assetSvdImageWidget(image: DefaultImages.searchCircleIcn))
                ],
              ),
            ),
            Expanded(
              child: Obx(() {
                return Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          tabWidget(
                            title: 'Service Order'.trr,
                            isSelected: purchaseHistoryController.isOrder.value,
                            onTap: () {
                              purchaseHistoryController.isOrder.value = true;
                              purchaseHistoryController.isBalanceTopUp.value =
                                  false;
                            },
                          ),
                          tabWidget(
                            title: "Balance Topup".trr,
                            isSelected:
                                purchaseHistoryController.isBalanceTopUp.value,
                            onTap: () {
                              purchaseHistoryController.isOrder.value = false;
                              purchaseHistoryController.isBalanceTopUp.value =
                                  true;
                            },
                          ),
                        ],
                      ),
                      verticalSpace(24),
                      purchaseHistoryController.isOrder.value == true
                          ? OrderScreen()
                          : BalanceTopupHistoryScreen(),
                    ],
                  ),
                );
              }),
            )
          ],
        ),
      ),
      /* bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: GestureDetector(
          onTap: () {
            showModalBottomSheet(
              context: context,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
              backgroundColor: AppColor.cBackGround,
              barrierColor: AppColor.cBlackOpacity,
              isScrollControlled: true,
              builder: (context) {
                return purchaseHistoryController.isOrder.value==true?OrderFilterScreen():BalanceHistoryFilterScreen();
              },
            );
          },
          child: Container(
            decoration: BoxDecoration(color: AppColor.themeDarkBlueColor, borderRadius: BorderRadius.circular(6)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                assetSvdImageWidget(image: DefaultImages.filterIcn),
                horizontalSpace(8),
                Text(
                  "Filter".trr,
                  style: pRegular13.copyWith(
                    color: AppColor.cWhiteFont,
                  ),
                )
              ],
            ),
          ),
        ),
      ),*/
    );
  }
}
