import 'dart:convert';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/reports/report_places.dart';
import 'package:waie_app/models/reports/report_product.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';

import '../../../../models/profile.dart';
import '../../../../models/usemenulist.dart';
import '../../../../utils/api_endpoints.dart';

class StationListController extends GetxController {
  List reportList = [
    {'title': "Expenses Summary", 'subTitle': ""},
    {'title': "Consumption Report", 'subTitle': ""},
    {'title': "Service Report", 'subTitle': ""},
    {'title': "Quota Change Report", 'subTitle': ""},
  ];

  List<String> sortByList = [
    "Station",
    "Place",
    "Branch",
    "Area",
  ];
  RxBool isListview = true.obs;
  RxBool isGraphview = false.obs;

  // Fuel Consumption By Customer
  List connectionStatusList = [''];
  RxString selectedConnectionStatus = ''.obs;
  List plateList = [''];
  RxString selectedPlate = ''.obs;
  List divisionList = [''];
  RxString selectedDivision = ''.obs;
  List departmentList = [''];
  RxString selectedDepartment = ''.obs;
  List stationList = [''];
  RxString selectedStation = ''.obs;
  List driverList = [''];
  RxString selectedDriver = ''.obs;
  List productList = [''];

  RxString selectedProduct = ''.obs;
  RxString selectedsortByList = 'Station'.obs; // Default value
  List serviceList = [''];
  RxString selectedService = ''.obs;
  List vehicleTypeList = [''];
  RxString selectedVehicleType = ''.obs;
  final TextEditingController datePickerController = TextEditingController();
  RxBool isGroupByPlate = false.obs;

  //Fleet Wise Fuel Usage
  final TextEditingController datePickerFleetFromController =
  TextEditingController();
  final TextEditingController datePickerFleetToController =
  TextEditingController();


 // RxList reportProductList = [].obs;
 // RxList reportPlacesList = [].obs;
 /* List<Report_Places> reportPlacesList = [];
  List<Report_Products> reportProductsList = [];*/


  final reportPlacesList = <Report_Places>[].obs;
  final reportProductsList = <Report_Products>[].obs;
  RxList<String> reportPlacesList2 = <String>[].obs;
  RxList<String> reportProductList2 = <String>[].obs;

  //Station Info List

  //Monthly Quota Variance Summary
  final TextEditingController datePickerMonthyController =  TextEditingController();
  final TextEditingController selectedPlaceController =  TextEditingController();
  final TextEditingController selectedProductController =  TextEditingController();
  List placeList = [''];
  RxString selectedPlace = ''.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    loadPlaces();
    loadProducts();
  }
  Future<List<Report_Places>> loadPlaces() async {
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl +
                  ApiEndPoints.authEndpoints.reportLoadPlaces),
          body: {
          });
      print("Report Places List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Places count =
        Report_Places.fromMap(result[i] as Map<String, dynamic>);
        reportPlacesList.add(count);
      }
      reportPlacesList2.value = reportPlacesList.map((item) => item.PLACE_DESC).toList();
      print("reportPlacesList2.value===> $reportPlacesList2");
      print("reportPlacesList.value===> $reportPlacesList");
      return reportPlacesList;
    } catch (e) {
      log("Report Places List Error $e");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<List<Report_Products>> loadProducts() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadProducts),
          body: {"userId": userid});
      //body: {"userId": "000038345"});
      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        Report_Products count =
        Report_Products.fromMap(result[i] as Map<String, dynamic>);
        reportProductsList.add(count);
      }
      reportProductList2.value = reportProductsList.map((item) => item.TYPEDESC).toList();

      return reportProductsList;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  reportRequestSubmit() async{
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    String sorted="";
    if(selectedsortByList.value=="station")
      {
        sorted="S";
      }
    else if(selectedsortByList.value=="branch")
    {
      sorted="B";
    }
    else if(selectedsortByList.value=="place")
    {
      sorted="P";
    }
    else  {
      sorted="A";
    }
    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map dataBody = {};
    dataBody = {"STATION_SORT_BY":sorted,
      "LIST_FUELTYPE":selectedProductController.text,
      "LIST_PLACES":selectedPlaceController.text,
      "EMAILID":userData.auCust!.emailid!,
      "CUSTID":userid,
      "REPORTTYPE":"StationInfoList"};

    print("Json Value=================="+dataBody.toString());
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportReqSubmit),
        //  body: jsonEncode(dataBody));
          //body: dataBody,
          body: jsonEncode(dataBody),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          }
      );
      print("REPORT RESPONSE============"+response.body);
      //List result = jsonDecode(response.body);
      commonToast(response.body);
      Get.back();
      return "";
    } catch (e) {
      log(e.toString());
      commonToast(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}