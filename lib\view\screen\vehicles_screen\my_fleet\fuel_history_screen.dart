// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:waie_app/utils/insert_dictionary.dart';

import 'action_widget.dart';
import 'my_fleet_screen.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'assign_digital_coupon_widget.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';

class FuelHistoryScreen extends StatelessWidget {
  FuelHistoryScreen({super.key});

  VehicleController vehicleController = Get.find();

  @override
  Widget build(BuildContext context) {
    String status = 'Active';
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            fuelHistoryTitleDataWidget(
              code: '0411XTA',
              status: status.toString().trr,
              title: 'Nissan NV300',
              type: 'Diesel'.toString().trr,
              viewMore: () {
                showModalBottomSheet(
                  context: context,
                  shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16))),
                  backgroundColor: AppColor.cBackGround,
                  barrierColor: AppColor.cBlackOpacity,
                  isScrollControlled: true,
                  builder: (context) {
                    return ActionWidget(
                      code: '0411XTA',
                      serviceStatus: status.toString(),
                      isComplaint: status == "Active" ? true : false,
                    );
                  },
                );
              },
              scanTap: () {
                showModalBottomSheet(
                  context: context,
                  shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16))),
                  backgroundColor: AppColor.cBackGround,
                  barrierColor: AppColor.cBlackOpacity,
                  isScrollControlled: true,
                  builder: (context) {
                    return AssignDigitalCouponWidget(
                      code: '0411XTA',
                    );
                  },
                );
              },
            ),
            verticalSpace(28),
            ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: vehicleController.fuelDetailList.length,
              itemBuilder: (context, index) {
                var data = vehicleController.fuelDetailList[index];
                return Obx(() {
                  return fuelDetailWidget(
                    date: data['date'],
                    driver: data['driver'],
                    amount: data['amount'],
                    location: data['location'],
                    price: data['price'],
                    litres: data['litres'],
                    service: data['service'].toString().trr,
                    isShowMore: data['isShowMore'].value,
                    onTap: () {
                      data['isShowMore'].value = !data['isShowMore'].value;
                    },
                  );
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget fuelDetailWidget({
    String? date,
    String? driver,
    String? amount,
    String? location,
    String? price,
    String? litres,
    String? service,
    bool? isShowMore,
    Function()? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
        decoration: BoxDecoration(
          color: AppColor.lightBlueColor,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: AppColor.cLightGrey),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildRowWidget("Date".trr, date!),
            verticalSpace(12),
            buildRowWidget("Driver".trr, driver!),
            verticalSpace(12),
            buildRowWidget("Amount".trr, amount!),
            verticalSpace(12),
            isShowMore == true
                ? Column(children: [
                    buildRowWidget("Location".trr, location!),
                    verticalSpace(12),
                    buildRowWidget("Price / Liter, SAR".trr, price!),
                    verticalSpace(12),
                    buildRowWidget("Litres".trr, litres!),
                    verticalSpace(12),
                    buildRowWidget("Service type".trr, service!),
                  ])
                : SizedBox(
                    width: 0,
                    height: 0,
                  ),
            GestureDetector(
              onTap: onTap,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    isShowMore == true ? "Details".trr : "Hide".trr,
                    style: pSemiBold12.copyWith(color: AppColor.cDarkBlueFont),
                  ),
                  horizontalSpace(4),
                  assetSvdImageWidget(image: DefaultImages.blueArrowDownIcn)
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  buildRowWidget(String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              title,
              style: pRegular10.copyWith(
                  fontSize: 13, color: AppColor.cDarkGreyFont),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: pRegular10.copyWith(fontSize: 13, color: AppColor.cText),
            ),
          ),
        ],
      ),
    );
  }

  fuelHistoryTitleDataWidget({
    required String code,
    String? status,
    String? title,
    String? type,
    Function()? viewMore,
    Function()? scanTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        color: status == "New"
                            ? AppColor.cLightGrey
                            : AppColor.cLightBlueContainer,
                        border: Border.all(
                            color: status == "New"
                                ? AppColor.cLightGrey
                                : AppColor.cLightBlueContainer),
                        borderRadius: BorderRadius.circular(4)),
                    padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 16,
                            color: status == 'New'
                                ? AppColor.cDarkGreyFont
                                : AppColor.cDarkBlueText),
                      ),
                    )),
                horizontalSpace(8),
                statusWidget(
                    text: status,
                    textColor: status == "Inactive"
                        ? AppColor.cYellow
                        : status == "New"
                            ? AppColor.cDarkBlueFont
                            : AppColor.cGreen,
                    color: status == "Inactive"
                        ? AppColor.cLightYellow
                        : status == "New"
                            ? AppColor.cLightBlueContainer
                            : AppColor.cLightGreen,
                    tag: status == "Inactive"
                        ? DefaultImages.inactiveIcn
                        : DefaultImages.tagIcn,
                    horizontalSpace: 4),
                horizontalSpace(8),
                GestureDetector(
                  onTap: status == "Active" ? scanTap : null,
                  child: assetSvdImageWidget(
                      image: DefaultImages.scannerIcn,
                      colorFilter: ColorFilter.mode(
                          status == "Active"
                              ? AppColor.cText
                              : AppColor.cLightBlueFont,
                          BlendMode.srcIn)),
                )
              ],
            ),
            GestureDetector(
                onTap: viewMore,
                child:
                    assetSvdImageWidget(image: DefaultImages.verticleMoreIcn))
          ],
        ),
        verticalSpace(8),
        title == ''
            ? SizedBox()
            : Row(
                children: [
                  Text(
                    title!,
                    style: pRegular13,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: assetSvdImageWidget(image: DefaultImages.dotIcn),
                  ),
                  Text(
                    type!,
                    style: pRegular13,
                  ),
                ],
              ),
      ],
    );
  }
}
