import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_player/video_player.dart';
import 'package:audio_session/audio_session.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../core/controller/splash_controller/splash_controller.dart';
import '../../../utils/colors.dart';
import '../login_manager/login_manager_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  SplashController splashController = Get.put(SplashController());
  late VideoPlayerController _videoController;

  @override
  void initState() {
    super.initState();
    _loadSplashImage();
    _setupAudioSession();
    _initializeVideo();

    startTime();
  }

  Future<void> _initializeVideo() async {
    _videoController =
        VideoPlayerController.asset('asset/video/splash_videoaudio.mp4')
          ..initialize().then((_) {
            setState(() {});
            _videoController.play();
            _videoController.setLooping(false);
          });
  }

  Future<void> _setupAudioSession() async {
    final session = await AudioSession.instance;
    await session.configure(AudioSessionConfiguration(
      avAudioSessionCategory: AVAudioSessionCategory.ambient,
      avAudioSessionCategoryOptions:
          AVAudioSessionCategoryOptions.mixWithOthers,
      avAudioSessionMode: AVAudioSessionMode.defaultMode,
      avAudioSessionRouteSharingPolicy:
          AVAudioSessionRouteSharingPolicy.defaultPolicy,
      avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
      androidAudioAttributes: const AndroidAudioAttributes(
        contentType: AndroidAudioContentType.movie,
        flags: AndroidAudioFlags.none,
        usage: AndroidAudioUsage.media,
      ),
      androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      androidWillPauseWhenDucked: true,
    ));
  }

  startTime() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    print("connectivityResult");
    print(connectivityResult);
    if (connectivityResult == ConnectivityResult.none) {
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text(
              'Please turn on your wifi or mobile data and restart the App')));
    } else {
      var onResponse = await splashController.checkUpdate();
      // print("_SplashScreenState");
      print("onResponse");
      print(onResponse);
      // print("_SplashScreenState");
      // print(onResponse[0]["RESULT"]);
      // print(onResponse[0]["REMARKS"]);

      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      var appName = packageInfo.appName;
      var packageName = packageInfo.packageName;
      var version = packageInfo.version;
      var buildNumber = packageInfo.buildNumber;

      if (onResponse[0]["RESULT"] == "MANDATORY") {
        _launchURL();
      } else if (onResponse[0]["RESULT"] == "TERMINATED") {
        var terminateMsg = onResponse[0]["REMARKS"];
        var terminateLink = onResponse[0]["LINK"];
        await showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  verticalSpace(14),
                  // Text("APP TERMINATED".tr,
                  //     style: pBold20.copyWith(
                  //       color: AppColor.cRedText,
                  //     )),
                  Text("NOTICE".tr,
                      style: pBold20.copyWith(
                        color: AppColor.cRedText,
                      )),
                  verticalSpace(24),
                  Text(
                    "$terminateMsg".tr,
                    style: pRegular16,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  // TextButton(
                  //     onPressed: () {
                  //       exit(0);
                  //     },
                  //     child: Text(
                  //       "OK",
                  //       style: pBold18,
                  //     ))
                  CommonButton(
                    title: "Download now".tr,
                    onPressed: () {
                      if (Platform.isIOS) {
                        _launchNewURL(terminateLink);
                      } else {
                        _launchNewURL(terminateLink);
                      }
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                  // TextButton(
                  //     onPressed: () {
                  //       _launchNewURL();
                  //     },
                  //     child: Text(
                  //       "OK",
                  //       style: pBold18,
                  //     ))
                ],
              ),
            );
          },
        );
      } else {
        var duracion = const Duration(seconds: 8);
        return Timer(duracion, route);
      }
    }
  }

  route() {
    Navigator.pushReplacement(
        context,
        //MaterialPageRoute(builder: (context) => const LoginWithEmailScreen()));
        // MaterialPageRoute(
        //     builder: (context) => const LoginManagerWithEmailScreen()));
        // Default Login Using to phone number to user
        MaterialPageRoute(builder: (context) => LoginManagerScreen()));
    // MaterialPageRoute(builder: (context) => LoginScreen());
  }

  _launchNewURL(terminateLink) async {
    if (Platform.isIOS) {
      //splashController.updateURLIOS;
      if (await canLaunch("$terminateLink")) {
        await launch("$terminateLink");
      } else {
        throw 'Could not launch ${"$terminateLink"}';
      }
    } else {
      //splashController.updateURLANDROID;
      if (await canLaunch("$terminateLink")) {
        await launch("$terminateLink");
      } else {
        throw 'Could not launch ${"$terminateLink"}';
      }
    }
  }

  _launchURL() async {
    if (Platform.isIOS) {
      splashController.updateURLIOS;
      if (await canLaunch(splashController.updateURLIOS)) {
        await launch(splashController.updateURLIOS);
      } else {
        throw 'Could not launch ${splashController.updateURLIOS}';
      }
    } else {
      splashController.updateURLANDROID;
      if (await canLaunch(splashController.updateURLANDROID)) {
        await launch(splashController.updateURLANDROID);
      } else {
        throw 'Could not launch ${splashController.updateURLANDROID}';
      }
    }
  }

  Future<void> _loadSplashImage() async {
    String path = await splashController.loadImage('MOBLOGINIMGS');

    log("splash image path ${path.isEmpty}");

    if (path.isNotEmpty) {
      splashController.splashImage = NetworkImage(path);

      await precacheImage(splashController.splashImage, context);

      setState(() {
        splashController.imagePath = path;
      });
      log("check preload image ${await precacheImage(NetworkImage(splashController.imagePath), context).toString()}");
    }

    // setState(() {
    //   splashController.imagePath =
    //       path; //https://waie.aldrees.com/Content/images/
    // });

    // await precacheImage(NetworkImage(splashController.imagePath), context);
  }

  @override
  void dispose() {
    _videoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // if (imagePath.isNotEmpty) {
    //   return Image.network(
    //     imagePath,
    //     width: double.maxFinite,
    //   );
    // } else {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: FittedBox(
          fit: BoxFit.cover,
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: VideoPlayer(_videoController),
          ),
        ),
      ),
    );

    //}
  }

  Widget initWidget(BuildContext context) {
    return Scaffold(body: imageWidget(DefaultImages.splashNationalDayImg)
        // body: Stack(
        //   children: [
        //     Container(
        //       decoration: const BoxDecoration(
        //           color: Colors.black,
        //           gradient: LinearGradient(
        //               colors: [(Colors.black), (Colors.black)],
        //               begin: Alignment.topCenter,
        //               end: Alignment.bottomCenter)),
        //     ),
        //     // Center(
        //     //   child: Image.asset(
        //     //     DefaultImages.splashIcon,
        //     //     height: 100,
        //     //     width: Get.width,
        //     //   ),
        //     // )
        //   ],
        // ),
        );
  }

  Container imageWidget(String image) {
    return Container(
      height: 150,
      width: Get.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        image: DecorationImage(image: AssetImage(image), fit: BoxFit.fill),
      ),
    );
  }
}
