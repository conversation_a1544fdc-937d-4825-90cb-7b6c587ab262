import 'dart:convert';

class UserAccess {
  String hasCloseAccountRequest = "N";
  String Usebalan = "N";
  String promoMenu = "N";
  String promoPayment = "N";
  String ETrans = "N";
  String newETrans = "N";
  String virtualAccountBTN = "N";
  String SCrbBtn = "N";
  String PlaceOrdrBtn = "N";
  String TagrbBtn = "N";
  String OrderHist = "N";
  String BalHist = "N";
  String TopUpBtn = "N";
  String NewOrderBtn = "N";
  String BookingBtn = "N";
  String UnalocAmtView = "N";
  String ResAmtView = "N";
  String isActiveDC = "N";
  String isRSRVDView = "N";
  String MadaPayOpt = "N";
  String profileEnable = "N";
  String ReportSTNList = "N";
  String ReportMnthlyTrStmnt = "N";
  String ReportFleetStmnt = "N";
  String ReportFuelCmsp = "N";
  String ReportMnthlyQtaSum = "N";
  String FleetStruc = "N";
  String BankRYD = "N";
  String BankNCB = "N";
  String BankARB = "N";
  String YGGEnable = "N";
  String STCEnable = "N";
  String AlrajhiMADAEnable = "N";
  String isApplePayEnable = "N";
  String isAlrajhiApplePaySwitchEnable = "N";
  String IsAlinmaEnable = "N";
  String isSTNWAIE = "N";
  String isSTNMOSQ = "N";
  String isSTNCARSERVICE = "N";
  String isSTNFOODRES = "N";
  String isSTNCARRENT = "N";
  String isSTNATM = "N";
  String isDCShow = "N";

  UserAccess({
    required this.hasCloseAccountRequest,
    required this.Usebalan,
    required this.promoMenu,
    required this.promoPayment,
    required this.ETrans,
    required this.newETrans,
    required this.virtualAccountBTN,
    required this.SCrbBtn,
    required this.PlaceOrdrBtn,
    required this.OrderHist,
    required this.BalHist,
    required this.TopUpBtn,
    required this.NewOrderBtn,
    required this.BookingBtn,
    required this.UnalocAmtView,
    required this.ResAmtView,
    required this.isActiveDC,
    required this.isRSRVDView,
    required this.MadaPayOpt,
    required this.profileEnable,
    required this.ReportSTNList,
    required this.ReportMnthlyTrStmnt,
    required this.ReportFleetStmnt,
    required this.ReportFuelCmsp,
    required this.ReportMnthlyQtaSum,
    required this.FleetStruc,
    required this.BankRYD,
    required this.BankNCB,
    required this.BankARB,
    required this.YGGEnable,
    required this.STCEnable,
    required this.AlrajhiMADAEnable,
    required this.isApplePayEnable,
    required this.isAlrajhiApplePaySwitchEnable,
    required this.IsAlinmaEnable,
    required this.isSTNWAIE,
    required this.isSTNMOSQ,
    required this.isSTNCARSERVICE,
    required this.isSTNFOODRES,
    required this.isSTNCARRENT,
    required this.isSTNATM,
    required this.isDCShow,
  });

  Map<String, dynamic> toMap() {
    return {
      'hasCloseAccountRequest': hasCloseAccountRequest,
      'Usebalan': Usebalan,
      'promoMenu': promoMenu,
      'promoPayment': promoPayment,
      'ETrans': ETrans,
      'newETrans': newETrans,
      'virtualAccountBTN': virtualAccountBTN,
      'SCrbBtn': SCrbBtn,
      'PlaceOrdrBtn': PlaceOrdrBtn,
      'OrderHist': OrderHist,
      'BalHist': BalHist,
      'TopUpBtn': TopUpBtn,
      'NewOrderBtn': NewOrderBtn,
      'BookingBtn': BookingBtn,
      'UnalocAmtView': UnalocAmtView,
      'ResAmtView': ResAmtView,
      'isActiveDC': isActiveDC,
      'isRSRVDView': isRSRVDView,
      'MadaPayOpt': MadaPayOpt,
      'profileEnable': profileEnable,
      'ReportSTNList': ReportSTNList,
      'ReportMnthlyTrStmnt': ReportMnthlyTrStmnt,
      'ReportFleetStmnt': ReportFleetStmnt,
      'ReportFuelCmsp': ReportFuelCmsp,
      'ReportMnthlyQtaSum': ReportMnthlyQtaSum,
      'FleetStruc': FleetStruc,
      'BankRYD': BankRYD,
      'BankNCB': BankNCB,
      'BankARB': BankARB,
      'YGGEnable': YGGEnable,
      'STCEnable': STCEnable,
      'AlrajhiMADAEnable': AlrajhiMADAEnable,
      'isApplePayEnable': isApplePayEnable,
      'isAlrajhiApplePaySwitchEnable': isAlrajhiApplePaySwitchEnable,
      'IsAlinmaEnable': IsAlinmaEnable,
      'isSTNWAIE': isSTNWAIE,
      'isSTNMOSQ': isSTNMOSQ,
      'isSTNCARSERVICE': isSTNCARSERVICE,
      'isSTNFOODRES': isSTNFOODRES,
      'isSTNCARRENT': isSTNCARRENT,
      'isSTNATM': isSTNATM,
      'isDCShow': isDCShow,
    };
  }

  factory UserAccess.fromMap(Map<String, dynamic> map) {
    return UserAccess(
      hasCloseAccountRequest: map['hasCloseAccountRequest'] ?? '',
      Usebalan: map['Usebalan'] ?? '',
      promoMenu: map['promoMenu'] ?? '',
      promoPayment: map['promoPayment'] ?? '',
      ETrans: map['ETrans'] ?? '',
      newETrans: map['newETrans'] ?? '',
      virtualAccountBTN: map['virtualAccountBTN'] ?? '',
      SCrbBtn: map['SCrbBtn'] ?? '',
      PlaceOrdrBtn: map['PlaceOrdrBtn'] ?? '',
      OrderHist: map['OrderHist'] ?? '',
      BalHist: map['BalHist'] ?? '',
      TopUpBtn: map['TopUpBtn'] ?? '',
      NewOrderBtn: map['NewOrderBtn'] ?? '',
      BookingBtn: map['BookingBtn'] ?? '',
      UnalocAmtView: map['UnalocAmtView'] ?? '',
      ResAmtView: map['ResAmtView'] ?? '',
      isActiveDC: map['isActiveDC'] ?? '',
      isRSRVDView: map['isRSRVDView'] ?? '',
      MadaPayOpt: map['MadaPayOpt'] ?? '',
      profileEnable: map['profileEnable'] ?? '',
      ReportSTNList: map['ReportSTNList'] ?? '',
      ReportMnthlyTrStmnt: map['ReportMnthlyTrStmnt'] ?? '',
      ReportFleetStmnt: map['ReportFleetStmnt'] ?? '',
      ReportFuelCmsp: map['ReportFuelCmsp'] ?? '',
      ReportMnthlyQtaSum: map['ReportMnthlyQtaSum'] ?? '',
      FleetStruc: map['FleetStruc'] ?? '',
      BankRYD: map['BankRYD'] ?? '',
      BankNCB: map['BankNCB'] ?? '',
      BankARB: map['BankARB'] ?? '',
      YGGEnable: map['YGGEnable'] ?? '',
      STCEnable: map['STCEnable'] ?? '',
      AlrajhiMADAEnable: map['AlrajhiMADAEnable'] ?? '',
      isApplePayEnable: map['isApplePayEnable'] ?? '',
      isAlrajhiApplePaySwitchEnable: map['isAlrajhiApplePaySwitchEnable'] ?? '',
      IsAlinmaEnable: map['IsAlinmaEnable'] ?? '',
      isSTNWAIE: map['isSTNWAIE'] ?? '',
      isSTNMOSQ: map['isSTNMOSQ'] ?? '',
      isSTNCARSERVICE: map['isSTNCARSERVICE'] ?? '',
      isSTNFOODRES: map['isSTNFOODRES'] ?? '',
      isSTNCARRENT: map['isSTNCARRENT'] ?? '',
      isSTNATM: map['isSTNATM'] ?? '',
      isDCShow: map['isDCShow'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory UserAccess.fromJson(String source) =>
      UserAccess.fromMap(json.decode(source));
}
