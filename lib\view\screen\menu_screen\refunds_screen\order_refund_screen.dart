import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refunds_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../user_management_screen/user_management_screen.dart';
import 'no_refund_found_screen.dart';

class OrderRefundScreen extends StatefulWidget {
  const OrderRefundScreen({super.key});

  @override
  State<OrderRefundScreen> createState() => _OrderRefundScreenState();
}

class _OrderRefundScreenState extends State<OrderRefundScreen> {
  RefundsController refundsController = Get.find();

  OrderRefundsController orderRefundsController =
      Get.put(OrderRefundsController());

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () => orderRefundsController.refundablesServices.isEmpty
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : ListView.builder(
                      itemCount:
                          orderRefundsController.refundablesServices.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data =
                            orderRefundsController.refundablesServices[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: refundDataWidget(
                            isShowCheckBox: true,
                            onChanged: (value) {
                              setState(() {
                                data.isvalue = value ?? false;
                              });
                              if (value == true) {
                                refundsController.selectedOrderRefundList
                                    .add(data);
                              } else {
                                refundsController.selectedOrderRefundList
                                    .removeAt(index);
                              }
                              refundsController.selectedOrderRefundList
                                  .refresh();
                            },
                            value: data.isvalue,
                            code: data.reforderid,
                            status: data.servicestatusDisp,
                            orderType: data.servicetype,
                            plate: data.plateno ?? "-",
                            vehicleType: data.vehicletypeDisp,
                            orderDate: data.orderdate,
                            amount: data.rateDisc.toStringAsFixed(2),
                            vat: data.vat.toStringAsFixed(2),
                            textColor: data.servicestatusDisp == "ACTIVE"
                                ? AppColor.cGreen
                                : data.servicestatusDisp == "IN-ACTIVE"
                                    ? AppColor.cYellow
                                    : data.servicestatusDisp == "TERMINATED"
                                        ? AppColor.cRedText
                                        : AppColor.cDarkBlueFont,
                            color: data.servicestatusDisp == "ACTIVE"
                                ? AppColor.cLightGreen
                                : data.servicestatusDisp == "IN-ACTIVE"
                                    ? AppColor.cLightYellow
                                    : data.servicestatusDisp == "TERMINATED"
                                        ? AppColor.cLightRedContainer
                                        : AppColor.cLightBlueContainer,
                          ),
                        );
                      },
                    ),
            ),
            // ListView.builder(
            //     itemCount: refundsController.orderRefundList.length,
            //     shrinkWrap: true,
            //     physics: NeverScrollableScrollPhysics(),
            //     itemBuilder: (context, index) {
            //       var data = refundsController.orderRefundList[index];
            //       return Padding(
            //         padding: const EdgeInsets.only(bottom: 8.0),
            //         child: Obx(() {
            //           return refundDataWidget(
            //             isShowCheckBox: true,
            //             onChanged: (value) {
            //               data['value'].value = value;
            //               if (value == true) {
            //                 refundsController.selectedOrderRefundList
            //                     .add(data);
            //               } else {
            //                 refundsController.selectedOrderRefundList
            //                     .removeAt(index);
            //               }
            //               refundsController.selectedOrderRefundList
            //                   .refresh();
            //             },
            //             value: data['value'].value,
            //             code: data['code'],
            //             status: data['status'].toString().trr,
            //             orderType: data['orderType'].toString().trr,
            //             plate: data['plate'],
            //             vehicleType: data['vehicleType'],
            //             orderDate: data['orderDate'],
            //             amount: data['amount'],
            //             vat: data['vat'],
            //             textColor: data['status'] == "Confirmed"
            //                 ? AppColor.cGreen
            //                 : AppColor.cDarkBlueFont,
            //             color: data['status'] == "Confirmed"
            //                 ? AppColor.cLightGreen
            //                 : AppColor.cLightBlueContainer,
            //           );
            //         }),
            //       );
            //     },
            //   ),
          ],
        ),
      ),
    );
  }
}

Widget refundDataWidget({
  required String code,
  required String status,
  Color? color,
  Color? textColor,
  bool? value,
  ValueChanged<bool?>? onChanged,
  required String orderType,
  required String plate,
  required String vehicleType,
  required String orderDate,
  required String amount,
  required String vat,
  bool? isShowButton = false,
  bool? isShowCheckBox = false,
  Function()? cancelReqOnTap,
}) {
  return Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(4),
      border: Border.all(
          color: value == true ? AppColor.cDarkBlueFont : AppColor.cLightGrey),
    ),
    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                isShowCheckBox == false
                    ? const SizedBox()
                    : SizedBox(
                        height: 24,
                        width: 24,
                        child: Checkbox(
                          value: value,
                          onChanged: onChanged,
                          activeColor: AppColor.themeDarkBlueColor,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                              side: BorderSide(color: AppColor.cBorder)),
                        ),
                      ),
                horizontalSpace(isShowCheckBox == false ? 0 : 8),
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.cLightBlueBorder),
                        borderRadius: BorderRadius.circular(4)),
                    padding:
                        const EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 13, color: AppColor.cDarkBlueText),
                      ),
                    )),
                horizontalSpace(8),
                newWidget(text: status, color: color, textColor: textColor),
              ],
            ),
            // assetSvdImageWidget(image: DefaultImages.verticleMoreIcn)
          ],
        ),
        verticalSpace(18),
        userDataRowWidget(
            title: "Order type".trr,
            value: orderType == "T" ? "Tag" : "Smart Card"),
        verticalSpace(12),
        userDataRowWidget(
            title: "${"Plate".trr} #",
            value: plate,
            textColor: AppColor.cDarkBlueText),
        verticalSpace(12),
        userDataRowWidget(title: "Vehicle type".trr, value: vehicleType),
        verticalSpace(12),
        userDataRowWidget(title: "Order date".trr, value: orderDate),
        verticalSpace(12),
        userDataRowWidget(title: "Amount".trr, value: amount),
        verticalSpace(12),
        userDataRowWidget(title: "VAT".trr, value: vat),
        verticalSpace(isShowButton == true ? 14 : 0),
        isShowButton == true
            ? CommonIconBorderButton(
                iconData: DefaultImages.cancelRequestIcn,
                title: "Cancel request".trr,
                onPressed: cancelReqOnTap,
              )
            : const SizedBox()
      ],
    ),
  );
}
