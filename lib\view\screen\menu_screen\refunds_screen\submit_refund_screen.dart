// ignore_for_file: prefer_const_constructors, must_be_immutable, prefer_interpolation_to_compose_strings

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/submit_order_refund_service_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/new_affiliate_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import '../../auth/digital_coupon/create_dc_screen.dart';
import 'bank_ac_detail.dart';

class SubmitRefundScreen extends StatelessWidget {
  String routedScreen;
  SubmitRefundScreen({super.key, required this.routedScreen});
  final tagOrderRefund = GetStorage();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  RefundsController refundsController = Get.find();
  SubmitOrderRefundServiceController submitOrderRefundServiceController =
      Get.put(SubmitOrderRefundServiceController());

  @override
  Widget build(BuildContext context) {
    log("routed Screen ${this.routedScreen}");
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 16, right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                          top: 15,
                          bottom: 15,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.backIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cDarkBlueFont, BlendMode.srcIn)),
                            horizontalSpace(10),
                            Text(
                              "Back".trr,
                              style: pRegular18.copyWith(
                                  color: AppColor.cDarkBlueFont, fontSize: 17),
                              textAlign: TextAlign.start,
                            )
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "Service Refund Details".trr,
                          style: pBold20,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Obx(() {
                  return ListView(
                    scrollDirection: Axis.vertical,
                    shrinkWrap: true,
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    children: [
                      verticalSpace(24),
                      Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: AppColor.cLightGrey),
                            color: AppColor.lightBlueColor),
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          children: [
                            Gap(12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Current balance".trr + ": ",
                                  style: pSemiBold17,
                                ),
                                Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      assetSvdImageWidget(
                                          image: DefaultImages.saudiRiyal,
                                          width: 16,
                                          height: 16),
                                      Gap(4),
                                      Text(Constants.custBalance,
                                          style: pSemiBold17),
                                    ],
                                  ),
                                ),
                                // Text(
                                //   Constants.custBalance,
                                //   style: pSemiBold17,
                                // ),
                              ],
                            ),
                            Gap(12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'VAT'.trr + ": ",
                                  style: pSemiBold17,
                                ),
                                Text(
                                  tagOrderRefund.read('tVAT').toString(),
                                  style: pSemiBold17,
                                ),
                              ],
                            ),
                            horizontalDivider(),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Total Amount'.trr + ": ",
                                  style: pSemiBold17,
                                ),
                                Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      assetSvdImageWidget(
                                          image: DefaultImages.saudiRiyal,
                                          width: 16,
                                          height: 16),
                                      Gap(4),
                                      Text(
                                          tagOrderRefund
                                              .read('tAmt')
                                              .toString(),
                                          style: pSemiBold17),
                                    ],
                                  ),
                                ),
                                // Text(
                                //   tagOrderRefund.read('tAmt').toString(),
                                //   style: pSemiBold17,
                                // ),
                              ],
                            ),
                            Gap(12),
                          ],
                        ),
                      ),
                      verticalSpace(24),
                      Row(
                        children: [
                          Expanded(
                            flex: 1,
                            child: selectRadioWidget(
                                onTap: () {
                                  refundsController.isBalance.value = true;
                                  refundsController.isDeposit.value = false;
                                },
                                icon: refundsController.isBalance.value == true
                                    ? DefaultImages.checkCircleIcn
                                    : DefaultImages.circleIcn,
                                title: "Convert to balance".trr,
                                borderColor:
                                    refundsController.isBalance.value == true
                                        ? AppColor.themeDarkBlueColor
                                        : AppColor.cBorder),
                          ),
                          // horizontalSpace(16),
                          // // Expanded(
                          //   flex: 1,
                          //   child: selectRadioWidget(
                          //       onTap: () {
                          //         refundsController.isBalance.value = false;
                          //         refundsController.isDeposit.value = true;
                          //       },
                          //       icon: refundsController.isDeposit.value == true
                          //           ? DefaultImages.checkCircleIcn
                          //           : DefaultImages.circleIcn,
                          //       title: "Debit Note".trr,
                          //       borderColor:
                          //           refundsController.isDeposit.value == true
                          //               ? AppColor.themeDarkBlueColor
                          //               : AppColor.cBorder),
                          // ),
                      
                        ],
                      ),
                      verticalSpace(24),
                      refundsController.isBalance.value == true
                          ? convertToBalanceWidget(context)
                          : BankAcDetailWidget(
                              isDebit: refundsController.isDeposit.value),
                    ],
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget convertToBalanceWidget(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          CommonTextField(
            controller:
                submitOrderRefundServiceController.reasonRefundController,
            labelText: "Reason for refund".trr,
            hintText: "Let us know why you need a refund".trr,
            maxLines: 5,
            validator: (value) {
              return Validator.validateRequired(value);
            },
          ),
          verticalSpace(24),
          Container(
            padding: EdgeInsets.symmetric(vertical: 7, horizontal: 16),
            decoration: BoxDecoration(
                color: AppColor.lightOrangeColor,
                borderRadius: BorderRadius.circular(6)),
            child: Text(
                "!  " +
                    "Refunded orders don't count towards your bulk discount on your next order."
                        .trr,
                style: pRegular13.copyWith(color: AppColor.cDarkOrangeText)),
          ),
          verticalSpace(24),
          CommonButton(
            title: "Submit".trr,
            onPressed: () {
              log("submit button tapped");
              log("submit button tapped routedCheck ${this.routedScreen}");

              if (_formKey.currentState!.validate()) {
                if (refundsController.isBalance.value == true) {
                  submitOrderRefundServiceController.rblRefundOPT.value = "B";

                  log("submit button tapped check2");

                  if (this.routedScreen == "OrderRefundServiceMenuScreen") {
                    log("submit button tapped check3");

                    submitOrderRefundServiceController.submitOrderRefundFormB();
                  } else if (this.routedScreen ==
                      "OrderRefundTopupMenuScreen") {
                    log("submit button tapped check4");

                    submitOrderRefundServiceController
                        .submitOrderRefundForTopupNReserved();
                  }
                } else {}
              }
              // Get.back();
              // showDialog(
              //   context: context,
              //   builder: (context) {
              //     return AlertDialog(
              //       shape: RoundedRectangleBorder(
              //           borderRadius: BorderRadius.circular(12)),
              //       insetPadding: EdgeInsets.all(16),
              //       contentPadding: EdgeInsets.all(24),
              //       content: successDialogWidget(
              //           title: "You've submitted a refund request".trr,
              //           subTitle:
              //               "We'll let you know when it's been approved.".trr, () {
              //         Get.back();
              //       }, isBorderBtn: true),
              //     );
              //   },
              // );
            },
            btnColor: AppColor.themeOrangeColor,
          ),
        ],
      ),
    );
  }
}
