class UserMenulist {
  UserMenulist({
    required this.menuCode,
    required this.menuName,
    required this.menu,
  });

  final String? menuCode;
  final String? menuName;
  final List<SubMenu> menu;

  factory UserMenulist.fromJson(Map<String, dynamic> json) {
    return UserMenulist(
      menuCode: json["MenuCode"] ?? '',
      menuName: json["MenuName"] ?? '',
      menu: json["Menu"] == null
          ? []
          : List<SubMenu>.from(json["Menu"]!.map((x) => SubMenu.fromJson(x))),
    );
  }
}

class SubMenu {
  SubMenu({
    required this.menuCode,
    required this.controlCode,
    required this.controlName,
    required this.subCont,
  });

  final String? menuCode;
  final String? controlCode;
  final String? controlName;
  final List<SubMenu> subCont;

  factory SubMenu.fromJson(Map<String, dynamic> json) {
    return SubMenu(
      menuCode: json["MenuCode"] ?? '',
      controlCode: json["ControlCode"] ?? '',
      controlName: json["ControlName"] ?? '',
      subCont: json["SubCont"] == null
          ? []
          : List<SubMenu>.from(json["SubCont"]!.map((x) => SubMenu.fromJson(x))),
    );
  }
}

class SubCont {
  String menuCode;
  String controlCode;
  String controlName;

  SubCont({
    required this.menuCode,
    required this.controlCode,
    required this.controlName,
  });

  factory SubCont.fromJson(Map<String, dynamic> json) => SubCont(
        menuCode: json["MenuCode"] ?? '',
        controlCode: json["ControlCode"] ?? '',
        controlName: json["ControlName"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "MenuCode": menuCode,
        "ControlCode": controlCode,
        "ControlName": controlName,
      };
}
