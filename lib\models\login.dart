/// Item1 : "1000000887"
/// Item2 : "<EMAIL>"
/// Item3 : "0.15"

class Login {
  Login({
      String? item1, 
      String? item2, 
      String? item3,}){
    _userId = item1;
    _emailId = item2;
    _balance = item3;
}

  Login.fromJson(dynamic json) {
    _userId = json['Item1'];
    _emailId = json['Item2'];
    _balance = json['Item3'];
  }
  String? _userId;
  String? _emailId;
  String? _balance;
Login copyWith({  String? item1,
  String? item2,
  String? item3,
}) => Login(  item1: item1 ?? _userId,
  item2: item2 ?? _emailId,
  item3: item3 ?? _balance,
);
  String? get item1 => _userId;
  String? get item2 => _emailId;
  String? get item3 => _balance;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['Item1'] = _userId;
    map['Item2'] = _emailId;
    map['Item3'] = _balance;
    return map;
  }

}