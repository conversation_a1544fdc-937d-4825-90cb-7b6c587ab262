import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/generalnotification.dart';
import 'package:waie_app/models/notycount.dart';

import '../../../models/profile.dart';
import '../../../utils/api_endpoints.dart';

class NotificationController extends GetxController {
  RxBool isActivity = true.obs;
  RxBool isComplaints = false.obs;
  RxString activityValue = 'All Activities'.obs;
  RxList genNotification = [].obs;
  RxList genCompNotification = [].obs;
  RxBool isHistory = true.obs;
  RxString genNotyCount = "".obs;
  RxString genCompNotyCount = "".obs;

  @override
  void onInit() {
    super.onInit();
    print('BalanceController');
    fetchNotificationCount();
  }

  getDatas() async {
    await fetchNotification();
    await fetchComplaintNotification();
  }

  Future<dynamic> fetchNotification() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getGenNotification),
          body: {"custid": userid, "is_complaint": "false"});
//          body: {"custid": userid, "is_complaint": "false"});
      print("Notifivation response.body=============" + response.body);
      //List <GeneralNotification>notyList = jsonDecode(response.body);
      List result = jsonDecode(response.body);
      genNotification.clear();
      for (int i = 0; i < result.length; i++) {
        GeneralNotification notyList =
            GeneralNotification.fromMap(result[i] as Map<String, dynamic>);

        await Future.delayed(
            const Duration(seconds: 0), () => genNotification.add(notyList));
      }

      return genNotification;
    } catch (e) {
      log("Notification Gen Error======" + e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<dynamic> fetchComplaintNotification() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getGenNotification),
          body: {"custid": userid, "is_complaint": "true"});
      print("Notifivation Comp response.body=============" + response.body);
      List result = jsonDecode(response.body);
      genCompNotification.clear();
      for (int i = 0; i < result.length; i++) {
        GeneralNotification notyList =
            GeneralNotification.fromMap(result[i] as Map<String, dynamic>);

        await Future.delayed(const Duration(seconds: 0),
            () => genCompNotification.add(notyList));
      }
      return genCompNotification;
    } catch (e) {
      log("Notification Comp Error======" + e.toString());
      return [];
    } finally {
      client.close();
    }
  }

  Future<dynamic> fetchNotificationCount() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getNotyCount),
          body: {"custid": userid});
      print("NOTIDICATIOCOUNT================" + response.body);
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        NotyCount notyList =
            NotyCount.fromMap(result[i] as Map<String, dynamic>);
        genCompNotyCount.value = notyList.compCount.toString();
        await Future.delayed(const Duration(seconds: 0),
            () => genNotyCount.value = notyList.genCount.toString());
      }
      return genCompNotification;
    } catch (e) {
      log("Notification Comp Error======" + e.toString());
      return [];
    } finally {
      client.close();
    }
  }

  Future<dynamic> UpdateNotyRead() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getNotyCount),
          body: {"custid": userid});
      print("NOTIDICATIOCOUNT================" + response.body);
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        NotyCount notyList =
        NotyCount.fromMap(result[i] as Map<String, dynamic>);
        genCompNotyCount.value = notyList.compCount.toString();
        await Future.delayed(const Duration(seconds: 0),
                () => genNotyCount.value = notyList.genCount.toString());
      }
      // genNotyCount.value=count[0].genCount.toString();

      return genCompNotification;
    } catch (e) {
      log("Notification Comp Error======" + e.toString());
      return [];
    } finally {      // Then finally destroy the client.
      client.close();
    }
  }
}
