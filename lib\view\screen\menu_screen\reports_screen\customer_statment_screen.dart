// ignore_for_file: prefer_const_constructors

import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/customer_statment_controller.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/reports_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/reports_controller/monthly_quota_var_summary_controller.dart';
import '../../../widget/common_appbar_widget.dart';

class CustomerStatmentScreen extends StatefulWidget {
  final String title;
  const CustomerStatmentScreen({super.key, required this.title});

  @override
  State<CustomerStatmentScreen> createState() => _CustomerStatmentScreenState();
}

class _CustomerStatmentScreenState extends State<CustomerStatmentScreen> {
  CustomerStatmentController reportController =
  Get.put((CustomerStatmentController()));

  pickDate() async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColor.themeOrangeColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor:
                AppColor.themeDarkBlueColor, // button text color
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (pickedDate != null) {
      print(pickedDate);
      //String formattedDate = DateFormat('MM/dd/yy').format(pickedDate);
      String formattedDate = DateFormat('MM/yyyy').format(pickedDate);
      print(formattedDate);

      reportController.datePickerFleetFromController.text = formattedDate;
    } else {
      print("Date is not selected");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  simpleMyAppBar(
                      title: "".trr,
                      backString: "Back".trr,
                      onTap: () {
                        Get.back();
                      },
                      backColor: AppColor.cBlueFont),
                ],
              ),
            ),
           /* Expanded(
              child: Obx(() {
                return ListView(
                  padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
                  physics: BouncingScrollPhysics(),
                  shrinkWrap: true,
                  children: [
                    Expanded(
                      flex: 2,
                      child: CommonTextField(
                        controller:
                        reportController.datePickerFleetFromController,
                        labelText: '${"Period".trr}*',
                        suffix: assetSvdImageWidget(
                            image: DefaultImages.calendarIcn),
                        fillColor: AppColor.cWhite,
                        filled: true,
                        readOnly: true,
                        onTap: pickDate,
                      ),
                    ),
                    Gap(16),
                    Expanded(
                      flex: 2,
                      child: CommonDropdownButtonWidget(
                        hint: '',
                        labelText: 'Connection Status'.trr,
                        list: reportController.connectionStatusList, // Your adjusted list
                        value: reportController.selectedconnectionStatusList.value, // Currently selected value
                        onChanged: (value) {
                          if(value != null) { // Assuming onChanged provides a non-null value
                            reportController.selectedconnectionStatusList.value = value; // Update the selected value
                          }
                        },
                        fontColor: AppColor.cDarkGreyFont,
                        filledColor: AppColor.cFilled,
                      ),
                    ),
                    Gap(16),
                    Row(
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: Checkbox(
                            value: reportController.isGroupByPlate.value,
                            onChanged: (value) {
                              reportController.isGroupByPlate.value = value!;
                            },
                            activeColor: AppColor.themeBlueColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                            materialTapTargetSize:
                            MaterialTapTargetSize.shrinkWrap,
                          ),
                        ),
                        horizontalSpace(8),
                        Text(
                          'Group By Plate'.trr,
                          style: pRegular14,
                        )
                      ],
                    ),
                  ],
                );
              }),
            ),*/
            Center(
              child: Text(
                widget.title,
                style: pBold20,
                textAlign: TextAlign.center,
              ),
            ),
            Gap(16),
            ListView(
              padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
              physics: BouncingScrollPhysics(),
              shrinkWrap: true,
              children: [
                CommonTextField(
                  controller: reportController.datePickerFleetFromController,
                  labelText: '${"Period".trr}*',
                  suffix: assetSvdImageWidget(image: DefaultImages.calendarIcn),
                  fillColor: AppColor.cWhite,
                  filled: true,
                  readOnly: true,
                  onTap: pickDate,
                ),
                Gap(16),
                CommonDropdownButtonWidget(
                  hint: '',
                  labelText: 'Connection Status'.trr,
                  list: reportController.connectionStatusList,
                  value: reportController.selectedconnectionStatusList.value,
                  onChanged: (value) {
                    if(value != null) {
                      reportController.selectedconnectionStatusList.value = value;
                    }
                  },
                  fontColor: AppColor.cDarkGreyFont,
                  filledColor: AppColor.cFilled,
                ),
                Gap(16),
                // The rest of your ListView children...
              ],
            ),
          ],
        ),
      ),
     /* bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: Expanded(
          child: CommonButton(
            title: 'SUBMIT'.trr,
            onPressed: () {
              reportController.reportRequestSubmit();
            },
            textColor: AppColor.cWhiteFont,
            btnColor: AppColor.themeOrangeColor,
          ),
        ),
      ),*/
      bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: CommonButton(  // No need for Expanded here, directly place your button.
          title: 'SUBMIT'.trr,
          onPressed: () {
            reportController.reportRequestSubmit();
          },
          textColor: AppColor.cWhiteFont,
          btnColor: AppColor.themeOrangeColor,
        ),
      ),
    );
  }
}
