import 'dart:convert';

List<PriceTagCardModel> priceTagCardFromJson(String str) =>
    List<PriceTagCardModel>.from(
        json.decode(str).map((x) => PriceTagCardModel.fromJson(x)));

String priceTagCardToJson(List<PriceTagCardModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class PriceTagCardModel {
  String topUpAmount;
  String unitPrice;
  String serviceType;
  String vatAmount;
  String amount;
  String totalAMount;
  String subTotal;
  String qty;

  PriceTagCardModel({
    required this.topUpAmount,
    required this.unitPrice,
    required this.serviceType,
    required this.vatAmount,
    required this.amount,
    required this.totalAMount,
    required this.subTotal,
    required this.qty,
  });

  factory PriceTagCardModel.fromMap(Map<String, dynamic> map) {
    return PriceTagCardModel(
      topUpAmount: map["TOPUPAMT"] ?? "",
      unitPrice: map["UNITPRICE"] ?? "",
      serviceType: map["SERVICETYPE"] ?? "",
      vatAmount: map["VATAMT"] ?? "",
      amount: map["AMT"] ?? "",
      totalAMount: map["TOTAMT"] ?? "",
      subTotal: map["SUBTOTAL"] ?? "",
      qty: map["QUANTITY"] ?? "",
    );
  }

  factory PriceTagCardModel.fromJson(Map<String, dynamic> json) =>
      PriceTagCardModel(
        topUpAmount: json["TOPUPAMT"],
        unitPrice: json["UNITPRICE"],
        serviceType: json["SERVICETYPE"],
        vatAmount: json["VATAMT"],
        amount: json["AMT"],
        totalAMount: json["TOTAMT"],
        subTotal: json["SUBTOTAL"],
        qty: json["QUANTITY"],
      );

  Map<String, dynamic> toJson() => {
        "TOPUPAMT": topUpAmount,
        "UNITPRICE": unitPrice,
        "SERVICETYPE": serviceType,
        "VATAMT": amount,
        "AMT": amount,
        "TOTAMT": totalAMount,
        "SUBTOTAL": subTotal,
        "QUANTITY": qty,
      };
}
