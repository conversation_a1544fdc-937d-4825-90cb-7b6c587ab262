import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/utils/api_endpoints.dart';

class RemoteServices {
  /*static Future<List<FleetModel>?> fetchFleets() async {
    var client = http.Client();

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map body = {
      "SEARCHBY": "",
      "TOP": "10",
      "SKIP": "2",
     // "CUSTID": "000000054A",
      "CUSTID": "000003944",
      "SERIALID": ""
    };
    var response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
        body: jsonEncode(body),
        headers: {
          'authorization': basicAuth,
          "Content-Type": "application/json",
        });
    print("statusCode===> ${response.statusCode}");
    print("response body===> ${response.body}");
    if (response.statusCode == 200) {
      var jsonString = response.body;
      print("jsonString ===> $jsonString");
      print(
          "fleetModelFromJson(jsonString) ===> $fleetModelFromJson(jsonString)");
      return fleetModelFromJson(jsonString);
    } else {
      return null;
    }
  }*/
}
