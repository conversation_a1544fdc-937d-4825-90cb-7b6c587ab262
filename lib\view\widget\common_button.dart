// ignore_for_file: prefer_const_constructors_in_immutables, prefer_const_constructors

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class CommonButton extends StatelessWidget {
  final String? title;
  final double? height;
  final double? width;
  final double? fontSize;
  final double? horizontalPadding;
  final Function()? onPressed;
  final Color? bColor;
  final Color? btnColor;
  final Color? textColor;

  CommonButton({
    super.key,
    this.title,
    this.height,
    this.width,
    this.onPressed,
    this.bColor,
    this.btnColor,
    this.textColor,
    this.fontSize,
    this.horizontalPadding,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        elevation: 8,
        shadowColor: AppColor.cLightOrangeContainer,
        child: Container(
          width: width ?? Get.width,
          height: height ?? 44,
          decoration: BoxDecoration(
              color: btnColor ?? AppColor.themeBlueColor,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: bColor ?? AppColor.cTransparent)),
          padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding ?? Get.width * 0.1),
          child: Center(
            child: Text(
              title!,
              style: pRegular16.copyWith(
                  color: textColor ?? AppColor.cWhite,
                  fontSize: fontSize ?? 13),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}

class CommonIconButton extends StatelessWidget {
  final String? title;
  final double? height;
  final double? width;
  final double? radius;
  final double? fontSize;
  final String? iconData;
  final Function()? onPressed;
  final Color? bColor;
  final Color? btnColor;
  final Color? textColor;
  final TextStyle? textStyle;
  final Color? iconColor;

  CommonIconButton({
    super.key,
    this.title,
    this.height,
    this.width,
    this.onPressed,
    this.bColor,
    this.btnColor,
    this.iconData,
    this.radius,
    this.textColor,
    this.textStyle,
    this.fontSize,
    this.iconColor
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Card(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius ?? 6),
            side: BorderSide(color: bColor ?? AppColor.cTransparent, width: 1)),
        elevation: 8,
        shadowColor: AppColor.cLightOrangeContainer,
        child: Container(
          width: width ?? Get.width,
          // height: height ?? Get.height * 0.07,
          height: height ?? 44,
          decoration: BoxDecoration(
              color: btnColor ?? AppColor.themeDarkBlueColor,
              borderRadius: BorderRadius.circular(radius ?? 6),
              border:
                  Border.all(color: bColor ?? AppColor.cTransparent, width: 1)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              iconData == null
                  ? SizedBox()
                  : assetSvdImageWidget(
                      height: 24,
                      image: iconData!,
                      backgroundColor: iconColor
                      
                      ),
              horizontalSpace(iconData == null ? 0 : 8),
              Text(
                title!,
                style: textStyle ??
                    pRegular14.copyWith(
                        color: textColor ?? AppColor.cWhite,
                        fontSize: fontSize ?? 13),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CommonBorderButton extends StatelessWidget {
  final String? title;
  final double? height;
  final double? width;
  final double? radius;
  final Function()? onPressed;
  final Color? bColor;
  final Color? btnColor;
  final Color? textColor;

  CommonBorderButton({
    super.key,
    this.title,
    this.height,
    this.width,
    this.onPressed,
    this.bColor,
    this.btnColor,
    this.radius,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Card(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius ?? 6),
            side: BorderSide(
                color: bColor ?? AppColor.themeDarkBlueColor, width: 1)),
        child: Container(
          width: width ?? Get.width,
          height: height ?? 44,
          decoration: BoxDecoration(
              color: btnColor ?? AppColor.cWhite,
              borderRadius: BorderRadius.circular(radius ?? 6),
              border: Border.all(
                  color: bColor ?? AppColor.themeDarkBlueColor, width: 1)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title!,
                style: pRegular16.copyWith(
                    color: textColor ?? AppColor.cDarkBlueFont, fontSize: 13),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CommonIconBorderButton extends StatelessWidget {
  final String? title;
  final double? height;
  final double? width;
  final double? radius;
  final String? iconData;
  final Function()? onPressed;
  final Color? bColor;
  final Color? btnColor;
  final Color? textColor;

  CommonIconBorderButton({
    super.key,
    this.title,
    this.height,
    this.width,
    this.onPressed,
    this.bColor,
    this.btnColor,
    this.iconData,
    this.radius,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius ?? 6),
          side: BorderSide(
              color: bColor ?? AppColor.themeDarkBlueColor, width: 1)),
      elevation: 2,
      shadowColor: AppColor.cLightOrangeContainer,
      child: GestureDetector(
        onTap: onPressed,
        child: Container(
            width: width ?? Get.width,
            // height: height ?? Get.height * 0.07,
            height: height ?? 44,
            decoration: BoxDecoration(
                color: btnColor ?? AppColor.cWhite,
                borderRadius: BorderRadius.circular(radius ?? 6),
                border: Border.all(
                    color: bColor ?? AppColor.themeDarkBlueColor, width: 1)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                assetSvdImageWidget(
                  image: iconData!,
                ),
                horizontalSpace(10),
                Text(
                  title!,
                  style: pRegular16.copyWith(
                    color: textColor ?? AppColor.cDarkBlueFont,
                  ),
                  overflow: TextOverflow
                      .ellipsis, // Add this to handle long texts gracefully
                  textAlign: TextAlign.center,
                ),
                // Add more Expanded or Flexible widgets as needed
              ],
            )),
      ),
    );
  }
}
