import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:convert';
import 'dart:developer';

import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/load_data.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/promotions.dart';
import 'package:waie_app/view/widget/loading_widget.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/constants.dart';
import '../../../../utils/text_style.dart';
import '../../../../view/widget/common_space_divider_widget.dart';

class PromotionController extends GetxController {
  RxBool isNewPromotion = true.obs;
  RxBool isHistory = false.obs;
  var promotionList = <Promotions>[].obs;

  TextEditingController PromoFieldValue = TextEditingController();
  TextEditingController tawuniyaTopupController = TextEditingController();
  TextEditingController walaTopupController = TextEditingController();
  TextEditingController stcTopupController = TextEditingController();
  TextEditingController yggCODETopupController = TextEditingController();
  TextEditingController yggPINTopupController = TextEditingController();

  List historyList = [
    {
      "title": "Tawuniya Promotions",
      "service": "Smart card",
      "activationCode": "B0BE20F410CC",
      "activationDate": "04.21.2023",
      "amount": '15',
    },
    {
      "title": "WALA Plus Promotions",
      "service": "Code",
      "activationCode": "F04N5H405FKG",
      "activationDate": "04.19.2023",
      "amount": '20',
    },
    {
      "title": "Tawuniya Promotions",
      "service": "Code",
      "activationCode": "GL30FIG931930",
      "activationDate": "03.03.2023",
      "amount": '10',
    },
  ];

  //Future<String> activePromoCode(String promoType,String promo, String promoCode) async {
  Future<String> activePromoCode(String promoType, String promo) async {
    Loader.showLoader();
    var client1 = http.Client();
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    var userid = sharedUser.getString('userid');
    print("Promo Details ==============promotype : $promoType, Promo : $promo");
    print(PromoFieldValue.text);
    print(tawuniyaTopupController.text);
    print(walaTopupController.text);
    print(stcTopupController.text);
    print(yggCODETopupController.text);
    print(yggPINTopupController.text);
    try {
      var provResponse = await client1.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.activatePromoCode),
          body: {
            "custid": userid,
            "promoType": promoType,
            "promo": promo,
            "promoCode": promoType == "CARD" && promo == "TW"
                ? PromoFieldValue.text
                : promoType == "CODE" && promo == "TW"
                    ? tawuniyaTopupController.text
                    : promoType == "CODE" && promo == "WP"
                        ? walaTopupController.text
                        : promoType == "CODE" && promo == "STCP"
                            ? stcTopupController.text
                            : promoType == "CODE" && promo == "YGG"
                                ? yggCODETopupController.text
                                : "",
            "IsAR": Constants.IsAr_App,
            "pin": yggPINTopupController.text ?? ""
          });

      print("responseActivePromo===> ${jsonDecode(provResponse.body)}");

      String Result = jsonDecode(provResponse.body);

      print("PromoActiveResult===> $Result");

      String DialogHeaderMsg = "";
      if (Result.contains("SUCCESSFULLY")) {
        Loader.hideLoader();
        yggCODETopupController.clear();
        yggPINTopupController.clear();
        PromoFieldValue.clear();
        tawuniyaTopupController.clear();
        walaTopupController.clear();
        DialogHeaderMsg = "Activation Success";
      } else if (Result.contains("ERROR")) {
        Loader.hideLoader();
        DialogHeaderMsg = "Activation Fail";
      } else {
        Loader.hideLoader();
        yggCODETopupController.clear();
        yggPINTopupController.clear();
        PromoFieldValue.clear();
        tawuniyaTopupController.clear();
        walaTopupController.clear();
        DialogHeaderMsg = "Activation Success";
      }
      PromoFieldValue.text = "";
      showDialog(
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(16),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(DialogHeaderMsg, style: pBold20),
                verticalSpace(24),
                Text(
                  Result,
                  style: pRegular16,
                  textAlign: TextAlign.center,
                ),
                verticalSpace(24),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text("OK"),
                )
              ],
            ),
          );
        },
      );

      return Result;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client1.close();
    }
  }

  Future<List<Promotions>> promotionHistoryList() async {
    var client1 = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    promotionList.clear();
    try {
      var promoResponse = await client1.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.PromoHistory),
          body: {"custid": userid, "IsAR": Constants.IsAr_App});

      print(
          "PROMO HISTORY promoResponse==============${promoResponse.statusCode}");
      print("responsePromoHist===> ${jsonDecode(promoResponse.body)}");

      List promoResult = jsonDecode(promoResponse.body);

      for (int i = 0; i < promoResult.length; i++) {
        Promotions loadData =
            Promotions.fromMap(promoResult[i] as Map<String, dynamic>);
        promotionList.add(loadData);
        print("promoResult ===============${loadData.PROMOTYPE}");
      }

      // cityList.value = promotionList.map((item) => item.TYPEDESC).toList();

      return promotionList;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client1.close();
    }
  }
}
