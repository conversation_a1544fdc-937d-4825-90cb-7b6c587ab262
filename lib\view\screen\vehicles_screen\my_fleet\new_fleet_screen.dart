import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/fliter_action_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/fleet_structure_controller/new_fleet_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/overview_controller.dart';
import '../../../../core/controller/vehicle_controller/filter_controller.dart';
import '../../../widget/common_button.dart';
import '../../../widget/common_text_field.dart';
import '../../menu_screen/fleet_structure_screen/fleet_structure_screen.dart';
import '../../menu_screen/fleet_structure_screen/group_process_add_widget.dart';
import '../../menu_screen/fleet_structure_screen/group_process_edit_widget.dart';
import 'add_pure_dc_vehicle_screen.dart';
import 'assign_digital_coupon_widget.dart';
import 'fleet_vehicle_screen.dart';

class NewFleetScreen extends StatefulWidget {
  const NewFleetScreen({super.key});

  @override
  State<NewFleetScreen> createState() => _NewFleetScreenState();
}

class _NewFleetScreenState extends State<NewFleetScreen> {
  NewFleetController controller = Get.put(NewFleetController());
  FilterController filterController = Get.put(FilterController());
  //var controller = Get.find<NewFleetController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.only(right: 16, left: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      padding: const EdgeInsets.only(
                        top: 15,
                        bottom: 16,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          assetSvdImageWidget(
                              image: DefaultImages.backIcn,
                              colorFilter: ColorFilter.mode(
                                  AppColor.cDarkBlueFont, BlendMode.srcIn)),
                          horizontalSpace(10),
                          Text(
                            "Back".trr,
                            style: pRegular18.copyWith(
                                color: AppColor.cDarkBlueFont, fontSize: 17),
                            textAlign: TextAlign.start,
                          )
                        ],
                      ),
                    ),
                  ),
                  // horizontalSpace(35),
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        "Vehicle".trr,
                        style: pBold20,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            verticalSpace(10),
            Padding(
              padding: const EdgeInsets.only(right: 16, left: 16),
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Get.to(() => AddPureDCVehicleScreen(
                              title: "Add DC vehicle".trr,
                            ));
                      },
                      child: Container(
                        height: 44,
                        padding: const EdgeInsets.symmetric(
                            vertical: 12, horizontal: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          border:
                              Border.all(color: AppColor.themeDarkBlueColor),
                        ),
                        child: FittedBox(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  assetSvdImageWidget(
                                      image: DefaultImages.scannerIcn),
                                  horizontalSpace(8),
                                  Text(
                                    "Add digital coupon vehicle".trr,
                                    style: pRegular13.copyWith(
                                      color: AppColor.cDarkBlueFont,
                                    ),
                                  )
                                ],
                              ),
                              // assetSvdImageWidget(
                              //   image: DefaultImages.blueArrowDownIcn,
                              //   colorFilter: ColorFilter.mode(AppColor.cDarkGreyFont, BlendMode.srcIn),
                              // ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  horizontalSpace(10),
                  Expanded(
                    child: CommonIconButton(
                      title: "Filter",
                      onPressed: () {
                        showDialog(
                          barrierDismissible: false,
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              insetPadding: const EdgeInsets.all(16),
                              contentPadding: const EdgeInsets.all(24),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              content: filterActionWidget(),
                            );
                          },
                        );
                      },
                      iconData: DefaultImages.circleAddIcn,
                      btnColor: AppColor.themeOrangeColor,
                    ),
                  ),
                ],
              ),
            ),
            verticalSpace(10),
            const Expanded(
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: Padding(
                  padding: EdgeInsets.only(right: 16, left: 16),
                  child: Column(
                    children: [],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // return Container(
  //   child: Column(
  //     children: [
  //       GetBuilder<NewFleetController>(
  //         id: 'aVeryUniqueID', // here
  //         init: NewFleetController(),
  //         builder: (value) => Text(
  //           '${value.counter}', // this will update
  //         ),
  //       ),
  //       ElevatedButton(
  //         onPressed: () {
  //           newFleetController.fetch2();
  //         },
  //         child: const Text("CLICK"),
  //       ),
  //       // Expanded(
  //       // child: SingleChildScrollView(
  //       //   physics: BouncingScrollPhysics(),
  //       //   child: Column(
  //       //     children: [
  //     ],
  //   ),
  // );
  //   return Scaffold(
  //     backgroundColor: AppColor.cBackGround,
  //     body: SafeArea(
  //       child: Obx(() {
  //         return Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             GetBuilder<NewFleetController>(
  //               id: 'aVeryUniqueID', // here
  //               init: NewFleetController(),
  //               builder: (value) => Text(
  //                 '${value.counter}', // this will update
  //               ),
  //             ),
  //             Expanded(
  //               child: SingleChildScrollView(
  //                 child: Padding(
  //                   padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
  //                   child: Column(
  //                     children: [
  //                       Row(
  //                         children: [
  //                           Expanded(
  //                             child: CommonIconButton(
  //                               iconData: DefaultImages.addDivisionIcn,
  //                               title: 'Add Division'.trr,
  //                               onPressed: () {
  //                                 showModalBottomSheet(
  //                                   context: context,
  //                                   shape: const RoundedRectangleBorder(
  //                                       borderRadius: BorderRadius.vertical(
  //                                           top: Radius.circular(16))),
  //                                   backgroundColor: AppColor.cBackGround,
  //                                   barrierColor: AppColor.cBlackOpacity,
  //                                   isScrollControlled: true,
  //                                   builder: (context) {
  //                                     return GroupProcessAddWidget(
  //                                       name: "Division",
  //                                       typeid: "CUSTDIVISION",
  //                                       parentid: "NA",
  //                                     );
  //                                   },
  //                                 );
  //                               },
  //                               btnColor: AppColor.themeOrangeColor,
  //                             ),
  //                           ),
  //                         ],
  //                       ),
  //                       verticalSpace(24),
  //                       Container(
  //                         child: ListView.builder(
  //                           itemCount: controller.fleetLists.length,
  //                           shrinkWrap: true,
  //                           physics: const NeverScrollableScrollPhysics(),
  //                           itemBuilder: (context, index) {
  //                             var data = controller.fleetLists[index];
  //                             return Padding(
  //                               padding: const EdgeInsets.only(bottom: 8.0),
  //                               child: Container(
  //                                 decoration: BoxDecoration(
  //                                     border: Border.all(
  //                                         color: AppColor.cLightBlueContainer),
  //                                     borderRadius: BorderRadius.circular(4)),
  //                                 child: Column(
  //                                   children: [
  //                                     fleetStructureDataWidget(
  //                                         context: context,
  //                                         title: data.typedesc,
  //                                         status: data.ishold == "Y"
  //                                             ? "HOLD"
  //                                             : "ACTIVE",
  //                                         // dropDownIcn:
  //                                         //     data.isSelected.value ==
  //                                         //             true
  //                                         //         ? DefaultImages
  //                                         //             .blackArrowDownIcn
  //                                         //         : DefaultImages
  //                                         //             .arrowRightIcn,
  //                                         divisionImage:
  //                                             DefaultImages.addDivisionIcn,
  //                                         divisionTitle: "Division",
  //                                         division: data.typecode,
  //                                         tooltipItemList: [
  //                                           "View Branch",
  //                                           "Edit Division",
  //                                           "Delete Division",
  //                                           data.ishold == "Y"
  //                                               ? "UnHold Division"
  //                                               : "Hold Division",
  //                                         ],
  //                                         moreOnTap: (String value) {}),
  //                                   ],
  //                                 ),
  //                               ),
  //                             );
  //                           },
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             )
  //           ],
  //         );
  //       }),
  //     ),
  //   );
  // }
  filterActionWidget() {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // GestureDetector(
          //   onTap: () {
          //     Get.back();
          //     // Get.offAll(
          //     //   () => DashBoardManagerScreen(
          //     //     currantIndex: 0,
          //     //   ),
          //     //   //preventDuplicates: false,
          //     // );
          //   },
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.end,
          //     children: [assetSvdImageWidget(image: DefaultImages.cancelIcn)],
          //   ),
          // ),
          // verticalSpace(24),
          Row(
            children: [
              Expanded(
                child: CommonTextField(
                  labelText: 'Old Plate'.trr,
                  hintText: '',
                ),
              ),
              horizontalSpace(10),
              Expanded(
                child: CommonTextField(
                  labelText: 'New Plate'.trr,
                  hintText: '',
                ),
              ),
            ],
          ),
          verticalSpace(12),
          Row(
            children: [
              Expanded(
                child: CommonTextField(
                  labelText: 'Old Plate'.trr,
                  hintText: '',
                ),
              ),
              horizontalSpace(10),
              Expanded(
                child: CommonTextField(
                  labelText: 'New Plate'.trr,
                  hintText: '',
                ),
              ),
            ],
          ),

          DropdownButtonFormField(
            items: filterController.vehicleTypeList.map((data) {
              return DropdownMenuItem(
                value: data.TYPECODE,
                child: Text(
                  data.TYPEDESC,
                  style: pMedium12,
                  textAlign: TextAlign.center,
                ),
              );
            }).toList(),
            onChanged: (value) {
              // companyAddressController.selectedRegion
              //     .value = value.toString();
              // companyAddressController
              //         .regionController.text =
              //     companyAddressController
              //         .selectedRegion.value;
            },
            style: pRegular14.copyWith(color: AppColor.cLabel),
            borderRadius: BorderRadius.circular(6),
            dropdownColor: AppColor.cLightGrey,
            icon: assetSvdImageWidget(image: DefaultImages.dropDownIcn),
            decoration: InputDecoration(
              hintText: 'Region'.trr,
              hintStyle: pRegular14.copyWith(color: AppColor.cHintFont),
              contentPadding: const EdgeInsets.only(left: 16, right: 16),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
            ),
          ),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonButton(
                title: 'Back'.trr,
                onPressed: () {
                  Get.back();
                  // Get.offAll(
                  //   () => DashBoardManagerScreen(
                  //     currantIndex: 0,
                  //   ),
                  //   //preventDuplicates: false,
                  // );
                },
                btnColor: AppColor.cBackGround,
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Cancel complaint'.trr,
                  onPressed: () {},
                  btnColor: AppColor.themeOrangeColor,
                  horizontalPadding: 16,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
