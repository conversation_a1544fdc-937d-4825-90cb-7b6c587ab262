import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:mime/mime.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class CompanyDetailController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  TextEditingController cpNameController = TextEditingController();
  TextEditingController arabicCpNameController = TextEditingController();
  TextEditingController designationController = TextEditingController();
  TextEditingController contactPersonController = TextEditingController();
  TextEditingController cpPhoneNoController = TextEditingController();
  TextEditingController cpFaxController = TextEditingController();
  TextEditingController regNoController = TextEditingController();
  TextEditingController vatNoController = TextEditingController();
  TextEditingController salesManController = TextEditingController();
  List exproList = ['Yes'.tr, 'No'.tr];
  RxString selectedExproValue = 'No'.obs;

  List salesmanList = ['Joen', 'Alex'];
  RxString selectedSalesMan = ''.obs;
  RxString selectedSalesmanValue = ''.obs;
  RxString regDate = ''.obs;
  RxString userID = ''.obs;
  RxString compType = ''.obs;
  RxString custID = ''.obs;

  RxString vatDocument = ''.obs;
  RxString idDocument = ''.obs;

  getstoredCompanyDetails() async {
    var custData = custsData.read('custData');
    cpNameController.text = custData['COMPANYNAME'];
    designationController.text = custData['DESIGNATION'];
    contactPersonController.text = custData['CONTACTPERSON'];
    cpPhoneNoController.text = custData['COMPANYTEL'];
    cpFaxController.text = custData['COMPANYFAX'];
    regNoController.text = custData['CRNO'];
    vatNoController.text = custData['VAT_NO'];
    regDate.value = custData['REGACTDATE'];
    userID.value = custData['CUSTID'];
    compType.value = custData['ACCTTYPE'];
  }

  updateCompanyDetail(invVatdocx, invIddocx) async {
    // showDialog(
    //   barrierDismissible: false,
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custData = custsData.read('custData');
    var client = http.Client();
    print("==============================================");
    print("cpNameController.text >>>>>>>>> ${cpNameController.text}");
    print("designationController.text >>>>>>>>> ${designationController.text}");
    print(
        "contactPersonController.text >>>>>>>>> ${contactPersonController.text}");
    print("cpPhoneNoController.text >>>>>>>>> ${cpPhoneNoController.text}");
    print("cpFaxController.text >>>>>>>>> ${cpFaxController.text}");
    print("regNoController.text >>>>>>>>> ${regNoController.text}");
    print("vatNoController.text >>>>>>>>> ${vatNoController.text}");
    print("regDate.value >>>>>>>>> ${regDate.value.toString()}");
    print("userID.value >>>>>>>>> ${userID.value.toString()}");
    print("compType.value >>>>>>>>> ${compType.value.toString()}");
    print("salesManController >>>>>>>>> ${salesManController.text}");
    print("==============================================");
    try {
      var vatDocx = invVatdocx.readStream;
      var idDocx = invIddocx.readStream;

      print("vatDocx $vatDocx");
      print("vatDocx ${invVatdocx.size}");
      print("vatDocx ${invVatdocx.name}");
      print("idDocx $idDocx");
      print("vatDocx ${invIddocx.size}");
      print("vatDocx ${invIddocx.name}");

      var request = http.MultipartRequest(
          'POST',
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.updateProfileDetails));

      request.files.add(http.MultipartFile('vatDocx', vatDocx, invVatdocx.size,
          filename: invVatdocx.name,
          contentType: MediaType('application', 'pdf')));
      vatDocx.cast();

      request.files.add(http.MultipartFile('idDocx', idDocx, invIddocx.size,
          filename: invIddocx.name,
          contentType: MediaType('application', 'pdf')));
      idDocx.cast();

      request.fields['ACTION'] = "cdetail";
      request.fields['CUSTID'] = custData['CUSTID'] ?? "";
      request.fields['EMAILID'] = custData['EMAILID'] ?? "";
      request.fields['COMPANYNAME'] = cpNameController.text;
      request.fields['COMPANYNAMEAR'] = custData['COMPANYNAMEAR'] ?? "";
      request.fields['CONTACTPERSON'] = contactPersonController.text;
      request.fields['DESIGNATION'] = designationController.text;
      request.fields['CRNO'] = custData['CRNO'] ?? "";
      request.fields['VAT_NO'] = vatNoController.text;
      request.fields['SALESMAN__CODE'] = salesManController.text;
      request.fields['COUNTRY_CODE'] = custData['SELECTEDCOUNTRY'] ?? "";
      request.fields['REGION_CODE'] = custData['SELECTEDREG'] ?? "";
      request.fields['CITY_CODE'] = custData['SELECTEDCITY'] ?? "";
      request.fields['DISTRICT_CODE'] = custData['SELECTEDDISTRICT'] ?? "";
      request.fields['STREET'] = custData['STREET'] ?? "";
      request.fields['BUILDING_NO'] = custData['BUILDING_NO'] ?? "";
      request.fields['MOBILENO'] = custData['MOBILENO'] ?? "";
      request.fields['LANG'] = custData['SELECTEDLANG'] ?? "";
      request.fields['SERVICEKNOWN_CODE'] = custData['SERVICEKNOWN_CODE'] ?? "";
      request.fields['POSTALCODE'] = custData['POSTALCODE'] ?? "";
      request.fields['POBOX'] = custData['POBOX'] ?? "";
      request.fields['FIRSTNAME'] = custData['FIRSTNAME'] ?? "";
      request.fields['MIDNAME'] = custData['MIDNAME'] ?? "";
      request.fields['LASTNAME'] = custData['LASTNAME'] ?? "";
      request.fields['COMPANYTEL'] = cpPhoneNoController.text;
      request.fields['COMPANYFAX'] = cpFaxController.text;
      request.fields['HOUSE_NO'] = custData['HOUSE_NO'] ?? "";
      request.fields['ID_TYPE'] = custData['SELECTEDIDTYPE'] ?? "CRN";
      request.fields['ID_NUMBER'] = regNoController.text;
      request.fields['GPS'] = custData['CUSTID'] ?? "";
      request.fields['FUEL91'] = custData['FUEL91'] ?? "";
      request.fields['FUEL95'] = custData['FUEL95'] ?? "";
      request.fields['DIESEL'] = custData['DIESEL'] ?? "";
      request.fields['OTHERS'] = custData['OTHERS'] ?? "";
      var res = await request.send();

      return res.stream.bytesToString().asStream().listen((event) {
        var parsedJson = json.decode(event);
        print(
            "===============================================================");
        print(parsedJson);
        print(parsedJson['response']['Action']);
        print(parsedJson['response']['Message']);
        print(
            "===============================================================");
        if (parsedJson['response']['Action'] == "EXCEPTION" ||
            parsedJson['response']['Action'] == "SUCCESS") {
          //Navigator.of(Get.context!).pop();
          regNoController.clear();
          cpFaxController.clear();
          cpPhoneNoController.clear();
          vatNoController.clear();
          designationController.clear();
          contactPersonController.clear();
          cpNameController.clear();
          vatDocument.value = '';
          idDocument.value = '';
          custsData.remove('custData');
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      parsedJson['response']['Message'].toString(),
                      style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        await Get.to(
                            () => DashBoardManagerScreen(
                                  currantIndex: 0,
                                ),
                            preventDuplicates: false);
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
      });

      // var response = await client.post(
      //     Uri.parse(ApiEndPoints.baseUrl +
      //         ApiEndPoints.authEndpoints.updateProfileDetails),
      //     body: {
      //       "CUSTID": custData['CUSTID'] ?? "",
      //       "EMAILID": custData['EMAILID'] ?? "",
      //       "COMPANYNAME": cpNameController.text,
      //       "COMPANYNAMEAR": custData['COMPANYNAMEAR'] ?? "",
      //       "CONTACTPERSON": contactPersonController.text,
      //       "DESIGNATION": designationController.text,
      //       "CRNO": custData['CRNO'] ?? "",
      //       "VAT_NO": vatNoController.text,
      //       "SALESMAN__CODE": salesManController.text,
      //       "COUNTRY_CODE": custData['SELECTEDCOUNTRY'] ?? "",
      //       "REGION_CODE": custData['SELECTEDREG'] ?? "",
      //       "CITY_CODE": custData['SELECTEDCITY'] ?? "",
      //       "DISTRICT_CODE": custData['SELECTEDDISTRICT'] ?? "",
      //       "STREET": custData['STREET'] ?? "",
      //       "BUILDING_NO": custData['BUILDING_NO'] ?? "",
      //       "MOBILENO": custData['MOBILENO'] ?? "",
      //       "LANG": custData['SELECTEDLANG'] ?? "",
      //       "SERVICEKNOWN_CODE": custData['SERVICEKNOWN_CODE'] ?? "",
      //       "POSTALCODE": custData['POSTALCODE'] ?? "",
      //       "POBOX": custData['POBOX'] ?? "",
      //       "FIRSTNAME": custData['FIRSTNAME'] ?? "",
      //       "MIDNAME": custData['MIDNAME'] ?? "",
      //       "LASTNAME": custData['LASTNAME'] ?? "",
      //       "COMPANYTEL": cpPhoneNoController.text,
      //       "COMPANYFAX": cpFaxController.text,
      //       "HOUSE_NO": custData['HOUSE_NO'] ?? "",
      //       "ID_TYPE": custData['SELECTEDIDTYPE'] ?? "",
      //       "ID_NUMBER": regNoController.text,
      //       "GPS": custData['CUSTID'] ?? "",
      //       "FUEL91": custData['FUEL91'] ?? "",
      //       "FUEL95": custData['FUEL95'] ?? "",
      //       "DIESEL": custData['DIESEL'] ?? "",
      //       "OTHERS": custData['OTHERS'] ?? "",
      //     });
      // print("===============================================================");
      // print(
      //     "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      // print(
      //     "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");
      // print("===============================================================");
      // if (response.statusCode == 200) {
      //   Navigator.of(Get.context!).pop();
      //   regNoController.clear();
      //   cpFaxController.clear();
      //   cpPhoneNoController.clear();
      //   vatNoController.clear();
      //   designationController.clear();
      //   contactPersonController.clear();
      //   cpNameController.clear();
      //   custsData.remove('custData');
      //   await Get.to(
      //       () => DashBoardManagerScreen(
      //             currantIndex: 4,
      //           ),
      //       preventDuplicates: false);
      // } else {
      //   print('Failed');
      // }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<void> updateCompanyDetailNoVat() async {
    print("updateCompanyDetailNoVat");
    // showDialog(
    //   barrierDismissible: false,
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custData = custsData.read('custData');
    var client = http.Client();
    print("==============================================");
    print("cpNameController.text >>>>>>>>> ${cpNameController.text}");
    print("designationController.text >>>>>>>>> ${designationController.text}");
    print(
        "contactPersonController.text >>>>>>>>> ${contactPersonController.text}");
    print("cpPhoneNoController.text >>>>>>>>> ${cpPhoneNoController.text}");
    print("cpFaxController.text >>>>>>>>> ${cpFaxController.text}");
    print("regNoController.text >>>>>>>>> ${regNoController.text}");
    print("vatNoController.text >>>>>>>>> ${vatNoController.text}");
    print("regDate.value >>>>>>>>> ${regDate.value.toString()}");
    print("userID.value >>>>>>>>> ${userID.value.toString()}");
    print("compType.value >>>>>>>>> ${compType.value.toString()}");
    print("salesManController >>>>>>>>> ${salesManController.text}");
    print("==============================================");
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.updateProfileDetails),
          body: {
            "ACTION": "cdetail",
            "CUSTID": custData['CUSTID'] ?? "",
            "EMAILID": custData['EMAILID'] ?? "",
           /* "COMPANYNAME": custData['COMPANYNAME'] ?? "",
            "COMPANYNAMEAR": custData['COMPANYNAMEAR'] ?? "",*/
            "COMPANYNAME": cpNameController.text,
            "COMPANYNAMEAR": cpNameController.text,
            "CONTACTPERSON": contactPersonController.text,
            "DESIGNATION": designationController.text,
           // "CRNO": custData['CRNO'] ?? "",
            "CRNO": regNoController.text,
            "VAT_NO": vatNoController.text ?? "",
            "SALESMAN__CODE": salesManController.text,
            "COUNTRY_CODE": "SA",
            "REGION_CODE": custData['SELECTEDREG'] ?? "",
            "CITY_CODE": custData['SELECTEDCITY'] ?? "",
            "DISTRICT_CODE": custData['SELECTEDDISTRICT'] ?? "",
            "STREET": custData['STREET'] ?? "",
            "BUILDING_NO": custData['BUILDING_NO'] ?? "",
            "MOBILENO": custData['MOBILENO'] ?? "",
            "LANG": custData['SELECTEDLANG'] ?? "",
            "SERVICEKNOWN_CODE": custData['SERVICEKNOWN_CODE'] ?? "",
            "POSTALCODE": custData['POSTALCODE'] ?? "",
            "POBOX": custData['POBOX'] ?? "",
            "FIRSTNAME": custData['FIRSTNAME'] ?? "",
            "MIDNAME": custData['MIDNAME'] ?? "",
            "LASTNAME": custData['LASTNAME'] ?? "",
            "COMPANYTEL": cpPhoneNoController.text,
            "COMPANYFAX": cpFaxController.text,
            "HOUSE_NO": custData['HOUSE_NO'] ?? "",
            "ID_TYPE": custData['SELECTEDIDTYPE'] ?? "CRN",
            "ID_NUMBER": regNoController.text,
            "GPS": custData['GPS'] ?? "",
            "FUEL91": custData['FUEL91'] ?? "",
            "FUEL95": custData['FUEL95'] ?? "",
            "DIESEL": custData['DIESEL'] ?? "",
            "OTHERS": custData['OTHERS'] ?? "",
          });
      print("++++++++++++++++++++");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");
      print("+++++++++++++++++++");
      if (response.statusCode == 200) {
        // Navigator.of(Get.context!).pop();
        regNoController.clear();
        cpFaxController.clear();
        cpPhoneNoController.clear();
        vatNoController.clear();
        designationController.clear();
        contactPersonController.clear();
        cpNameController.clear();
        vatDocument.value = '';
        idDocument.value = '';
        custsData.remove('custData');
        await Get.to(
            () => DashBoardManagerScreen(
                  currantIndex: 0,
                ),
            preventDuplicates: false);
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
