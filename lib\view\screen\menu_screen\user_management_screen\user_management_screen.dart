// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/user_management_controller/user_management_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/search_user_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/bulk_actions_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import '../../../../core/controller/home_controller/balance_controller.dart';
import '../../../../utils/constants.dart';
import 'edit_user_screen.dart';
import 'edit_user_widget.dart';
import 'new_user_screen.dart';

class UserManagementScreen extends StatelessWidget {
  UserManagementScreen({super.key});
  UserManagementController userManagementController =
      Get.put(UserManagementController());
  BalanceController balanceController = Get.put(BalanceController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        top: 15,
                        bottom: 16,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          assetSvdImageWidget(
                              image: DefaultImages.backIcn,
                              colorFilter: ColorFilter.mode(
                                  AppColor.cDarkBlueFont, BlendMode.srcIn)),
                          horizontalSpace(10),
                          Text(
                            // "Menu".trr,
                            "Back".trr,
                            style: pRegular18.copyWith(
                                color: AppColor.cDarkBlueFont, fontSize: 17),
                            textAlign: TextAlign.start,
                          )
                        ],
                      ),
                    ),
                  ),
                  // horizontalSpace(35),
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        "Users".trr,
                        style: pBold20,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Obx(
                () => // Wrap your ListView with Obx
                    ListView(
                  shrinkWrap: true,
                  padding: EdgeInsets.all(16),
                  physics: BouncingScrollPhysics(),
                  children: [
                    ListView.builder(
                      itemCount: userManagementController.userDataList.length,
                      // This now automatically updates
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = userManagementController.userDataList[index];
                        print("User Cust ID=========" + data.CUSTID);
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: userDetailWidgetUpdated(
                            // Add your widget details here
                            User_Id: data.CUSTID,
                            Email: data.EMAILID,
                            Primary: data.IS_PRIMARY,
                            Recieve_Email: data.RCV_MAIL,
                            Two_Factor_Authentication: data.ISAUTH_CODE,
                            Status: data.USRSTATUS,
                            isShow: false,
                            onTap: () {
                              // Your onTap functionality
                            },
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      /* if(Constants.custAcctType!="I")*/
      /* bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 24),
        child: CommonIconButton(
            iconData: DefaultImages.addUserIcn,
            title: 'Add user'.trr,
            onPressed: () {
              Get.to(() => NewUserScreen());
            },
            btnColor: AppColor.themeOrangeColor,
            radius: 6),
      ),*/
      bottomNavigationBar: Constants.custAcctType == "I"
          ? Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 24),
              child: CommonIconButton(
                  iconData: DefaultImages.addUserIcn,
                  title: 'Add user'.trr,
                  onPressed: () {
                    Get.to(() => NewUserScreen());
                  },
                  btnColor: AppColor.themeOrangeColor,
                  radius: 6),
            )
          : null,
    );
  }
}

Widget userDetailWidget({
  String? name,
  String? status,
  String? vehiclesAssigned,
  String? email,
  String? id,
  String? created,
  String? lastVisit,
  bool? isShow,
  Function()? onTap,
}) {
  return Container(
    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
    decoration: BoxDecoration(
        color: AppColor.lightBlueColor,
        border: Border.all(color: AppColor.cLightGrey),
        borderRadius: BorderRadius.circular(4)),
    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                decoration: BoxDecoration(
                    color: AppColor.lightBlueColor,
                    border: Border.all(color: AppColor.cDarkBlueText),
                    borderRadius: BorderRadius.circular(4)),
                child: Text(
                  name!,
                  style: pBold12.copyWith(
                      fontSize: 13, color: AppColor.cDarkBlueFont),
                ),
              ),
              horizontalSpace(8),
              Container(
                height: 24,
                padding: EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                decoration: BoxDecoration(
                    color: AppColor.cLightGreen,
                    borderRadius: BorderRadius.circular(4)),
                child: Center(
                    child: Text(status!,
                        style: pSemiBold12.copyWith(color: AppColor.cGreen))),
              )
            ],
          )
          /*isShow == true
              ? GestureDetector(onTap: onTap, child: assetSvdImageWidget(image: DefaultImages.verticleMoreIcn))
              : SizedBox()*/
        ],
      ),
      verticalSpace(18),
      userDataRowWidget(
          title: "Vehicles Assigned".trr, value: vehiclesAssigned ?? "N/A"),
      verticalSpace(12),
      userDataRowWidget(
          title: "Email".trr, value: email!, textColor: AppColor.cDarkBlueText),
      verticalSpace(12),
      userDataRowWidget(title: "ID".trr, value: id!),
      verticalSpace(12),
      userDataRowWidget(title: "Created".trr, value: created!),
      verticalSpace(12),
      userDataRowWidget(title: "Last Visit".trr, value: lastVisit!),
    ]),
  );
}

Widget userDetailWidgetUpdated({
  String? User_Id,
  String? Email,
  String? Primary,
  String? Recieve_Email,
  String? Two_Factor_Authentication,
  String? Status,
  bool? isShow,
  Function()? onTap,
}) {
  return Container(
    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
    decoration: BoxDecoration(
        color: AppColor.lightBlueColor,
        border: Border.all(color: AppColor.cLightGrey),
        borderRadius: BorderRadius.circular(4)),
    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                decoration: BoxDecoration(
                    color: AppColor.lightBlueColor,
                    border: Border.all(color: AppColor.cDarkBlueText),
                    borderRadius: BorderRadius.circular(4)),
                child: Text(
                  User_Id!,
                  style: pBold12.copyWith(
                      fontSize: 13, color: AppColor.cDarkBlueFont),
                ),
              ),
            ],
          ),
          /* isShow == true
              ? GestureDetector(onTap: onTap, child: assetSvdImageWidget(image: DefaultImages.verticleMoreIcn))
              : SizedBox()*/
          isShow == true
              ? GestureDetector(
                  onTap: () {
                    // Show options menu on tap
                    showMenu(
                      context: Get.context!,
                      position: RelativeRect.fromLTRB(100, 200, 100, 100),
                      // Adjust position as needed
                      items: [
                        PopupMenuItem(
                          value: 'Activate',
                          child: Text('Activate'),
                          onTap: () {},
                        ),
                        PopupMenuItem(
                          value: 'edit',
                          child: Text('Edit'),
                          /*onTap: () {
                            Get.to(() => _onEditMenuTap(
                                Get.context!,
                                User_Id,
                                Email!,
                                Primary!,
                                Recieve_Email!,
                                Two_Factor_Authentication!,
                                Status!));
                          },*/
                          onTap: () {
                            Get.to(() => EditUserScreen(
                                User_Id: User_Id,
                                Email: Email!,
                                Primary: Primary!,
                                Recieve_Email: Recieve_Email!,
                                Two_Factor_Authentication:
                                    Two_Factor_Authentication!,
                                Status: Status!));
                          },
                          /* onTap: () {
                            Get.to(() => EditUserScreen(initialEmail: data.EMAILID, initialName: data.CUSTID));
                          },*/
                        ),
                      ],
                    );
                  },
                  child:
                      Icon(Icons.more_vert), // Updated to use Icon for clarity
                )
              : SizedBox()
        ],
      ),
      verticalSpace(18),
      userDataRowWidget(title: "Email".trr, value: Email ?? "N/A"),
      verticalSpace(18),
      userDataRowWidget(title: "Primary".trr, value: Primary ?? "N/A"),
      verticalSpace(12),
      userDataRowWidget(title: "Recieve Email".trr, value: Recieve_Email!),
      verticalSpace(12),
      userDataRowWidget(
          title: "Two Factor Authentication".trr,
          value: Two_Factor_Authentication!),
      verticalSpace(12),
      userDataRowWidget(title: "Status".trr, value: Status!),
    ]),
  );
}

Widget userActionWidget({
  Function()? editFunction,
  Function()? deleteFunction,
  required String name,
}) {
  return Container(
    padding: EdgeInsets.all(16),
    decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
    child: Column(mainAxisSize: MainAxisSize.min, children: [
      Text(
        name,
        style: pBold20,
      ),
      verticalSpace(21),
      bulkActionWidget(title: "Edit user".trr, onTap: editFunction!),
      bulkActionWidget(
          title: "Delete user".trr,
          onTap: deleteFunction!,
          textColor: AppColor.cDarkOrangeText),
    ]),
  );
}

userDataRowWidget(
    {required String title, required String value, Color? textColor}) {
  return Row(
    children: [
      Expanded(
        child: SizedBox(
          width: 100,
          child: Text(
            title,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
        ),
      ),
      Expanded(
        child: Text(
          value,
          style: pRegular14.copyWith(color: textColor),
        ),
      ),
    ],
  );
}

userTotalValueWidget(
    {required String title, required String value, Color? textColor}) {
  return Row(
    children: [
      Expanded(
        child: SizedBox(
          width: 100,
          child: Text(
            title,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
        ),
      ),
      Expanded(
        child: Directionality(
          textDirection: TextDirection.ltr,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              assetSvdImageWidget(
                  image: DefaultImages.saudiRiyal, width: 13, height: 13),
              Gap(4),
              Text(
                value,
                style: pRegular14.copyWith(color: textColor),
              ),
            ],
          ),
        ),
      ),
      // Expanded(
      //   child: Text(
      //     value,
      //     style: pRegular14.copyWith(color: textColor),
      //   ),
      // ),
    ],
  );
}

void _onEditMenuTap(BuildContext context, String userId, String email,
    String primary, String rcvEmail, String tfa, String status) {
  // Navigate to the EditPage, passing the data
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => EditUserScreen(
        User_Id: userId,
        Email: email,
        Primary: primary,
        Recieve_Email: rcvEmail,
        Two_Factor_Authentication: tfa,
        Status: status,
        // Pass other necessary data here
      ),
    ),
  );
}
