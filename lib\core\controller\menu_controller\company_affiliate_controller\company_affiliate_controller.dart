// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/company_affiliates_menu_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../../../../utils/constants.dart';
import '../../../../view/widget/common_snak_bar_widget.dart';

class CompanyAffiliateController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  RxString fileName = ''.obs;
  final ImagePicker picker = ImagePicker();
  File? postImage;

  pickImage(ImageSource imageSource) async {
    XFile? pickedImage = await picker.pickImage(source: imageSource);
    if (pickedImage != null) {
      print(pickedImage);
      postImage = File(pickedImage.path);
      fileName.value = File(pickedImage.path).path.split('/').last;
      print("==== f $postImage");
      print("==== uri ${postImage!.uri}");
      print("====  ${fileName.value}");
    } else {
      commonToast('Image Not Pick');
    }
  }

  RxBool isAffiliated = true.obs;
  RxBool isHistory = false.obs;

  RxList companyAffiliateList = [].obs;
  RxList dummyAffiliateList = [
    {
      "name": "Zahran Western",
      "status": "Active",
      "requestType": "Affiliate",
      "requestDate": "04.21.2023",
    },
    {
      "name": "Zahran Eastern",
      "status": "Active",
      "requestType": "Unaffiliate",
      "requestDate": "04.21.2023",
    },
    {
      "name": "Zahran Eastern",
      "status": "Active",
      "requestType": "Unaffiliate",
      "requestDate": "04.21.2023",
    },
    {
      "name": "Zahran Eastern",
      "status": "Active",
      "requestType": "Unaffiliate",
      "requestDate": "04.21.2023",
    },
    {
      "name": "Zahran Eastern",
      "status": "Active",
      "requestType": "Unaffiliate",
      "requestDate": "04.21.2023",
    },
    {
      "name": "Zahran Eastern",
      "status": "Active",
      "requestType": "Unaffiliate",
      "requestDate": "04.21.2023",
    },
  ].obs;
  RxList requestHistoryList = [
    {
      "name": "Zahran Western",
      "status": "New",
      "requestType": "Affiliate",
      "requestDate": "04.21.2023",
      "request": "A000028812",
    },
    {
      "name": "Logistics International Ltd.",
      "status": "Confirmed",
      "requestType": "Unaffiliate",
      "requestDate": "04.21.2023",
      "request": "A000028812",
    },
  ].obs;
  TextEditingController affiliateCompanyController = TextEditingController();

  cancelCompanyAffiliateRequest(reqId) async {
    print("cancelCompanyAffiliateRequest reqId>>>>>>> $reqId");
    showDialog(
      barrierDismissible: false,
      context: Get.context!,
      builder: (context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
    var custData = jsonEncode(custsData.read('custData'));
    print("cancelCompanyAffiliateRequest custData>>>>>>> $custData");
    Navigator.of(Get.context!).pop();
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.cancelAffiliates),
          body: {
            "custdata": custData,
            "reqId": reqId,
            "IsAR": Constants.IsAr_App,
          });
      var parsedJson = jsonDecode(response.body);
      if (parsedJson['response']['Action'] == "EXCEPTION") {
        Navigator.of(Get.context!).pop();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    parsedJson['response']['Message'].toString(),
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.offAll(() => CompanyAffiliateMenuScreen());
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
      if (parsedJson['response']['Action'] == "POPUP") {
        Navigator.of(Get.context!).pop();
        var msg = "";
        if (parsedJson['message'] != null) {
          msg = parsedJson['message'].toString();
        } else {
          msg = parsedJson['response']['Message'].toString();
        }
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    msg,
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.off(() => CompanyAffiliateMenuScreen(),
                          preventDuplicates: true);
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
      if (parsedJson['response']['Action'] == "SUCCESS" ||
          parsedJson['response']['Action'] == "POPUPCUSTOM" ||
          parsedJson['response']['Action'] == "OKRECUPD") {
        Navigator.of(Get.context!).pop();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Your company affiliate request has been cancelled",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  Center(
                      child: Text("Thank you!",
                          style: pRegular13, textAlign: TextAlign.center)),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.offAll(() => CompanyAffiliateMenuScreen());
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
      print(parsedJson);
      print(parsedJson['message']);
      print(parsedJson['response']['Action']);
      print(parsedJson['response']['Message']);

      return response;
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
