import 'dart:convert';

List<ComplaintReasonModel> complaintReasonModelFromJson(String str) =>
    List<ComplaintReasonModel>.from(
        json.decode(str).map((x) => ComplaintReasonModel.fromJson(x)));

String complaintReasonModelToJson(List<ComplaintReasonModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ComplaintReasonModel {
  String typeid;
  String typecode;
  String typedesc;

  ComplaintReasonModel({
    required this.typeid,
    required this.typecode,
    required this.typedesc,
  });

  factory ComplaintReasonModel.fromJson(Map<String, dynamic> json) =>
      ComplaintReasonModel(
        typeid: json["TYPEID"],
        typecode: json["TYPECODE"],
        typedesc: json["TYPEDESC"],
      );

  Map<String, dynamic> toJson() => {
        "TYPEID": typeid,
        "TYPECODE": typecode,
        "TYPEDESC": typedesc,
      };
}
