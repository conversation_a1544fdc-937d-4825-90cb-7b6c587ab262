import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/vehicle_controller/change_status_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../utils/validator.dart';
import '../../../widget/common_text_field.dart';
import 'bulk_actions_widget.dart';

class ChangeStatusWidget extends StatefulWidget {
  final String serviceStatus;
  final String vehicleID;
  final String serialID;
  bool btnActivate;
  bool btnDeactivate;
  ChangeStatusWidget({
    super.key,
    required this.serviceStatus,
    required this.vehicleID,
    required this.serialID,
    required this.btnActivate,
    required this.btnDeactivate,
  });

  @override
  State<ChangeStatusWidget> createState() => _ChangeStatusWidgetState();
}

class _ChangeStatusWidgetState extends State<ChangeStatusWidget> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  int selectedOption = 1;
  var serviceStat = "A";

  ChangeStatusController changeStatusController =
      Get.put(ChangeStatusController());
  VehicleController vehicleController = Get.put(VehicleController());

  final vehicle = GetStorage();

  @override
  Widget build(BuildContext context) {
    print("box >>>>> ${vehicle.read('vehicleID')}");
    return Container(
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              children: [
                GestureDetector(
                    onTap: () {
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.back();
                      // Get.offAll(
                      //   () => DashBoardManagerScreen(
                      //     currantIndex: 0,
                      //   ),
                      //   //preventDuplicates: false,
                      // );
                    },
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: AppColor.cLightBlueContainer,
                      child: Center(
                          child: assetSvdImageWidget(
                              image: DefaultImages.backIcn)),
                    )),
                Expanded(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(
                        "Change status".trr,
                        style: pBold20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          verticalSpace(24),
          if (widget.serviceStatus != "A")
            if (widget.btnActivate = true)
              ListTile(
                title: const Text("Active"),
                leading: Radio(
                  value: 1,
                  groupValue: selectedOption,
                  onChanged: (value) {
                    setState(() {
                      selectedOption = value!;
                      serviceStat = "A"; //ACTIVE
                      print(serviceStat);
                    });
                  },
                ),
              ),
          if (widget.serviceStatus != "I")
            if (widget.btnDeactivate = true)
              ListTile(
                title: const Text("Deactivate"),
                leading: Radio(
                  value: 2,
                  groupValue: selectedOption,
                  onChanged: (value) {
                    setState(() {
                      selectedOption = value!;
                      serviceStat = "I"; //DEACTIVATE
                      print(serviceStat);
                    });
                  },
                ),
              ),
          if (widget.serviceStatus != "T")
            if (widget.serviceStatus == "A")
              ListTile(
                title: const Text("Terminate"),
                leading: Radio(
                  value: 3,
                  groupValue: selectedOption,
                  onChanged: (value) {
                    setState(() {
                      selectedOption = value!;
                      serviceStat = "T"; //TERMINATE
                      print(serviceStat);
                    });
                  },
                ),
              ),
          verticalSpace(16),
          Row(
            children: [
              Expanded(
                child: CommonBorderButton(
                  title: 'Cancel'.trr,
                  onPressed: () {
                    vehicleController.selectedSerialList.clear();
                    vehicleController.selectedVehicleList.clear();
                    vehicleController.selectedFleetList.clear();
                    vehicleController.filterValueList.refresh();
                    vehicleController.selectedVehicleList.refresh();
                    vehicleController.selectedSerialList.refresh();
                    vehicleController.selectedFleetList.refresh();
                    Get.back();
                    // Get.offAll(
                    //   () => DashBoardManagerScreen(
                    //     currantIndex: 0,
                    //   ),
                    //   //preventDuplicates: false,
                    // );
                  },
                  textColor: AppColor.cDarkBlueFont,
                  bColor: AppColor.cDarkBlueFont,
                  btnColor: AppColor.cBackGround,
                ),
              ),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Submit'.trr,
                  onPressed: () async {
                    // final vehicleid = vehicle.read('vehicleID');
                    // print(vehicleid);
                    // List<String> vehicleids = [];
                    // vehicleids.add(vehicleid.toString());
                    // String vehicleIDList = vehicleids.join(",");
                    // print("vehicleIDList -------- $vehicleIDList");

                    // final vehicleid = vehicle.read('vehicleID');
                    // final vehicleSerialID = vehicle.read('vehicleSerialID');
                    // print(vehicleid);
                    // print(vehicleSerialID);

                    if (serviceStat == "T") {
                      showDialog(
                        barrierDismissible: false,
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            insetPadding: const EdgeInsets.all(16),
                            contentPadding: const EdgeInsets.all(24),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            content: terminateWidget(
                              onTap: () {
                                print(changeStatusController
                                    .passwordController.text);
                                print("serviceStatus +++++++++ $serviceStat");
                                print(
                                    "vehicleid +++++++++ ${widget.vehicleID}");
                                print(
                                    "vehicleSerialID +++++++++ ${widget.serialID}");

                                if (changeStatusController
                                        .passwordController.text ==
                                    "") {
                                  commonToast("Invalid Password");
                                } else {
                                  changeStatusController.changeServiceStatus(
                                    serviceStat,
                                    widget.vehicleID,
                                    widget.serialID,
                                  );
                                }

                                // vehicleController.myFleetList.refresh();
                                // fileComplaintController.cancelComplaint(serialid);
                              },
                            ),
                          );
                        },
                      );
                    } else {
                      changeStatusController.changeServiceStatus(
                        serviceStat,
                        widget.vehicleID,
                        widget.serialID,
                      );
                    }
                  },
                  textColor: AppColor.cWhiteFont,
                  btnColor: AppColor.themeOrangeColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  terminateWidget({required Function() onTap}) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  vehicleController.selectedSerialList.clear();
                  vehicleController.selectedVehicleList.clear();
                  vehicleController.selectedFleetList.clear();
                  vehicleController.filterValueList.refresh();
                  vehicleController.selectedVehicleList.refresh();
                  vehicleController.selectedSerialList.refresh();
                  vehicleController.selectedFleetList.refresh();
                  Get.back();
                  // Get.offAll(
                  //   () => DashBoardManagerScreen(
                  //     currantIndex: 0,
                  //   ),
                  //   //preventDuplicates: false,
                  // );
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    assetSvdImageWidget(image: DefaultImages.cancelIcn)
                  ],
                ),
              ),
              verticalSpace(24),
              Text("TERMINATE CONFIRMATION".trr,
                  style: pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                  textAlign: TextAlign.center),
              verticalSpace(8),
              Text(
                  "If  terminate the tag  before 6 months from date of installation, it will deduct tag value 100 SAR without vat from account balance."
                      .trr,
                  style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  textAlign: TextAlign.center),
              verticalSpace(24),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonTextField(
                    controller: changeStatusController.passwordController,
                    labelText: 'Please Enter your password'.trr,
                    hintText: "**********",
                    obscureText: changeStatusController.isPassword.value,
                    obscuringCharacter: '*',
                    // validator: (value) {
                    //   return Validator.validateName(value, "Password".trr);
                    // },
                  ),
                  verticalSpace(15),
                  CommonTextField(
                    controller:
                        changeStatusController.confirmPasswordController,
                    labelText: 'Confirm your password'.trr,
                    hintText: "**********",
                    obscureText: changeStatusController.isConfirmPass.value,
                    obscuringCharacter: '*',
                    validator: (value) {
                      return Validator.validateConfirmPassword(value,
                          changeStatusController.passwordController.text);
                    },
                  ),
                ],
              ),
              verticalSpace(24),
              Row(
                children: [
                  Expanded(
                      child: CommonButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.back();
                      // Get.offAll(
                      //   () => DashBoardManagerScreen(
                      //     currantIndex: 0,
                      //   ),
                      //   //preventDuplicates: false,
                      // );
                    },
                    btnColor: AppColor.cBackGround,
                    bColor: AppColor.themeDarkBlueColor,
                    textColor: AppColor.cDarkBlueFont,
                  )),
                  horizontalSpace(16),
                  Expanded(
                    child: CommonButton(
                      title: 'Submit'.trr,
                      onPressed: onTap,
                      btnColor: AppColor.themeOrangeColor,
                      horizontalPadding: 16,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
