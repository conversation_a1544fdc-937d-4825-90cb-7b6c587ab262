// ignore_for_file: prefer_const_constructors, avoid_print, prefer_const_constructors_in_immutables, prefer_interpolation_to_compose_strings

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/auth/auth_controller.dart';
import 'package:waie_app/core/controller/signup_controller/otp_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/auth_background.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_otp_textfield.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class SignupOTPValidationScreen extends StatelessWidget {
  SignupOTPValidationScreen({super.key});

  AuthController authController = Get.put(AuthController());
  OTPController otpController = Get.put(OTPController());
  final getOTPDataVerify = GetStorage();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Scaffold(
          backgroundColor: AppColor.cBackGround,
          body: SafeArea(
            child: AuthBackGroundWidget(
              widget: ListView(
                scrollDirection: Axis.vertical,
                shrinkWrap: true,
                children: [
                  verticalSpace(Get.height * 0.08),
                  Text(
                    "Confirm your phone number".trr,
                    style: pBold28,
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 25),
                    child: Text(
                      "We've sent a 4-digit code to your phone. Please enter it here."
                          .trr,
                      style: pRegular17,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Obx(() {
                    print(authController.verificationCode.value);
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16, top: 24),
                      child: FittedBox(
                        fit: BoxFit.fill,
                        child: CommonOtpTextField(
                          numberOfFields: 4,
                          autoFocus: true,
                          borderColor: AppColor.cBorder,
                          enabledBorderColor: AppColor.cBorder,
                          disabledBorderColor: AppColor.cBorder,
                          focusedBorderColor: AppColor.cBorder,
                          fieldWidth: 54,
                          filled: true,
                          fillColor: AppColor.cLightGrey,
                          borderRadius: BorderRadius.circular(6),
                          textStyle: pRegular14.copyWith(
                              color: AppColor.cDarkGreyFont),
                          keyboardType: TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          decoration: InputDecoration(),
                          showFieldAsBox: true,
                          hintText: 'X',
                          onCodeChanged: (String code) {
                            print("code==> $code");
                          },
                          onSubmit: (String verificationCode) {
                            print("verificationCode==> $verificationCode");
                            authController.verificationCode.value =
                                verificationCode;
                          }, // end onSubmit
                        ),
                      ),
                    );
                  }),
                  CommonButton(
                      title: 'Submit'.trr,
                      onPressed: () {
                        getOTPDataVerify.remove('otpCode');
                        var code = authController.verificationCode.value;

                        if (authController.verificationCode.value == '') {
                          commonToast("Enter OTP".trr);
                        } else {
                          getOTPDataVerify.writeIfNull(
                              'otpCode', authController.verificationCode.value);
                          otpController.signupOTPVerify(code);
                        }
                      }
                      //=> OTPController(),
                      // {
                      //   if (authController.verificationCode.value == '') {
                      //     commonToast("Enter OTP".trr);
                      //   } else {
                      //     showDialog(
                      //       context: context,
                      //       builder: (context) {
                      //         return AlertDialog(
                      //           insetPadding: EdgeInsets.all(16),
                      //           contentPadding: EdgeInsets.all(16),
                      //           shape: RoundedRectangleBorder(
                      //               borderRadius: BorderRadius.circular(12)),
                      //           content: Column(
                      //             mainAxisSize: MainAxisSize.min,
                      //             children: [
                      //               Text("Thanks for registration".trr,
                      //                   style: pBold20),
                      //               verticalSpace(24),
                      //               Text(
                      //                 "We will send a confirmation link to your email. Please check mailbox and follow the link to complete registration. You will have limited access untill confirm the registration."
                      //                     .trr,
                      //                 style: pRegular16,
                      //                 textAlign: TextAlign.center,
                      //               ),
                      //               verticalSpace(24),
                      //               CommonButton(
                      //                 title: "Back to log in".trr,
                      //                 onPressed: () {
                      //                   Get.offAll(() => LoginScreen());
                      //                 },
                      //               )
                      //             ],
                      //           ),
                      //         );
                      //       },
                      //     );
                      //   }
                      // },
                      ),
                  verticalSpace(20),
                  Text.rich(
                    TextSpan(
                      text: 'Didn`t get the code?'.trr + " ",
                      style: pRegular14,
                      children: <TextSpan>[
                        TextSpan(
                          text: 'Resend'.trr,
                          style: pBold14.copyWith(color: AppColor.cBlueFont),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              otpController.signupResendOTP();
                            },
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
