import 'dart:convert';

List<TagTransferVehicleDetailModel> tagTransferVehicleDetailModelFromJson(
        String str) =>
    List<TagTransferVehicleDetailModel>.from(
        json.decode(str).map((x) => TagTransferVehicleDetailModel.fromJson(x)));

String tagTransferVehicleDetailModelToJson(
        List<TagTransferVehicleDetailModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TagTransferVehicleDetailModel {
  int rowNumber;
  String plateno;
  String serialid;
  String vehicletypeDisp;
  String fueltypeDisp;
  String tankNo;
  String divisionname;
  String branchname;
  String deptname;
  String operationname;
  String servicetype;
  String servicestatus;

  TagTransferVehicleDetailModel({
    required this.rowNumber,
    required this.plateno,
    required this.serialid,
    required this.vehicletypeDisp,
    required this.fueltypeDisp,
    required this.tankNo,
    required this.divisionname,
    required this.branchname,
    required this.deptname,
    required this.operationname,
    required this.servicetype,
    required this.servicestatus,
  });

  factory TagTransferVehicleDetailModel.fromMap(Map<String, dynamic> map) {
    return TagTransferVehicleDetailModel(
      rowNumber: map["RowNumber"] ?? '',
      plateno: map["PLATENO"] ?? '',
      serialid: map["SERIALID"] ?? '',
      vehicletypeDisp: map["VEHICLETYPE_DISP"] ?? '',
      fueltypeDisp: map["FUELTYPE_DISP"] ?? '',
      tankNo: map["TANK_NO"] ?? '',
      divisionname: map["DIVISIONNAME"] ?? '',
      branchname: map["BRANCHNAME"] ?? '',
      deptname: map["DEPTNAME"] ?? '',
      operationname: map["OPERATIONNAME"] ?? '',
      servicetype: map["SERVICETYPE"] ?? '',
      servicestatus: map["SERVICESTATUS"] ?? '',
    );
  }

  factory TagTransferVehicleDetailModel.fromJson(Map<String, dynamic> json) =>
      TagTransferVehicleDetailModel(
        rowNumber: json["RowNumber"],
        plateno: json["PLATENO"],
        serialid: json["SERIALID"],
        vehicletypeDisp: json["VEHICLETYPE_DISP"],
        fueltypeDisp: json["FUELTYPE_DISP"],
        tankNo: json["TANK_NO"],
        divisionname: json["DIVISIONNAME"],
        branchname: json["BRANCHNAME"],
        deptname: json["DEPTNAME"],
        operationname: json["OPERATIONNAME"],
        servicetype: json["SERVICETYPE"],
        servicestatus: json["SERVICESTATUS"],
      );

  Map<String, dynamic> toJson() => {
        "RowNumber": rowNumber,
        "PLATENO": plateno,
        "SERIALID": serialid,
        "VEHICLETYPE_DISP": vehicletypeDisp,
        "FUELTYPE_DISP": fueltypeDisp,
        "TANK_NO": tankNo,
        "DIVISIONNAME": divisionname,
        "BRANCHNAME": branchname,
        "DEPTNAME": deptname,
        "OPERATIONNAME": operationname,
        "SERVICETYPE": servicetype,
        "SERVICESTATUS": servicestatus,
      };
}
