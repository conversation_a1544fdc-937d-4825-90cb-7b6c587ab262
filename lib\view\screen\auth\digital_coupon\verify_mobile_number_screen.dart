// ignore_for_file: prefer_const_constructors, must_be_immutable, avoid_print, prefer_interpolation_to_compose_strings

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:waie_app/core/controller/auth/digital_coupon_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/screen/auth/digital_coupon/create_dc_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_otp_textfield.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';

import '../../../../utils/locale_string.dart';

class VerifyMobileNumberScreen extends StatelessWidget {
  VerifyMobileNumberScreen({super.key});

  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  DigitalCouponController digitalCouponController =
      Get.put(DigitalCouponController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Scaffold(
          backgroundColor: AppColor.cBackGround,
          body: SafeArea(
            child: Form(
              key: formKey,
              child: Obx(() {
                print(
                    "-=-=== ${digitalCouponController.verificationCode.value}");
                return Column(
                  children: [
                    simpleMyAppBar(
                        title: "Verification".trr,
                        onTap: () {
                          Get.back();
                        },
                        backString: "Login".trr),
                    verticalSpace(64),
                    Expanded(
                      child: ListView(
                        scrollDirection: Axis.vertical,
                        shrinkWrap: true,
                        padding: const EdgeInsets.all(16),
                        children: [
                          Container(
                            width: Get.width,
                            decoration: BoxDecoration(
                              color: AppColor.lightBlueColor,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: EdgeInsets.only(
                                left: 19, bottom: 16, top: 24, right: 19),
                            child: Column(
                              children: [
                                Text(
                                  "Verify your mobile number".trr,
                                  style: pBold20,
                                ),
                                verticalSpace(24),
                                /*   CommonTextField(
                                controller:
                                    digitalCouponController.mobileController,
                                labelText: 'Mobile number'.trr + "*",
                                keyboardType: TextInputType.number,
                                hintText: '',
                                maxLength: 12,
                                validator: (value) {
                                  return Validator.validateMobile(value);
                                },
                              ),*/
                                Container(
                                  height: 44,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(6),
                                    color: AppColor.cBorder,
                                    border: Border.all(
                                      color: AppColor.cBorder,
                                    ),
                                  ),
                                  child: InternationalPhoneNumberInput(
                                    onInputChanged: (PhoneNumber number) {
                                      digitalCouponController.isoCode.value =
                                          number.isoCode!;
                                      //authController.phoneNoController.text =number.phoneNumber.toString();
                                      digitalCouponController
                                              .mobileController.text =
                                          number.phoneNumber
                                              .toString()
                                              .replaceAll("+", "");
                                      print("Entered Number=======" +
                                          number.phoneNumber.toString());
                                    },
                                    //maxLength:9,
                                    /* onInputValidated: (bool value) {
                                  print(value);
                                },*/
                                    cursorColor: AppColor.cHintFont,
                                    selectorConfig: SelectorConfig(
                                      selectorType:
                                          PhoneInputSelectorType.BOTTOM_SHEET,
                                      leadingPadding: 16,
                                      setSelectorButtonAsPrefixIcon: true,
                                    ),
                                    ignoreBlank: false,
                                    // autoValidateMode: AutovalidateMode.disabled,
                                    textStyle: pRegular14.copyWith(
                                      color: AppColor.cLabel,
                                    ),
                                    initialValue: PhoneNumber(
                                        isoCode: authController.isoCode.value,
                                        dialCode: authController.isoCode.value),
                                    inputBorder: OutlineInputBorder(),
                                    keyboardAction: TextInputAction.done,
                                    scrollPadding: EdgeInsets.zero,
                                    selectorTextStyle: pRegular14.copyWith(
                                        color: AppColor.cLabel, fontSize: 14),
                                    textAlign: TextAlign.start,
                                    textAlignVertical: TextAlignVertical.center,
                                    inputDecoration: InputDecoration(
                                      contentPadding:
                                          EdgeInsets.only(left: 16, bottom: 8),
                                      isDense: true,
                                      prefixText: "|  ",
                                      prefixStyle: TextStyle(
                                          fontSize: 30,
                                          color: AppColor.cBorder),
                                      counterText: '',
                                      hintText: " " + 'Please enter here'.trr,
                                      counterStyle:
                                          TextStyle(fontSize: 0, height: 0),
                                      errorStyle:
                                          TextStyle(fontSize: 0, height: 0),
                                      hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont,
                                      ),
                                      border: InputBorder.none,
                                      filled: true,
                                      fillColor: AppColor.cFilled,
                                    ),
                                    /*  onSaved: (PhoneNumber number) {
                                },*/
                                  ),
                                ),
                                //verticalSpace(16),
                                verticalSpace(16),
                                CommonButton(
                                  title: 'Generate OTP'.trr,
                                  btnColor: AppColor.themeDarkBlueColor,
                                  bColor: AppColor.themeDarkBlueColor,
                                  onPressed: () {
                                    print(
                                        "digitalCouponController.mobileController.text.toString().length" +
                                            digitalCouponController
                                                .mobileController.text.length
                                                .toString());
                                    if (digitalCouponController
                                            .mobileController.text
                                            .toString()
                                            .length ==
                                        12) {
                                      digitalCouponController
                                          .VerifyMobileNumber();
                                      digitalCouponController
                                          .verificationCode.value = '';
                                    } else {
                                      showDialog(
                                        barrierDismissible: false,
                                        context: Get.context!,
                                        builder: (context) {
                                          return AlertDialog(
                                            insetPadding:
                                                const EdgeInsets.all(16),
                                            contentPadding:
                                                const EdgeInsets.all(24),
                                            shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(12)),
                                            content: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  "Please enter valid mobile number"
                                                      .trr,
                                                  style: pBold20,
                                                  textAlign: TextAlign.center,
                                                ),
                                                verticalSpace(24),
                                                CommonButton(
                                                  title: "BACK".trr,
                                                  onPressed: () {
                                                    Get.back();
                                                  },
                                                  btnColor:
                                                      AppColor.themeOrangeColor,
                                                )
                                              ],
                                            ),
                                          );
                                        },
                                      );
                                    }
                                    /* if (formKey.currentState!.validate()) {
                                    digitalCouponController.VerifyMobileNumber();

                                    digitalCouponController
                                        .verificationCode.value = '';
                                  }*/
                                  },
                                ),
                              ],
                            ),
                          ),
                          verticalSpace(12),
                          digitalCouponController.isShowOtp.value
                              ? Column(
                                  children: [
                                    Text(
                                      "Verify your mobile number".trr,
                                      style: pBold17,
                                      textAlign: TextAlign.center,
                                    ),
                                    verticalSpace(6),
                                    Text(
                                      "Response code".trr,
                                      style: pBold17,
                                      textAlign: TextAlign.center,
                                    ),
                                    verticalSpace(16),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          bottom: 16, top: 24),
                                      child: FittedBox(
                                        fit: BoxFit.fill,
                                        child: CommonOtpTextField(
                                          numberOfFields: 6,
                                          borderColor: AppColor.cBorder,
                                          enabledBorderColor: AppColor.cBorder,
                                          disabledBorderColor: AppColor.cBorder,
                                          focusedBorderColor: AppColor.cBorder,
                                          fieldWidth: 54,
                                          filled: true,
                                          autoFocus: true,
                                          clearText: true,
                                          fillColor: AppColor.cLightGrey,
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          textStyle: pRegular14.copyWith(
                                              color: AppColor.cDarkGreyFont),
                                          keyboardType:
                                              TextInputType.numberWithOptions(
                                                  signed: true, decimal: true),
                                          inputFormatters: [
                                            FilteringTextInputFormatter
                                                .digitsOnly
                                          ],
                                          hintText: 'X',
                                          showFieldAsBox: true,
                                          cursorColor: AppColor.cBorder,

                                          onCodeChanged: (String code) {
                                            print("code==> $code");
                                          },
                                          onSubmit: (String verificationCode) {
                                            print(
                                                "verificationCode==> $verificationCode");
                                            digitalCouponController
                                                .verificationCode
                                                .value = verificationCode;
                                          }, // end onSubmit
                                        ),
                                      ),
                                    ),
                                    CommonButton(
                                      title: 'Verify'.trr,
                                      onPressed: () {
                                        // digitalCouponController.mobileController.clear();
                                        /* digitalCouponController.isShowOtp.value = false;
                                      digitalCouponController.verificationCode.value = "";*/
                                        digitalCouponController
                                            .VerifyDriverOTP();

                                        /* Get.to(() => CreateDCScreen(
                                            isBack: false,
                                          ));*/
                                      },
                                      btnColor: digitalCouponController
                                                  .verificationCode.value ==
                                              ''
                                          ? AppColor.cLightOrange
                                          : AppColor.themeOrangeColor,
                                    ),
                                    verticalSpace(24),
                                    Text.rich(
                                      TextSpan(
                                        text: 'Didn`t get the code?'.trr + ' ',
                                        style: pRegular14,
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: 'Resend'.trr,
                                            style: pBold14.copyWith(
                                                color: AppColor.cOrangeFont),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {},
                                          ),
                                        ],
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  ],
                );
              }),
            ),
          ),
        ),
      ),
    );
  }
}
