import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/menu_controller/order_controller/new_order_controller.dart';

import '../../../../models/pricetagcard.dart';
import '../../../../models/profile.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../view/widget/loading_widget.dart';

class Controller extends GetxController {
  // int counter = 0;

  // void increment() {
  //   counter++;
  //   update(['aVeryUniqueID', 'someOtherID']); // and then here
  // }


  /*List<PriceTagCardModel> prices = [];
  RxString topUpAmount = "0".obs;
  RxString unitPrice = "0".obs;
  RxString servicetype = "0".obs;
  RxString vatAmount = "0".obs;
  RxString amount = "0".obs;
  RxString totalAmount = "0".obs;
  RxString subTotal = "0".obs;
  RxString quantty = "0".obs;
  RxInt quantity = 0.obs;
  RxString srvType = "".obs;
  //var priceTagCards = [];

  fetchPriceTagCards() async {
    Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var custid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.newGetPrice),
        body: {
          "custid": custid,
          "servType": srvType.value.toString() ?? "T",
          "qty": "1",
        });
    if (response.statusCode == 200) {
      Loader.hideLoader();
      print(response.statusCode);
      print(jsonDecode(response.body));

      final Map<String, dynamic> result = jsonDecode(response.body);

      print(result["SERVICETYPE"]);

      topUpAmount.value = result["TOPUPAMT"];
      unitPrice.value = result["UNITPRICE"];
      servicetype.value = result["SERVICETYPE"];
      vatAmount.value = result["VATAMT"];
      amount.value = result["AMT"];
      totalAmount.value = result["TOTAMT"];
      subTotal.value = result["SUBTOTAL"];
      quantty.value = result["QUANTITY"];
    }

    // for (int i = 0; i < result.length; i++) {
    //   PriceTagCardModel price =
    //       PriceTagCardModel.fromMap(result[i] as Map<String, dynamic>);
    //   prices.add(price);
    //   update(['isView']);
    // }
    //priceTagCards.value = prices;

    //return prices;
  }

  initialFetchPriceTagCards() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var custid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.newGetPrice),
        body: {
          "custid": custid,
          "servType": srvType.value.toString() ?? "T",
          "qty": quantity.value.toString() ?? "1",
        });
    if (response.statusCode == 200) {
      print(response.statusCode);
      print(jsonDecode(response.body));

      final Map<String, dynamic> result = jsonDecode(response.body);

      print(result["SERVICETYPE"]);

      topUpAmount.value = result["TOPUPAMT"];
      unitPrice.value = result["UNITPRICE"];
      servicetype.value = result["SERVICETYPE"];
      vatAmount.value = result["VATAMT"];
      amount.value = result["AMT"];
      totalAmount.value = result["TOTAMT"];
      subTotal.value = result["SUBTOTAL"];
      quantty.value = result["QUANTITY"];
    }

    // for (int i = 0; i < result.length; i++) {
    //   PriceTagCardModel price =
    //       PriceTagCardModel.fromMap(result[i] as Map<String, dynamic>);
    //   prices.add(price);
    //   update(['isView']);
    // }
    //priceTagCards.value = prices;

    //return prices;
  }*/
}
