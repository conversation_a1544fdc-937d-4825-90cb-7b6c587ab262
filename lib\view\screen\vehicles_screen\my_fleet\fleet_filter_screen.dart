// ignore_for_file: prefer_const_constructors, must_be_immutable, avoid_print

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/vehicle_controller/vehicle_controller.dart';

class FleetFilterScreen extends StatelessWidget {
  FleetFilterScreen({Key? key}) : super(key: key);
  VehicleController vehicleController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:  EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        height: Get.height - 60,
        decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Text("More filters".trr, style: pBold24.copyWith(fontSize: 22)),
                              horizontalSpace(16),
                              Container(
                                padding: EdgeInsets.symmetric(vertical: 4, horizontal: 12),
                                decoration:
                                    BoxDecoration(color: AppColor.cLightRed, borderRadius: BorderRadius.circular(12)),
                                child: Text("Clear all".trr, style: pSemiBold12.copyWith(color: AppColor.cRedText)),
                              )
                            ],
                          ),
                          GestureDetector(
                            onTap: () {
                              Get.back();
                            },
                            child: Container(
                              padding: EdgeInsets.all(8),
                              decoration: BoxDecoration(shape: BoxShape.circle, color: AppColor.cLightGrey),
                              child: assetSvdImageWidget(image: DefaultImages.cancelIcn),
                            ),
                          )
                        ],
                      ),
                    ),
                    horizontalDivider(),
                    ListView.separated(
                      itemCount: vehicleController.filterValueList.length,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = vehicleController.filterValueList[index];
                        return Obx(() {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextButton(
                                onPressed: () {
                                  for (int i = 0; i < vehicleController.filterValueList.length; i++) {
                                    if (index == i) {
                                      data['isExpand'].value = !data['isExpand'].value;
                                      // data['isExpand'].value = true;
                                    } else {
                                      vehicleController.filterValueList[i]['isExpand'].value = false;
                                    }
                                    vehicleController.filterValueList.refresh();
                                  }
                                },
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      data['title'].toString().trr,
                                      style: pSemiBold17.copyWith(
                                          color: data['isExpand'].value == true ? AppColor.cText : AppColor.cFont),
                                    ),
                                    assetSvdImageWidget(
                                        image: data['isExpand'].value == true
                                            ? DefaultImages.dropUpIcn
                                            : DefaultImages.blueArrowDownIcn),
                                  ],
                                ),
                              ),
                              Obx(() {
                                print("${data["subtitle"].length}");
                                return data['isExpand'].value == true
                                    ? data['title'] == "Offline limit"
                                        ? Padding(
                                          padding: const EdgeInsets.fromLTRB(16,16,16,24),
                                          child: Column(
                                              children: [
                                                CommonDropdownButtonWidget(
                                                  labelText: "Limit type".trr,
                                                  value: vehicleController.limitType.value,
                                                  list: data['subtitle'],
                                                  onChanged: (value) {
                                                    vehicleController.limitType.value = value;
                                                  },
                                                ),
                                                verticalSpace(16),
                                                Row(
                                                  children: [
                                                    Expanded(child: CommonTextField(labelText: 'From'.trr,hintText: '0-10 000',keyboardType: TextInputType.number,)),horizontalSpace(16),
                                                    Expanded(child: CommonTextField(labelText: 'To'.trr,hintText: '0-10 000',keyboardType: TextInputType.number,)),
                                                  ],
                                                ),
                                                verticalSpace(16),
                                                Row(
                                                  children: [
                                                    Expanded(child: CommonBorderButton(title: "Reset".trr, onPressed: () {},)),
                                                    horizontalSpace(16),
                                                    Expanded(child: CommonButton(title: 'Apply'.trr, onPressed: () {}, horizontalPadding: 16, btnColor: AppColor.themeDarkBlueColor,)),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          )
                                        : Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: data["subtitle"].map<Widget>((name) {
                                              print("name$name");
                                              return Padding(
                                                padding: const EdgeInsets.only(bottom: 10, left: 24),
                                                child: GestureDetector(
                                                  onTap: () {},
                                                  child: Text(
                                                    name,
                                                    style: pMedium12,
                                                    textAlign: TextAlign.start,
                                                  ),
                                                ),
                                              );
                                            }).toList(),
                                          )
                                    : SizedBox();
                              })
                            ],
                          );
                        });
                      },
                      separatorBuilder: (context, index) => horizontalDivider(),
                    ),
                  ],
                ),
              ),
              Container(
                color: AppColor.cLightGrey,
                padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: CommonButton(
                        title: 'Cancel'.trr,
                        onPressed: () {
                          Get.back();
                        },
                        textColor: AppColor.cText,
                        btnColor: AppColor.cBackGround,
                      ),
                    ),
                    horizontalSpace(16),
                    Expanded(
                      child: CommonButton(
                        title: 'Apply filters'.trr,
                        onPressed: () {
                          Get.back();
                        },
                        textColor: AppColor.cWhiteFont,
                        btnColor: AppColor.themeOrangeColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  moreFilterWidget(String title, icon) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16),
      width: Get.width,
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(
          title,
          style: pSemiBold17,
        ),
        assetSvdImageWidget(image: icon, colorFilter: ColorFilter.mode(AppColor.cText, BlendMode.srcIn))
      ]),
    );
  }
}
