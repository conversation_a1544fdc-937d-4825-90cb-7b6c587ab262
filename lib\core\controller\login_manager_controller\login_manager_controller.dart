// ignore_for_file: prefer_const_constructors

import 'package:get/get.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/view/screen/auth/login_screen.dart';
import 'package:waie_app/view/screen/login_manager/contact_us_webview_screen.dart';
import 'package:waie_app/view/screen/login_manager/location_webview_screen.dart';
import 'package:waie_app/view/screen/login_manager/more_screen.dart';

import '../../../utils/constant.dart';
import '../../../view/screen/auth/login_with_email_screen.dart';
import '../../../view/screen/login_manager/common_webview_screen.dart';

class LoginManagerController extends GetxController {
  RxInt currantIndex = 2.obs;
  RxList naviBarItemList = [
    {
      'icon': DefaultImages.aboutIcn,
      'title': 'About',
      'screen': AboutWebViewScreen(url: AppConstant.aboutUsUrl)
    },
    {
      'icon': DefaultImages.contactusIcn,
      'title': 'Contact us',
      'screen': ContactUsScreen(url: AppConstant.contactUsUrl)
    },
    {
      'icon': DefaultImages.waieIcn,
      'title': 'Waie',
      'screen': LoginScreen(),
    },
    {
      'icon': DefaultImages.loginLocationIcn,
      'title': 'Locations',
      'screen': LocationWebViewScreen(url: AppConstant.locationUrl)
    },
    {
      'icon': DefaultImages.moreIcn,
      'title': 'More',
      'screen': MoreScreen(),
    },
  ].obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    currantIndex.value = 2;
  }
}
