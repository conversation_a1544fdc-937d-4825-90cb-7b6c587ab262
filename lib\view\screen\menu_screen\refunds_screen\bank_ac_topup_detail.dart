// ignore_for_file: prefer_const_constructors, must_be_immutable
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/bank_list_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/submit_order_refund_service_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/submit_order_refund_topup_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:file_picker/file_picker.dart';

import '../../../../core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import '../../../widget/common_button.dart';
import '../../../widget/common_drop_down_widget.dart';
import '../company_affiliates_screen/new_affiliate_screen.dart';

class BankAcTopupDetailWidget extends StatefulWidget {
  bool isDebit;
  BankAcTopupDetailWidget({super.key, required this.isDebit});

  @override
  State<BankAcTopupDetailWidget> createState() =>
      _BankAcTopupDetailWidgetState();
}

class _BankAcTopupDetailWidgetState extends State<BankAcTopupDetailWidget> {
  FilePickerResult? companyRegistration;
  FilePickerResult? bankLetter;

  String errorString = '';
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  BankListController bankListController = Get.put(BankListController());
  SubmitOrderRefundTopupController submitOrderRefundTopupController =
      Get.put(SubmitOrderRefundTopupController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Enter your bank account details".trr,
              style: pBold20,
            ),
            verticalSpace(24),
            CommonTextField(
              controller:
                  submitOrderRefundTopupController.accountHolderController,
              labelText: "Name of account holder".trr,
              hintText: "Enter a name".trr,
              validator: (value) {
                return Validator.validateRequired(value);
              },
            ),
            verticalSpace(16),
            Text(
              "Company registration document".trr,
              style: pRegular12,
            ),
            verticalSpace(6),
            GestureDetector(
              child: Container(
                height: 44,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: AppColor.cBorder)),
                padding: EdgeInsets.symmetric(
                  horizontal: 6,
                ),
                child: Row(
                  children: [
                    Container(
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: AppColor.lightBlueColor,
                      ),
                      padding: EdgeInsets.only(right: 12, left: 8),
                      child: Row(
                        children: [
                          assetSvdImageWidget(
                            image: DefaultImages.fileIcn,
                          ),
                          horizontalSpace(8),
                          Text(
                            "Choose file".trr,
                            style: pRegular14,
                          ),
                        ],
                      ),
                    ),
                    horizontalSpace(8),
                    Expanded(
                      child: Text(
                        submitOrderRefundTopupController.companyReg.isEmpty
                            ? "No file chosen".trr
                            : submitOrderRefundTopupController.companyReg.value
                                .split("/")
                                .last,
                        style: pRegular14.copyWith(
                          color: AppColor.cDarkGreyFont,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              onTap: () async {
                //refundsController.companyReg.value =
                //await refundsController.pickImage(ImageSource.gallery);
                companyRegistration = await FilePicker.platform.pickFiles(
                  type: FileType.custom,
                  allowedExtensions: ['jpg', 'pdf', 'doc'],
                  withReadStream: true,
                );
                if (companyRegistration == null) {
                  print("No file selected");
                } else {
                  setState(() {
                    for (var element in companyRegistration!.files) {
                      print(element.name);
                      submitOrderRefundTopupController.companyReg.value =
                          element.name;
                    }
                  });
                }
              },
            ),
            verticalSpace(16),
            Text(
              "Bank letter".trr,
              style: pRegular12,
            ),
            verticalSpace(6),
            GestureDetector(
              child: Container(
                height: 44,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: AppColor.cBorder)),
                padding: EdgeInsets.symmetric(
                  horizontal: 6,
                ),
                child: Row(
                  children: [
                    Container(
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: AppColor.lightBlueColor,
                      ),
                      padding: EdgeInsets.only(right: 12, left: 8),
                      child: Row(
                        children: [
                          assetSvdImageWidget(
                            image: DefaultImages.fileIcn,
                          ),
                          horizontalSpace(8),
                          Text(
                            "Choose file".trr,
                            style: pRegular14,
                          ),
                        ],
                      ),
                    ),
                    horizontalSpace(8),
                    Expanded(
                      child: Text(
                        submitOrderRefundTopupController.bankLetter.isEmpty
                            ? "No file chosen".trr
                            : submitOrderRefundTopupController.bankLetter.value
                                .split("/")
                                .last,
                        style: pRegular14.copyWith(
                          color: AppColor.cDarkGreyFont,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  ],
                ),
              ),
              onTap: () async {
                bankLetter = await FilePicker.platform.pickFiles(
                  type: FileType.custom,
                  allowedExtensions: ['jpg', 'pdf', 'doc'],
                  withReadStream: true,
                );
                if (bankLetter == null) {
                  print("No file selected");
                } else {
                  setState(() {
                    for (var element in bankLetter!.files) {
                      print(element.name);
                      submitOrderRefundTopupController.bankLetter.value =
                          element.name;
                    }
                  });
                }
              },
            ),
            verticalSpace(16),
            // CommonDropdownButtonWidget(
            //   labelText: "Select Bank".trr,
            //   list: refundsController.selectBankList,
            //   value: refundsController.selectedBank.value,
            //   onChanged: (value) {
            //     refundsController.selectedBank.value = value;
            //   },
            //   isExpanded: true,
            // ),
            Text(
              '${"Select Bank ".trr}*',
              style: pRegular13,
            ),
            verticalSpace(10),
            // CommonHintDropdownWidget(
            DropdownButtonFormField(
              items: bankListController.bankLists.map((data) {
                return DropdownMenuItem(
                  value: data.typecode,
                  child: Text(
                    data.typedesc,
                    style: pMedium12,
                    textAlign: TextAlign.center,
                  ),
                );
              }).toList(),
              /*value: bankListController
                                  .bankController.value,*/
              onChanged: (value) {
                bankListController.bankController.text = value.toString();
                print(
                    "bankListController.value +++++++++++++++ ${bankListController.bankController.text}");
                submitOrderRefundTopupController.bankController.text =
                    value.toString();
                print(
                    "submitOrderRefundTopupController.bankController.text +++++++++++++++ ${submitOrderRefundTopupController.bankController.text}");
              },
              validator: (value) {
                setState(() {
                  if (value == null) {
                    errorString = 'Please select bank'.trr;
                  } else {
                    errorString = '';
                  }
                });
                return null;
              },
              style: pRegular14.copyWith(color: AppColor.cLabel),
              borderRadius: BorderRadius.circular(6),
              dropdownColor: AppColor.cLightGrey,
              icon: assetSvdImageWidget(image: DefaultImages.dropDownIcn),
              decoration: InputDecoration(
                hintText: 'Banks'.trr,
                hintStyle: pRegular14.copyWith(color: AppColor.cHintFont),
                contentPadding: EdgeInsets.only(left: 16, right: 16),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
              ),
            ),
            errorString == ''
                ? SizedBox()
                : Text(
                    errorString,
                    style: pRegular12.copyWith(color: AppColor.cRedText),
                  ),
            verticalSpace(16),
            CommonDropdownButtonWidget(
              labelText: "IBAN number".trr,
              list: submitOrderRefundTopupController.ibanList,
              value: submitOrderRefundTopupController.selectedIBAN.value,
              onChanged: (value) {
                submitOrderRefundTopupController.selectedIBAN.value = value;
                print(
                    "submitOrderRefundTopupController.selectedIBAN.value +++++++++++++++ ${submitOrderRefundTopupController.selectedIBAN.value}");
              },
              isExpanded: true,
            ),
            verticalSpace(16),
            CommonTextField(
              controller: submitOrderRefundTopupController.iBanNoController,
              labelText: '',
              hintText: '1420EC0208992000009289',
              validator: (value) {
                return Validator.validateRequired(value);
              },
            ),
            verticalSpace(16),
            CommonTextField(
              controller:
                  submitOrderRefundTopupController.reasonRefundController,
              labelText: "Reason for refund".trr,
              hintText: "Let us know why you need a refund".trr,
              maxLines: 5,
              validator: (value) {
                return Validator.validateRequired(value);
              },
            ),
            verticalSpace(24),
            Container(
              padding: EdgeInsets.symmetric(vertical: 7, horizontal: 16),
              decoration: BoxDecoration(
                  color: AppColor.lightOrangeColor,
                  borderRadius: BorderRadius.circular(6)),
              child: Text(
                  "!  ${"Refunded orders don't count towards your bulk discount on your next order.".trr}",
                  style: pRegular13.copyWith(color: AppColor.cDarkOrangeText)),
            ),
            verticalSpace(24),
            CommonButton(
              title: "Submit".trr,
              onPressed: () {
                //if (_formKey.currentState!.validate()) {
                if (widget.isDebit == true) {
                  submitOrderRefundTopupController.rblRefundOPT.value = "Q";
                }
                if (bankLetter == null) {
                  commonToast('Please Upload Bank Letter');
                } else if (companyRegistration == null) {
                  commonToast('Please Upload Company Registration');
                } else {
                  PlatformFile bankletter = bankLetter!.files.first;
                  PlatformFile companyregistration =
                      companyRegistration!.files.first;
                  // var bankletter = bankLetter;
                  // var companyregistration = companyRegistration;
                  submitOrderRefundTopupController.submitOrderRefundFormQ(
                      bankletter, companyregistration);
                }
                //}
                // showDialog(
                //   context: context,
                //   builder: (context) {
                //     return AlertDialog(
                //       shape: RoundedRectangleBorder(
                //           borderRadius: BorderRadius.circular(12)),
                //       contentPadding: EdgeInsets.all(24),
                //       content: successDialogWidget(
                //           title: "Your refund request".trr, () {
                //         Get.back();
                //       }, isBorderBtn: true),
                //     );
                //   },
                // );
              },
              btnColor: AppColor.themeOrangeColor,
            ),
            verticalSpace(24),
          ],
        ),
      );
    });
  }
}
