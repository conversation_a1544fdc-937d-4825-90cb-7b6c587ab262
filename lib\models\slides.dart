import 'dart:convert';

class Slides
{
  String IMG_NAME;
  String IMG_LOC;

  Slides({
    required this.IMG_NAME,
    required this.IMG_LOC,
  });

  Map<String, dynamic> toMap() {
    return {
      'IMG_NAME': IMG_NAME,
      'IMG_LOC': IMG_LOC,
    };
  }

  factory Slides.fromMap(Map<String, dynamic> map) {
    return Slides(
      IMG_NAME: map['IMG_NAME'] ?? '',
      IMG_LOC: map['IMG_LOC'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Slides.fromJson(String source) =>
      Slides.fromMap(json.decode(source));
}