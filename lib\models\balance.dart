import 'dart:convert';

class BalanceModel {
  final String unallocatbalance;
  final String currentbalance;
  final String reservebalance;
  BalanceModel({
    required this.unallocatbalance,
    required this.currentbalance,
    required this.reservebalance,
  });

  Map<String, dynamic> toMap() {
    return {
      'UNALLOCATBALANCE': unallocatbalance,
      'CURRENTBALANCE': currentbalance,
      'RESERVEBALANCE': reservebalance,
    };
  }

  factory BalanceModel.fromMap(Map<String, dynamic> map) {
    return BalanceModel(
      unallocatbalance: map['UNALLOCATBALANCE'] ?? '',
      currentbalance: map['CURRENTBALANCE'] ?? '',
      reservebalance: map['RESERVEBALANCE'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory BalanceModel.fromJson(String source) =>
      BalanceModel.fromMap(json.decode(source));
}
