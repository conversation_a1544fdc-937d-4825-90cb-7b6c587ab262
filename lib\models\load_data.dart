import 'dart:convert';

class Load_Data_Model {
  final String TYPECODE;
  final String TYPEDESC;

 // Load_Data({this.tYPECODE, this.tYPEDESC});
  Load_Data_Model({required this.TYPECODE, required this.TYPEDESC});

 /* Load_Data.fromJson(Map<String, dynamic> json) {
    typecode = json['TYPECODE'];
    typedesc = json['TYPEDESC'];
  }
*/
  Map<String, dynamic> toMap() {
    return {
      'TYPECODE': TYPECODE,
      'TYPEDESC': TYPEDESC,
    };
  }

  factory Load_Data_Model.fromMap(Map<String, dynamic> map) {
    return Load_Data_Model(
      TYPECODE: map['TYPECODE'] ?? '',
      TYPEDESC: map['TYPEDESC'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Load_Data_Model.fromJson(String source) =>
      Load_Data_Model.fromMap(json.decode(source));


}