// ignore_for_file: avoid_print

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:webview_flutter_plus/webview_flutter_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../widget/loading_widget.dart';

class AldreesDonationScreen extends StatefulWidget {
  final String url;
  const AldreesDonationScreen({
    super.key,
    required this.url,
  });

  @override
  State<AldreesDonationScreen> createState() => _AldreesDonationScreenState();
}

class _AldreesDonationScreenState extends State<AldreesDonationScreen> {
  late WebViewController _controler;
  final double _height = 1;
  bool isLoading = true;

  @override
  void initState() {
    _controler = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.url))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (progress) {
            print("progress---> $progress");
            if (progress == 100) {
              setState(() {
                isLoading = false;
              });
            }
          },
        ),
      );
    super.initState();
  }

  Future<bool> _onWillPop() async {
    if (await _controler.canGoBack()) {
      // If there's history, go back to the previous page
      _controler.goBack();
      return false; // Prevent the app from exiting
    } else {
      return true; // Allow the app to exit
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: AppColor.cWhite,
        appBar: AppBar(
          backgroundColor: AppColor.cWhite,
          leading: Container(
            height: 0,
            padding: EdgeInsets.only(left: 22, right: 22),
            child: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: Container(
                // padding: EdgeInsets.only(
                //   top: 3,
                //   bottom: 0,
                // ),
            
                child: assetSvdImageWidget(
                    image: DefaultImages.backIcn,
                    height: 0,
                    colorFilter: ColorFilter.mode(
                        AppColor.cBlack, BlendMode.srcIn)),
              ),
            ),
          ),
          title: Text(
            "SOCIAL RESPONSIBILITY".trr,
            style: pBold18.copyWith(color: AppColor.cBlack),
            textAlign: TextAlign.center,
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
                child: Image.asset(
                  DefaultImages.socialResLogoImg,
                  height: 40,
                ),
              ),
            ),
          ],
        ),
        body: isLoading
            ? const Center(
                child: LoadingWidget(),
              )
            : WebViewWidget(
                controller: _controler,
              ),
      ),
    );
  }
}
