import 'dart:convert';

class Report_Places {
  final String PLACE_CODE;
  final String PLACE_DESC;

  Report_Places({
    required this.PLACE_CODE,
    required this.PLACE_DESC,
  });

  Map<String, dynamic> toMap() {
    return {
      'PLACE_CODE': PLACE_CODE,
      'PLACE_DESC': PLACE_DESC,
    };
  }

  factory Report_Places.fromMap(Map<String, dynamic> map) {
    return Report_Places(
      PLACE_CODE: map['PLACE_CODE'] ?? '',
      PLACE_DESC: map['PLACE_DESC'] ?? '',
    );
  }
  String toJson() => json.encode(toMap());

  factory Report_Places.fromJson(String source) =>
      Report_Places.fromMap(json.decode(source));
}