// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/fleet_structure_controller/overview_controller.dart';
import 'add_pure_dc_vehicle_screen.dart';
import 'assign_digital_coupon_widget.dart';
import 'fleet_vehicle_screen.dart';

class MyFleetScreen extends StatefulWidget {
  const MyFleetScreen({super.key});

  @override
  State<MyFleetScreen> createState() => _MyFleetScreenState();
}

class _MyFleetScreenState extends State<MyFleetScreen> {
  VehicleController vehicleController = Get.put(VehicleController());
  FleetController fleetController = Get.put(FleetController());
  OverviewController overviewController = Get.put(OverviewController());
  // ComplaintReasonController complaintReasonController =
  //     Get.put(ComplaintReasonController());
  final vehicle = GetStorage();
  final isPinblock = GetStorage();
  final isDCBlock = GetStorage();

  // @override
  // void initState() {
  //   super.initState();
  //   fleetController.fetchFleets();
  //   complaintReasonController.fetchComplaintReasons();
  // }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Expanded(
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(),
            child: Column(
              children: [
                buttonRowWidget(
                    // totalVehicle: "(22)",
                    // addVehicleFun: () {
                    //   Get.to(() => AddPureDCVehicleScreen(
                    //         title: "Add a vehicle".trr,
                    //       ));
                    // },
                    addDCVehicleFun: () {
                  Get.to(() => AddPureDCVehicleScreen(
                        title: "Add DC vehicle".trr,
                      ));
                      
                }),
                // verticalSpace(24),
                verticalSpace(12),
                fleetController.fleetList.isNotEmpty
                    ? ListView.builder(
                        itemCount: fleetController.fleetList.length,
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          var data = fleetController.fleetList[index];
                          print("data.plateno >>>>>>> ${data.plateno}");
                          print("data.vehicleid >>>>>>> ${data.vehicleid}");
                          print(
                              "data.servicestatusDisp >>>>>>> ${data.servicestatusDisp}");
                          return GestureDetector(
                            onTap: () async {
                              overviewController.placeList.value =
                                  fleetController.loadPlaces;
                              overviewController.placeList.refresh();
                              print("data.vehicleid >>>>>>> ${data.vehicleid}");
                              setState(() {
                                var isPinActivate =
                                    data.pinblock == "Y" ? true : false;
                                isPinblock.write(
                                    'isPinActivate', isPinActivate);
                                var isdcBlock =
                                    data.servicetype == "D" || data.isDC == true
                                        ? true
                                        : false;
                                isDCBlock.write('isdcBlock', isdcBlock);
                                Get.to(() => FleetVehicleScreen(), arguments: {
                                  'plateno': data.plateno,
                                  'vehicletypeDisp': data.vehicletypeDisp,
                                  'quotaString': data.quotatypeDisp,
                                  'servicestatusDisp': data.servicestatusDisp,
                                  'branchDisp': data.branchDisp,
                                  'fueltype': data.fueltype,
                                  'driver': data.driver,
                                  'quotaTotal': data.quotaDisp,
                                  'password': data.passwordDisp,
                                  'tankNo1': data.tankNo,
                                  'tankNo2': data.tankNo2,
                                  'fillingDays': data.fillingdays,
                                  'fuelType': data.fueltype,
                                  'fuelTypeDisp': data.fueltypeDisp,
                                  'vehicleID': data.vehicleid,
                                  'vehicleSerialID': data.serialid,
                                  'serviceStatus': data.servicestatus,
                                  'serviceType': data.servicetype,
                                  'serialXID': data.servicetype == "C"
                                      ? data.serialid
                                      : data.serialcode,
                                  'insTermDate': data.servicestatus == "T"
                                      ? data.terminateDate
                                      : data.instdate,
                                  'isDC': data.isDC,
                                  'sample': data,
                                });
                              });
                            },
                            child: fleetWidget(
                              //isShowCheck: true,
                              // onChanged: (value) {
                              //   setState(() {
                              //     data.isDC = value ?? false;
                              //   });
                              //   if (value == true) {
                              //     vehicleController.selectedFleetList
                              //         .add(data.vehicleid);
                              //     vehicleController.selectedVehicleList
                              //         .add(data.vehicleid);
                              //     vehicleController.selectedSerialList
                              //         .add(data.serialid);
                              //   } else {
                              //     vehicleController.selectedFleetList
                              //         .remove(index);
                              //     vehicleController.selectedVehicleList
                              //         .remove(index);
                              //     vehicleController.selectedSerialList
                              //         .remove(index);
                              //     print(
                              //         "*****************||********************");
                              //     print(
                              //         "vehicleController.selectedSerialList remove>>>> ${jsonDecode(jsonEncode(vehicleController.selectedSerialList))}");
                              //     print(
                              //         "*****************||********************");
                              //   }
                              //   print("*************************************");
                              //   print(
                              //       "vehicleController.selectedFleetList.lenght >>>> ${vehicleController.selectedFleetList.length}");
                              //   print("*************************************");
                              //   print(
                              //       "vehicleController.selectedVehicleList >>>> ${jsonDecode(jsonEncode(vehicleController.selectedVehicleList))}");
                              //   print(
                              //       "vehicleController.selectedSerialList >>>> ${jsonDecode(jsonEncode(vehicleController.selectedSerialList))}");

                              //   String vehicleIDList = vehicleController
                              //       .selectedVehicleList
                              //       .join(",");
                              //   String serialIDList = vehicleController
                              //       .selectedSerialList
                              //       .join(",");

                              //   print("*************************************");
                              //   print("vehicleIDList >>>> $vehicleIDList");
                              //   print("serialIDList >>>> $serialIDList");
                              //   vehicle.write('vehicleID', vehicleIDList);
                              //   vehicle.write('vehicleSerialID', serialIDList);
                              //   print("*************************************");
                              //   vehicleController.selectedVehicleList.refresh();
                              //   vehicleController.selectedSerialList.refresh();
                              //   vehicleController.selectedFleetList.refresh();
                              //   vehicleController.filterValueList.refresh();
                              // },
                              pinblock: data.pinblock,
                              pinblockDate: data.pinblockDate,
                              value: data.isDC,
                              code: data.plateno,
                              status: data.servicestatusDisp.toString().trr,
                              title: data.vehicletypeDisp,
                              type: data.fueltypeDisp,
                              driver: data.driver,
                              quotaTotal: data.quotaDisp,
                              quotaString: data.quotatypeDisp,
                              division: data.branchDisp,
                              serviceStatus: data.servicestatus,
                              serviceType: data.servicetype,
                              serialXID: data.servicetype == "C"
                                  ? data.serialid
                                  : data.serialcode,
                              insTermDate: data.servicestatus == "T"
                                  ? data.terminateDate
                                  : data.instdate,
                              textColor: data.servicestatusDisp ==
                                          "IN-ACTIVE" ||
                                      data.servicestatusDisp == 'In progress'
                                  ? AppColor.cRedText
                                  : data.servicestatusDisp == "NEW"
                                      ? AppColor.cDarkBlueFont
                                      : data.servicestatusDisp == "TERMINATED"
                                          ? AppColor.cDarkGreyFont
                                          : AppColor.cGreen,
                              color: data.servicestatusDisp == "IN-ACTIVE" ||
                                      data.servicestatusDisp == 'In progress'
                                  ? AppColor.cLightRedContainer
                                  : data.servicestatusDisp == "NEW"
                                      ? AppColor.cLightBlueContainer
                                      : data.servicestatusDisp == "TERMINATED"
                                          ? AppColor.cMediumGreyContainer
                                          : AppColor.cLightGreen,
                              tag: data.servicetype == "D" || data.isDC == true
                                  ? DefaultImages.scannerIcn
                                  : data.servicetype == "T"
                                      ? DefaultImages.tagIcn
                                      : DefaultImages.cardIcn,
                              // tag: data.servicestatusDisp == "IN-ACTIVE" ||
                              //         data.servicestatusDisp == 'TERMINATED'
                              //     ? DefaultImages.inactiveIcn
                              //     : DefaultImages.tagIcn,
                              viewMore: () {
                                //   serviceType == "D"
                                // ? serviceType == "T"
                                //     ? DefaultImages.scannerIcn
                                //     : DefaultImages.scannerIcn
                                // : DefaultImages.scannerIcn,
                                // showModalBottomSheet(
                                //   context: context,
                                //   shape: RoundedRectangleBorder(
                                //       borderRadius: BorderRadius.vertical(
                                //           top: Radius.circular(16))),
                                //   backgroundColor: AppColor.cBackGround,
                                //   barrierColor: AppColor.cBlackOpacity,
                                //   isScrollControlled: true,
                                //   builder: (context) {
                                //     return ActionWidget(
                                //       code: data.plateno,
                                //       isComplaint:
                                //           data.servicestatusDisp == "ACTIVE"
                                //               ? true
                                //               : false,
                                //     );
                                //   },
                                // );
                              },
                              scanTap: () {
                                showModalBottomSheet(
                                  context: context,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.vertical(
                                          top: Radius.circular(16))),
                                  backgroundColor: AppColor.cBackGround,
                                  barrierColor: AppColor.cBlackOpacity,
                                  isScrollControlled: true,
                                  builder: (context) {
                                    return AssignDigitalCouponWidget(
                                      code: data.plateno,
                                    );
                                  },
                                );
                              },
                            ),
                          );
                        },
                      )
                    : const Center(child: CircularProgressIndicator()),
              ],
            ),
          ),
        ));
  }
}

Widget fleetWidget({
  required String code,
  String? status,
  Color? textColor,
  Color? color,
  bool? value,
  //bool? isShowCheck,
  //ValueChanged<bool?>? onChanged,
  String? pinblock,
  String? pinblockDate,
  String? title,
  String? tag,
  String? type,
  String? driver,
  String? quotaTotal,
  String? quotaString,
  String? division,
  Function()? viewMore,
  Function()? scanTap,
  String? serviceStatus,
  String? serviceType,
  String? serialXID,
  String? insTermDate,
}) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8.0),
    child: Container(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
            color: value == true
                ? AppColor.themeDarkBlueColor
                : AppColor.cLightGrey),
        color: AppColor.lightBlueColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  // status == "TERMINATED" || status == "NEW"
                  //     ? SizedBox()
                  //     : isShowCheck == true
                  //         ? SizedBox(
                  //             height: 24,
                  //             width: 24,
                  //             child: Checkbox(
                  //               value: value,
                  //               onChanged: onChanged,
                  //               activeColor: AppColor.themeDarkBlueColor,
                  //               shape: RoundedRectangleBorder(
                  //                   borderRadius: BorderRadius.circular(4),
                  //                   side: BorderSide(color: AppColor.cBorder)),
                  //             ),
                  //           )
                  //         : SizedBox(),
                  // horizontalSpace(isShowCheck == true ? 8 : 0),
                  Container(
                      height: 24,
                      decoration: BoxDecoration(
                          color: status == "NEW"
                              ? AppColor.cLightGrey
                              : AppColor.cLightBlueContainer,
                          border: Border.all(
                              color: status == "NEW"
                                  ? AppColor.cLightGrey
                                  : AppColor.cLightBlueContainer),
                          borderRadius: BorderRadius.circular(4)),
                      padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                      child: Center(
                        child: Text(
                          code,
                          style: pBold12.copyWith(
                              fontSize: 13,
                              color: status == 'NEW'
                                  ? AppColor.cDarkGreyFont
                                  : AppColor.cDarkBlueText),
                        ),
                      )),
                  horizontalSpace(8),
                  statusWidget(
                      text: status,
                      textColor: textColor,
                      color: color,
                      tag: tag),
                  // if (value == true || serviceType == "D") horizontalSpace(8),
                  // if (value == true || serviceType == "D")
                  //   GestureDetector(
                  //     onTap: status == "ACTIVE" ? scanTap : null,
                  //     child: assetSvdImageWidget(
                  //         image: DefaultImages.scannerIcn,
                  //         colorFilter: ColorFilter.mode(
                  //             status == "ACTIVE"
                  //                 ? AppColor.cText
                  //                 : AppColor.cLightBlueFont,
                  //             BlendMode.srcIn)),
                  //   )
                ],
              ),
              // GestureDetector(
              //     onTap: viewMore,
              //     child:
              //         assetSvdImageWidget(image: DefaultImages.verticleMoreIcn))
            ],
          ),
          verticalSpace(8),
          title == ''
              ? SizedBox()
              : Row(
                  children: [
                    Text(
                      title!,
                      style: pRegular13,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: assetSvdImageWidget(image: DefaultImages.dotIcn),
                    ),
                    Text(
                      type!,
                      style: pRegular13,
                    ),
                  ],
                ),
          verticalSpace(18),
          Row(
            children: [
              SizedBox(
                  width: 140,
                  child: Text(
                    "Service Type".trr,
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  )),
              Expanded(
                child: Text.rich(
                  TextSpan(
                    text: serviceType == "D"
                        ? "DC"
                        : serviceType == "T"
                            ? "TAG"
                            : "SMART CARD",
                    style: pRegular13,
                  ),
                ),
              )
              // Text(
              //   driver!,
              //   style: pRegular13,
              // ),
            ],
          ),
          Row(
            children: [
              SizedBox(
                  width: 140,
                  child: Text(
                    "Driver".trr,
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  )),
              Expanded(
                child: Text.rich(
                  TextSpan(
                    text: '$driver ',
                    style: pRegular13,
                  ),
                ),
              )
              // Text(
              //   driver!,
              //   style: pRegular13,
              // ),
            ],
          ),
          verticalSpace(12),
          Row(
            children: [
              SizedBox(
                  width: 140,
                  child: Text(
                    "Quota (used/total)".trr,
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  )),
              Expanded(
                child: Text.rich(
                  TextSpan(
                    text: '$quotaTotal ',
                    style: pBold14.copyWith(fontSize: 13),
                    children: <TextSpan>[
                      TextSpan(text: quotaString, style: pRegular13),
                    ],
                  ),
                ),
              )
            ],
          ),
          verticalSpace(12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                  width: 140,
                  child: Text(
                    "Division".trr,
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  )),
              Text(
                division!,
                style: pRegular13,
              ),
            ],
          ),
          verticalSpace(12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                  width: 140,
                  child: Text(
                    serviceStatus != "T" //"Division".trr,
                        ? serviceType == "T"
                            ? "Installation Date"
                            : "Activation Date"
                        : "Terminated Date",
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  )),
              Text(
                insTermDate!,
                style: pRegular13,
              ),
            ],
          ),
          verticalSpace(12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                  width: 140,
                  child: Text(
                    serviceType == "C" //"Division".trr,
                        ? "Card No"
                        : "RFID",
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  )),
              Text(
                serialXID!,
                style: pRegular13,
              ),
            ],
          ),
          if (pinblock == "Y") verticalSpace(12),
          if (pinblock == "Y")
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                    width: 140,
                    child: Text(
                      "PINBLOCK",
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    )),
                Text(
                  pinblock == "Y" //"Division".trr,
                      ? "ON"
                      : "OFF",
                  style: pRegular13,
                ),
              ],
            ),
          if (pinblock == "Y") verticalSpace(12),
          if (pinblock == "Y")
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                    width: 140,
                    child: Text(
                      "PINBLOCK Date",
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    )),
                Text(
                  pinblockDate.toString(),
                  style: pRegular13,
                ),
              ],
            ),
        ],
      ),
    ),
  );
}

Widget buttonRowWidget({
  Function()? addVehicleFun,
  Function()? addDCVehicleFun,
  String? totalVehicle,
}) {
  return Row(
    children: [
      // Expanded(
      //   child: CommonIconButton(
      //     title: "${"Add Vehicle".trr} $totalVehicle",
      //     onPressed: addVehicleFun,
      //     iconData: DefaultImages.circleAddIcn,
      //     btnColor: AppColor.themeOrangeColor,
      //   ),
      // ),
      // horizontalSpace(10),
      Expanded(
        child: GestureDetector(
          onTap: addDCVehicleFun,
          child: Container(
            height: 44,
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: AppColor.themeDarkBlueColor),
            ),
            child: FittedBox(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      assetSvdImageWidget(image: DefaultImages.scannerIcn),
                      horizontalSpace(8),
                      Text(
                        "Add digital coupon vehicle".trr,
                        style: pRegular13.copyWith(
                          color: AppColor.cDarkBlueFont,
                        ),
                      )
                    ],
                  ),
                  // assetSvdImageWidget(
                  //   image: DefaultImages.blueArrowDownIcn,
                  //   colorFilter: ColorFilter.mode(AppColor.cDarkGreyFont, BlendMode.srcIn),
                  // ),
                ],
              ),
            ),
          ),
        ),
      )
    ],
  );
}

Widget statusWidget(
    {String? text,
    String? tag,
    Color? textColor,
    Color? color,
    double? horizontalSpace,
    double? left}) {
  return Container(
      height: 24,
      decoration: BoxDecoration(
          color: color ?? AppColor.cLightGreen,
          borderRadius: BorderRadius.circular(4)),
      padding: EdgeInsets.only(right: 8, left: left ?? 8),
      child: Row(
        children: [
          tag == null
              ? SizedBox()
              : assetSvdImageWidget(
                  image: tag,
                  colorFilter: ColorFilter.mode(
                      textColor ?? AppColor.cGreen, BlendMode.srcIn)),
          SizedBox(width: horizontalSpace ?? 5),
          Text(
            text!,
            style: pSemiBold12.copyWith(color: textColor ?? AppColor.cGreen),
          ),
        ],
      ));
}

Widget listButton(String total) {
  return Container(
    decoration: BoxDecoration(
        color: AppColor.cBackGround,
        border: Border.all(color: AppColor.cBorder),
        borderRadius: BorderRadius.circular(6)),
    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        assetSvdImageWidget(image: DefaultImages.listGridIcn),
        horizontalSpace(8),
        Container(
          padding: EdgeInsets.symmetric(vertical: 1, horizontal: 8),
          decoration: BoxDecoration(
              color: AppColor.cLightBlueContainer,
              borderRadius: BorderRadius.circular(6)),
          child: Text(
            total,
            style: pMedium12.copyWith(
              color: AppColor.cDarkBlueFont,
            ),
          ),
        )
      ],
    ),
  );
}

Widget filterButton() {
  return Container(
    decoration: BoxDecoration(
        color: AppColor.themeDarkBlueColor,
        borderRadius: BorderRadius.circular(6)),
    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        assetSvdImageWidget(image: DefaultImages.filterIcn),
        horizontalSpace(8),
        Text(
          "Filter".trr,
          style: pRegular13.copyWith(
            color: AppColor.cWhiteFont,
          ),
        )
      ],
    ),
  );
}
