// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_structure_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class AddSubElementsWidget extends StatelessWidget {
  AddSubElementsWidget({super.key});

  FleetStructureController fleetStructureController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: EdgeInsets.all(16),
        child: Obx(() {
          return Column(mainAxisSize: MainAxisSize.min, children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                    flex: 3,
                    child: Text(
                      "Assign Subcategory".trr,
                      style: pSemiBold17,
                      textAlign: TextAlign.center,
                    )),
                // Spacer(),
                GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: assetSvdImageWidget(image: DefaultImages.cancelIcn))
              ],
            ),
            verticalSpace(24),
            CommonHintDropdownWidget(
              hint: "${'Please select here'.trr}...",
              labelText: 'Subcategory type'.trr,
              list: fleetStructureController.subCategoriesList.value,
              value: fleetStructureController.selectedCategory.value,
              onChanged: (value) {
                fleetStructureController.selectedCategory.value = value;
              },
            ),
            verticalSpace(24),
            CommonTextField(
              controller: fleetStructureController.subCategoryNameController,
              labelText: 'Subcategory Name'.trr,
              hintText: 'Enter name'.trr,
            ),
            verticalSpace(24),
            Row(
              children: [
                Expanded(
                    child: CommonBorderButton(
                  title: 'Cancel'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  bColor: AppColor.themeDarkBlueColor,
                  textColor: AppColor.cDarkBlueFont,
                )),
                horizontalSpace(8),
                Expanded(
                    child: CommonButton(
                  title: 'Confirm'.trr,
                  onPressed: () {
                    Get.back();
                    fleetStructureController.fleetStructureList.value = fleetStructureController.dummyFleetStructureList;
                    fleetStructureController.fleetStructureList.refresh();
                  },
                  btnColor: AppColor.themeOrangeColor,
                ))
              ],
            ),
            verticalSpace(16),
          ]);
        }),
      ),
    );
  }
}
