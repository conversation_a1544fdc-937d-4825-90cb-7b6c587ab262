import 'dart:convert';

import 'package:get/get_rx/src/rx_types/rx_types.dart';

List<FleetStructureModel> fleetStructureModelFromJson(String str) =>
    List<FleetStructureModel>.from(
        json.decode(str).map((x) => FleetStructureModel.fromJson(x)));

String fleetStructureModelToJson(List<FleetStructureModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class FleetStructureModel {
  RxBool isSelected = false.obs;
  String typecode;
  String typedesc;
  String ishold;
  String isparenthold;
  String isdivhold;
  dynamic hasholdrefill;

  FleetStructureModel({
    required this.isSelected,
    required this.typecode,
    required this.typedesc,
    required this.ishold,
    required this.isparenthold,
    required this.isdivhold,
    required this.hasholdrefill,
  });

  factory FleetStructureModel.fromJson(Map<String, dynamic> json) =>
      FleetStructureModel(
        isSelected: json["isSelected"] ?? false.obs,
        typecode: json["TYPECODE"] ?? "",
        typedesc: json["TYPEDESC"] ?? "",
        ishold: json["ISHOLD"] ?? "",
        isparenthold: json["ISPARENTHOLD"] ?? "",
        isdivhold: json["ISDIVHOLD"] ?? "",
        hasholdrefill: json["HASHOLDREFILL"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "isSelected": isSelected,
        "TYPECODE": typecode,
        "TYPEDESC": typedesc,
        "ISHOLD": ishold,
        "ISPARENTHOLD": isparenthold,
        "ISDIVHOLD": isdivhold,
        "HASHOLDREFILL": hasholdrefill,
      };
}
