import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class DownloadDocumentsScreen extends StatelessWidget {
  const DownloadDocumentsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Download Documents".trr, style: pSemiBold17),
        verticalSpace(24),
        downloadDataWidget("WAIE Manual".trr),
        horizontalDivider(),
        downloadDataWidget("Steps for Electronic Transfer".trr),   horizontalDivider(),
        downloadDataWidget("Fuel Stations with Point of Sale (POS)".trr),   horizontalDivider(),
        downloadDataWidget("WAIE Fleet Password".trr),   horizontalDivider(),
      ],
    );
  }

  Padding downloadDataWidget(String string) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16,horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(string, style: pSemiBold17),
            assetSvdImageWidget(image: DefaultImages.downloadIcn)
          ],
        ),
      );
  }
}
