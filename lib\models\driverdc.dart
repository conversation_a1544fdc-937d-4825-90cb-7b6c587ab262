class Driverdc {
  Driverdc({
    required this.custid,
    required this.guid,
    required this.mobileno,
    required this.plateno,
    required this.platenos,
    required this.fueltype,
    required this.remquotavalue,
    required this.quotavalue,
    required this.rsvQuota,
    required this.rsvQty,
    required this.remquotavalue2,
    required this.quotavalue2,
    required this.rsvQuota2,
    required this.dcStations,
    required this.waieStations,
    required this.accttype,
    required this.rsvType,
    required this.quotaclass,
    required this.valid,
    required this.deviceid,
    required this.prodCode,
    required this.serviceType,
    required this.subserviceType,
    required this.companyName,
    required this.qty,
    required this.price,
    required this.serviceCode,
    required this.qtyType,
    required this.itemList,
    required this.invDetails,
    required this.discrip,
    required this.dValue,
    required this.mertImage,
    required this.otp,
    required this.secCode,
    required this.trxid,
    required this.itemcode,
    required this.unitprice,
    required this.custBal,
    required this.tokenid,
    required this.serialid,
    required this.stationInfoList,
    required this.fillDayStatus,
    required this.stnNo,
    required this.type,
    required this.servicepw,
    required this.docno,
    required this.password,
    required this.dcDeviceid,
  });

  final String custid;
  final dynamic guid;
  final String mobileno;
  final dynamic plateno;
  final List<Plateno> platenos;
  final dynamic fueltype;
  final dynamic remquotavalue;
  final dynamic quotavalue;
  final dynamic rsvQuota;
  final dynamic rsvQty;
  final dynamic remquotavalue2;
  final dynamic quotavalue2;
  final dynamic rsvQuota2;
  final dynamic dcStations;
  final dynamic waieStations;
  final dynamic accttype;
  final dynamic rsvType;
  final dynamic quotaclass;
  final String valid;
  final dynamic deviceid;
  final dynamic prodCode;
  final dynamic serviceType;
  final dynamic subserviceType;
  final dynamic companyName;
  final dynamic qty;
  final dynamic price;
  final dynamic serviceCode;
  final dynamic qtyType;
  final dynamic itemList;
  final dynamic invDetails;
  final dynamic discrip;
  final dynamic dValue;
  final dynamic mertImage;
  final String otp;
  final dynamic secCode;
  final dynamic trxid;
  final dynamic itemcode;
  final dynamic unitprice;
  final num custBal;
  final dynamic tokenid;
  final String serialid;
  final dynamic stationInfoList;
  final dynamic fillDayStatus;
  final dynamic stnNo;
  final dynamic type;
  final dynamic servicepw;
  final dynamic docno;
  final dynamic password;
  final dynamic dcDeviceid;

  factory Driverdc.fromJson(Map<String, dynamic> json){
    return Driverdc(
      custid: json["CUSTID"] ?? "",
      guid: json["GUID"],
      mobileno: json["MOBILENO"] ?? "",
      plateno: json["PLATENO"] ?? "",
      platenos: json["PLATENOS"] == null ? [] : List<Plateno>.from(json["PLATENOS"]!.map((x) => Plateno.fromJson(x))),
      fueltype: json["FUELTYPE"] ?? "",
      remquotavalue: json["REMQUOTAVALUE"]?? "",
      quotavalue: json["QUOTAVALUE"]?? "",
      rsvQuota: json["RSV_QUOTA"]?? "",
      rsvQty: json["RSV_QTY"]?? "",
      remquotavalue2: json["REMQUOTAVALUE2"]?? "",
      quotavalue2: json["QUOTAVALUE2"]?? "",
      rsvQuota2: json["RSV_QUOTA2"]?? "",
      dcStations: json["DC_STATIONS"]?? "",
      waieStations: json["WAIE_STATIONS"]?? "",
      accttype: json["ACCTTYPE"]?? "",
      rsvType: json["RSV_TYPE"]?? "",
      quotaclass: json["QUOTACLASS"]?? "",
      valid: json["VALID"] ?? ""?? "",
      deviceid: json["DEVICEID"]?? "",
      prodCode: json["PROD_CODE"]?? "",
      serviceType: json["SERVICE_TYPE"]?? "",
      subserviceType: json["SUBSERVICE_TYPE"]?? "",
      companyName: json["COMPANY_NAME"]?? "",
      qty: json["QTY"]?? "",
      price: json["PRICE"]?? "",
      serviceCode: json["SERVICE_CODE"]?? "",
      qtyType: json["QTY_TYPE"]?? "",
      itemList: json["ItemList"]?? "",
      invDetails: json["InvDetails"]?? "",
      discrip: json["DISCRIP"]?? "",
      dValue: json["D_VALUE"]?? "",
      mertImage: json["MERT_IMAGE"]?? "",
      otp: json["OTP"] ?? ""?? "",
      secCode: json["SEC_CODE"]?? "",
      trxid: json["TRXID"]?? "",
      itemcode: json["ITEMCODE"]?? "",
      unitprice: json["UNITPRICE"]?? "",
      custBal: json["CUST_BAL"] ?? 0?? "",
      tokenid: json["TOKENID"]?? "",
      serialid: json["SERIALID"] ?? ""?? "",
      stationInfoList: json["StationInfoList"]?? "",
      fillDayStatus: json["FILL_DAY_STATUS"]?? "",
      stnNo: json["STN_NO"]?? "",
      type: json["TYPE"]?? "",
      servicepw: json["SERVICEPW"]?? "",
      docno: json["DOCNO"]?? "",
      password: json["PASSWORD"]?? "",
      dcDeviceid: json["DC_DEVICEID"]?? "",
    );
  }

  Map<String, dynamic> toJson() => {
    "CUSTID": custid,
    "GUID": guid,
    "MOBILENO": mobileno,
    "PLATENO": plateno,
    "PLATENOS": platenos.map((x) => x?.toJson()).toList(),
    "FUELTYPE": fueltype,
    "REMQUOTAVALUE": remquotavalue,
    "QUOTAVALUE": quotavalue,
    "RSV_QUOTA": rsvQuota,
    "RSV_QTY": rsvQty,
    "REMQUOTAVALUE2": remquotavalue2,
    "QUOTAVALUE2": quotavalue2,
    "RSV_QUOTA2": rsvQuota2,
    "DC_STATIONS": dcStations,
    "WAIE_STATIONS": waieStations,
    "ACCTTYPE": accttype,
    "RSV_TYPE": rsvType,
    "QUOTACLASS": quotaclass,
    "VALID": valid,
    "DEVICEID": deviceid,
    "PROD_CODE": prodCode,
    "SERVICE_TYPE": serviceType,
    "SUBSERVICE_TYPE": subserviceType,
    "COMPANY_NAME": companyName,
    "QTY": qty,
    "PRICE": price,
    "SERVICE_CODE": serviceCode,
    "QTY_TYPE": qtyType,
    "ItemList": itemList,
    "InvDetails": invDetails,
    "DISCRIP": discrip,
    "D_VALUE": dValue,
    "MERT_IMAGE": mertImage,
    "OTP": otp,
    "SEC_CODE": secCode,
    "TRXID": trxid,
    "ITEMCODE": itemcode,
    "UNITPRICE": unitprice,
    "CUST_BAL": custBal,
    "TOKENID": tokenid,
    "SERIALID": serialid,
    "StationInfoList": stationInfoList,
    "FILL_DAY_STATUS": fillDayStatus,
    "STN_NO": stnNo,
    "TYPE": type,
    "SERVICEPW": servicepw,
    "DOCNO": docno,
    "PASSWORD": password,
    "DC_DEVICEID": dcDeviceid,
  };

}

class Plateno {
  Plateno({
    required this.plateNo,
    required this.serialid,
  });

  final String plateNo;
  final String serialid;

  factory Plateno.fromJson(Map<String, dynamic> json){
    return Plateno(
      plateNo: json["PLATE_NO"] ?? "",
      serialid: json["SERIALID"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
    "PLATE_NO": plateNo,
    "SERIALID": serialid,
  };

}
