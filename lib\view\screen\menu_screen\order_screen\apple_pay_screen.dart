import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:pay/pay.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/payment_config.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import '../../../../utils/custom_icons.dart' as CustomIcon;

import '../../../../core/controller/menu_controller/order_controller/confirm_order_controller.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/text_style.dart';
import '../../../widget/common_space_divider_widget.dart';
import '../../../widget/loading_widget.dart';
import '../../dashboard_manager/dashboard_manager.dart';

class ApplePayScreen extends StatefulWidget {
  final String unitPrice;
  final String totalPurchased;
  final String sessionID;
  final String orderID;
  final String depositto;
  final String serviceType;
  final String qty;
  final String orderType;
  final String custid;
  final String subTotal;
  final String vat;
  const ApplePayScreen({
    super.key,
    required this.unitPrice,
    required this.totalPurchased,
    required this.sessionID,
    required this.orderID,
    required this.depositto,
    required this.serviceType,
    required this.qty,
    required this.orderType,
    required this.custid,
    required this.subTotal,
    required this.vat,
  });

  @override
  State<ApplePayScreen> createState() => _ApplePayScreenState();
}

class _ApplePayScreenState extends State<ApplePayScreen> {
  final ConfirmOrderController confirmOrderController =
      Get.put(ConfirmOrderController());
  String os = Platform.operatingSystem;

  // var applePayButton = ApplePayButton(
  //   paymentConfiguration: PaymentConfiguration.fromJsonString(defaultApplePay),
  //   paymentItems: const [
  //     PaymentItem(
  //       label: 'Item A',
  //       amount: '0.01',
  //       status: PaymentItemStatus.final_price,
  //     ),
  //     PaymentItem(
  //       label: 'Item B',
  //       amount: '0.01',
  //       status: PaymentItemStatus.final_price,
  //     ),
  //     PaymentItem(
  //       label: 'Total',
  //       amount: '0.02',
  //       status: PaymentItemStatus.final_price,
  //     )
  //   ],
  //   style: ApplePayButtonStyle.black,
  //   width: double.infinity,
  //   height: 50,
  //   type: ApplePayButtonType.buy,
  //   margin: const EdgeInsets.only(top: 15.0),
  //   onPaymentResult: (result) => debugPrint('Payment Result $result'),
  //   loadingIndicator: const Center(
  //     child: CircularProgressIndicator(),
  //   ),
  // );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              verticalSpace(25),
              simpleMyAppBar(
                  //title: "Payment".trr,
                  title: "",
                  onTap: () {
                    Get.offAll(DashBoardManagerScreen(
                      currantIndex: 0,
                    ));
                  },
                  backString: "Cancel".trr,
                  horizontalSize: 45),
              verticalSpace(16),
              Center(
                child: SizedBox(
                  width: 100,
                  child: Image.asset('asset/image/image/logotransparent.png'),
                ),
              ),
              verticalSpace(16),
              Container(
                padding: const EdgeInsets.only(left: 16, right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Align(
                          alignment: Alignment.center,
                          child: Center(
                              child: Text(
                            "ALDREES Petroleum & Transport Services",
                            style: pBold18,
                          ))),
                    ),
                  ],
                ),
              ),
              verticalSpace(25),
              Container(
                decoration: BoxDecoration(
                    color: AppColor.cLightGrey,
                    borderRadius: BorderRadius.circular(6)),
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        "Cart Summary".trr,
                        style: pSemiBold14.copyWith(
                          color: AppColor.cBlackFont,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              verticalSpace(8),
              Container(
                decoration: BoxDecoration(
                    color: AppColor.cLightGrey,
                    borderRadius: BorderRadius.circular(6)),
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Image.asset(
                            widget.serviceType == "T"
                                ? DefaultImages.tagImg
                                : DefaultImages.smartCardImg,
                            height: 120,
                            width: Get.width,
                          ),
                        ),
                        horizontalSpace(10),
                        Expanded(
                          child: Text(
                              widget.serviceType == "T"
                                  ? "Applies only to specified cars, installed in the fuel tank and fuel-up by RFID."
                                      .trr
                                  : "No more bills or cash, means faster transaction."
                                      .trr,
                              style: pRegular13.copyWith(
                                  color: AppColor.cDarkGreyFont)),
                        ),
                      ],
                    ),
                    verticalSpace(8),
                    totalsDataWidget(
                        title: "Order".trr,
                        value:
                            widget.serviceType == "C" ? "Smart Card" : "Tag"),
                    verticalSpace(8),
                    totalsDataWidget(
                        title: "Unit Price".trr,
                        value: widget.unitPrice), // + " SAR".trr
                    verticalSpace(8),
                    totalsDataWidget(title: "Quantity".trr, value: widget.qty),
                    verticalSpace(8),
                    // totalsDataWidget(title: "SubTotal".trr, value: subTotal!),
                    totalsDataWidget(
                        title: "Total w/o VAT".trr,
                        value: widget.subTotal), // + " SAR".trr
                    verticalSpace(8),
                    totalsDataWidget(
                        title: "VAT".trr, value: widget.vat), // + " SAR".trr
                    verticalSpace(8),
                    totalsDataWidget(
                        title: "Purchase total".trr,
                        value: widget.totalPurchased), // + " SAR".trr
                  ],
                ),
              ),
              verticalSpace(10),
              Center(
                  child: Text(
                "${'We keep your order'.trr} #${widget.orderID}",
                style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
              )),
              // Expanded(
              //   child: ApplePayButton(
              //     paymentConfiguration:
              //         PaymentConfiguration.fromJsonString(defaultApplePay),
              //     paymentItems: [
              //       PaymentItem(
              //         label: widget.orderID,
              //         amount: widget.totalPurchased,
              //         status: PaymentItemStatus.final_price,
              //       ),
              //       PaymentItem(
              //         label: 'Total',
              //         amount: widget.totalPurchased,
              //         status: PaymentItemStatus.final_price,
              //       )
              //     ],
              //     style: ApplePayButtonStyle.black,
              //     width: double.infinity,
              //     height: 50,
              //     type: ApplePayButtonType.buy,
              //     margin: const EdgeInsets.only(top: 15.0),
              //     onPaymentResult: (result) =>
              //         debugPrint('Payment Result $result'),
              //     loadingIndicator: const Center(
              //       child: CircularProgressIndicator(),
              //     ),
              //   ),
              // ),
              verticalSpace(10),
              Center(
                child: ApplePayButton(
                  paymentConfiguration: Constants
                              .IsAlrajhiApplePaySwitchEnable ==
                          "Y"
                      ? PaymentConfiguration.fromJsonString(
                          defaultAlrajhiApplePay)
                      : PaymentConfiguration.fromJsonString(defaultApplePay),
                  paymentItems: [
                    PaymentItem(
                      label: 'Order Amount',
                      amount: widget.totalPurchased,
                      status: PaymentItemStatus.final_price,
                    ),
                    PaymentItem(
                      label: 'Total w/o VAT',
                      amount: widget.subTotal,
                      status: PaymentItemStatus.final_price,
                    ),
                    PaymentItem(
                      label: 'VAT',
                      amount: widget.vat,
                      status: PaymentItemStatus.final_price,
                    ),
                    PaymentItem(
                      label: 'ALDREES Petroleum & Transport Services',
                      amount: widget.totalPurchased,
                      status: PaymentItemStatus.final_price,
                      type: PaymentItemType.total,
                    )
                  ],
                  style: ApplePayButtonStyle.whiteOutline,
                  width: RawApplePayButton.minimumButtonWidth,
                  height: RawApplePayButton.minimumButtonHeight,
                  type: ApplePayButtonType.plain,
                  //margin: const EdgeInsets.only(top: 15.0),
                  onPaymentResult: (result) {
                    debugPrint('Payment Result $result');
                    if (result['token'] != null) {
                      if (Constants.IsAlrajhiApplePaySwitchEnable == "Y") {
                        confirmOrderController.sendAlraljhiApplePay(
                          result['token'],
                          result['paymentMethod'],
                          result['transactionIdentifier'],
                          widget.totalPurchased,
                          widget.orderID,
                          widget.serviceType,
                          widget.qty,
                          widget.orderType,
                          widget.custid,
                          widget.unitPrice,
                          widget.vat,
                        );
                      } else {
                        confirmOrderController.updateSession(
                          result['token'],
                          widget.sessionID,
                          widget.orderID,
                          widget.totalPurchased,
                          widget.depositto,
                          widget.serviceType,
                          widget.qty,
                          widget.orderType,
                          widget.custid,
                          widget.subTotal,
                          widget.vat,
                        );
                      }
                    } else {
                      commonToast("cannot pay with apple");
                    }
                  },
                  loadingIndicator: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              ),
              // const Row(
              //   children: [
              //     // Expanded(
              //     //     flex: 2,
              //     //     child: applePayButton(
              //     //       title: 'Pay with '.trr,
              //     //       onPressed: madaPayment,
              //     //       btnColor: Colors.black,
              //     //       horizontalPadding: 16,
              //     //     )),
              //     // ApplePayButton(
              //     //   paymentConfiguration:
              //     //       PaymentConfiguration.fromJsonString(defaultApplePay),
              //     //   paymentItems: [
              //     //     PaymentItem(
              //     //       label: 'Order Amount',
              //     //       amount: widget.totalPurchased,
              //     //       status: PaymentItemStatus.final_price,
              //     //     ),
              //     //     PaymentItem(
              //     //       label: 'Total w/o VAT',
              //     //       amount: widget.subTotal,
              //     //       status: PaymentItemStatus.final_price,
              //     //     ),
              //     //     PaymentItem(
              //     //       label: 'VAT',
              //     //       amount: widget.vat,
              //     //       status: PaymentItemStatus.final_price,
              //     //     ),
              //     //     PaymentItem(
              //     //       label: 'ALDREES Petroleum & Transport Services',
              //     //       amount: widget.totalPurchased,
              //     //       status: PaymentItemStatus.final_price,
              //     //       type: PaymentItemType.total,
              //     //     )
              //     //   ],
              //     //   style: ApplePayButtonStyle.whiteOutline,
              //     //   width: RawApplePayButton.minimumButtonWidth,
              //     //   height: RawApplePayButton.minimumButtonHeight,
              //     //   type: ApplePayButtonType.order,
              //     //   //margin: const EdgeInsets.only(top: 15.0),
              //     //   onPaymentResult: (result) {
              //     //     debugPrint('Payment Result $result');
              //     //     if (result['token'] != null) {
              //     //       confirmOrderController.updateSession(
              //     //         result['token'],
              //     //         widget.sessionID,
              //     //         widget.orderID,
              //     //         widget.totalPurchased,
              //     //         widget.depositto,
              //     //         widget.serviceType,
              //     //         widget.qty,
              //     //         widget.orderType,
              //     //         widget.custid,
              //     //         widget.subTotal,
              //     //         widget.vat,
              //     //       );
              //     //     } else {
              //     //       commonToast("cannot pay with apple");
              //     //     }
              //     //   },
              //     //   loadingIndicator: const Center(
              //     //     child: CircularProgressIndicator(),
              //     //   ),
              //     // ),
              //     //     horizontalSpace(20),
              //     //     Expanded(
              //     //         flex: 1,
              //     //         child: commonBtn(
              //     //           title: "Cancel".trr,
              //     //           onPressed: () {
              //     //             Get.offAll(DashBoardManagerScreen(
              //     //               currantIndex: 0,
              //     //             ));
              //     //           },
              //     //           btnColor: Colors.black,
              //     //           horizontalPadding: 16,
              //     //         ))
              //   ],
              // ),
              // verticalSpace(25), //USE THIS FOR CUSTOMISING YOUR BUTTON
              // Container(
              //   height: 30.0,
              //   width: double.infinity,
              //   decoration: const BoxDecoration(
              //       borderRadius: BorderRadius.all(Radius.circular(8.0)),
              //       color: Colors.black),
              //   child: GestureDetector(
              //     onTap: () {
              //       //ADD THE FUNCTIONS OF THIS BUTTON HERE
              //     },
              //     child: const Row(
              //       mainAxisAlignment: MainAxisAlignment.center,
              //       children: [
              //         Text(
              //           'Pay with ',
              //           style: TextStyle(color: Colors.white),
              //         ),
              //         SizedBox(
              //           width: 8.0,
              //         ),
              //         Icon(
              //           CustomIcon.Custom.apple_pay,
              //           color: Colors.white,
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget commonBtn({
    final String? title,
    final double? height,
    final double? width,
    final double? fontSize,
    final double? horizontalPadding,
    final Function()? onPressed,
    final Color? bColor,
    final Color? btnColor,
    final Color? textColor,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: width ?? Get.width,
        height: height ?? 50,
        decoration: BoxDecoration(
            color: btnColor ?? AppColor.themeBlueColor,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: bColor ?? AppColor.cTransparent)),
        padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding ?? Get.width * 0.1),
        child: Center(
          child: Text(
            title!,
            style: pRegular16.copyWith(
                color: textColor ?? AppColor.cWhite, fontSize: fontSize ?? 13),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

Widget totalsDataWidget(
    {required String title,
    required String value,
    double? fontSize,
    Color? fontColor}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(
        title,
        style: pRegular13.copyWith(
            fontSize: fontSize ?? 13, color: fontColor ?? AppColor.cText),
      ),
      Directionality(
        textDirection: TextDirection.ltr,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            assetSvdImageWidget(
              image: DefaultImages.saudiRiyal,
              width: fontSize == 14
                  ? 13
                  : fontSize == 17
                      ? 16
                      : 13,
              height: fontSize == 14
                  ? 13
                  : fontSize == 17
                      ? 16
                      : 13,
            ),
            Gap(4),
            Text(
              value,
              // double.parse(value).toStringAsFixed(2),
              style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
            ),
          ],
        ),
      ),
      // Text(
      //   value,
      //   // double.parse(value).toStringAsFixed(2),
      //   style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
      // ),
    ],
  );
}
