import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/models/loadplaces.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../../view/widget/loading_widget.dart';

class NewFleetController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  GetStorage userStorage = GetStorage('User');
  final fleetList = [].obs;
  RxList fleetLists = <FleetModel>[].obs;
  List<LoadPlaces> loadPlaces = [];
  RxList placeList = [].obs;
  RxBool chckbox = false.obs;

  RxInt counter = 0.obs;
  ScrollController scrollController = ScrollController();
  int currentMax = 10;

  void increment() {
    counter++;
    update(['aVeryUniqueID']);
    print(counter); // and then here
  }

  @override
  void onInit() {
    // called immediately after the widget is allocated memory
    super.onInit();
    fetch2();
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent * 0.8) {
        fetch1();
      }
    });
  }

  // @override
  // void onReady() {
  //   // called after the widget is rendered on screen
  //   fetch2();
  //   super.onReady();
  // }

  void fetch2() async {
    //Loader.showLoader();
    fleetLists.clear();
    fleetList.clear();
    counter.value = 0;
    var client = http.Client();
    var custData = custsData.read('custData');

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map body = {
      "SEARCHBY": ",,,,,,,,,,,,PL",
      "TOP": currentMax,
      "SKIP": "0",
      "CUSTID": "000000054A", //"000003944",
      "SERIALID": ""
    };
    var response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
        body: jsonEncode(body),
        headers: {
          'authorization': basicAuth,
          "Content-Type": "application/json",
        });
    print(jsonDecode(response.body));

    print("response body===> ${response.body}");
    List<dynamic> result = jsonDecode(response.body);
    print("statusCode===> ${response.statusCode}");
    print("response body===> ${response.body}");

    for (int i = 0; i < result.length; i++) {
      FleetModel structures =
          FleetModel.fromMap(result[i] as Map<String, dynamic>);
      fleetLists.add(structures);
    }
    print("===============================================================");

    print("response fleetLists===> ${fleetLists.length}");
    fleetList.value = fleetLists;
    print("response fleetList===> ${fleetList.length}");
    counter.value = fleetLists.length;
    update(['aVeryUniqueID']);
    // if (response.statusCode == 200) {
    //   Loader.hideLoader();
    //   print(response.statusCode);
    //   print(jsonDecode(response.body));

    //   final Map<String, dynamic> result = jsonDecode(response.body);

    //   print(result["SERVICETYPE"]);

    //   topUpAmount.value = result["TOPUPAMT"];
    //   unitPrice.value = result["UNITPRICE"];
    //   servicetype.value = result["SERVICETYPE"];
    //   vatAmount.value = result["VATAMT"];
    //   amount.value = result["AMT"];
    //   totalAmount.value = result["TOTAMT"];
    //   subTotal.value = result["SUBTOTAL"];
    //   quantty.value = result["QUANTITY"];
    // }

    // for (int i = 0; i < result.length; i++) {
    //   PriceTagCardModel price =
    //       PriceTagCardModel.fromMap(result[i] as Map<String, dynamic>);
    //   prices.add(price);
    //   update(['isView']);
    // }
    //priceTagCards.value = prices;

    //return prices;
  }

  void fetch1() async {
    //Loader.showLoader();
    fleetLists.clear();
    fleetList.clear();
    counter.value = 0;
    var client = http.Client();
    var custData = custsData.read('custData');

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map body = {
      "SEARCHBY": "123,,,,,,,,,,,,PL",
      "TOP": currentMax,
      "SKIP": "0",
      "CUSTID": "000000054A", //"000003944",
      "SERIALID": ""
    };
    var response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
        body: jsonEncode(body),
        headers: {
          'authorization': basicAuth,
          "Content-Type": "application/json",
        });
    print(jsonDecode(response.body));

    print("response body===> ${response.body}");
    List<dynamic> result = jsonDecode(response.body);
    print("statusCode===> ${response.statusCode}");
    print("response body===> ${response.body}");

    for (int i = 0; i < result.length; i++) {
      FleetModel structures =
          FleetModel.fromMap(result[i] as Map<String, dynamic>);
      fleetLists.add(structures);
    }
    print("===============================================================");

    print("response fleetLists===> ${fleetLists.length}");
    fleetList.value = fleetLists;
    print("response fleetList===> ${fleetList.length}");
    counter.value = fleetLists.length;
    update(['aVeryUniqueID']);
    currentMax = currentMax + 10;

    //return fleetList;
    // if (response.statusCode == 200) {
    //   Loader.hideLoader();
    //   print(response.statusCode);
    //   print(jsonDecode(response.body));

    //   final Map<String, dynamic> result = jsonDecode(response.body);

    //   print(result["SERVICETYPE"]);

    //   topUpAmount.value = result["TOPUPAMT"];
    //   unitPrice.value = result["UNITPRICE"];
    //   servicetype.value = result["SERVICETYPE"];
    //   vatAmount.value = result["VATAMT"];
    //   amount.value = result["AMT"];
    //   totalAmount.value = result["TOTAMT"];
    //   subTotal.value = result["SUBTOTAL"];
    //   quantty.value = result["QUANTITY"];
    // }

    // for (int i = 0; i < result.length; i++) {
    //   PriceTagCardModel price =
    //       PriceTagCardModel.fromMap(result[i] as Map<String, dynamic>);
    //   prices.add(price);
    //   update(['isView']);
    // }
    //priceTagCards.value = prices;

    //return prices;
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }

//   // @override
//   // void onInit() async {
//   //   super.onInit();
//   //   print('FleetController');
//   //   await getDatas();
//   // }

//   getDatas() async {
//     await fetchFleets();
//     print("fleetList ${jsonDecode(jsonEncode(fleetList))}");
//     if (fleetList.isNotEmpty) {
//       await getLoadPlaces();
//       print("loadPlaces ${jsonDecode(jsonEncode(loadPlaces))}");
//     }
//   }

//   // void fetchFleets() async {
//   //   var fleets = await RemoteServices.fetchFleets();
//   //   print("MODEL fleets ====> $fleets");
//   //   print("MODEL pasok");
//   //   print("MODEL COUNT ===> $fleets");
//   //   print("RETURNED fleetList ===> ${fleetList.length}");

//   //   if (fleets != null) {
//   //     fleetList.value = fleets;
//   //   }
//   // }

//   Future<List<FleetModel>> fetchFleets() async {
//     var client = http.Client();
//     List<FleetModel> fleets = [];
//     var custid = userStorage.read('custid');
//     var emailid = userStorage.read('emailid');
//     print("custid>>>>>>> $custid");
//     print("emailid>>>>>>> $emailid");

//     try {
//       String username = 'aldrees';
//       String password = 'testpass';
//       String basicAuth =
//           'Basic ${base64Encode(utf8.encode('$username:$password'))}';
//       Map body = {
//         "SEARCHBY": "",
//         "TOP": "250",
//         "SKIP": "2",
//         "CUSTID": custid, //"000003944",
//         "SERIALID": ""
//       };
//       var response = await client.post(
//           Uri.parse(
//               ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
//           body: jsonEncode(body),
//           headers: {
//             'authorization': basicAuth,
//             "Content-Type": "application/json",
//           });

//       print("response body===> ${response.body}");
//       List<dynamic> result = jsonDecode(response.body);
//       print("statusCode===> ${response.statusCode}");
//       print("response body===> ${response.body}");
//       for (int i = 0; i < result.length; i++) {
//         print("i===> $i --- ${result.length}");
//       }

//       for (int i = 0; i < result.length; i++) {
//         try {
//           FleetModel fleet =
//               FleetModel.fromMap(result[i] as Map<String, dynamic>);
//           fleets.add(fleet);
//         } catch (e) {
//           print("e.toString()===> ${e.toString()}");
//         }
//       }
//       print("response fleets===> $fleets");
//       fleetList.value = fleets;

//       print("response fleetList===> ${fleetList.length}");

//       return fleetList;
//     } catch (e) {
//       log(e.toString());
//       return [];
//     } finally {
//       // Then finally destroy the client.
//       client.close();
//     }
//   }

//   Future<List<LoadPlaces>> getLoadPlaces() async {
//     var custData = custsData.read('custData');
//     print("custid>>>>>>> $custData['CUSTID']");
//     print("emailid>>>>>>> $custData['EMAILID']");
//     var client = http.Client();
//     try {
//       var response = await client.post(
//           Uri.parse(
//               ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadPlaces),
// //          body: {"userId": "000037345","ACCTTYPE" : "C"});
//           body: {
//             "CUSTID": custData['CUSTID'],
//             "OTHERSTN": custData['OTHERSTN'],
//             "StationType": "W",
//             "fuelType": "PE910",
//             "IsAR": Constants.IsAr_App,
//           });
//       List result = jsonDecode(response.body);

//       print("resultresultresultresult >>>>> $result");
//       print("===============================================================");
//       print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
//       print("===============================================================");
//       print(
//           "jsonDecode(response.body)['PlaceName'] >>>>> ${jsonDecode(response.body)[0]['PlaceName']}");
//       print(
//           "jsonDecode(response.body)['PlaceName'] >>>>> ${jsonDecode(response.body)[0]['Stations'][0]}");
//       print("===============================================================");

//       for (int i = 0; i < result.length; i++) {
//         LoadPlaces place =
//             LoadPlaces.fromJson(result[i] as Map<String, dynamic>);
//         loadPlaces.add(place);
//       }
//       print("===============================================================");
//       print(
//           "loadPlacesloadPlacesloadPlacesloadPlaces >>>>> ${jsonDecode(jsonEncode(loadPlaces))}");

//       return loadPlaces;
//     } catch (e) {
//       log(e.toString());
//       return [];
//     } finally {
//       // Then finally destroy the client.
//       client.close();
//     }
//   }
}
