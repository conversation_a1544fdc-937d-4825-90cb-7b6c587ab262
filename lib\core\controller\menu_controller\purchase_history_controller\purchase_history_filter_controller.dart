import 'package:get/get.dart';

class PurchaseHistoryFilterController extends GetxController{
  RxList filterValueList = [
    {
      'title': 'Date',
      "isExpand": false.obs,
      "subtitle": ["Date1", 'Date2'],
    }.obs,
    {
      'title': 'Order type',
      "isExpand": false.obs,
      "subtitle": ["Pending", 'Claimed',],
    }.obs,
    {
      'title': 'Payment method',
      "isExpand": false.obs,
      "subtitle": ["MADA", 'STC', 'Card'],
    }.obs,
    {
      'title': 'Quantity',
      "isExpand": false.obs,
      "subtitle": ["5", '10', '15'],
    }.obs,
    {
      'title': 'Total value',
      "isExpand": false.obs,
      "subtitle": ["500.00", '100.00', '200.00'],
    }.obs,
  ].obs;
  RxList balanceFilterValueList = [
    {
      'title': 'Date',
      "isExpand": false.obs,
      "subtitle": ["Date1", 'Date2'],
    }.obs,
    {
      'title': 'Payment method',
      "isExpand": false.obs,
      "subtitle": ["MADA", 'STC', 'Card'],
    }.obs,
    {
      'title': 'Amount',
      "isExpand": false.obs,
      "subtitle": ["500.00", '100.00', '200.00'],
    }.obs,
    {
      'title': 'Status',
      "isExpand": false.obs,
      "subtitle": ["Pending", 'Claimed',],
    }.obs,
  ].obs;
}