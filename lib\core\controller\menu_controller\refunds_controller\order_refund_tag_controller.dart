// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../../view/widget/common_snak_bar_widget.dart';

class OrderRefundTagController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  var refundableTagServices = <RefundableService>[].obs;
  double totaltAmt = 0.0;
  double totaltVAT = 0.0;
  Future<List<RefundableService>> getRefundableTagService() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = jsonEncode(custsData.read('custData'));
    print("OrderRefundTagController custid>>>>>>> $custid");
    print("OrderRefundTagController emailid>>>>>>> $emailid");
    var client = http.Client();
    try {
      if (refundableTagServices.isEmpty) {
        var response = await client.post(
            Uri.parse(ApiEndPoints.baseUrl +
                ApiEndPoints.authEndpoints.getAllRefundablesService),
            body: {
              "custid": custid,
              "stype": "T",
            });
        List result = jsonDecode(response.body);
        print("OrderRefundTagController response >>>>> $response");
        print("OrderRefundTagController STATUS >>>>> ${response.statusCode}");

        print("OrderRefundTagController result >>>>> $result");
        print("OrderRefundTagController COUNT >>>>> ${result.length}");
        print(
            "===============================================================");
        print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
        print(
            "===============================================================");

        if (result.isEmpty) {
          commonToast("No Data Found, Please Check Internet Connection.");
        } else {
          for (int i = 0; i < result.length; i++) {
            RefundableService order =
                RefundableService.fromJson(result[i] as Map<String, dynamic>);
            refundableTagServices.add(order);
          }
        }
        print(
            "===============================================================");
        print(
            "OrderRefundTagController >>>>> ${jsonDecode(jsonEncode(refundableTagServices))}");
        print(
            "===============================================================");

        return refundableTagServices;
      }
      print("ERROR: NO DATA");
      return [];
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('OrderRefundTagController');
    print(jsonDecode(jsonEncode(refundableTagServices)));
    if (refundableTagServices.isEmpty) {
      print("sulod");
      await getRefundableTagService();
    }
    //Navigator.of(Get.context!).pop();
  }
}
