import 'dart:convert';

List<FleetModel> fleetModelFromJson(String str) =>
    List<FleetModel>.from(json.decode(str).map((x) => FleetModel.fromJson(x)));

String fleetModelToJson(List<FleetModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class FleetModel {
  int line;
  String custid;
  String serialid;
  String serialcode;
  String servicetype;
  String servicestatus;
  String servicestatusDisp;
  dynamic servicepw;
  String passwordDisp;
  dynamic pwflag;
  dynamic serviceactivedate;
  dynamic plateno;
  dynamic driver;
  dynamic fueltype;
  String quotatype;
  String iSDC;
  bool isDC;
  dynamic quotaplanid;
  double quotavalue;
  dynamic quotabal;
  dynamic dcMobileno;
  double dcQuotavalue;
  dynamic dcStations;
  double dcQuotarem;
  double dcQuotares;
  double dcQuotavalue2;
  double dcQuotarem2;
  double dcQuotares2;
  dynamic dcType;
  String fillingdays;
  String fillingdaysDisp;
  String reforderid;
  String refjobid;
  dynamic instby;
  dynamic instdate;
  dynamic instremarks;
  String division;
  String divisionname;
  String branch;
  String branchname;
  String deptno;
  String deptname;
  String operation;
  String operationname;
  String quotaclass;
  String quotaclassDisp;
  String isoffline;
  String isofflineDisp;
  double offlinelimit;
  dynamic replacement;
  dynamic stations;
  dynamic pendingplate;
  double scamt;
  String tankNo;
  dynamic transferdate;
  dynamic uniquefield;
  dynamic vehicleid;
  dynamic complaintjobid;
  String stationPush;
  dynamic refundreqid;
  double unliValue;
  String districts;
  dynamic holdingid;
  String pinblock;
  String vehlicType;
  String vehlicTypeDisp;
  dynamic pinblockDate;
  String vehicletype;
  String vehicletypeDisp;
  dynamic fueltypeDisp;
  String quotatypeDisp;
  String quotaDisp;
  dynamic paid;
  dynamic paidon;
  dynamic invno;
  double vatAmt;
  double scRefund;
  double rate;
  double rateDisc;
  dynamic replacerfid;
  dynamic replacefor;
  double vat;
  dynamic orderdate;
  double vatPerc;
  String divisionDisp;
  String branchDisp;
  String departmentDisp;
  String operationDisp;
  String divOnhold;
  String braOnhold;
  String dptOnhold;
  dynamic count;
  int rowno;
  dynamic hiddenOfflimit;
  dynamic macAddr;
  dynamic groupid;
  dynamic companyid;
  dynamic rfid;
  dynamic tempBranch;
  dynamic tempBranchname;
  dynamic tempDeptno;
  dynamic tempDeptname;
  dynamic modifiedby;
  dynamic tankNo2;
  dynamic objectId;
  dynamic prevCustid;
  dynamic transferreqid;
  String country;
  String terminateDate;
  String coupType;
  String dcDocNo;
  String dcQR;

  FleetModel({
    required this.line,
    required this.custid,
    required this.serialid,
    required this.serialcode,
    required this.servicetype,
    required this.servicestatus,
    required this.servicestatusDisp,
    required this.servicepw,
    required this.passwordDisp,
    required this.pwflag,
    required this.serviceactivedate,
    required this.plateno,
    required this.driver,
    required this.fueltype,
    required this.quotatype,
    required this.iSDC,
    required this.isDC,
    required this.quotaplanid,
    required this.quotavalue,
    required this.quotabal,
    required this.dcMobileno,
    required this.dcQuotavalue,
    required this.dcStations,
    required this.dcQuotarem,
    required this.dcQuotares,
    required this.dcQuotavalue2,
    required this.dcQuotarem2,
    required this.dcQuotares2,
    required this.dcType,
    required this.fillingdays,
    required this.fillingdaysDisp,
    required this.reforderid,
    required this.refjobid,
    required this.instby,
    required this.instdate,
    required this.instremarks,
    required this.division,
    required this.divisionname,
    required this.branch,
    required this.branchname,
    required this.deptno,
    required this.deptname,
    required this.operation,
    required this.operationname,
    required this.quotaclass,
    required this.quotaclassDisp,
    required this.isoffline,
    required this.isofflineDisp,
    required this.offlinelimit,
    required this.replacement,
    required this.stations,
    required this.pendingplate,
    required this.scamt,
    required this.tankNo,
    required this.transferdate,
    required this.uniquefield,
    required this.vehicleid,
    required this.complaintjobid,
    required this.stationPush,
    required this.refundreqid,
    required this.unliValue,
    required this.districts,
    required this.holdingid,
    required this.pinblock,
    required this.vehlicType,
    required this.vehlicTypeDisp,
    required this.pinblockDate,
    required this.vehicletype,
    required this.vehicletypeDisp,
    required this.fueltypeDisp,
    required this.quotatypeDisp,
    required this.quotaDisp,
    required this.paid,
    required this.paidon,
    required this.invno,
    required this.vatAmt,
    required this.scRefund,
    required this.rate,
    required this.rateDisc,
    required this.replacerfid,
    required this.replacefor,
    required this.vat,
    required this.orderdate,
    required this.vatPerc,
    required this.divisionDisp,
    required this.branchDisp,
    required this.departmentDisp,
    required this.operationDisp,
    required this.divOnhold,
    required this.braOnhold,
    required this.dptOnhold,
    required this.count,
    required this.rowno,
    required this.hiddenOfflimit,
    required this.macAddr,
    required this.groupid,
    required this.companyid,
    required this.rfid,
    required this.tempBranch,
    required this.tempBranchname,
    required this.tempDeptno,
    required this.tempDeptname,
    required this.modifiedby,
    required this.tankNo2,
    required this.objectId,
    required this.prevCustid,
    required this.transferreqid,
    required this.country,
    required this.terminateDate,
    required this.coupType,
    required this.dcDocNo,
    required this.dcQR,
  });

  factory FleetModel.fromMap(Map<String, dynamic> map) {
    return FleetModel(
      // unallocatbalance: map['UNALLOCATBALANCE'] ?? '',
      // currentbalance: map['CURRENTBALANCE'] ?? '',
      line: map["LINE"] ?? 0,
      custid: map["CUSTID"] ?? "",
      serialid: map["SERIALID"] ?? "",
      serialcode: map["SERIALCODE"] ?? "",
      servicetype: map["SERVICETYPE"] ?? "",
      servicestatus: map["SERVICESTATUS"] ?? "",
      servicestatusDisp: map["SERVICESTATUS_DISP"] ?? "",
      servicepw: map["SERVICEPW"] ?? "",
      passwordDisp: map["PASSWORD_DISP"] ?? "",
      pwflag: map["PWFLAG"] ?? "",
      serviceactivedate: map["SERVICEACTIVEDATE"] ?? "",
      plateno: map["PLATENO"] ?? "" ?? "",
      driver: map["DRIVER"] ?? "",
      fueltype: map["FUELTYPE"] ?? "",
      quotatype: map["QUOTATYPE"] ?? "",
      iSDC: map["ISDC"] ?? "",
      isDC: map["IsDC"] ?? "",
      quotaplanid: map["QUOTAPLANID"] ?? "",
      quotavalue: map["QUOTAVALUE"] ?? 0,
      quotabal: map["QUOTABAL"] ?? 0,
      dcMobileno: map["DC_MOBILENO"] ?? "",
      dcQuotavalue: map["DC_QUOTAVALUE"] ?? 0,
      dcStations: map["DC_STATIONS"] ?? "",
      dcQuotarem: map["DC_QUOTAREM"] ?? 0,
      dcQuotares: map["DC_QUOTARES"] ?? 0,
      dcQuotavalue2: map["DC_QUOTAVALUE2"] ?? 0,
      dcQuotarem2: map["DC_QUOTAREM2"] ?? 0,
      dcQuotares2: map["DC_QUOTARES2"] ?? 0,
      dcType: map["DC_TYPE"] ?? "",
      fillingdays: map["FILLINGDAYS"] ?? "",
      fillingdaysDisp: map["FILLINGDAYS_DISP"] ?? "",
      reforderid: map["REFORDERID"] ?? "",
      refjobid: map["REFJOBID"] ?? "",
      instby: map["INSTBY"] ?? "",
      instdate: map["INSTDATE"] ?? "",
      instremarks: map["INSTREMARKS"] ?? "",
      division: map["DIVISION"] ?? "",
      divisionname: map["DIVISIONNAME"] ?? "",
      branch: map["BRANCH"] ?? "",
      branchname: map["BRANCHNAME"] ?? "",
      deptno: map["DEPTNO"] ?? "",
      deptname: map["DEPTNAME"] ?? "",
      operation: map["OPERATION"] ?? "",
      operationname: map["OPERATIONNAME"] ?? "",
      quotaclass: map["QUOTACLASS"] ?? "",
      quotaclassDisp: map["QUOTACLASS_DISP"] ?? "",
      isoffline: map["ISOFFLINE"] ?? "",
      isofflineDisp: map["ISOFFLINE_DISP"] ?? "",
      offlinelimit: map["OFFLINELIMIT"] ?? 0,
      replacement: map["REPLACEMENT"] ?? "",
      stations: map["STATIONS"] ?? "",
      pendingplate: map["PENDINGPLATE"] ?? "",
      scamt: map["SCAMT"] ?? 0,
      tankNo: map["TANK_NO"] ?? "",
      transferdate: map["TRANSFERDATE"] ?? "",
      uniquefield: map["UNIQUEFIELD"] ?? "",
      vehicleid: map["VEHICLEID"] ?? "",
      complaintjobid: map["COMPLAINTJOBID"] ?? "",
      stationPush: map["STATION_PUSH"] ?? "",
      refundreqid: map["REFUNDREQID"] ?? "",
      unliValue: map["UNLI_VALUE"] ?? 0,
      districts: map["DISTRICTS"] ?? "",
      holdingid: map["HOLDINGID"] ?? "",
      pinblock: map["PINBLOCK"] ?? "",
      vehlicType: map["VEHLIC_TYPE"] ?? "",
      vehlicTypeDisp: map["VEHLIC_TYPE_DISP"] ?? "",
      pinblockDate: map["PINBLOCK_DATE"] ?? "",
      vehicletype: map["VEHICLETYPE"] ?? "",
      vehicletypeDisp: map["VEHICLETYPE_DISP"] ?? "",
      fueltypeDisp: map["FUELTYPE_DISP"] ?? "",
      quotatypeDisp: map["QUOTATYPE_DISP"] ?? "",
      quotaDisp: map["QUOTA_DISP"] ?? "",
      paid: map["PAID"] ?? "",
      paidon: map["PAIDON"] ?? "",
      invno: map["INVNO"] ?? "",
      vatAmt: map["VAT_AMT"] ?? 0,
      scRefund: map["SC_REFUND"] ?? 0,
      rate: map["RATE"] ?? 0,
      rateDisc: map["RATE_DISC"] ?? 0,
      replacerfid: map["REPLACERFID"] ?? "",
      replacefor: map["REPLACEFOR"] ?? "",
      vat: map["VAT"] ?? 0,
      orderdate: map["ORDERDATE"] ?? "",
      vatPerc: map["VAT_PERC"] ?? 0,
      divisionDisp: map["DIVISION_DISP"] ?? "",
      branchDisp: map["BRANCH_DISP"] ?? "",
      departmentDisp: map["DEPARTMENT_DISP"] ?? "",
      operationDisp: map["OPERATION_DISP"] ?? "",
      divOnhold: map["DIV_ONHOLD"] ?? "",
      braOnhold: map["BRA_ONHOLD"] ?? "",
      dptOnhold: map["DPT_ONHOLD"] ?? "",
      count: map["COUNT"] ?? 0,
      rowno: map["ROWNO"] ?? 0,
      hiddenOfflimit: map["HIDDEN_OFFLIMIT"] ?? "",
      macAddr: map["MAC_ADDR"] ?? "",
      groupid: map["GROUPID"] ?? "",
      companyid: map["COMPANYID"] ?? "",
      rfid: map["RFID"] ?? "",
      tempBranch: map["TEMP_BRANCH"] ?? "",
      tempBranchname: map["TEMP_BRANCHNAME"] ?? "",
      tempDeptno: map["TEMP_DEPTNO"] ?? "",
      tempDeptname: map["TEMP_DEPTNAME"] ?? "",
      modifiedby: map["MODIFIEDBY"] ?? "",
      tankNo2: map["TANK_NO2"] ?? "",
      objectId: map["OBJECT_ID"] ?? "",
      prevCustid: map["PREV_CUSTID"] ?? "",
      transferreqid: map["TRANSFERREQID"] ?? "",
      country: map["COUNTRY"] ?? "",
      terminateDate: map["TERMINATEDATE"] ?? "",
      coupType: map["COUPTYPE"] ?? "",
      dcDocNo: map["DC_DOCNO"] ?? "",
      dcQR: map["DC_QR"] ?? "",
    );
  }

  factory FleetModel.fromJson(Map<String, dynamic> json) => FleetModel(
        line: json["LINE"],
        custid: json["CUSTID"],
        serialid: json["SERIALID"],
        serialcode: json["SERIALCODE"],
        servicetype: json["SERVICETYPE"],
        servicestatus: json["SERVICESTATUS"],
        servicestatusDisp: json["SERVICESTATUS_DISP"],
        servicepw: json["SERVICEPW"],
        passwordDisp: json["PASSWORD_DISP"],
        pwflag: json["PWFLAG"],
        serviceactivedate: json["SERVICEACTIVEDATE"],
        plateno: json["PLATENO"],
        driver: json["DRIVER"],
        fueltype: json["FUELTYPE"],
        quotatype: json["QUOTATYPE"],
        iSDC: json["ISDC"],
        isDC: json["IsDC"],
        quotaplanid: json["QUOTAPLANID"],
        quotavalue: json["QUOTAVALUE"],
        quotabal: json["QUOTABAL"],
        dcMobileno: json["DC_MOBILENO"],
        dcQuotavalue: json["DC_QUOTAVALUE"],
        dcStations: json["DC_STATIONS"],
        dcQuotarem: json["DC_QUOTAREM"],
        dcQuotares: json["DC_QUOTARES"],
        dcQuotavalue2: json["DC_QUOTAVALUE2"],
        dcQuotarem2: json["DC_QUOTAREM2"],
        dcQuotares2: json["DC_QUOTARES2"],
        dcType: json["DC_TYPE"],
        fillingdays: json["FILLINGDAYS"],
        fillingdaysDisp: json["FILLINGDAYS_DISP"],
        reforderid: json["REFORDERID"],
        refjobid: json["REFJOBID"],
        instby: json["INSTBY"],
        instdate: json["INSTDATE"],
        instremarks: json["INSTREMARKS"],
        division: json["DIVISION"],
        divisionname: json["DIVISIONNAME"],
        branch: json["BRANCH"],
        branchname: json["BRANCHNAME"],
        deptno: json["DEPTNO"],
        deptname: json["DEPTNAME"],
        operation: json["OPERATION"],
        operationname: json["OPERATIONNAME"],
        quotaclass: json["QUOTACLASS"],
        quotaclassDisp: json["QUOTACLASS_DISP"],
        isoffline: json["ISOFFLINE"],
        isofflineDisp: json["ISOFFLINE_DISP"],
        offlinelimit: json["OFFLINELIMIT"],
        replacement: json["REPLACEMENT"],
        stations: json["STATIONS"],
        pendingplate: json["PENDINGPLATE"],
        scamt: json["SCAMT"],
        tankNo: json["TANK_NO"],
        transferdate: json["TRANSFERDATE"],
        uniquefield: json["UNIQUEFIELD"],
        vehicleid: json["VEHICLEID"],
        complaintjobid: json["COMPLAINTJOBID"],
        stationPush: json["STATION_PUSH"],
        refundreqid: json["REFUNDREQID"],
        unliValue: json["UNLI_VALUE"],
        districts: json["DISTRICTS"],
        holdingid: json["HOLDINGID"],
        pinblock: json["PINBLOCK"],
        vehlicType: json["VEHLIC_TYPE"],
        vehlicTypeDisp: json["VEHLIC_TYPE_DISP"],
        pinblockDate: json["PINBLOCK_DATE"],
        vehicletype: json["VEHICLETYPE"],
        vehicletypeDisp: json["VEHICLETYPE_DISP"],
        fueltypeDisp: json["FUELTYPE_DISP"],
        quotatypeDisp: json["QUOTATYPE_DISP"],
        quotaDisp: json["QUOTA_DISP"],
        paid: json["PAID"],
        paidon: json["PAIDON"],
        invno: json["INVNO"],
        vatAmt: json["VAT_AMT"],
        scRefund: json["SC_REFUND"],
        rate: json["RATE"],
        rateDisc: json["RATE_DISC"],
        replacerfid: json["REPLACERFID"],
        replacefor: json["REPLACEFOR"],
        vat: json["VAT"],
        orderdate: json["ORDERDATE"],
        vatPerc: json["VAT_PERC"],
        divisionDisp: json["DIVISION_DISP"],
        branchDisp: json["BRANCH_DISP"],
        departmentDisp: json["DEPARTMENT_DISP"],
        operationDisp: json["OPERATION_DISP"],
        divOnhold: json["DIV_ONHOLD"],
        braOnhold: json["BRA_ONHOLD"],
        dptOnhold: json["DPT_ONHOLD"],
        count: json["COUNT"],
        rowno: json["ROWNO"],
        hiddenOfflimit: json["HIDDEN_OFFLIMIT"],
        macAddr: json["MAC_ADDR"],
        groupid: json["GROUPID"],
        companyid: json["COMPANYID"],
        rfid: json["RFID"],
        tempBranch: json["TEMP_BRANCH"],
        tempBranchname: json["TEMP_BRANCHNAME"],
        tempDeptno: json["TEMP_DEPTNO"],
        tempDeptname: json["TEMP_DEPTNAME"],
        modifiedby: json["MODIFIEDBY"],
        tankNo2: json["TANK_NO2"],
        objectId: json["OBJECT_ID"],
        prevCustid: json["PREV_CUSTID"],
        transferreqid: json["TRANSFERREQID"],
        country: json["COUNTRY"],
        terminateDate: json["TERMINATEDATE"],
        coupType: json["COUPTYPE"],
        dcDocNo: json["DC_DOCNO"],
        dcQR: json["DC_QR"],
      );

  Map<String, dynamic> toJson() => {
        "LINE": line,
        "CUSTID": custid,
        "SERIALID": serialid,
        "SERIALCODE": serialcode,
        "SERVICETYPE": servicetype,
        "SERVICESTATUS": servicestatus,
        "SERVICESTATUS_DISP": servicestatusDisp,
        "SERVICEPW": servicepw,
        "PASSWORD_DISP": passwordDisp,
        "PWFLAG": pwflag,
        "SERVICEACTIVEDATE": serviceactivedate,
        "PLATENO": plateno,
        "DRIVER": driver,
        "FUELTYPE": fueltype,
        "QUOTATYPE": quotatype,
        "ISDC": iSDC,
        "IsDC": isDC,
        "QUOTAPLANID": quotaplanid,
        "QUOTAVALUE": quotavalue,
        "QUOTABAL": quotabal,
        "DC_MOBILENO": dcMobileno,
        "DC_QUOTAVALUE": dcQuotavalue,
        "DC_STATIONS": dcStations,
        "DC_QUOTAREM": dcQuotarem,
        "DC_QUOTARES": dcQuotares,
        "DC_QUOTAVALUE2": dcQuotavalue2,
        "DC_QUOTAREM2": dcQuotarem2,
        "DC_QUOTARES2": dcQuotares2,
        "DC_TYPE": dcType,
        "FILLINGDAYS": fillingdays,
        "FILLINGDAYS_DISP": fillingdaysDisp,
        "REFORDERID": reforderid,
        "REFJOBID": refjobid,
        "INSTBY": instby,
        "INSTDATE": instdate,
        "INSTREMARKS": instremarks,
        "DIVISION": division,
        "DIVISIONNAME": divisionname,
        "BRANCH": branch,
        "BRANCHNAME": branchname,
        "DEPTNO": deptno,
        "DEPTNAME": deptname,
        "OPERATION": operation,
        "OPERATIONNAME": operationname,
        "QUOTACLASS": quotaclass,
        "QUOTACLASS_DISP": quotaclassDisp,
        "ISOFFLINE": isoffline,
        "ISOFFLINE_DISP": isofflineDisp,
        "OFFLINELIMIT": offlinelimit,
        "REPLACEMENT": replacement,
        "STATIONS": stations,
        "PENDINGPLATE": pendingplate,
        "SCAMT": scamt,
        "TANK_NO": tankNo,
        "TRANSFERDATE": transferdate,
        "UNIQUEFIELD": uniquefield,
        "VEHICLEID": vehicleid,
        "COMPLAINTJOBID": complaintjobid,
        "STATION_PUSH": stationPush,
        "REFUNDREQID": refundreqid,
        "UNLI_VALUE": unliValue,
        "DISTRICTS": districts,
        "HOLDINGID": holdingid,
        "PINBLOCK": pinblock,
        "VEHLIC_TYPE": vehlicType,
        "VEHLIC_TYPE_DISP": vehlicTypeDisp,
        "PINBLOCK_DATE": pinblockDate,
        "VEHICLETYPE": vehicletype,
        "VEHICLETYPE_DISP": vehicletypeDisp,
        "FUELTYPE_DISP": fueltypeDisp,
        "QUOTATYPE_DISP": quotatypeDisp,
        "QUOTA_DISP": quotaDisp,
        "PAID": paid,
        "PAIDON": paidon,
        "INVNO": invno,
        "VAT_AMT": vatAmt,
        "SC_REFUND": scRefund,
        "RATE": rate,
        "RATE_DISC": rateDisc,
        "REPLACERFID": replacerfid,
        "REPLACEFOR": replacefor,
        "VAT": vat,
        "ORDERDATE": orderdate,
        "VAT_PERC": vatPerc,
        "DIVISION_DISP": divisionDisp,
        "BRANCH_DISP": branchDisp,
        "DEPARTMENT_DISP": departmentDisp,
        "OPERATION_DISP": operationDisp,
        "DIV_ONHOLD": divOnhold,
        "BRA_ONHOLD": braOnhold,
        "DPT_ONHOLD": dptOnhold,
        "COUNT": count,
        "ROWNO": rowno,
        "HIDDEN_OFFLIMIT": hiddenOfflimit,
        "MAC_ADDR": macAddr,
        "GROUPID": groupid,
        "COMPANYID": companyid,
        "RFID": rfid,
        "TEMP_BRANCH": tempBranch,
        "TEMP_BRANCHNAME": tempBranchname,
        "TEMP_DEPTNO": tempDeptno,
        "TEMP_DEPTNAME": tempDeptname,
        "MODIFIEDBY": modifiedby,
        "TANK_NO2": tankNo2,
        "OBJECT_ID": objectId,
        "PREV_CUSTID": prevCustid,
        "TRANSFERREQID": transferreqid,
        "COUNTRY": country,
        "TERMINATEDATE": terminateDate,
        "COUPTYPE": coupType,
        "DC_DOCNO": dcDocNo,
        "DC_QR": dcQR,
      };
}
