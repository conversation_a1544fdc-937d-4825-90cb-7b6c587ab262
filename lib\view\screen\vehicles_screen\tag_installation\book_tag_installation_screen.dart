// ignore_for_file: must_be_immutable, prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../../../../core/controller/menu_controller/profile_controller/personal_detail_controller.dart';
import '../../../../core/controller/vehicle_controller/book_tag_installation_controller.dart';
import '../../../../utils/images.dart';
import '../../../../utils/text_style.dart';
import '../../../widget/icon_and_image.dart';

class BookTagInstallationScreen extends StatefulWidget {
  const BookTagInstallationScreen({super.key});

  @override
  State<BookTagInstallationScreen> createState() =>
      _BookTagInstallationScreenState();
}

class _BookTagInstallationScreenState extends State<BookTagInstallationScreen> {
  final firstname = '';
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  BookTagInstallationController bookTagInstallationController =
      Get.put(BookTagInstallationController());
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  pickDate() async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColor.themeOrangeColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor:
                    AppColor.themeDarkBlueColor, // button text color
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (pickedDate != null) {
      print(pickedDate);
      String formattedDate = DateFormat('MM/dd/yy').format(pickedDate);
      print(formattedDate);

      bookTagInstallationController.datePickerController.text = formattedDate;

      bookTagInstallationController.LoadPlaces();
    } else {
      print("Date is not selected");
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            children: [
              simpleMyAppBar(
                  title: "Address".trr,
                  onTap: () {
                    Get.back();
                  },
                  backString: "Back".trr),
              Expanded(
                child: Form(
                  key: _formKey,
                  child: ListView(
                    shrinkWrap: true,
                    padding:
                        const EdgeInsets.only(top: 24, left: 16, right: 16),
                    children: [
                      DropdownButtonFormField(
                        items:
                            bookTagInstallationController.OrderList.map((data) {
                          return DropdownMenuItem(
                            value: data.TYPECODE,
                            child: Text(
                              data.TYPECODE,
                              style: pMedium12,
                              textAlign: TextAlign.center,
                            ),
                          );
                        }).toList(),

                        onChanged: (value) {
                          bookTagInstallationController.selectedOrder.value =
                              value.toString();
                          bookTagInstallationController
                              .fetchSelectOrderDetails();
                        },
                        style: pRegular14.copyWith(color: AppColor.cLabel),
                        borderRadius: BorderRadius.circular(6),
                        dropdownColor: AppColor.cLightGrey,
                        icon: assetSvdImageWidget(
                            image: DefaultImages.dropDownIcn),
                        decoration: InputDecoration(
                          hintText: 'Orderid'.trr,
                          hintStyle:
                              pRegular14.copyWith(color: AppColor.cHintFont),
                          contentPadding: EdgeInsets.only(left: 16, right: 16),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          disabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                        ),
                        // fontColor: AppColor.cDarkGreyFont,
                        //filledColor: AppColor.cFilled,
                      ),
                      verticalSpace(16),
                      CommonTextField(
                        labelText: "Waiting for update".trr + '*',
                        //hintText: 'Waiting for update'.trr,
                        hintText:
                            bookTagInstallationController.OrderWaitingobs.value,
                        readOnly: true,
                        //initialValue:bookTagInstallationController.OrderWaitingobs.value,
                      ),
                      verticalSpace(16),
                      CommonTextField(
                        labelText: 'Appointed Qty'.trr,
                        //hintText: 'Appointed Qty'.trr,
                        hintText: bookTagInstallationController
                            .OrderAppointedobs.value,
                        readOnly: true,
                      ),
                      verticalSpace(16),
                      CommonTextField(
                        labelText: 'Available for Appointment'.trr + '*',
                        //hintText: 'Available for Appointment'.trr,
                        hintText: bookTagInstallationController
                            .OrderRemainingobs.value,
                        readOnly: true,
                        fillColor: AppColor.cFocusedTextField,
                      ),
                      verticalSpace(16),
                      Expanded(
                        flex: 2,
                        child: CommonTextField(
                          controller: bookTagInstallationController
                              .datePickerController,
                          labelText: "Appointed Date".trr + '*',
                          suffix: assetSvdImageWidget(
                              image: DefaultImages.calendarIcn),
                          fillColor: AppColor.cWhite,
                          filled: true,
                          readOnly: true,
                          onTap: pickDate,
                        ),
                      ),
                      verticalSpace(16),
                      // CommonTextField(
                      //   labelText: "Appointed Date".trr + '*',
                      //   //hintText: 'Enter Mobile number'.trr,
                      //   keyboardType: TextInputType.number,
                      //   maxLength: 10,
                      //   validator: (value) {
                      //     return Validator.validateMobile(value);
                      //   },
                      // ),
                      CommonDropdownButtonWidget(
                        hint: '',
                        labelText: 'Place'.trr,
                        list: bookTagInstallationController.placeList,
                        value:
                            bookTagInstallationController.selectedPlace.value,
                        onChanged: (value) {},
                        fontColor: AppColor.cDarkGreyFont,
                        filledColor: AppColor.cFilled,
                      ),
                      verticalSpace(16),
                      CommonDropdownButtonWidget(
                        hint: '',
                        labelText: 'Center'.trr,
                        list: bookTagInstallationController.centerList,
                        value:
                            bookTagInstallationController.selectedCenter.value,
                        onChanged: (value) {},
                        fontColor: AppColor.cDarkGreyFont,
                        filledColor: AppColor.cFilled,
                      ),
                      verticalSpace(16),
                      CommonDropdownButtonWidget(
                        hint: '',
                        labelText: 'Vehicle Type'.trr,
                        list: bookTagInstallationController.vehicleTypeList,
                        value: bookTagInstallationController
                            .selectedvehicleType.value,
                        onChanged: (value) {},
                        fontColor: AppColor.cDarkGreyFont,
                        filledColor: AppColor.cFilled,
                      ),
                      verticalSpace(16),
                      CommonDropdownButtonWidget(
                        hint: '',
                        labelText: 'Tank Type'.trr,
                        list: bookTagInstallationController.tankTypeList,
                        value: bookTagInstallationController
                            .selectedTankType.value,
                        onChanged: (value) {},
                        fontColor: AppColor.cDarkGreyFont,
                        filledColor: AppColor.cFilled,
                      ),
                      verticalSpace(16),
                      CommonTextField(
                        labelText: "Required Qty".trr + '*',
                        hintText: 'Required Qty'.trr,
                        keyboardType: TextInputType.numberWithOptions(
                            signed: true, decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        maxLength: 10,
                        validator: (value) {
                          return Validator.validateMobile(value);
                        },
                      ),
                      verticalSpace(16),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        bottomNavigationBar: Container(
          color: AppColor.cLightGrey,
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: CommonButton(
                  title: 'Cancel'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  textColor: AppColor.cText,
                  btnColor: AppColor.cBackGround,
                ),
              ),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'View Slots'.trr,
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      // Call Controller
                    }
                  },
                  textColor: AppColor.cWhiteFont,
                  btnColor: AppColor.themeOrangeColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
