// ignore_for_file: prefer_const_constructors, must_be_immutable, prefer_interpolation_to_compose_strings

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/user_management_controller/user_management_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import '../../../../core/controller/home_controller/balance_controller.dart';
import '../../../../models/menu_access.dart';
import '../../../../utils/validator.dart';
import '../../../widget/common_space_divider_widget.dart';

class NewUserScreen extends StatefulWidget {
  const NewUserScreen({super.key});

  @override
  State<NewUserScreen> createState() => _NewUserScreenState();
}

class _NewUserScreenState extends State<NewUserScreen> {
  UserManagementController userManagementController =
      Get.put(UserManagementController());
  BalanceController balanceController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Form(
        key: _formKey,
        child: Scaffold(
          backgroundColor: AppColor.cBackGround,
          body: SafeArea(
            child: Column(
              children: [
                simpleAppBar(
                    title: "Add a new user".trr,
                    onTap: () {
                      Get.back();
                    },
                    backString: "Users".trr),
                Obx(() {
                  return Expanded(
                    child: ListView(
                      shrinkWrap: true,
                      padding: EdgeInsets.only(
                          top: 24, bottom: 16, right: 16, left: 16),
                      physics: BouncingScrollPhysics(),
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            verticalSpace(24),
                            CommonTextField(
                              controller:
                                  userManagementController.usernameController,
                              labelText: "User name".trr,
                              hintText: "Enter the user name".trr,
                            ),
                            verticalSpace(16),
                            CommonTextField(
                              controller:
                                  userManagementController.useremailController,
                              labelText: "User email".trr,
                              hintText: "Enter the user email".trr,
                              keyboardType: TextInputType.emailAddress,
                            ),
                            verticalSpace(16),
                            CommonDropdownButtonWidget(
                              labelText: "Status".trr,
                              list: userManagementController.statusList,
                              value: userManagementController.statusValue.value,
                              onChanged: (value) {
                                userManagementController.statusValue.value =
                                    value;
                              },
                            ),
                            verticalSpace(16),
                            CommonTextField(
                              controller:
                                  userManagementController.passwordController,
                              labelText: 'Please Enter your password'.trr,
                              hintText: "**********",
                              obscureText:
                                  userManagementController.isPassword.value,
                              obscuringCharacter: '*',
                              validator: (value) {
                                return Validator.validateName(
                                    value, "Password".trr);
                              },
                            ),
                            verticalSpace(16),
                            CommonDropdownButtonWidget(
                              labelText: "Recieve Email".trr,
                              list: userManagementController.rcvEmailList,
                              value:
                                  userManagementController.rcvEmailValue.value,
                              onChanged: (value) {
                                userManagementController.rcvEmailValue.value =
                                    value;
                              },
                            ),
                            verticalSpace(16),
                            CommonDropdownButtonWidget(
                              labelText: "Two Factor Authentication".trr,
                              list: userManagementController.twoFAList,
                              value: userManagementController.twoFAValue.value,
                              onChanged: (value) {
                                userManagementController.twoFAValue.value =
                                    value;
                              },
                            )
                          ],
                        ),
                        verticalSpace(40),
                        /**==================================================START MENU ACCESS================================================== */
                        menuTitleRowWidget(
                          title: "Menu Access".trr,
                          isSelected:
                              userManagementController.isMenuAccess.value,
                          onTap: () {
                            userManagementController.isMenuAccess.value =
                                !userManagementController.isMenuAccess.value;
                          },
                        ),
                        // Text(
                        //   "Menu Access".trr,
                        //   style: pBold20,
                        // ),
                        verticalSpace(12),
                        userManagementController.isMenuAccess.value
                            ? Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 10, horizontal: 8),
                                decoration: BoxDecoration(
                                  color: AppColor.cLightBlueContainer,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics: NeverScrollableScrollPhysics(),
                                      itemCount:
                                          balanceController.userMenuList.length,
                                      itemBuilder: (context, index) {
                                        var data = balanceController
                                            .userMenuList[index];
                                        print(data.menuName);
                                        print(data.menuCode);
                                        print(data.isSelected);
                                        print(data.value);
                                        print(data);

                                        return Obx(() {
                                          return Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 8.0),
                                            child: GestureDetector(
                                              onTap: () {
                                                data.isSelected.value =
                                                    !data.isSelected.value;
                                                print(data.isSelected.value);
                                              },
                                              child: Column(
                                                children: [
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      // Row(
                                                      //   children: [
                                                      //     SizedBox(
                                                      //       height: 24,
                                                      //       width: 24,
                                                      //       child: Checkbox(
                                                      //         value: data
                                                      //             .value.value,
                                                      //         onChanged:
                                                      //             (value) {
                                                      //           data.value
                                                      //                   .value =
                                                      //               value!;
                                                      //         },
                                                      //         activeColor: AppColor
                                                      //             .themeDarkBlueColor,
                                                      //         side: BorderSide(
                                                      //             color: AppColor
                                                      //                 .cBlack),
                                                      //         shape: RoundedRectangleBorder(
                                                      //             side: BorderSide(
                                                      //                 color: AppColor
                                                      //                     .cBlack),
                                                      //             borderRadius:
                                                      //                 BorderRadius
                                                      //                     .circular(
                                                      //                         4)),
                                                      //       ),
                                                      //     ),
                                                      //     horizontalSpace(8),
                                                      //     Text(
                                                      //       data.menuName,
                                                      //       style: pRegular16,
                                                      //     ),
                                                      //   ],
                                                      // ),
                                                      Text(
                                                        data.menuName,
                                                        style: pRegular16,
                                                      ),
                                                      assetSvdImageWidget(
                                                          image: data.isSelected
                                                                      .value ==
                                                                  true
                                                              ? DefaultImages
                                                                  .arrowUpIcn
                                                              : DefaultImages
                                                                  .dropDownIcn,
                                                          height: 24,
                                                          width: 24)
                                                    ],
                                                  ),
                                                  // Text(
                                                  //   data.menuName,
                                                  //   style: pRegular14,
                                                  // ),
                                                  data.isSelected.value == true
                                                      ? Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .only(
                                                            left: 10,
                                                            top: 6,
                                                          ),
                                                          child:
                                                              ListView.builder(
                                                            itemCount: data
                                                                .menu.length,
                                                            physics:
                                                                NeverScrollableScrollPhysics(),
                                                            shrinkWrap: true,
                                                            itemBuilder:
                                                                (context, i) {
                                                              var myData =
                                                                  data.menu[i];
                                                              print(myData
                                                                  .controlName);
                                                              print(myData
                                                                  .controlCode);
                                                              print(myData
                                                                  .menuCode);
                                                              print(myData);
                                                              print("=====");

                                                              late String
                                                                  myDtControlname;
                                                              late String
                                                                  myDtControlcode;
                                                              RxBool myDtvalue =
                                                                  true.obs;
                                                              for (var element
                                                                  in myData
                                                                      .subCont!) {
                                                                print(
                                                                    "=====1111111=======");
                                                                print(element
                                                                    .controlName);
                                                                print(
                                                                    "============");
                                                                myDtControlname =
                                                                    element
                                                                        .controlName;
                                                                myDtControlcode =
                                                                    element
                                                                        .controlCode;
                                                                myDtvalue =
                                                                    element
                                                                        .value;
                                                              }

                                                              return Obx(
                                                                () {
                                                                  return Padding(
                                                                    padding: const EdgeInsets
                                                                        .only(
                                                                        top:
                                                                            10),
                                                                    child:
                                                                        GestureDetector(
                                                                      onTap:
                                                                          () {
                                                                        myData.isSelected.value = !myData
                                                                            .isSelected
                                                                            .value;
                                                                        print(myData
                                                                            .isSelected
                                                                            .value);
                                                                      },
                                                                      child:
                                                                          Column(
                                                                        children: [
                                                                          Row(
                                                                            children: [
                                                                              SizedBox(
                                                                                height: 24,
                                                                                width: 24,
                                                                                child: Checkbox(
                                                                                  value: myData.value.value,
                                                                                  onChanged: (value) {
                                                                                    setState(() {
                                                                                      myData.value.value = value ?? false;
                                                                                      if (value == false) {
                                                                                        data.value.value = false;
                                                                                      }
                                                                                    });
                                                                                    if (value == true) {
                                                                                      userManagementController.selectedMenuList.add(myData.controlCode);
                                                                                    } else {
                                                                                      userManagementController.selectedMenuList.remove(myData.controlCode);
                                                                                    }
                                                                                    print("*****************||********************");
                                                                                    print("selectedMenuList>>>> ${jsonDecode(jsonEncode(userManagementController.selectedMenuList))}");
                                                                                    print("*****************||********************");
                                                                                  },
                                                                                  activeColor: AppColor.themeDarkBlueColor,
                                                                                  side: BorderSide(color: AppColor.cBlack),
                                                                                  shape: RoundedRectangleBorder(side: BorderSide(color: AppColor.cBlack), borderRadius: BorderRadius.circular(4)),
                                                                                ),
                                                                              ),
                                                                              horizontalSpace(8),
                                                                              Text(
                                                                                myData.controlName,
                                                                                style: pRegular16,
                                                                              ),
                                                                              myData.subCont!.isNotEmpty
                                                                                  ? assetSvdImageWidget(
                                                                                      image: myData.isSelected.value == true ? DefaultImages.arrowUpIcn : DefaultImages.dropDownIcn,
                                                                                      height: 24,
                                                                                      width: 24,
                                                                                    )
                                                                                  : SizedBox()
                                                                            ],
                                                                          ),
                                                                          myData.subCont!.isNotEmpty
                                                                              ? myData.isSelected.value == true
                                                                                  ? Row(
                                                                                      children: [
                                                                                        Padding(
                                                                                          padding: EdgeInsets.only(
                                                                                            left: 10,
                                                                                            top: 6,
                                                                                          ),
                                                                                          child: Column(
                                                                                            children: [
                                                                                              Row(
                                                                                                children: [
                                                                                                  SizedBox(
                                                                                                    height: 24,
                                                                                                    width: 24,
                                                                                                    child: Checkbox(
                                                                                                      value: myDtvalue.value,
                                                                                                      onChanged: (value) {
                                                                                                        myDtvalue.value = value!;

                                                                                                        setState(() {
                                                                                                          myDtvalue.value = value ?? false;
                                                                                                        });

                                                                                                        if (value == true) {
                                                                                                          userManagementController.selectedMenuList.add(myDtControlcode);
                                                                                                        } else {
                                                                                                          userManagementController.selectedMenuList.remove(myDtControlcode);
                                                                                                        }
                                                                                                        print("*****************||********************");
                                                                                                        print("selectedMenuList>>>> ${jsonDecode(jsonEncode(userManagementController.selectedMenuList))}");
                                                                                                        print("*****************||********************");
                                                                                                      },
                                                                                                      activeColor: AppColor.themeDarkBlueColor,
                                                                                                      side: BorderSide(color: AppColor.cBlack),
                                                                                                      shape: RoundedRectangleBorder(side: BorderSide(color: AppColor.cBlack), borderRadius: BorderRadius.circular(4)),
                                                                                                    ),
                                                                                                  ),
                                                                                                  horizontalSpace(8),
                                                                                                  Text(
                                                                                                    myDtControlname,
                                                                                                    style: pRegular16,
                                                                                                  ),
                                                                                                ],
                                                                                              ),
                                                                                            ],
                                                                                          ),
                                                                                        )
                                                                                      ],
                                                                                    )
                                                                                  : SizedBox()
                                                                              : SizedBox()
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  );
                                                                },
                                                              );
                                                            },
                                                          ),
                                                        )
                                                      : SizedBox(),
                                                ],
                                              ),
                                            ),
                                          );
                                        });
                                      },
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox(),
                        /**==================================================END MENU ACCESS================================================== */
                        verticalSpace(24),
                        /**==================================================START ORG LEVEL================================================== */
                        menuTitleRowWidget(
                          title: "Organisation Level".trr,
                          isSelected: userManagementController.isOrgLevel.value,
                          onTap: () {
                            userManagementController.isOrgLevel.value =
                                !userManagementController.isOrgLevel.value;
                          },
                        ),
                        verticalSpace(12),
                        userManagementController.isOrgLevel.value
                            ? Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 10, horizontal: 8),
                                decoration: BoxDecoration(
                                  color: AppColor.cLightBlueContainer,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics: NeverScrollableScrollPhysics(),
                                      itemCount: balanceController
                                          .orgLevelMenuList.length,
                                      itemBuilder: (context, index) {
                                        var datas = balanceController
                                            .orgLevelMenuList[index];
                                        print(datas.divName);
                                        print(datas.divCode);
                                        print(datas.isSelected);
                                        print(datas.value);
                                        print(datas);

                                        return Obx(() {
                                          return Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 8.0),
                                            child: GestureDetector(
                                              onTap: () {
                                                datas.isSelected.value =
                                                    !datas.isSelected.value;
                                                print(datas.isSelected.value);
                                              },
                                              child: Column(
                                                children: [
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      // Row(
                                                      //   children: [
                                                      //     SizedBox(
                                                      //       height: 24,
                                                      //       width: 24,
                                                      //       child: Checkbox(
                                                      //         value: datas
                                                      //             .value.value,
                                                      //         onChanged:
                                                      //             (value) {
                                                      //           datas.value
                                                      //                   .value =
                                                      //               value!;
                                                      //         },
                                                      //         activeColor: AppColor
                                                      //             .themeDarkBlueColor,
                                                      //         side: BorderSide(
                                                      //             color: AppColor
                                                      //                 .cBlack),
                                                      //         shape: RoundedRectangleBorder(
                                                      //             side: BorderSide(
                                                      //                 color: AppColor
                                                      //                     .cBlack),
                                                      //             borderRadius:
                                                      //                 BorderRadius
                                                      //                     .circular(
                                                      //                         4)),
                                                      //       ),
                                                      //     ),
                                                      //     horizontalSpace(8),
                                                      //     Text(
                                                      //       datas.divName,
                                                      //       style: pRegular16,
                                                      //     ),
                                                      //   ],
                                                      // ),
                                                      Text(
                                                        datas.divName,
                                                        style: pRegular16,
                                                      ),
                                                      assetSvdImageWidget(
                                                          image: datas.isSelected
                                                                      .value ==
                                                                  true
                                                              ? DefaultImages
                                                                  .arrowUpIcn
                                                              : DefaultImages
                                                                  .dropDownIcn,
                                                          height: 24,
                                                          width: 24)
                                                    ],
                                                  ),
                                                  // Text(
                                                  //   data.menuName,
                                                  //   style: pRegular14,
                                                  // ),
                                                  datas.isSelected.value == true
                                                      ? Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .only(
                                                            left: 10,
                                                            top: 6,
                                                          ),
                                                          child:
                                                              ListView.builder(
                                                            itemCount: datas
                                                                .orglevel
                                                                .length,
                                                            physics:
                                                                NeverScrollableScrollPhysics(),
                                                            shrinkWrap: true,
                                                            itemBuilder:
                                                                (context, i) {
                                                              var myDatas = datas
                                                                  .orglevel[i];
                                                              print(myDatas
                                                                  .brName);
                                                              print(myDatas
                                                                  .brCode);
                                                              print(myDatas
                                                                  .divCode);
                                                              print(myDatas);
                                                              print("=====");

                                                              return Obx(
                                                                () {
                                                                  return Padding(
                                                                    padding: const EdgeInsets
                                                                        .only(
                                                                        top:
                                                                            10),
                                                                    child:
                                                                        Column(
                                                                      children: [
                                                                        Row(
                                                                          children: [
                                                                            SizedBox(
                                                                              height: 24,
                                                                              width: 24,
                                                                              child: Checkbox(
                                                                                value: myDatas.value.value,
                                                                                onChanged: (value) {
                                                                                  setState(() {
                                                                                    myDatas.value.value = value ?? false;
                                                                                    if (value == false) {
                                                                                      datas.value.value = false;
                                                                                    }
                                                                                  });
                                                                                  if (value == true) {
                                                                                    userManagementController.selectedOrgList.add(myDatas.brCode);
                                                                                  } else {
                                                                                    userManagementController.selectedOrgList.remove(myDatas.brCode);
                                                                                  }
                                                                                  print("*****************||********************");
                                                                                  print("selectedOrgList>>>> ${jsonDecode(jsonEncode(userManagementController.selectedOrgList))}");
                                                                                  print("*****************||********************");
                                                                                },
                                                                                activeColor: AppColor.themeDarkBlueColor,
                                                                                side: BorderSide(color: AppColor.cBlack),
                                                                                shape: RoundedRectangleBorder(side: BorderSide(color: AppColor.cBlack), borderRadius: BorderRadius.circular(4)),
                                                                              ),
                                                                            ),
                                                                            horizontalSpace(8),
                                                                            Text(
                                                                              myDatas.brName,
                                                                              style: pRegular16,
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  );
                                                                },
                                                              );
                                                            },
                                                          ),
                                                        )
                                                      : SizedBox(),
                                                ],
                                              ),
                                            ),
                                          );
                                        });
                                      },
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox(),
                        /**==================================================END ORG LEVEL================================================== */
                      ],
                    ),
                  );
                })
              ],
            ),
          ),
          bottomNavigationBar: Container(
            color: AppColor.cLightGrey,
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: CommonButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      Get.back();
                    },
                    textColor: AppColor.cText,
                    btnColor: AppColor.cBackGround,
                  ),
                ),
                horizontalSpace(16),
                Expanded(
                  child: CommonButton(
                    title: 'Save'.trr,
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        userManagementController.saveUser();
                      }
                    },
                    textColor: AppColor.cWhiteFont,
                    btnColor: AppColor.themeOrangeColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget bgContainerWidget(Widget widget) {
    return Container(
      decoration: BoxDecoration(
        color: AppColor.lightBlueColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppColor.cLightGrey),
      ),
      padding: EdgeInsets.all(16),
      child: widget,
    );
  }

  Container userDetailWidget() {
    return Container(
      width: Get.width,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "User details".trr,
            style: pBold20,
          ),
          assetSvdImageWidget(
              image: DefaultImages.dropUpIcn,
              width: 20,
              height: 20,
              colorFilter: ColorFilter.mode(AppColor.cText, BlendMode.srcIn))
        ],
      ),
    );
  }
}

Widget checkBoxWidget(
    {bool? value, Function(bool?)? onChanged, String? title}) {
  return GestureDetector(
    onTap: () {
      value != value;
    },
    child: Row(
      children: [
        SizedBox(
          width: 24,
          height: 24,
          child: Checkbox(
            value: value,
            onChanged: onChanged,
            activeColor: AppColor.themeDarkBlueColor,
            side: BorderSide(color: AppColor.cBlack),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
          ),
        ),
        horizontalSpace(6),
        Text(
          title!,
          style: pRegular14,
        )
      ],
    ),
  );
}

class CustomSwitch extends StatefulWidget {
  final bool value;
  final ValueChanged<bool> onChanged;

  const CustomSwitch({Key? key, required this.value, required this.onChanged})
      : super(key: key);

  @override
  CustomSwitchState createState() => CustomSwitchState();
}

class CustomSwitchState extends State<CustomSwitch>
    with SingleTickerProviderStateMixin {
  AnimationController? _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 30));
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController!,
      builder: (context, child) {
        return GestureDetector(
          onTap: () {
            if (_animationController!.isCompleted) {
              _animationController!.reverse();
            } else {
              _animationController!.forward();
            }
            widget.value == false
                ? widget.onChanged(true)
                : widget.onChanged(false);
          },
          child: Container(
            width: 40.0,
            height: 24.0,
            decoration: BoxDecoration(
              color: widget.value
                  ? AppColor.themeOrangeColor
                  : AppColor.cTransparent,
              borderRadius: BorderRadius.circular(24.0),
              border: Border.all(
                color:
                    widget.value ? AppColor.themeOrangeColor : AppColor.cText,
                width: 2,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.only(
                top: 1.0,
                bottom: 1.0,
                right: 1.0,
                left: 1.0,
              ),
              child: Container(
                alignment:
                    widget.value ? Alignment.centerRight : Alignment.centerLeft,
                child: Container(
                  width: 20.0,
                  height: 20.0,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.value ? AppColor.cBackGround : AppColor.cText,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

Widget menuTitleRowWidget(
    {required String title, required bool isSelected, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      color: AppColor.cBackGround,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pBold20,
          ),
          assetSvdImageWidget(
              image: isSelected == true
                  ? DefaultImages.arrowUpIcn
                  : DefaultImages.dropDownIcn,
              width: 24,
              height: 24)
        ],
      ),
    ),
  );
}
