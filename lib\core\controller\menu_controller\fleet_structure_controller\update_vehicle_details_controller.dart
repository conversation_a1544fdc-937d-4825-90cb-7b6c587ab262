import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/models/loadplaces.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/final_view_vehicle_details_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../models/load_data.dart';
import '../../../../models/newfleet.dart';
import '../../../../utils/constants.dart';
import '../../../../view/screen/vehicles_screen/my_fleet/final_edit_vehicle_details_screen.dart';
import '../../../../view/widget/loading_widget.dart';

class EditVehicleDetailsController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  GetStorage userStorage = GetStorage('User');
  List<LoadPlaces> loadPlaces = [];
  RxList placeList = [].obs;
  RxBool chckbox = false.obs;
  final countryList = <Load_Data_Model>[].obs;
  final vehlicTypeList = <Load_Data_Model>[].obs;
  final vehicleTypeList = <Load_Data_Model>[].obs;
  final fuelList = <Load_Data_Model>[].obs;
  final quotaTypeList = <Load_Data_Model>[].obs;
  final quotaClassList = <Load_Data_Model>[].obs;
  final List<ServiceObj> vehicleDetails = [];
  List<Load_Data_Model> countryModelList = [];
  List<Load_Data_Model> vehlicTypeModelList = [];
  List<Load_Data_Model> vehicleTypeModelList = [];
  List<Load_Data_Model> fuelModelList = [];
  List<Load_Data_Model> quotaTypeModelList = [];
  List<Load_Data_Model> quotaClassModelList = [];

  getVehicleDetails(serialid) async {
    print("serialid>>>>>>> $serialid");
    Loader.showLoader();
    var client = http.Client();
    List<ServiceObj> details = [];
    var custData = custsData.read('custData');
    print("custid>>>>>>> $custData['CUSTID']");
    print("ViewVehicleDetailsController>>>>>>> getVehicleDetails");
    //print("emailid>>>>>>> $custData['EMAILID']");
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");

    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      Map body = {
        "SEARCHBY": "",
        "TOP": "1",
        "SKIP": "0",
        "CUSTID": custid, //"000003944",
        "SERIALID": serialid
      };
      var vehicleResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
          body: jsonEncode(body),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          });

      //List vehicleResult = jsonDecode(vehicleResponse.body);

      var countryResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "COUNTRY",
            "CustId": custData["CUSTID"],
          }
          );

      var vehlicTypeResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "VEHLIC_TYPE",
            "CustId": custData["CUSTID"],
          });

      var vehicleTypeResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "VEHTYPE",
            "CustId": custData["CUSTID"],
          });

      var fuelResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "FUELTYPE",
            "CustId": custData["CUSTID"],
          });

      var quotaTypeResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "QUOTYPE",
            "CustId": custData["CUSTID"],
          });

      var quotaClassResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "QUOCLASS",
            "CustId": custData["CUSTID"],
          });

      var wResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadPlaces),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
          body: {
            "CUSTID": custid,
            "OTHERSTN": custData['OTHERSTN'],
            "StationType": "W",
            "fuelType": "PE910",
            "IsAR": Constants.IsAr_App,
          });
      List wResult = jsonDecode(wResponse.body);

      final vehicleResult = json.decode(vehicleResponse.body);
      inspect(vehicleResult);
      final dataList = vehicleResult["serviceObj"] as List<dynamic>;

      final List<ServiceObj> newData =
          dataList.map((item) => ServiceObj.fromJson(item)).toList();

      vehicleDetails.addAll(newData);

      print("countryResponse===> ${jsonDecode(countryResponse.body)}");

      List countryResult = jsonDecode(countryResponse.body);

      for (int i = 0; i < countryResult.length; i++) {
        Load_Data_Model loadData =
            Load_Data_Model.fromMap(countryResult[i] as Map<String, dynamic>);
        countryModelList.add(loadData);
        print("countryResponse ===============${loadData.TYPEDESC}");
      }

      countryList.value = countryModelList;

      print("vehlicTypeResponse===> ${jsonDecode(vehlicTypeResponse.body)}");

      List vehlicTypeResult = jsonDecode(vehlicTypeResponse.body);

      for (int i = 0; i < vehlicTypeResult.length; i++) {
        Load_Data_Model loadData1 = Load_Data_Model.fromMap(
            vehlicTypeResult[i] as Map<String, dynamic>);
        vehlicTypeModelList.add(loadData1);
        print("vehlicTypeResponse ===============${loadData1.TYPEDESC}");
      }

      vehlicTypeList.value = vehlicTypeModelList;

      print("vehicleTypeResponse===> ${jsonDecode(vehicleTypeResponse.body)}");

      List vehicleTypeResult = jsonDecode(vehicleTypeResponse.body);

      for (int i = 0; i < vehicleTypeResult.length; i++) {
        Load_Data_Model loadData2 = Load_Data_Model.fromMap(
            vehicleTypeResult[i] as Map<String, dynamic>);
        vehicleTypeModelList.add(loadData2);
        print("vehicleTypeResponse ===============${loadData2.TYPEDESC}");
      }

      vehicleTypeList.value = vehicleTypeModelList;

      print("fuelResponse===> ${jsonDecode(fuelResponse.body)}");

      List fuelResult = jsonDecode(fuelResponse.body);

      for (int i = 0; i < fuelResult.length; i++) {
        Load_Data_Model loadData3 =
            Load_Data_Model.fromMap(fuelResult[i] as Map<String, dynamic>);
        fuelModelList.add(loadData3);
        print("fuelResponse ===============${loadData3.TYPEDESC}");
      }

      fuelList.value = fuelModelList;

      print("quotaTypeResponse===> ${jsonDecode(quotaTypeResponse.body)}");

      List quotaTypeResult = jsonDecode(quotaTypeResponse.body);

      for (int i = 0; i < quotaTypeResult.length; i++) {
        Load_Data_Model loadData4 =
            Load_Data_Model.fromMap(quotaTypeResult[i] as Map<String, dynamic>);
        quotaTypeModelList.add(loadData4);
        print("quotaTypeResponse ===============${loadData4.TYPEDESC}");
      }

      quotaTypeList.value = quotaTypeModelList;

      print("quotaClassResponse===> ${jsonDecode(quotaClassResponse.body)}");

      List quotaClassResult = jsonDecode(quotaClassResponse.body);

      for (int i = 0; i < quotaClassResult.length; i++) {
        Load_Data_Model loadData5 = Load_Data_Model.fromMap(
            quotaClassResult[i] as Map<String, dynamic>);
        quotaClassModelList.add(loadData5);
        print("quotaClassResponse ===============${loadData5.TYPEDESC}");
      }

      quotaClassList.value = quotaClassModelList;

      // for (int i = 0; i < vehicleResult.length; i++) {
      //   print("i===> $i --- ${vehicleResult.length}");
      // }

      // for (int i = 0; i < vehicleResult.length; i++) {
      //   try {
      //     FleetModel detail =
      //         FleetModel.fromMap(vehicleResult[i] as Map<String, dynamic>);
      //     details.add(detail);
      //   } catch (e) {
      //     print("e.toString()===> ${e.toString()}");
      //   }
      // }

      for (int i = 0; i < wResult.length; i++) {
        LoadPlaces place =
            LoadPlaces.fromJson(wResult[i] as Map<String, dynamic>);
        loadPlaces.add(place);
      }

      print("response vehicleDetails===> ${vehicleDetails.length}");

      if (wResponse.statusCode == 200) {
        Loader.hideLoader();
        await Get.to(() => const FinalEditVehicleDetailsScreen());
      } else {
        Loader.hideLoader();
        print('Failed');
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Failed to Load Vehicle Details",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }

      return vehicleDetails;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
