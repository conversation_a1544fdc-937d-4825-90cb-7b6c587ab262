import 'dart:convert';

class Report_Products {
  final String CODE;
  final String TYPEDESC;

  Report_Products({
    required this.CODE,
    required this.TYPEDESC,
  });

  Map<String, dynamic> toMap() {
    return {
      'CODE': CODE,
      'TYPEDESC': TYPEDESC,
    };
  }

  factory Report_Products.fromMap(Map<String, dynamic> map) {
    return Report_Products(
      CODE: map['CODE'] ?? '',
      TYPEDESC: map['TYPEDESC'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Report_Products.fromJson(String source) =>
      Report_Products.fromMap(json.decode(source));
}