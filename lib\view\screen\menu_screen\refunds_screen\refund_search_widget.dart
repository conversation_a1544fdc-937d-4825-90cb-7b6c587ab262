// ignore_for_file: prefer_const_constructors, prefer_const_constructors_in_immutables

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_order_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'order_refund_screen.dart';

class SearchRefundWidget extends StatefulWidget {
  SearchRefundWidget({super.key});

  @override
  State<SearchRefundWidget> createState() => _SearchRefundWidgetState();
}

class _SearchRefundWidgetState extends State<SearchRefundWidget> {
  RefundsController refundsController = Get.find();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    refundsController.itemList.clear();
  }

  void filterSearchResults(String query) {
    if (refundsController.isOrderRefund.value == true) {
      refundsController.itemList.value = refundsController.orderRefundList
          .where((item) => item['code'].toLowerCase().contains(query.toLowerCase()))
          .toList();
    } else {
      refundsController.itemList.value = refundsController.refundHistoryList
          .where((item) => item['code'].toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Get.height - 60,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      padding: EdgeInsets.all(16),
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalSpace(16),
            Row(
              children: [
                Expanded(
                  child: CommonTextField(
                    controller: refundsController.searchController.value,
                    labelText: '',
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(12),
                      child: assetSvdImageWidget(image: DefaultImages.searchIcn, width: 24, height: 24),
                    ),
                    hintText: 'Search'.trr,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        refundsController.itemList.clear();
                        refundsController.itemList.refresh();
                      } else {
                        refundsController.searchController.refresh();
                        filterSearchResults(value);
                      }
                    },
                  ),
                ),
                refundsController.searchController.value.text.isEmpty
                    ? SizedBox()
                    : cancelButton(
                        () {
                          refundsController.searchController.value.clear();
                          refundsController.searchController.refresh();
                          refundsController.itemList.clear();
                          refundsController.itemList.refresh();
                        },
                      )
              ],
            ),
            verticalSpace(16),
            refundsController.itemList.isEmpty
                ? Expanded(
                    child: Center(
                        child: Text(
                    "No matches".trr,
                    style: pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                  )))
                : Expanded(
                    child: ListView.builder(
                      itemCount: refundsController.itemList.length,
                      shrinkWrap: true,
                      physics: BouncingScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = refundsController.itemList[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: refundDataWidget(
                            code: data['code'],
                            status: data['status'].toString().trr,
                            orderType: data['orderType'].toString().trr,
                            plate: data['plate'],
                            vehicleType: data['vehicleType'],
                            orderDate: data['orderDate'],
                            amount: data['amount'],
                            vat: data['vat'],
                            textColor: data['status'] == "Confirmed" ? AppColor.cGreen : AppColor.cDarkBlueFont,
                            color: data['status'] == "Confirmed" ? AppColor.cLightGreen : AppColor.cLightBlueContainer,
                            isShowButton: refundsController.isOrderRefund.value == true
                                ? false
                                : data['status'] == "Confirmed"
                                    ? false
                                    : true,
                            cancelReqOnTap: () {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return AlertDialog(
                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                    contentPadding: EdgeInsets.all(24),
                                    content: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          children: [
                                            GestureDetector(
                                                onTap: () {
                                                  Get.back();
                                                },
                                                child: assetSvdImageWidget(image: DefaultImages.cancelIcn)),
                                          ],
                                        ),
                                        verticalSpace(24),
                                        Text("${"Are you sure you want to cancel refund request".trr} ${data['code']}?",
                                            style: pBold20, textAlign: TextAlign.center),
                                        verticalSpace(14),
                                        Center(
                                            child: Text("This action can not be undone.".trr,
                                                style: pRegular13, textAlign: TextAlign.center)),
                                        verticalSpace(24),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: CommonButton(
                                                title: "OK".trr,
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                textColor: AppColor.cDarkBlueFont,
                                                btnColor: AppColor.cBackGround,
                                                bColor: AppColor.cDarkBlueFont,
                                              ),
                                            ),
                                            horizontalSpace(16),
                                            Expanded(
                                              child: CommonButton(
                                                title: "Yes, cancel".trr,
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                textColor: AppColor.cWhiteFont,
                                                btnColor: AppColor.cRedText,
                                                bColor: AppColor.cTransparent,
                                                horizontalPadding: 16,
                                              ),
                                            ),
                                          ],
                                        )
                                      ],
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
          ],
        );
      }),
    );
  }
}
