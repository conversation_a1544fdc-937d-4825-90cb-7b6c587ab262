// ignore_for_file: prefer_const_constructors, avoid_print

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class CustomDropdownButtonWidget extends StatefulWidget {
  final String? value;
  final String? labelText;
  final Widget? widget;
  final List? list;
  final ValueChanged? onChanged;
  final FormFieldValidator? validator;
  final Color? fontColor;
  final Color? filledColor;
  final String hint;
  final bool? isExpanded;

  const CustomDropdownButtonWidget(
      {super.key,
      this.hint = '',
      this.value,
      this.labelText,
      this.widget,
      this.list,
      this.onChanged,
      this.validator,
      this.fontColor,
      this.filledColor,
      this.isExpanded});

  @override
  State<CustomDropdownButtonWidget> createState() =>
      _CustomDropdownButtonWidgetState();
}

class _CustomDropdownButtonWidgetState
    extends State<CustomDropdownButtonWidget> {
  String err = '';

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.labelText == ''
            ? SizedBox(
                height: 0,
              )
            : Text(
                widget.labelText!,
                style: pRegular12,
              ),
        widget.labelText == '' ? verticalSpace(0) : verticalSpace(6),
        SizedBox(
          height: 44,
          child: DropdownButtonFormField(
            value: widget.value,
            items: widget.list!.map((data) {
              return DropdownMenuItem(
                value: data,
                child: Text(
                  data.toString().trr,
                  style: pRegular13.copyWith(color: widget.fontColor),
                  overflow: TextOverflow.ellipsis,
                ),
              );
            }).toList(),
            dropdownColor: AppColor.cLightGrey,
            icon: Padding(
              padding: const EdgeInsets.only(right: 0),
              child: assetSvdImageWidget(image: DefaultImages.dropDownIcn),
            ),
            onChanged: widget.onChanged!,
            validator: (v) {
              setState(() {
                err = widget.validator!(v)!;
              });
              print("======= $err");
              if (err == '') {
                return null;
              } else {
                return err;
              }
            },
            style: pMedium14.copyWith(
              color: AppColor.cWhiteFont,
            ),
            isExpanded: widget.isExpanded ?? false,
            decoration: InputDecoration(
              fillColor: widget.filledColor ?? AppColor.cTransparent,
              filled: true,
              hintText: widget.hint,
              hintStyle: pMedium12.copyWith(color: AppColor.cHintFont),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              contentPadding: EdgeInsets.only(left: 16, right: 16),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
            ),
          ),
        ),
        err == ''
            ? SizedBox()
            : Text(err, style: pMedium12.copyWith(color: AppColor.cRedText)),
      ],
    );
  }
}

class CommonHintDropdownWidget extends StatefulWidget {
  final String? value;
  final String? labelText;
  final Widget? widget;
  final String hint;

  final List? list;
  final ValueChanged? onChanged;
  final FormFieldValidator? validator;
  final Color? fontColor;
  final Color? filledColor;

  const CommonHintDropdownWidget(
      {super.key,
      this.value,
      this.labelText,
      this.widget,
      this.list,
      this.onChanged,
      this.validator,
      required this.hint,
      this.fontColor,
      this.filledColor});

  @override
  State<CommonHintDropdownWidget> createState() =>
      _CommonHintDropdownWidgetState();
}

class _CommonHintDropdownWidgetState extends State<CommonHintDropdownWidget> {
  String err = '';

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.labelText == ''
            ? SizedBox(
                height: 0,
              )
            : Text(
                widget.labelText!,
                style: pRegular12,
              ),
        widget.labelText == '' ? verticalSpace(0) : verticalSpace(6),
        SizedBox(
          height: 44,
          child: DropdownButtonFormField(
            // value: widget.value,
            items: widget.list!.map((data) {
              return DropdownMenuItem(
                value: data,
                child: Text(
                  data.toString().trr,
                  style: pMedium12.copyWith(color: widget.fontColor),
                  textAlign: TextAlign.center,
                ),
              );
            }).toList(),
            dropdownColor: AppColor.cLightGrey,
            icon: Padding(
              padding: const EdgeInsets.only(right: 0),
              child: assetSvdImageWidget(image: DefaultImages.dropDownIcn),
            ),
            onChanged: widget.onChanged!,
            style: pRegular14.copyWith(color: AppColor.cLabel),
            borderRadius: BorderRadius.circular(6),
            decoration: InputDecoration(
              filled: true,
              fillColor: widget.filledColor ?? AppColor.cTransparent,
              hintText: widget.hint,
              hintStyle: pRegular14.copyWith(color: AppColor.cHintFont),
              // errorText: '',
              // errorStyle: TextStyle(
              //   height: 0,
              //   fontSize: 0,
              //   decorationThickness: 0,
              // ),
              contentPadding: EdgeInsets.only(left: 16, right: 16),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: AppColor.cBorder,
                ),
              ),
            ),
          ),
        ),
        err == ''
            ? SizedBox()
            : Text(err, style: pMedium12.copyWith(color: AppColor.cRedText)),
      ],
    );
  }
}
