import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/view/screen/auth/otp_screen.dart';
import 'package:waie_app/view/screen/auth/signup_otp_validation_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/personal_details_screen.dart';

import '../../../utils/colors.dart';
import '../../../utils/text_style.dart';
import '../../../view/widget/common_button.dart';
import '../../../view/widget/common_space_divider_widget.dart';
import '../../../view/widget/loading_widget.dart';

class SignupController extends GetxController {
  final getOTPDataVerify = GetStorage();
  TextEditingController usernameController = TextEditingController();
  TextEditingController fNameController = TextEditingController();
  TextEditingController lNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController mobileNoController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController cityCodeController = TextEditingController();
  TextEditingController serviceKnownController = TextEditingController();
  TextEditingController compTypeController = TextEditingController();
  RxString isoCode = 'SA'.obs;
  registerWithEmail(isIndividual) async {
    Loader.showLoader();
    print("isIndividual===> $isIndividual");
    var client = http.Client();
    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      Map body = {};
      if (isIndividual == true) {
        body = {
          'EMAILID': emailController.text ?? "",
          'FIRSTNAME': fNameController.text,
          'LASTNAME': lNameController.text,
          'MOBILENO': mobileNoController.text,
          // ignore: dead_code
          'REGTYPE': "I",
          'LANG': Constants.IsAr_App == "true" ? "ar" : "en",
          'PASSWORD': passwordController.text,
          'SERVICEKNOWN': serviceKnownController.text,
          'CITY_CODE': cityCodeController.text,
          'USERNAME': usernameController.text ?? "",
        };
      } else {
        body = {
          'EMAILID': emailController.text ?? "",
          'FIRSTNAME': fNameController.text,
          'LASTNAME': lNameController.text,
          'MOBILENO': mobileNoController.text,
          // ignore: dead_code
          'REGTYPE': "C",
          'LANG': Constants.IsAr_App == "true" ? "ar" : "en",
          'PASSWORD': passwordController.text,
          //'USERNAME': usernameController.text ?? "",
          'COMPTYPE':
              compTypeController.text == "" ? "P" : compTypeController.text
        };
      }
      print("body =======================> $body");
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.registerEmail),
          body: jsonEncode(body),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          });
      print("isIndividual===> $isIndividual");
      print("statusCode===> ${response.statusCode}");
      print("response body===> ${response.body}");
      print(
          "response c===> ${response.statusCode == 200 || response.statusCode == 201}");
      print(
          "RETURNED MESSAGE===>${jsonDecode(response.body)["ReturnMessage"]["Message"]}");
      print("cityCodeController===>${cityCodeController.text}");
      print("serviceKnownController===>${serviceKnownController.text}");
      print("IsPrimay===> ${jsonDecode(response.body)["IsPrimay"]}");
      print("custid===> ${jsonDecode(response.body)["AuCust"]["CUSTID"]}");
      print("emailid===> ${jsonDecode(response.body)["AuCust"]["EMAILID"]}");
      print("otpCode===> ${jsonDecode(response.body)["AuCust"]["REGVALCODE"]}");

      var returnMessage = jsonDecode(response.body)["ReturnMessage"]["Message"];

      getOTPDataVerify.writeIfNull(
          'isPrimary', jsonDecode(response.body)["IsPrimay"]);
      getOTPDataVerify.writeIfNull(
          'custid', jsonDecode(response.body)["AuCust"]["CUSTID"]);
      getOTPDataVerify.writeIfNull(
          'emailid', jsonDecode(response.body)["AuCust"]["EMAILID"]);
      getOTPDataVerify.writeIfNull(
          'otpCode', jsonDecode(response.body)["AuCust"]["REGVALCODE"]);

      if (returnMessage == 'validation') {
        Loader.hideLoader();
        clear();
        Get.off(SignupOTPValidationScreen());
      } else if (returnMessage == 'profile') {
        Loader.hideLoader();
        clear();
        Get.off(const PersonalDetailScreen());
      } else {
        Loader.hideLoader();
        // throw jsonDecode(response.body)["ReturnMessage"]["Message"] ??
        //     "Unknown Error Occured";
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${jsonDecode(response.body)["ReturnMessage"]["Message"]}",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      Loader.hideLoader();
      showDialog(
        barrierDismissible: false,
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(24),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  e.toString(),
                  style: pBold20,
                  textAlign: TextAlign.center,
                ),
                verticalSpace(24),
                CommonButton(
                  title: "OK".tr,
                  onPressed: () {
                    Get.back();
                  },
                  btnColor: AppColor.themeOrangeColor,
                )
              ],
            ),
          );
        },
      );
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  clear() {
    emailController.clear();
    fNameController.clear();
    lNameController.clear();
    passwordController.clear();
    mobileNoController.clear();
    usernameController.clear();
  }
}
