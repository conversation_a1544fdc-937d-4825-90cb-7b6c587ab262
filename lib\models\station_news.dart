import 'dart:convert';

class Station_News {
  String? STN_DESC;
  String? PLACE_DESC;
  String? STN_COOR;
  String? STN_STATUS;

  Station_News({
    required this.STN_DESC,
    required this.PLACE_DESC,
    required this.STN_COOR,
    required this.STN_STATUS,
  });

  Map<String, dynamic> toMap() {
    return {
      'STN_DESC': STN_DESC,
      'PLACE_DESC': PLACE_DESC,
      'STN_COOR': STN_COOR,
      'STN_STATUS': STN_STATUS,
    };
  }

  factory Station_News.fromMap(Map<String, dynamic> map) {
    return Station_News(
      STN_DESC: map['STN_DESC'] ?? '',
      PLACE_DESC: map['PLACE_DESC'] ?? '',
      STN_COOR: map['STN_COOR'] ?? '',
      STN_STATUS: map['STN_STATUS'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Station_News.fromJson(String source) =>
      Station_News.fromMap(json.decode(source));
}