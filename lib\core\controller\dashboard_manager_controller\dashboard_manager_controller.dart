// ignore_for_file: prefer_const_constructors

import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/home_controller/home_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/regDB.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/screen/home_screen/home_screen.dart';
import 'package:waie_app/view/screen/location_screen/locations_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/profile_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/vehicles_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../models/profile.dart';
import '../../../utils/constants.dart';
import '../../../view/screen/balance_screen/balance_screen.dart';
import '../../../view/screen/location_screen/location_screen.dart';
import '../../../view/screen/menu_screen/menu_screen.dart';
import '../../../view/screen/vehicles_screen/my_fleet/final_fleet_screen.dart';
import '../../../view/screen/vehicles_screen/my_fleet/new_fleet_screen.dart';
import '../../../view/screen/vehicles_screen/my_fleet/paginated_fleet_screen.dart';

enum SupportState {
  unknown,
  supported,
  unSupported,
}

class DashboardManagerController extends GetxController {
  HomeController homeController = Get.put(HomeController());
  GlobalKey<FormState> passwordKey = GlobalKey<FormState>();
  final LocalAuthentication auth = LocalAuthentication();
  SupportState supportState = SupportState.unknown;
  List<BiometricType>? availableBiometrics;

  RegisterDatabase db = RegisterDatabase();
  // reference the hive register box
  final _isReg = Hive.box('isReg_DB');
  final _isActivate = Hive.box('isActivate_DB');
  RxInt currantIndex = 0.obs;
  RxList naviBarItemList = [
    {'icon': DefaultImages.homeIcn, 'title': 'Home', 'screen': HomeScreen()}
        .obs,
    /* {
      'icon': DefaultImages.balanceIcn,
      'title': 'Balance',
      'screen': BalanceScreen()
    }.obs,*/
    {
      'icon': DefaultImages.newVehicleIcn, //DefaultImages.vehiclesIcn,
      'title': 'Vehicles',
      'screen': Constants.hasCloseAccountRequest == 'N'
          ? FinalFleetScreen()
          : HomeScreen() //PaginatedDataTableView() / //VehiclesScreen()
    }.obs,
    {
      'icon': DefaultImages.newLocationIcn, //DefaultImages.locationIcn,
      'title': 'Locations',
      'screen': Constants.hasCloseAccountRequest == 'N'
          ? LocationsScreen()
          : HomeScreen()
    }.obs,
    {
      'icon': DefaultImages.menuIcn,
      'title': 'Menu',
      'screen': MenuScreen(),
    }.obs,
  ].obs;

  RxString userID = ''.obs;
  RxString userName = ''.obs;
  RxString emailID = ''.obs;
  RxString accttype = ''.obs;

  void getUpdateMenu() {
    if (Constants.TopUpBtn == 'Y') {
      RxMap<String, Object> newOption = {
        'icon': DefaultImages.newBalanceIcn, //DefaultImages.balanceIcn,
        'title': 'Balance Topup'.tr,
        'screen': Constants.hasCloseAccountRequest == 'N'
            ? BalanceScreen()
            : HomeScreen(),
      }.obs;
      naviBarItemList.insert(1, newOption);
    }
  }

  getUserID() async {
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    userID.value = userData.auCust!.custid!;
    emailID.value = userData.auCust!.emailid;

    if (Constants.IsAr_App == "true" && userData.auCust!.regtype! == 'C') {
      if (userData.auCust!.companynamear! == '' &&
          userData.auCust!.companyname == '') {
        userName.value =
            "${userData.auCust!.firstname!} ${userData.auCust!.lastname!}";
      } else {
        userName.value =
            userData.auCust!.companynamear! ?? userData.auCust!.companyname;
      }
    } else if (userData.auCust!.regtype! == 'I') {
      userName.value =
          "${userData.auCust!.firstname!} ${userData.auCust!.lastname!}";
    } else {
      if (userData.auCust!.companynamear! == '' &&
          userData.auCust!.companyname == '') {
        userName.value =
            "${userData.auCust!.firstname!} ${userData.auCust!.lastname!}";
      } else {
        userName.value =
            userData.auCust!.companyname ?? userData.auCust!.firstname;
      }
    }
    print("userName.value**************************************");
    print(userName.value);
    Constants.custAcctType = userData.auCust!.accttype!;
    Constants.custAcctStatus = userData.auCust!.acctstatus!;
    Constants.custRegType = userData.auCust!.regtype!;
    Constants.custOTHER_BAL = double.parse(userData.auCust!.otherbal!);
    Constants.custB2B_IBAN = userData.auCust!.b2biban!;
    Constants.custsalesrep = userData.auCust!.salesrep!;
    Constants.custIs_partner = userData.auCust!.ispartner!;
    Constants.custIBAN = userData.auCust!.b2biban!;
    Constants.custRegType = userData.auCust!.regtype!;
    Constants.custB2B_BANK = userData.auCust!.b2bbank!;

    // if (Constants.custAcctStatus != "A") {
    //   showDialog(
    //     context: Get.context!,
    //     builder: (context) {
    //       return AlertDialog(
    //         insetPadding: const EdgeInsets.all(16),
    //         contentPadding: const EdgeInsets.all(16),
    //         shape:
    //             RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    //         content: Column(
    //           mainAxisSize: MainAxisSize.min,
    //           children: [
    //             verticalSpace(24),
    //             Text("Please update your profile".tr, style: pBold16),
    //             verticalSpace(12),
    //             Text(
    //               // "Balance TopUp Failed.".tr,
    //               "يرجى تحديث ملف التعريف الخاص بك",
    //               style: pBold16,
    //               textAlign: TextAlign.center,
    //             ),
    //             verticalSpace(24),
    //             CommonButton(
    //               title:
    //                   "Please click here to update your profile\n /برجاء الضغط هنا لتحديث بيانات الحساب",
    //               textColor: AppColor.cWhiteFont,
    //               fontSize: 12,
    //               onPressed: () => Get.to(ProfileScreen()),
    //               btnColor: AppColor.themeOrangeColor,
    //             ),
    //             // verticalSpace(8),
    //             // Container(
    //             //   padding: EdgeInsets.symmetric(vertical: 7, horizontal: 16),
    //             //   decoration: BoxDecoration(
    //             //       color: AppColor.lightOrangeColor,
    //             //       borderRadius: BorderRadius.circular(6)),
    //             //   child: Text(
    //             //       "!  Please click here to update your profile /برجاء الضغط هنا لتحديث بيانات الحساب",
    //             //       style:
    //             //           pRegular12.copyWith(color: AppColor.cDarkOrangeText)),
    //             // ),
    //             verticalSpace(24),
    //           ],
    //         ),
    //       );
    //     },
    //   );
    // } else {
    if (_isReg.get("regUser") == null) {
      if (_isActivate.get("regActivate") == null) {
        //db.createUserInfoData();
        showDialog(
          context: Get.context!,
          barrierDismissible: false,
          builder: (context) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              contentPadding: EdgeInsets.all(24),
              insetPadding: EdgeInsets.all(16),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: AppColor
                              .cWhite, // Set your desired background color here
                          borderRadius: BorderRadius.circular(6.0),
                          border: Border.all(
                              color: AppColor.themeDarkBlueColor, width: 1),
                        ),
                        padding: EdgeInsets.all(5.0),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(6.0),
                          child: Image.asset(
                            Platform.isAndroid || Platform.isWindows
                                ? DefaultImages.fingerprintblack
                                : DefaultImages.faceidblack,
                            width: 33,
                            height: 33,
                            fit: BoxFit.cover,
                            color: AppColor.cDarkBlueFont,
                          ),
                        ),
                      ),
                    ],
                  ),
                  verticalSpace(24),
                  Center(
                      child: Platform.isAndroid || Platform.isWindows
                          ? Text('Activate Fingerprint Login'.tr,
                              style: pBold20, textAlign: TextAlign.center)
                          : Text('Activate Face ID Login'.tr,
                              style: pBold20, textAlign: TextAlign.center)),
                  // verticalSpace(14),
                  // Center(
                  //   child: Platform.isAndroid || Platform.isWindows
                  //       ? Text(
                  //           'Activate Fingerprint login to easily access Aldrees App'
                  //               .tr,
                  //           style: pRegular13,
                  //           textAlign: TextAlign.center)
                  //       : Text(
                  //           'Activate Face ID login to easily access Aldrees App'
                  //               .tr,
                  //           style: pRegular13,
                  //           textAlign: TextAlign.center),
                  // ),
                  verticalSpace(24),
                  Row(
                    children: [
                      Expanded(
                        child: CommonButton(
                          title: "SKIP".tr,
                          onPressed: () {
                            db.createActivate("Y");
                            Get.back();
                          },
                          textColor: AppColor.cDarkBlueFont,
                          btnColor: AppColor.cBackGround,
                          bColor: AppColor.cDarkBlueFont,
                        ),
                      ),
                      horizontalSpace(16),
                      Expanded(
                        child: CommonButton(
                          title: "ACTIVATE".tr,
                          onPressed: () {
                            authenticateWithBiometrics();
                            Get.back();
                          },
                          textColor: AppColor.cWhiteFont,
                          btnColor: AppColor.themeOrangeColor,
                          bColor: AppColor.cTransparent,
                          horizontalPadding: 16,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            );
          },
        );
      }
      //db.createUserInfoData();
      // showDialog(
      //   context: context,
      //   builder: (BuildContext context) {
      //     return ActivationDialog(); // passing value here
      //   },
      // );
    }
    //}
    /*if (Constants.custRegType == "I") {
      userName.value = userData.auCust!.firstnamear!;
      if (userData.auCust!.companynamear == "") {
        userName.value = userData.auCust!.companynamear!;
      }
      userName.value = userData.auCust!.firstnamear!;
    } else if (Constants.custRegType == "C") {
      if (userData.auCust!.companynamear == "") {
        userName.value = userData.auCust!.firstnamear!;
      } else {
        userName.value = userData.auCust!.companynamear!;
      }
    }*/
    print("User name ========${userName.value}");
  }

  Future<void> authenticateWithBiometrics() async {
    try {
      final authenticated = await auth.authenticate(
        localizedReason: 'Authenticate with fingerprint or Face ID',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
          useErrorDialogs: true,
        ),
      );

      // if (!mounted) {
      //   return;
      // }

      if (authenticated) {
        // Navigator.push(
        //     context, MaterialPageRoute(builder: (context) => const Home()));

        final regUser = _isReg.get('regUser');
        print('List is $regUser');
        print('username is ${regUser?['username']}');
        print('password is ${regUser?['password']}');
        print('status is ${regUser?['status']}');
        showDialog(
          context: Get.context!,
          barrierDismissible: false,
          builder: (context) {
            return AlertDialog(
              title: Center(
                  child: Text(
                'Confirmation'.tr,
                style: pBold18,
              )),
              content: Obx(() => Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: Text(
                          'Please enter password to verify your account'.tr,
                          style: pRegular14,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      verticalSpace(10),
                      Form(
                        key: passwordKey,
                        child: CommonTextField(
                            controller: homeController.checkPasswordController,
                            labelText: 'Password'.tr,
                            hintText: "Please enter here".tr,
                            obscuringCharacter: '*',
                            filled: true,
                            fillColor: AppColor.cFilled,
                            obscureText: homeController.isCheckPass.value,
                            validator: (value) {
                              return Validator.validatePassword(value);
                            },
                            suffix: GestureDetector(
                                onTap: () {
                                  homeController.isCheckPass.value =
                                      !homeController.isCheckPass.value;
                                },
                                child: assetSvdImageWidget(
                                    image: homeController.isCheckPass.value
                                        ? DefaultImages.eyeOffIcn
                                        : DefaultImages.eyeIcn))),
                      ),
                      verticalSpace(8),
                    ],
                  )),
              actions: [
                CommonButton(
                    title: "Confirm".tr,
                    onPressed: () async {
                      Loader.showLoader();
                      print(homeController.checkPasswordController.text);
                      homeController.checkUserPassword();
                    },
                    btnColor: AppColor.themeOrangeColor),
              ],
            );
          },
        );
        // login(regUser?['username'], regUser?['password']);
      }
    } on PlatformException catch (e) {
      print(e);
      return;
    }
  }

  Widget validateTextRow({required String image, required String title}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          assetSvdImageWidget(image: image),
          horizontalSpace(13),
          Expanded(
            child: Text(
              title,
              style: pRegular13,
              maxLines: 3,
            ),
          )
        ],
      ),
    );
  }

  @override
  void onInit() {
    super.onInit();
    getUserID();
    getUpdateMenu();
    auth.isDeviceSupported().then(
          (bool isSupported) => () => supportState =
              isSupported ? SupportState.supported : SupportState.unSupported,
        );
  }
}
