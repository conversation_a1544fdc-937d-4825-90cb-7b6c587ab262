// ignore_for_file: avoid_print

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../view/widget/common_snak_bar_widget.dart';

class RefundsController extends GetxController {
  RxBool isOrderRefund = true.obs;
  RxBool isTopUpRefund = false.obs;
  RxBool isRefundHistory = false.obs;
  RxList orderRefundList = [
    {
      "value": false.obs,
      "code": "#***********",
      "status": 'New',
      "orderType": "Tag",
      "plate": "2876ZTO",
      "vehicleType": "Car",
      "orderDate": "06.23.2023",
      "amount": "100.00 SAR",
      "vat": "10.00 SAR",
    },
    {
      "value": false.obs,
      "code": "#***********",
      "status": 'New',
      "orderType": "Tag",
      "plate": "-",
      "vehicleType": "-",
      "orderDate": "06.23.2023",
      "amount": "100.00 SAR",
      "vat": "10.00 SAR",
    },
    {
      "value": false.obs,
      "code": "#***********",
      "status": 'Confirmed',
      "orderType": "Tag",
      "plate": "-",
      "vehicleType": "-",
      "orderDate": "06.23.2023",
      "amount": "100.00 SAR",
      "vat": "10.00 SAR",
    },
    {
      "value": false.obs,
      "code": "#***********",
      "status": 'Confirmed',
      "orderType": "Tag",
      "plate": "-",
      "vehicleType": "-",
      "orderDate": "06.23.2023",
      "amount": "100.00 SAR",
      "vat": "10.00 SAR",
    },
  ].obs;

  RxList selectedOrderRefundTagList =
      [].obs; // added by fuzail for tags screen 5-13-2025
  RxList selectedOrderRefundCardList =
      [].obs; //added by fuzail for smart card screen 5-13-2025

  RxList selectedTopupList =
      [].obs; //added by fuzail For Topup screen 5-13-2025
  RxList selectedReservedList =
      [].obs; // added by fuzail For Reserved screen 5-13-2025

  RxList selectedOrderRefundList = [].obs;
//  RxList selectedOrderRefundTagList = [].obs;
  //added by fuzail
  // RxList selectedOrderRefundTagList = [].obs; 
  // RxList selectedOrderRefundCardList = [].obs;
  // /// 
  // /// 

  // RxList selectedOrderRefundList = [].obs;

  //RxList selectedOrderRefundTagList = [].obs; commented by fuzail
  RxList selectedOrderRefundTagSerialIDList = [].obs;
  RxList selectedOrderRefundTagOrderIDList = [].obs;
  RxList selectedOrderRefundCardSerialIDList = [].obs;
  RxList selectedOrderRefundCardOrderIDList = [].obs;

//added by fuzail
  RxList selectedOrderRefundCurrentOrderIDList = [].obs;

  RxList selectedOrderRefundCurrentSerialIDList = [].obs;
  RxList selectedOrderRefundReservedOrderIDList = [].obs;
  RxBool isBalance = true.obs;
  RxBool isDeposit = false.obs;
  RxString companyReg = ''.obs;
  RxString bankLetter = ''.obs;
  final ImagePicker picker = ImagePicker();
  File? postImage;

  pickImage(ImageSource imageSource) async {
    XFile? pickedImage = await picker.pickImage(source: imageSource);
    if (pickedImage != null) {
      print(pickedImage);
      postImage = File(pickedImage.path);
      print("==== f $postImage");
      print("==== f ${File(pickedImage.path).path}");
      return File(pickedImage.path).path;
    } else {
      commonToast('Image Not Pick');
    }
  }

  List selectBankList = ["RIYADH BANK / بنك الريا"];
  RxString selectedBank = 'RIYADH BANK / بنك الريا'.obs;
  List ibanList = ["SA", "PA"];
  RxString selectedIBAN = 'SA'.obs;
  RxList refundHistoryList = [
    {
      "code": "#***********",
      "status": 'New',
      "orderType": "Tag",
      "plate": "2876ZTO",
      "vehicleType": "Car",
      "orderDate": "06.23.2023",
      "amount": "100.00 SAR",
      "vat": "10.00 SAR",
    },
    {
      "code": "#***********",
      "status": 'New',
      "orderType": "Tag",
      "plate": "-",
      "vehicleType": "-",
      "orderDate": "06.23.2023",
      "amount": "100.00 SAR",
      "vat": "10.00 SAR",
    },
    {
      "code": "#***********",
      "status": 'Confirmed',
      "orderType": "Tag",
      "plate": "-",
      "vehicleType": "-",
      "orderDate": "06.23.2023",
      "amount": "100.00 SAR",
      "vat": "10.00 SAR",
    },
    {
      "code": "#***********",
      "status": 'Confirmed',
      "orderType": "Tag",
      "plate": "-",
      "vehicleType": "-",
      "orderDate": "06.23.2023",
      "amount": "100.00 SAR",
      "vat": "10.00 SAR",
    },
  ].obs;
  RxList itemList = [].obs;
  var searchController = TextEditingController().obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    searchController.value = TextEditingController();
  }
}
