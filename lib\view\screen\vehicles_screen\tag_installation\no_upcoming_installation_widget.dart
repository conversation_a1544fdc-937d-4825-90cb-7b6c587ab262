import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class NoUpcomingWidgetScreen extends StatelessWidget {
  NoUpcomingWidgetScreen({Key? key}) : super(key: key);
  VehicleController vehicleController = Get.put(VehicleController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        verticalSpace(66),
        assetSvdImageWidget(image: DefaultImages.upcomingImage),
        verticalSpace(32),
        Text(
          "You don't have any upcoming installations".trr,
          style: pBold18,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 29.5),
          child: Text(
            "When you order new tags, you can schedule installations here.".trr,
            style: pRegular13,
            textAlign: TextAlign.center,
          ),
        ),
        CommonButton(
          title: 'NEXT'.trr,
          onPressed: () {
            vehicleController.isMyFleet.value = true;
            vehicleController.isTagInstallation.value = false;
            vehicleController.isScheduleInstallation.value = true;
          },
          btnColor: AppColor.themeOrangeColor,
        )
      ],
    );
  }
}
