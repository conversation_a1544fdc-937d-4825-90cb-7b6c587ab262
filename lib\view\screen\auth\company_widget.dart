// ignore_for_file: must_be_immutable, prefer_interpolation_to_compose_strings

import 'dart:developer';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:waie_app/core/controller/auth/auth_controller.dart';
import 'package:waie_app/core/controller/signup_controller/signup_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class CompanyWidget extends StatefulWidget {
  final AuthController authController;

  const CompanyWidget({super.key, required this.authController});

  @override
  State<CompanyWidget> createState() => _CompanyWidgetState();
}

class _CompanyWidgetState extends State<CompanyWidget> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  SignupController signupController = Get.put(SignupController());

  String pattern =
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&-_])[A-Za-z\d@$!%*?&-_]{8,}$';
  RxBool isPatternMatch = false.obs;
  RxBool isPasswordLength = false.obs;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Obx(() {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                          flex: 1,
                          child: CommonIconButton(
                            title: 'Private'.trr,
                            iconData: widget.authController.isPrivate.value
                                ? DefaultImages.checkIcn
                                : null,
                            btnColor: widget.authController.isPrivate.value
                                ? AppColor.themeBlueColor
                                : AppColor.cLightGrey,
                            textColor: widget.authController.isPrivate.value
                                ? AppColor.cWhiteFont
                                : AppColor.cText,
                            onPressed: () {
                              widget.authController.isPrivate.value = true;
                              widget.authController.isGovernment.value = false;
                              setState(() {
                                signupController.compTypeController.text = "P";
                              });
                            },
                          )),
                      horizontalSpace(16),
                      Expanded(
                          child: CommonIconButton(
                        title: 'Government'.trr,
                        iconData: widget.authController.isGovernment.value
                            ? DefaultImages.checkIcn
                            : null,
                        btnColor: widget.authController.isGovernment.value
                            ? AppColor.themeBlueColor
                            : AppColor.cLightGrey,
                        textColor: widget.authController.isGovernment.value
                            ? AppColor.cWhiteFont
                            : AppColor.cText,
                        onPressed: () {
                          widget.authController.isPrivate.value = false;
                          widget.authController.isGovernment.value = true;
                          setState(() {
                            signupController.compTypeController.text = "G";
                          });
                        },
                      ))
                    ],
                  ),
                  verticalSpace(16),
                  Text(
                    "Fill the data to register your company".trr,
                    style: pRegular13,
                  ),
                  verticalSpace(16),
                  CommonTextField(
                    controller: signupController.fNameController,
                    labelText: 'First name'.trr + '*',
                    hintText: "First name".trr,
                    validator: (value) {
                      return Validator.validateName(value, "First name".trr);
                    },
                  ),
                  verticalSpace(16),
                  CommonTextField(
                    controller: signupController.lNameController,
                    labelText: 'Last name'.trr + '*',
                    hintText: "Last name".trr,
                    validator: (value) {
                      return Validator.validateName(value, "Last name".trr);
                    },
                  ),
                  /* verticalSpace(16),
                  CommonTextField(
                    controller: signupController.usernameController,
                    labelText: 'Username'.trr + '*',
                    hintText: "Username".trr,
                    validator: (value) {
                      return Validator.validateName(value, "Username".trr);
                    },
                  ),*/
                  verticalSpace(16),
                  CommonTextField(
                    controller: signupController.emailController,
                    keyboardType: TextInputType.emailAddress,
                    labelText: 'User email ID'.trr,
                    hintText: "Email address".trr,
                    validator: (value) {
                      return Validator.validateEmail(value);
                    },
                  ),
                  verticalSpace(16),
                  /* CommonTextField(
                    controller: signupController.mobileNoController,
                    keyboardType: TextInputType.number,
                    labelText: 'Mobile number'.trr + "*",
                    hintText: "Mobile number".trr,
                    maxLength: 12,
                    validator: (value) {
                      return Validator.validateMobile(value);
                    },
                  ),*/
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Mobile number'.trr + "*",
                        style: pRegular12.copyWith(color: AppColor.cLabel),
                        textAlign: TextAlign.start,
                      ),
                    ],
                  ),
                  verticalSpace(8),
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: Container(
                      height: 44,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: AppColor.cBorder,
                        border: Border.all(
                          color: AppColor.cBorder,
                        ),
                      ),
                      child: InternationalPhoneNumberInput(
                        onInputChanged: (PhoneNumber number) {
                          signupController.isoCode.value = number.isoCode!;
                          signupController.mobileNoController.text =
                              number.phoneNumber.toString().replaceAll("+", "");
                          print("Entered Number=======" +
                              number.phoneNumber
                                  .toString()
                                  .replaceAll("+", ""));
                        },
                        // maxLength:9,
                        /* onInputValidated: (bool value) {
                                    print(value);
                                  },*/
                        cursorColor: AppColor.cHintFont,
                        selectorConfig: const SelectorConfig(
                          selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                          leadingPadding: 16,
                          setSelectorButtonAsPrefixIcon: true,
                        ),
                        ignoreBlank: false,
                        // autoValidateMode: AutovalidateMode.disabled,
                        textStyle: pRegular14.copyWith(
                          color: AppColor.cLabel,
                        ),
                        initialValue: PhoneNumber(
                            isoCode: signupController.isoCode.value,
                            dialCode: signupController.isoCode.value),
                        inputBorder: const OutlineInputBorder(),
                        keyboardAction: TextInputAction.done,
                        scrollPadding: EdgeInsets.zero,
                        selectorTextStyle: pRegular14.copyWith(
                            color: AppColor.cLabel, fontSize: 14),
                        textAlign: TextAlign.start,
                        textAlignVertical: TextAlignVertical.center,
                        maxLength: 11,
                        inputDecoration: InputDecoration(
                          contentPadding:
                              const EdgeInsets.only(left: 16, bottom: 8),
                          isDense: true,
                          prefixText: "|  ",
                          prefixStyle:
                              TextStyle(fontSize: 30, color: AppColor.cBorder),
                          counterText: '',
                          hintText: " " + 'Please enter here'.trr,
                          counterStyle: const TextStyle(fontSize: 0, height: 0),
                          errorStyle: const TextStyle(fontSize: 0, height: 0),
                          hintStyle: pRegular14.copyWith(
                            color: AppColor.cHintFont,
                          ),
                          border: InputBorder.none,
                          filled: true,
                          fillColor: AppColor.cFilled,
                        ),
                        /*  onSaved: (PhoneNumber number) {
                                  },*/
                      ),
                    ),
                  ),
                  verticalSpace(8),
                  Center(
                    child: Text(
                      "We'll send a confirmation code to your email to finish your registration."
                          .trr,
                      style: pRegular12.copyWith(color: AppColor.cLabel),
                      textAlign: TextAlign.start,
                    ),
                  ),
                  verticalSpace(16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      verticalSpace(15),
                      Text(
                        "Your password should be...",
                        style: pSemiBold14,
                      ),
                      verticalSpace(8),
                      validateTextRow(
                          image: isPasswordLength.value
                              ? DefaultImages.validateIcn
                              : DefaultImages.notValidateIcn,
                          title: "At least 8 characters long.".trr),
                      validateTextRow(
                          image: isPatternMatch.value
                              ? DefaultImages.validateIcn
                              : DefaultImages.notValidateIcn,
                          title:
                              "A combination of uppercase letters, lowercase letters, numbers, and symbols."
                                  .trr),
                      // Text(
                      //   "• " +
                      //       "At least 8 characters long.".trr +
                      //       "\n• " +
                      //       "A combination of uppercase letters, lowercase letters, numbers, and symbols."
                      //           .trr,
                      //   style: pRegular12,
                      // ),
                    ],
                  ),
                  verticalSpace(8),
                  CommonTextField(
                      controller: signupController.passwordController,
                      labelText: 'Password'.trr + "*",
                      hintText: "**********",
                      obscureText: widget.authController.isPassword.value,
                      obscuringCharacter: '*',
                      validator: (value) {
                        return Validator.validatePassword(value);
                      },
                      onChanged: (value) {
                        RegExp regex = RegExp(pattern);
                        if (regex.hasMatch(value)) {
                          isPatternMatch.value = true;
                        } else {
                          isPatternMatch.value = false;
                        }
                        if (value.length > 7) {
                          isPasswordLength.value = true;
                        } else {
                          isPasswordLength.value = false;
                        }
                      },
                      suffix: GestureDetector(
                          onTap: () {
                            widget.authController.isPassword.value =
                                !widget.authController.isPassword.value;
                          },
                          child: assetSvdImageWidget(
                              image: widget.authController.isPassword.value
                                  ? DefaultImages.eyeOffIcn
                                  : DefaultImages.eyeIcn))),
                  verticalSpace(16),
                  CommonTextField(
                    controller:
                        widget.authController.cpConfirmPasswordController,
                    labelText: 'Confirm password'.trr + '*',
                    hintText: "**********",
                    obscureText: widget.authController.isConfirmPass.value,
                    obscuringCharacter: '*',
                    validator: (value) {
                      return Validator.validateConfirmPassword(
                          value, signupController.passwordController.text);
                    },
                    suffix: GestureDetector(
                        onTap: () {
                          widget.authController.isConfirmPass.value =
                              !widget.authController.isConfirmPass.value;
                        },
                        child: assetSvdImageWidget(
                            image: widget.authController.isConfirmPass.value
                                ? DefaultImages.eyeOffIcn
                                : DefaultImages.eyeIcn)),
                  ),
                  verticalSpace(16),
                  Row(
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: Checkbox(
                          value: widget.authController.isSelectedPolicy.value,
                          onChanged: (value) {
                            widget.authController.isSelectedPolicy.value =
                                value!;
                          },
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4)),
                          activeColor: AppColor.themeBlueColor,
                        ),
                      ),
                      horizontalSpace(10),
                      Expanded(
                        child: Text.rich(
                          TextSpan(
                            text: 'I agree to the'.trr + ' ',
                            style: pRegular14,
                            children: <TextSpan>[
                              TextSpan(
                                text: 'Terms and Condition'.trr,
                                style:
                                    pBold14.copyWith(color: AppColor.cBlueFont),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    showDialog(
                                      barrierDismissible: false,
                                      context: Get.context!,
                                      builder: (context) {
                                        return AlertDialog(
                                          contentPadding:
                                              const EdgeInsets.all(16),
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12)),
                                          insetPadding:
                                              const EdgeInsets.all(25),
                                          content: termsAndCondition(),
                                        );
                                      },
                                    );
                                  },
                              ),
                              // TextSpan(
                              //   text: " " + "and".trr + " ",
                              //   style: pRegular14,
                              // ),
                              // TextSpan(
                              //   text: 'Privacy Policy'.trr,
                              //   style:
                              //       pBold14.copyWith(color: AppColor.cBlueFont),
                              //   recognizer: TapGestureRecognizer()
                              //     ..onTap = () {},
                              // ),
                            ],
                          ),
                          maxLines: 2,
                          softWrap: true,
                        ),
                      )
                    ],
                  ),
                  verticalSpace(16),
                  CommonButton(
                    title: 'Continue'.trr,
                    onPressed: () {
                      print("Clicked");
                      //if (_formKey.currentState!.validate()) {
                      print("Clicked2");
                      if (widget.authController.isSelectedPolicy.value ==
                          true) {
                        print("isSelectedPolicy >>> TRUE");
                        signupController.registerWithEmail(false);
                      } else {
                        commonToast("Please select policy".trr);
                      }
                      //}
                    },
                    // onPressed: () => signupController.registerWithEmail(
                    //     authController.isIndividual.value,
                    //     authController.isCompany.value),

                    //**** PREV IMPLEMENTED
                    // onPressed: () {
                    //   if (formKey.currentState!.validate()) {
                    //     log('done');
                    //     if (authController.isSelectedPolicy.value == true) {
                    //       Get.to(() => OtpScreen(
                    //             authController: authController,
                    //             isLogin: true,
                    //           ));
                    //       authController.clear();
                    //     } else {
                    //       commonToast("Please select policy".trr);
                    //     }
                    //   }
                    // },
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget validateTextRow({required String image, required String title}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          assetSvdImageWidget(image: image),
          horizontalSpace(13),
          Expanded(
            child: Text(
              title,
              style: pRegular13,
              maxLines: 3,
            ),
          )
        ],
      ),
    );
  }

  Widget termsAndCondition() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
              child: Text(
            "Terms and Condition".trr,
            style: pBold20,
          )),
          verticalSpace(16),
          Text(
            "1. WAIE services are available only at Aldrees fuel stations that are listed in our website."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "2. The customer may cancel this agreement at any time and stop the services on condition that the customer must inform Aldrees Company in writing before 48 hours and handing over all the TAGS and the SMART CARDS. The customer will be refunded the remaining balance in his account."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "3. The customer assumes responsibility for all the information that he entered into the system."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "4. The tags are the property of the customer and under his responsibility and can't be transferred to another vehicle and the customer should instruct his driver not to tamper with the tags."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "5. Prices are subject to change by the government and the prices will change automatically in the WAIE system by the same amount."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "6. Upon registration in the WAIE system the customer will get an account number thru which he can manage his account from anywhere in the world with internet access and he will be responsible for all transactions in his account."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "7. The customer will get a 2-year free warranty period against manufacturing defects, starting from the date of installation in the vehicle. The warranty shall not cover any misuse by the users."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "8. Fuel prices in stations outside city limits are according to government regulations."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "9. The Electronic tag is Free, if the balance is charged 300 SAR per tag, noting that the balance can only be used after installing the tag."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "10. In case of the electronic tag is canceled before 6 months from the date of installation, the value of the tag 100 SAR (not include vat) will be deducted from account balance."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "11. In case of the electronic tag not installed within 90 days from the date of payment order, the tag order will be auto cancel."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "12. The price of the replaced tag is 50 SAR (without vat).".trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(5),
          Text(
            "13. Upon signing this agreement and acceptance to use WAIE system, the customer declares that he agrees and accepts the terms and conditions of the agreement between him & Aldrees Petroleum & Transport Services Co."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(16),
          CommonButton(
            title: "Close".trr,
            onPressed: () {
              Get.back();
            },
            btnColor: AppColor.themeOrangeColor,
            horizontalPadding: 16,
          ),
        ],
      ),
    );
  }
}
