import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/servicecount.dart';
import '../../../utils/api_endpoints.dart';

class ServiceCountController extends GetxController {
  // bool _serviceCalled = true;
  // Future<List<ServiceCountModel>> fetchServiceCount() async {
  //   if (_serviceCalled) {
  //     print("fetchServiceCount==============");
  //   }
  //   var client = http.Client();
  //   SharedPreferences sharedUser = await SharedPreferences.getInstance();
  //   var userid = sharedUser.getString('userid');
  //   List<ServiceCountModel> statusCount = [];
  //   if (_serviceCalled) {
  //     try {
  //       var response = await client.post(
  //           Uri.parse(ApiEndPoints.baseUrl +
  //               ApiEndPoints.authEndpoints.getServiceCount),
  //           // body: {"userId": userid});
  //           body: {"userId": "000038345"});
  //       // List result = jsonDecode(response.body);
  //       _serviceCalled = false;
  //       print(jsonDecode(response.body));
  //       if (response.statusCode == 200) {
  //         statusCount = parseServiceCount(jsonDecode(response.body)).toList();
  //       } else {
  //         ServiceCountModel countModel = ServiceCountModel(
  //             ActiveTags: "14", ActiveCards: "0", TagsToInstall: "5");
  //         statusCount.add(countModel);
  //       }
  //     } catch (e) {
  //       log("ServiceCounterController $e");
  //       return [];
  //     }
  //   }
  //   /* ServiceCountModel countModel=new ServiceCountModel(ActiveTags: "14", ActiveCards: "0", TagsToInstall: "5");
  //     statusCount.add(countModel);*/
  //   //print("Service Count=============="+statusCount.toString());
  //   return statusCount;
  // }

  List<ServiceCountModel> parseServiceCount(String jsonString) {
    List<dynamic> parsedJson = jsonDecode(jsonString);

    List<ServiceCountModel> users =
        parsedJson.map((json) => ServiceCountModel.fromJson(json)).toList();

    return users;
  }

  Future<List<ServiceCountModel>> fetchServiceCount() async {
    var client = http.Client();
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    var userid = sharedUser.getString('userid');
    List<ServiceCountModel> statusCounts = [];
    print("USER ID============ $userid");
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getServiceCount),
           body: {"userId": userid});
         // body: {"userId": "000038345"});
      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        ServiceCountModel count =
            ServiceCountModel.fromMap(result[i] as Map<String, dynamic>);
        print("activeTags ================== ${count.activeTags}");
        print("activeCards ================== ${count.activeCards}");
        print("tagsToInstall ================== ${count.tagsToInstall}");
        statusCounts.add(count);
      }

      return statusCounts;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
