// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'dart:convert';

import 'package:chips_choice/chips_choice.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_controller.dart';
import 'package:waie_app/core/controller/menu_controller/setting_controller/close_account_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/new_user_screen.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:file_picker/file_picker.dart';

import '../../../../core/controller/menu_controller/fleet_structure_controller/edit_vehicle_details_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/overview_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/view_vehicle_details_controller.dart';
import '../../../../core/controller/vehicle_controller/add_pure_dc_vehicle_controller.dart';
import '../../../../utils/validator.dart';
import '../../../widget/common_appbar_widget.dart';
import '../../../widget/common_button.dart';
import '../../../widget/loading_widget.dart';
import '../../../widget/common_drop_down_widget.dart';
import '../../dashboard_manager/dashboard_manager.dart';

class CloseAccountScreen extends StatefulWidget {
  const CloseAccountScreen({super.key});

  @override
  State<CloseAccountScreen> createState() => _CloseAccountScreenState();
}

class _CloseAccountScreenState extends State<CloseAccountScreen> {
  FilePickerResult? commercialRegistration;
  FilePickerResult? bankLetter;
  FilePickerResult? nationalID;
  AddPureDcVehicleController addPureDcVehicleController =
      Get.put(AddPureDcVehicleController());
  EditVehicleDetailsController editVehicleDetailsController =
      Get.put(EditVehicleDetailsController());
  CloseAccountController closeAccountController =
      Get.put(CloseAccountController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              simpleMyAppBar(
                  title: "",
                  onTap: () {
                    Get.back();
                    addPureDcVehicleController.tagLists.clear();
                  },
                  backString: "Back".trr),
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Obx(() {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Close your account".trr,
                            style: pBold20,
                          ),
                          // addDcTitleRowWidget(
                          //   title: "Close your account".trr,
                          //   isSelected: addPureDcVehicleController
                          //       .isVehicleDetail.value,
                          //   onTap: () {
                          //     addPureDcVehicleController.isVehicleDetail.value =
                          //         !addPureDcVehicleController
                          //             .isVehicleDetail.value;
                          //   },
                          // ),
                          verticalSpace(16),
                          //addPureDcVehicleController.isVehicleDetail.value
                          Container(
                            padding: const EdgeInsets.only(right: 16, left: 16),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Tell us why you're leaving".trr,
                                  style: pRegular13,
                                ),
                                verticalSpace(6),
                                DropdownButtonFormField(
                                  items: closeAccountController.closeAccntList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    print(value);
                                    closeAccountController.reasonLeaveController
                                        .text = value.toString();
                                    // PaginatedController.selectedDiv.value =
                                    //     value.toString();
                                    // reportController.loadBranch(value.toString());
                                    //vehicleList.value = value.toString();
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: "Tell us why you're leaving".trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                              ],
                            ),
                          ),
                          horizontalDivider(),
                          verticalSpace(16),
                          Text(
                            "Confirm your credentials".trr,
                            style: pBold14,
                          ),
                          verticalSpace(16),
                          Padding(
                            padding: const EdgeInsets.only(right: 16, left: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Bank letter".trr,
                                  style: pRegular12,
                                ),
                                verticalSpace(6),
                                GestureDetector(
                                  child: Container(
                                    height: 44,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(6),
                                        border: Border.all(
                                            color: AppColor.cBorder)),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 6,
                                    ),
                                    child: Row(
                                      children: [
                                        Container(
                                          height: 32,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            color: AppColor.themeOrangeColor,
                                          ),
                                          padding: EdgeInsets.only(
                                              right: 12, left: 8),
                                          child: Row(
                                            children: [
                                              assetSvdImageWidget(
                                                image: DefaultImages.fileIcn,
                                                colorFilter: ColorFilter.mode(
                                                    AppColor.cWhite,
                                                    BlendMode.srcIn),
                                              ),
                                              horizontalSpace(8),
                                              Text(
                                                "Choose file".trr,
                                                style: pRegular14.copyWith(
                                                    color: AppColor.cWhite),
                                              ),
                                            ],
                                          ),
                                        ),
                                        horizontalSpace(8),
                                        Expanded(
                                          child: Text(
                                            closeAccountController
                                                    .bankLetter.isEmpty
                                                ? "No file chosen".trr
                                                : closeAccountController
                                                    .bankLetter.value
                                                    .split("/")
                                                    .last,
                                            style: pRegular14.copyWith(
                                              color: AppColor.cDarkGreyFont,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  onTap: () async {
                                    bankLetter =
                                        await FilePicker.platform.pickFiles(
                                      type: FileType.custom,
                                      allowedExtensions: ['jpg', 'pdf', 'doc'],
                                      withReadStream: true,
                                    );
                                    if (bankLetter == null) {
                                      print("No file selected");
                                    } else {
                                      setState(() {
                                        for (var element in bankLetter!.files) {
                                          print(element.name);
                                          closeAccountController
                                              .bankLetter.value = element.name;
                                        }
                                      });
                                    }
                                  },
                                ),
                                verticalSpace(16),
                                if (Constants.custRegType == "C")
                                  Text(
                                    "Commercial Registration".trr,
                                    style: pRegular12,
                                  ),
                                if (Constants.custRegType == "C")
                                  verticalSpace(6),
                                if (Constants.custRegType == "C")
                                  GestureDetector(
                                    child: Container(
                                      height: 44,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                              color: AppColor.cBorder)),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 6,
                                      ),
                                      child: Row(
                                        children: [
                                          Container(
                                            height: 32,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              color: AppColor.themeOrangeColor,
                                            ),
                                            padding: EdgeInsets.only(
                                                right: 12, left: 8),
                                            child: Row(
                                              children: [
                                                assetSvdImageWidget(
                                                  image: DefaultImages.fileIcn,
                                                  colorFilter: ColorFilter.mode(
                                                      AppColor.cWhite,
                                                      BlendMode.srcIn),
                                                ),
                                                horizontalSpace(8),
                                                Text(
                                                  "Choose file".trr,
                                                  style: pRegular14.copyWith(
                                                      color: AppColor.cWhite),
                                                ),
                                              ],
                                            ),
                                          ),
                                          horizontalSpace(8),
                                          Expanded(
                                            child: Text(
                                              closeAccountController
                                                      .companyReg.isEmpty
                                                  ? "No file chosen".trr
                                                  : closeAccountController
                                                      .companyReg.value
                                                      .split("/")
                                                      .last,
                                              style: pRegular14.copyWith(
                                                color: AppColor.cDarkGreyFont,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    onTap: () async {
                                      //refundsController.companyReg.value =
                                      //await refundsController.pickImage(ImageSource.gallery);
                                      commercialRegistration =
                                          await FilePicker.platform.pickFiles(
                                        type: FileType.custom,
                                        allowedExtensions: [
                                          'jpg',
                                          'pdf',
                                          'doc'
                                        ],
                                        withReadStream: true,
                                      );
                                      if (commercialRegistration == null) {
                                        print("No file selected");
                                      } else {
                                        setState(() {
                                          for (var element
                                              in commercialRegistration!
                                                  .files) {
                                            print(element.name);
                                            closeAccountController.companyReg
                                                .value = element.name;
                                          }
                                        });
                                      }
                                    },
                                  ),
                                if (Constants.custRegType == "I")
                                  Text(
                                    "National ID".trr,
                                    style: pRegular12,
                                  ),
                                if (Constants.custRegType == "I")
                                  verticalSpace(6),
                                if (Constants.custRegType == "I")
                                  GestureDetector(
                                    child: Container(
                                      height: 44,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                              color: AppColor.cBorder)),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 6,
                                      ),
                                      child: Row(
                                        children: [
                                          Container(
                                            height: 32,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              color: AppColor.themeOrangeColor,
                                            ),
                                            padding: EdgeInsets.only(
                                                right: 12, left: 8),
                                            child: Row(
                                              children: [
                                                assetSvdImageWidget(
                                                  image: DefaultImages.fileIcn,
                                                  colorFilter: ColorFilter.mode(
                                                      AppColor.cWhite,
                                                      BlendMode.srcIn),
                                                ),
                                                horizontalSpace(8),
                                                Text(
                                                  "Choose file".trr,
                                                  style: pRegular14.copyWith(
                                                      color: AppColor.cWhite),
                                                ),
                                              ],
                                            ),
                                          ),
                                          horizontalSpace(8),
                                          Expanded(
                                            child: Text(
                                              closeAccountController
                                                      .nationalid.isEmpty
                                                  ? "No file chosen".trr
                                                  : closeAccountController
                                                      .nationalid.value
                                                      .split("/")
                                                      .last,
                                              style: pRegular14.copyWith(
                                                color: AppColor.cDarkGreyFont,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    onTap: () async {
                                      //refundsController.companyReg.value =
                                      //await refundsController.pickImage(ImageSource.gallery);
                                      nationalID =
                                          await FilePicker.platform.pickFiles(
                                        type: FileType.custom,
                                        allowedExtensions: [
                                          'jpg',
                                          'pdf',
                                          'doc'
                                        ],
                                        withReadStream: true,
                                      );
                                      if (nationalID == null) {
                                        print("No file selected");
                                      } else {
                                        setState(() {
                                          for (var element
                                              in nationalID!.files) {
                                            print(element.name);
                                            closeAccountController.nationalid
                                                .value = element.name;
                                          }
                                        });
                                      }
                                    },
                                  ),
                              ],
                            ),
                          ),
                          verticalSpace(16),
                          horizontalDivider(),
                          verticalSpace(16),
                          Padding(
                            padding: const EdgeInsets.only(right: 16, left: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CommonTextField(
                                  controller: closeAccountController
                                      .accountHolderNameController,
                                  labelText: "Accountholder Name".trr,
                                  hintText: 'Please enter full name here...'.trr,
                                ),
                                verticalSpace(16),
                                Text(
                                  "Select Bank".trr,
                                  style: pRegular13,
                                ),
                                verticalSpace(6),
                                DropdownButtonFormField(
                                  items: closeAccountController.bankList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    print(value);
                                    closeAccountController.bankController.text =
                                        value.toString();
                                    // PaginatedController.selectedDiv.value =
                                    //     value.toString();
                                    // reportController.loadBranch(value.toString());
                                    //vehicleList.value = value.toString();
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: "Select Bank".trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      closeAccountController.iBanNoController,
                                  labelText: "IBAN (SA Only)".trr,
                                  hintText:
                                      'Enter your IBAN Account # (SA00 0000 0000 0000 0000 0000)'
                                          .trr,
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      closeAccountController.remarksController,
                                  labelText: "Remarks".trr,
                                  hintText: "Please enter here...".trr,
                                  maxLines: 5,
                                  validator: (value) {
                                    return Validator.validateRequired(value);
                                  },
                                ),
                              ],
                            ),
                          )
                        ],
                      );
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(color: AppColor.cLightGrey),
          child: Row(
            children: [
              // Expanded(
              //   child: CommonButton(
              //     title: 'Cancel'.trr,
              //     onPressed: () {
              //       Get.back();
              //     },
              //     textColor: AppColor.cText,
              //     btnColor: AppColor.cBackGround,
              //   ),
              // ),
              // horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Submit Request'.trr,
                  onPressed: () {
                    // Loader.showLoader();
                    // Future.delayed(const Duration(seconds: 3), () {
                    //   Loader.hideLoader();
                    //   Get.back();
                    // });
                    if (bankLetter == null) {
                      commonToast('Please Upload Bank Letter');
                    } else if (Constants.custRegType == "C" &&
                        commercialRegistration == null) {
                      commonToast('Please Upload Company Registration');
                    } else if (Constants.custRegType == "I" &&
                        nationalID == null) {
                      commonToast('Please Upload National ID');
                    } else {
                      PlatformFile bankletter = bankLetter!.files.first;
                      PlatformFile docfile = Constants.custRegType == "I"
                          ? nationalID!.files.first
                          : commercialRegistration!.files.first;
                      // var bankletter = bankLetter;
                      // var companyregistration = companyRegistration;
                      // submitOrderRefundServiceController.submitOrderRefundFormQ(
                      //     bankletter, companyregistration);
                      showDialog(
                        barrierDismissible: false,
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            contentPadding: EdgeInsets.all(24),
                            insetPadding: EdgeInsets.all(16),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    GestureDetector(
                                        onTap: () {
                                          Get.back();
                                        },
                                        child: assetSvdImageWidget(
                                            image: DefaultImages.cancelIcn)),
                                  ],
                                ),
                                verticalSpace(24),
                                Center(
                                  child: Text(
                                      "Are you sure you want proceed?".trr,
                                      style: pBold20,
                                      textAlign: TextAlign.center),
                                ),
                                verticalSpace(34),
                                Row(
                                  children: [
                                    Expanded(
                                      child: CommonButton(
                                        title: "OK".trr,
                                        onPressed: () {
                                          Get.back();
                                        },
                                        textColor: AppColor.cDarkBlueFont,
                                        btnColor: AppColor.cBackGround,
                                        bColor: AppColor.cDarkBlueFont,
                                      ),
                                    ),
                                    horizontalSpace(16),
                                    Expanded(
                                      child: CommonButton(
                                        title: "Yes".trr,
                                        onPressed: () {
                                          closeAccountController
                                              .submitCloseAccountRequest(
                                                  bankletter, docfile);
                                        },
                                        textColor: AppColor.cWhiteFont,
                                        btnColor: AppColor.themeOrangeColor,
                                        bColor: AppColor.cTransparent,
                                        horizontalPadding: 16,
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          );
                        },
                      );
                    }
                  },
                  textColor: AppColor.cWhiteFont,
                  btnColor: AppColor.themeOrangeColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget addDcTitleRowWidget(
    {required String title, required bool isSelected, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      color: AppColor.cBackGround,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pBold20,
          ),
          assetSvdImageWidget(
              image: isSelected == true
                  ? DefaultImages.arrowUpIcn
                  : DefaultImages.dropDownIcn,
              width: 24,
              height: 24)
        ],
      ),
    ),
  );
}
