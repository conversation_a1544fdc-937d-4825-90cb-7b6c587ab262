// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/add_pure_dc_vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../widget/common_text_field.dart';

class AssignDigitalCouponWidget extends StatelessWidget {
  final String code;

  AssignDigitalCouponWidget({super.key, required this.code});

  AddPureDcVehicleController addPureDcVehicleController =
      Get.put(AddPureDcVehicleController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.symmetric(vertical: 10),
                child: Row(
                  children: [
                    GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: CircleAvatar(
                          radius: 20,
                          backgroundColor: AppColor.cLightBlueContainer,
                          child: Center(
                              child: assetSvdImageWidget(
                                  image: DefaultImages.backIcn)),
                        )),
                    Expanded(
                      child: Align(
                          alignment: Alignment.center,
                          child: Center(
                              child: Text(
                            "Assign digital coupon".trr,
                            style: pBold20,
                          ))),
                    ),
                  ],
                ),
              ),
              verticalSpace(21),
              CommonTextField(
                controller: addPureDcVehicleController.plateController
                  ..text = code,
                labelText: '${'Plate'.trr} #',
                prefixIcon: Padding(
                  padding: const EdgeInsets.all(12),
                  child: assetSvdImageWidget(
                      image: DefaultImages.searchIcn, width: 24, height: 24),
                ),
                hintText: '${"Search by vehicle plate".trr} #',
              ),
              verticalSpace(16),
              CommonTextField(
                labelText: "Driver phone number".trr,
                hintText: "Phone Number".trr,
                keyboardType: TextInputType.phone,
              ),
              verticalSpace(16),
              Row(
                children: [
                  Expanded(
                      child: CommonTextField(
                    controller: addPureDcVehicleController.fuelQuotaController,
                    labelText: 'Coupon fuel quota (liters)'.trr,
                    hintText: '0',
                    keyboardType: TextInputType.numberWithOptions(
                        signed: true, decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  )),
                  horizontalSpace(16),
                  Expanded(
                      child: CommonTextField(
                    controller:
                        addPureDcVehicleController.moneyBalanceController,
                    labelText: 'Coupon fuel balance (liters)'.trr,
                    hintText: '0',
                    keyboardType: TextInputType.numberWithOptions(
                        signed: true, decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    filled: true,
                    fillColor: AppColor.lightBlueColor,
                  )),
                ],
              ),
              verticalSpace(16),
              Row(
                children: [
                  Expanded(
                      child: CommonTextField(
                    controller: addPureDcVehicleController.moneyQuotaController,
                    labelText: 'Coupon money quota (SAR)'.trr,
                    hintText: '00.00',
                    keyboardType: TextInputType.numberWithOptions(
                        signed: true, decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    filled: true,
                    fillColor: AppColor.lightBlueColor,
                  )),
                  horizontalSpace(16),
                  Expanded(
                      child: CommonTextField(
                    controller:
                        addPureDcVehicleController.dcMoneyBalanceController,
                    labelText: 'Coupon money balance (SAR)'.trr,
                    hintText: '00.00',
                    keyboardType: TextInputType.numberWithOptions(
                        signed: true, decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    filled: true,
                    fillColor: AppColor.lightBlueColor,
                  )),
                ],
              ),
              verticalSpace(16),
              Row(
                children: [
                  Expanded(
                    child: CommonBorderButton(
                      title: 'Cancel'.trr,
                      onPressed: () {
                        Get.back();
                      },
                      textColor: AppColor.cDarkBlueFont,
                      bColor: AppColor.cDarkBlueFont,
                      btnColor: AppColor.cBackGround,
                    ),
                  ),
                  horizontalSpace(16),
                  Expanded(
                    child: CommonButton(
                      title: 'Assign'.trr,
                      onPressed: () {
                        Get.back();
                      },
                      textColor: AppColor.cWhiteFont,
                      btnColor: AppColor.themeOrangeColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
