import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../../core/controller/menu_controller/order_controller/confirm_order_controller.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/text_style.dart';
import '../../../widget/common_appbar_widget.dart';
import '../../../widget/common_button.dart';
import '../../../widget/common_space_divider_widget.dart';
import '../../dashboard_manager/dashboard_manager.dart';

class ConfirmBalanceScreen extends StatefulWidget {
  final String sessionID;
  final String totalPurchased;
  final String orderID;
  final String depositto;
  final String serviceType;
  final String qty;
  final String orderType;
  final String custid;
  const ConfirmBalanceScreen({
    super.key,
    required this.sessionID,
    required this.totalPurchased,
    required this.orderID,
    required this.depositto,
    required this.serviceType,
    required this.qty,
    required this.orderType,
    required this.custid,
  });

  @override
  State<ConfirmBalanceScreen> createState() => _ConfirmBalanceScreenState();
}

class _ConfirmBalanceScreenState extends State<ConfirmBalanceScreen> {
  final ConfirmOrderController confirmOrderController =
      Get.put(ConfirmOrderController());
  var channel = const MethodChannel('flutter.native.helper');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              verticalSpace(25),
              simpleMyAppBar(
                  title: "Payment".trr,
                  onTap: () {
                    Get.offAll(DashBoardManagerScreen(
                      currantIndex: 0,
                    ));
                  },
                  backString: "Cancel".trr,
                  horizontalSize: 45),
              verticalSpace(60),
              // Container(
              //   padding: const EdgeInsets.only(left: 16, right: 16),
              //   child: Row(
              //     mainAxisAlignment: MainAxisAlignment.start,
              //     crossAxisAlignment: CrossAxisAlignment.center,
              //     children: [
              //       Expanded(
              //         child: Align(
              //             alignment: Alignment.center,
              //             child: Center(
              //                 child: Text(
              //               "PAYMENT THRU MADA/الدفع من خلال مدى",
              //               style: pBold20,
              //             ))),
              //       ),
              //     ],
              //   ),
              // ),
              // verticalSpace(25),
              Row(
                children: [
                  Expanded(
                      flex: 2,
                      child: CommonButton(
                        title: 'Proceed to Payment'.trr,
                        onPressed: madaPayment,
                        btnColor: AppColor.themeOrangeColor,
                        horizontalPadding: 16,
                      )),
                  horizontalSpace(12),
                  Expanded(
                      flex: 1,
                      child: CommonButton(
                        title: "Cancel".trr,
                        onPressed: () {
                          Get.offAll(DashBoardManagerScreen(
                            currantIndex: 0,
                          ));
                        },
                        btnColor: AppColor.themeOrangeColor,
                        horizontalPadding: 16,
                      ))
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  madaPayment() async {
    Loader.showLoader();
    var sessionID = widget.sessionID;
    var totalPurchased = widget.totalPurchased;
    var orderID = widget.orderID;
    var depositTo = widget.depositto;
    var serviceType = widget.serviceType;
    var qty = widget.qty;
    var orderType = widget.orderType;
    var custid = widget.custid;
    // await confirmOrderController.createMADAtopup(
    //     sessionID, totalPurchased, orderID, depositTo, serviceType, qty);
    channel.invokeMethod(
      "MADAPayment",
      {
        'totalPurchased': totalPurchased,
        'sessionID': sessionID,
        'orderID': orderID,
        'serviceType': serviceType,
        'orderType': orderType,
        'custid': custid,
        'qty': qty,
      },
    );
    Future.delayed(
      const Duration(seconds: 2),
      () {
        // Get.off(() => DashBoardManagerScreen(
        //       currantIndex: 0,
        //     ));
        Loader.hideLoader();
        //confirmOrderController.createSingleMADAOrder(orderID);
        showDialog(
          barrierDismissible: false,
          context: context,
          builder: (context) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              contentPadding: const EdgeInsets.all(24),
              insetPadding: const EdgeInsets.all(16),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: CommonButton(
                          title: "Dashboard".trr,
                          onPressed: () async {
                            await confirmOrderController
                                .createSingleMADAOrder(orderID);
                            await Get.offAll(DashBoardManagerScreen(
                              currantIndex: 0,
                            ));
                          },
                          textColor: AppColor.cDarkBlueFont,
                          btnColor: AppColor.cBackGround,
                          bColor: AppColor.cDarkBlueFont,
                        ),
                      ),
                      horizontalSpace(16),
                      Expanded(
                        flex: 1,
                        child: CommonButton(
                          title: "Back".trr,
                          onPressed: () async {
                            await confirmOrderController
                                .createSingleMADAOrder(orderID);
                            Get.back();
                            Get.back();
                          },
                          textColor: AppColor.cWhiteFont,
                          btnColor: AppColor.themeOrangeColor,
                          bColor: AppColor.cTransparent,
                          horizontalPadding: 16,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }
}
