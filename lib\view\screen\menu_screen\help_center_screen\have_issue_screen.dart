// ignore_for_file: prefer_const_constructors, avoid_print

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/help_center_controller/help_center_controller.dart';
import '../../../../utils/colors.dart';
import '../../../widget/common_button.dart';
import '../../../widget/common_drop_down_widget.dart';

class HaveIssueScreen extends StatelessWidget {
  final HelpCenterController helpCenterController;

  const HaveIssueScreen({Key? key, required this.helpCenterController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        height: Get.height - 80,
        decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
        padding: EdgeInsets.all(16),
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Got an issue or question?".trr,
                          style: pSemiBold17,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: assetSvdImageWidget(image: DefaultImages.cancelIcn))
                ],
              ),
              verticalSpace(24),
              Text(
                "Let us know what you need help with and we'll come back to you as soon as possible.".trr,
                style: pRegular13,
                textAlign: TextAlign.center,
              ),
              verticalSpace(24),
              CommonTextField(
                labelText: "Your Email".trr,
                hintText: '${'Please enter here'.trr}...',
                onChanged: (value) {},
                keyboardType: TextInputType.emailAddress,
              ),
              verticalSpace(24),
              Text(
                "Phone Number".trr,
                style: pRegular12,
              ),
              verticalSpace(6),
              Container(
                height: 44,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: AppColor.cBorder)),
                child: InternationalPhoneNumberInput(
                  onInputChanged: (PhoneNumber number) {
                    print("====>${number.phoneNumber}");
                    print("---->${number.isoCode}");
                    helpCenterController.isoCode.value=number.isoCode!;
                  },
                  onInputValidated: (bool value) {
                    print(value);
                  },
                  cursorColor: AppColor.cHintFont,
                  selectorConfig: SelectorConfig(
                    selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                    leadingPadding: 16,
                    setSelectorButtonAsPrefixIcon: true,
                  ),
                  ignoreBlank: false,
                  autoValidateMode: AutovalidateMode.disabled,
                  textStyle: pRegular14.copyWith(color: AppColor.cLabel),
                  initialValue: PhoneNumber(isoCode: helpCenterController.isoCode.value,dialCode: helpCenterController.isoCode.value),
                  inputBorder: OutlineInputBorder(),
                  keyboardAction: TextInputAction.done,
                  scrollPadding: EdgeInsets.zero,
                  selectorTextStyle:
                  pRegular14.copyWith(color: AppColor.cLabel, fontSize: 14),
                  textAlign: TextAlign.start,
                  inputDecoration: InputDecoration(
                      contentPadding: EdgeInsets.only(left: 16, bottom: 8),
                      isDense: true,
                      prefixText: "|  ",
                      prefixStyle: TextStyle(fontSize: 30, color: AppColor.cBorder,),
                      counterText: '',
                      hintText: " ${'Please enter here'.trr}",
                      counterStyle: TextStyle(fontSize: 0, height: 0),
                      errorStyle: TextStyle(fontSize: 0, height: 0),
                      hintStyle: pRegular14.copyWith(
                        color: AppColor.cHintFont,
                      ),
                      border: InputBorder.none),
                  onSaved: (PhoneNumber number) {
                    print('On Saved: $number');
                    print('On Saved:111:: ${number.dialCode}');
                    print('On Saved:2222: ${number.phoneNumber}');
                  },
                ),
              ),
              verticalSpace(24),
              CommonDropdownButtonWidget(
                labelText: 'What can we help you with?'.trr,
                isExpanded: true,
                list: helpCenterController.helpValueList,
                value: helpCenterController.helpValue.value,
                onChanged: (value) {
                  helpCenterController.helpValue.value = value;
                },
              ),
              verticalSpace(24),
              CommonTextField(
                labelText: "Your question".trr,
                hintText: 'Give as much detail as you can'.trr,
                maxLines: 4,
              ),
              verticalSpace(24),
              Row(
                children: [
                  Expanded(
                    child: CommonBorderButton(
                      title: 'Cancel'.trr,
                      onPressed: () {
                        Get.back();
                      },
                      textColor: AppColor.themeDarkBlueColor,
                      btnColor: AppColor.cBackGround,
                      bColor: AppColor.themeDarkBlueColor,
                    ),
                  ),
                  horizontalSpace(16),
                  Expanded(
                    child: CommonButton(
                      title: 'Submit'.trr,
                      onPressed: () {
                        Get.back();
                      },
                      textColor: AppColor.cWhiteFont,
                      btnColor: AppColor.themeOrangeColor,
                    ),
                  ),
                ],
              ),
              verticalSpace(24),
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                    color: AppColor.cBackGround,
                    border: Border.all(color: AppColor.cLightBlueBorder),
                    borderRadius: BorderRadius.circular(12)),
                child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Center(
                    child: Text(
                      "If you need to speak to someone, call us on".trr,
                      style: pRegular14.copyWith(color: AppColor.cDarkBlueText),
                    ),
                  ),
                  Center(
                    child: Text(
                      "8001228800 ",
                      style: pSemiBold14.copyWith(color: AppColor.cDarkBlueText),
                    ),
                  ),
                  verticalSpace(16),
                  Container(
                    decoration:
                        BoxDecoration(color: AppColor.themeDarkBlueColor, borderRadius: BorderRadius.circular(6)),
                    padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
                      Text(
                        "${'Or ask us a question on social media'.trr}: ",
                        style: pRegular14.copyWith(color: AppColor.cLightBlueContainer),
                      ),
                      verticalSpace(8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          assetSvdImageWidget(image: DefaultImages.facebookIcn),
                          horizontalSpace(8),
                          assetSvdImageWidget(image: DefaultImages.linkedinIcn),
                          horizontalSpace(8),
                          assetSvdImageWidget(image: DefaultImages.twiterIcn),
                          horizontalSpace(8),
                          assetSvdImageWidget(image: DefaultImages.whatsupIcn),
                          horizontalSpace(8),
                        ],
                      )
                    ]),
                  )
                ]),
              )
            ],
          ),
        ),
      ),
    );
  }
}
