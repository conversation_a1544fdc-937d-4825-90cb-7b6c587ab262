import 'dart:async'; // Required for the Zone class to determine the calling context.

/// Logs an informational message globally.
void logInfo(String message) {
  _log("INFO", "❕❕❕❕", message);
}

/// Logs an error message globally.
void logError(String message) {
  _log("ERROR", "🛑🆘🛑🆘", message);
}

/// Logs a warning message globally.
void logWarning(String message) {
  _log("WARNING", "⚠️⚠️⚠️⚠️", message);
}

/// Logs a success message globally.
void logSuccess(String message) {
  _log("SUCCESS", "✅✅✅✅", message);
}

/// A private function to format and log messages globally.
void _log(String type, String symbols, String message) {
  final caller = Zone.current[#caller] ?? "Unknown"; // Dynamically gets the caller's class name.
  final timestamp = DateTime.now().toIso8601String(); // Formats the current time as a string.
  print("$symbols [$type] [$timestamp] From $caller: $message $symbols"); // Logs the message with details.
}
