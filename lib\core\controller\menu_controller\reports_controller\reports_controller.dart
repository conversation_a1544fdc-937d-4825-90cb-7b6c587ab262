import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ReportController extends GetxController {
  List reportList = [
    {'title': "Expenses Summary", 'subTitle': ""},
    {'title': "Consumption Report", 'subTitle': ""},
    {'title': "Service Report", 'subTitle': ""},
    {'title': "Quota Change Report", 'subTitle': ""},
  ];
  RxBool isListview = true.obs;
  RxBool isGraphview = false.obs;

  // Fuel Consumption By Customer
  List connectionStatusList = [''];
  RxString selectedConnectionStatus = ''.obs;
  List plateList = [''];
  RxString selectedPlate = ''.obs;
  List divisionList = [''];
  RxString selectedDivision = ''.obs;
  List departmentList = [''];
  RxString selectedDepartment = ''.obs;
  List stationList = [''];
  RxString selectedStation = ''.obs;
  List driverList = [''];
  RxString selectedDriver = ''.obs;
  List productList = [''];
  RxString selectedProduct = ''.obs;
  List serviceList = [''];
  RxString selectedService = ''.obs;
  List vehicleTypeList = [''];
  RxString selectedVehicleType = ''.obs;
  final TextEditingController datePickerController = TextEditingController();
  RxBool isGroupByPlate = false.obs;
  RxBool isShiftClose = false.obs;

  //Fleet Wise Fuel Usage
  final TextEditingController datePickerFleetFromController =
      TextEditingController();
  final TextEditingController datePickerFleetToController =
      TextEditingController();

  //Station Info List

  //Monthly Quota Variance Summary
  final TextEditingController datePickerMonthyController =
      TextEditingController();
  List placeList = [''];
  RxString selectedPlace = ''.obs;
}
