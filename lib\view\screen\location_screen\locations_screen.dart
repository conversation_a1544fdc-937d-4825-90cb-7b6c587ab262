import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/core/controller/location_controller/locations_controller.dart';
import 'package:waie_app/models/bankatm_station.dart';
import 'package:waie_app/models/car_rental_station.dart';
import 'package:waie_app/models/car_service_station.dart';
import 'package:waie_app/models/foodres_station.dart';
import 'package:waie_app/models/gas_station.dart';
import 'package:waie_app/models/installation_center.dart';
import 'package:waie_app/models/mosque_station.dart';
import 'package:waie_app/models/sales_station.dart';
import 'package:waie_app/models/stationby_fueltype.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/logger_extention/logger_helper.dart';
import 'package:waie_app/view/screen/location_screen/location_widgets.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:waie_app/view/screen/location_screen/sales_office_screen.dart';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:google_map_dynamic_key/google_map_dynamic_key.dart';

class LocationsScreen extends StatefulWidget {
  const LocationsScreen({Key? key}) : super(key: key);

  @override
  State<LocationsScreen> createState() => _LocationsScreenState();
}

class _LocationsScreenState extends State<LocationsScreen>
    with SingleTickerProviderStateMixin {
  late GoogleMapController mapController;
  LocationsController locationsController = Get.put(LocationsController());
  LatLng? selectedStation;

  final List<String> categories = [
    "Aldrees Gas stations",
    "WAIE Sales Offices",
    "Installation centers"
  ];

  final List<LatLng> stations = [
    LatLng(24.7221, 46.6706),
    LatLng(24.7282, 46.6821),
    LatLng(24.7158, 46.6554),
  ];
  final List<String> stationNames = ["Station 1", "Station 2", "Station 3"];

  final List<String> filterOptions = [
    "Car Service",
    "Mosque",
    "Food Resturants",
    "Car Rental",
    "Bank ATM",
    "PETROL91",
    "PETROL95",
    "DIESEL"
  ];
  Map<MarkerId, Marker> markers = <MarkerId, Marker>{};

  int selectedIndex = 0;
  bool isSearchActive = false;
  bool isBackBtnActive = false;
  String distanceText = "";
  LatLng? midpoint;
  Offset? distanceLabelOffset;

  bool isFilterApplied = false;
  String appliedFilter = "";

  final LatLng _defaultLatLng = const LatLng(24.741305, 46.805976);
  LatLng _initialPosition = const LatLng(24.741305, 46.805976);
  Offset? distancePosition;
  Completer<void>? _currentProcess;
  double currentZoomLevel = 14.0;
  LatLngBounds? visibleRegion;
  bool isNearestStations = true;

  @override
  void initState() {
    super.initState();

    _fetchUserLocation();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _fetchUserLocation() async {
    setState(() {
      locationsController.isLoading = true;
    });

    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please enable location services.")),
      );
      _useDefaultLocation();
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Location permissions are denied.")),
        );
        _useDefaultLocation();
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
              "Location permissions are permanently denied. Please enable them in settings."),
          action: SnackBarAction(
            label: 'Settings',
            onPressed: () {
              Geolocator.openAppSettings();
            },
          ),
        ),
      );
      _useDefaultLocation();
      return;
    }

    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        _initialPosition = LatLng(position.latitude, position.longitude);
        locationsController.userLatitude = position.latitude;
        locationsController.userLongitude = position.longitude;
      });

      await locationsController.getGasStationsDefault(
          locationsController.userLatitude!,
          locationsController.userLongitude!,
          locationsController.radiusInKm,
          true);

      setState(() {
        _addMarkers(locationsController.gasStationList);
        locationsController.isLoading = false;
      });

      mapController.animateCamera(
        CameraUpdate.newLatLng(_initialPosition),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Failed to get location: $e")),
      );
      _useDefaultLocation();
    }
  }

  void _useDefaultLocation() {
    locationsController.userLatitude = _defaultLatLng.latitude;
    locationsController.userLongitude = _defaultLatLng.longitude;

    locationsController
        .getGasStationsDefault(
            locationsController.userLatitude!,
            locationsController.userLongitude!,
            locationsController.radiusInKm,
            true)
        .then((_) {
      setState(() {
        _addMarkers(locationsController.gasStationList);
        locationsController.isLoading = false;
      });
    });
  }

  Future<void> _addMarkers(List<dynamic> stations) async {
    // if (_currentProcess != null && !_currentProcess!.isCompleted) {
    //   _currentProcess!.complete();
    // }
    // _currentProcess = Completer<void>();

    markers.clear();
    setState(() {});

    for (int i = 0; i < stations.length; i++) {
      //if (_currentProcess!.isCompleted) return;

      var markerIdVal = "$i";
      final MarkerId markerId = MarkerId(markerIdVal);

      final Marker marker = Marker(
        markerId: markerId,
        position: LatLng(
          double.parse(stations[i].latitude),
          double.parse(stations[i].longitude),
        ),
        icon: await createMarkerImageFromAsset(
            context, DefaultImages.gasStationMarker),
        onTap: () {
          if (categories[selectedIndex] == "Aldrees Gas stations") {
            if (isFilterApplied && appliedFilter.isNotEmpty) {
              switch (appliedFilter) {
                case "Mosque":
                  _showMosqueStationBottomSheet(context, stations[i]);
                  return;

                case "Car Service":
                  _showCarServiceStationBottomSheet(context, stations[i]);
                  return;

                case "Food Resturants":
                  _showFoorResStationBottomSheet(context, stations[i]);
                  return;

                case "Car Rental":
                  _showCarRentalStationBottomSheet(context, stations[i]);
                  return;

                case "Bank ATM":
                  _showBankAtmStationBottomSheet(context, stations[i]);
                  return;

                case "PETROL91":
                case "PETROL95":
                case "DIESEL":
                  _showStationByFuelTypeBottomSheet(context, stations[i]);
                  return;

                default:
                  return;
              }
            }

            if (stations[i] is GasStationModel) {
              _showGasStationBottomSheet(context, stations[i]);
            } else {
              print(
                  "Error: Station type '${stations[i].runtimeType}' is not a GasStationModel");
            }
          } else if (categories[selectedIndex] == "WAIE Sales Offices") {
            _showSalesOfficeBottomSheet(context, stations[i]);
          } else if (categories[selectedIndex] == "Installation centers") {
            _showInstallationCenterBottomSheet(context, stations[i]);
          }
        },
      );

      setState(() {
        markers[markerId] = marker;
      });
    }
    //   if (i == 0) {
    //     setState(() {
    //       locationsController.isLoading = false;
    //     });
    //   }

    //   if (i % 50 == 0 || i == stations.length - 1) {
    //     setState(() {});
    //   }

    //   await Future.delayed(Duration(milliseconds: 10));
    // }

    // _currentProcess!.complete();
  }

  void _showGasStationBottomSheet(
      BuildContext context, GasStationModel station) {
    showModalBottomSheet(
      backgroundColor: AppColor.cWhite,
      context: context,
      barrierColor: AppColor.cBlackOpacity,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return gasStationsBottomSheetWidget(
          title: station.stationName.toString(),
          subTitle: station.placeDesc.toString(),
          products: station.products,
          latitude: double.parse(station.latitude),
          longitude: double.parse(station.longitude),
          coordinates: station.stationCoordinates,
        );
      },
    );
  }

  void _showSalesOfficeBottomSheet(
      BuildContext context, SalesStationModel office) {
    showModalBottomSheet(
      backgroundColor: AppColor.cWhite,
      context: context,
      barrierColor: AppColor.cBlackOpacity,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return salesStationsBottomSheetWidget(
          title: office.placeDesc.toString(),
          subTitle: office.officeDesc.toString(),
          status: office.offStatus == "Yes" ? "Active" : "Inactive",
          latitude: double.parse(office.latitude),
          longitude: double.parse(office.longitude),
          coordinates:
              "https://www.google.com/maps/search/${double.parse(office.latitude)},${double.parse(office.longitude)}",
        );
      },
    );
  }

  void _showInstallationCenterBottomSheet(
      BuildContext context, InstallationCenterModel office) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      barrierColor: AppColor.cBlackOpacity,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return salesStationsBottomSheetWidget(
          title: office.placeDesc.toString(),
          subTitle: office.stationName.toString(),
          status: office.stationStatus == "Yes" ? "Active" : "Inactive",
          latitude: double.parse(office.latitude),
          longitude: double.parse(office.longitude),
          coordinates:
              "https://www.google.com/maps/search/${double.parse(office.latitude)},${double.parse(office.longitude)}",
        );
      },
    );
  }

  void _showMosqueStationBottomSheet(
      BuildContext context, MosqueStation office) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      backgroundColor: AppColor.cWhite,
      builder: (context) {
        List<String> availableProducts = [];

        if (office.stnPetrol91 != null && office.stnPetrol91 > 0) {
          availableProducts.add("STN_PETROL91");
        }
        if (office.stnPetrol95 != null && office.stnPetrol95 > 0) {
          availableProducts.add("STN_PETROL95");
        }
        if (office.stnDiesel != null && office.stnDiesel > 0) {
          availableProducts.add("STN_DIESEL");
        }

        String productsDisplay = availableProducts.isNotEmpty
            ? availableProducts.join(", ")
            : "No products";

        return gasStationsBottomSheetWidget(
          title: office.stnNameE,
          subTitle: office.stnNameA,
          products: productsDisplay,
          latitude: double.parse(office.latitude),
          longitude: double.parse(office.longitude),
          coordinates: office.stnNameE,
        );
      },
    );
  }

  void _showCarServiceStationBottomSheet(
      BuildContext context, CarServiceStation office) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      backgroundColor: AppColor.cWhite,
      builder: (context) {
        List<String> availableProducts = [];

        if (office.stnPetrol91 != null && office.stnPetrol91 > 0) {
          availableProducts.add("STN_PETROL91");
        }
        if (office.stnPetrol95 != null && office.stnPetrol95 > 0) {
          availableProducts.add("STN_PETROL95");
        }
        if (office.stnDiesel != null && office.stnDiesel > 0) {
          availableProducts.add("STN_DIESEL");
        }

        String productsDisplay = availableProducts.isNotEmpty
            ? availableProducts.join(", ")
            : "No products";

        return gasStationsBottomSheetWidget(
          title: office.stnNameE,
          subTitle: office.stnNameA,
          products: productsDisplay,
          latitude: double.parse(office.latitude),
          longitude: double.parse(office.longitude),
          coordinates: office.stnNameE,
        );
      },
    );
  }

  void _showFoorResStationBottomSheet(
      BuildContext context, FoodResStation office) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      backgroundColor: AppColor.cWhite,
      builder: (context) {
        List<String> availableProducts = [];

        if (office.stnPetrol91 != null && office.stnPetrol91 > 0) {
          availableProducts.add("STN_PETROL91");
        }
        if (office.stnPetrol95 != null && office.stnPetrol95 > 0) {
          availableProducts.add("STN_PETROL95");
        }
        if (office.stnDiesel != null && office.stnDiesel > 0) {
          availableProducts.add("STN_DIESEL");
        }

        String productsDisplay = availableProducts.isNotEmpty
            ? availableProducts.join(", ")
            : "No products";

        return gasStationsBottomSheetWidget(
          title: office.stnNameE,
          subTitle: office.stnNameA,
          products: productsDisplay,
          latitude: double.parse(office.latitude),
          longitude: double.parse(office.longitude),
          coordinates: office.stnNameE,
        );
      },
    );
  }

  void _showCarRentalStationBottomSheet(
      BuildContext context, CarRentalStation office) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      backgroundColor: AppColor.cWhite,
      builder: (context) {
        List<String> availableProducts = [];

        if (office.stnPetrol91 != null && office.stnPetrol91 > 0) {
          availableProducts.add("STN_PETROL91");
        }
        if (office.stnPetrol95 != null && office.stnPetrol95 > 0) {
          availableProducts.add("STN_PETROL95");
        }
        if (office.stnDiesel != null && office.stnDiesel > 0) {
          availableProducts.add("STN_DIESEL");
        }

        String productsDisplay = availableProducts.isNotEmpty
            ? availableProducts.join(", ")
            : "No products";

        return gasStationsBottomSheetWidget(
          title: office.stnNameE,
          subTitle: office.stnNameA,
          products: productsDisplay,
          latitude: double.parse(office.latitude),
          longitude: double.parse(office.longitude),
          coordinates: office.stnNameE,
        );
      },
    );
  }

  void _showBankAtmStationBottomSheet(
      BuildContext context, BankAtmStation office) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      backgroundColor: AppColor.cWhite,
      builder: (context) {
        List<String> availableProducts = [];

        if (office.stnPetrol91 != null && office.stnPetrol91 > 0) {
          availableProducts.add("STN_PETROL91");
        }
        if (office.stnPetrol95 != null && office.stnPetrol95 > 0) {
          availableProducts.add("STN_PETROL95");
        }
        if (office.stnDiesel != null && office.stnDiesel > 0) {
          availableProducts.add("STN_DIESEL");
        }

        String productsDisplay = availableProducts.isNotEmpty
            ? availableProducts.join(", ")
            : "No products";

        return gasStationsBottomSheetWidget(
          title: office.stnNameE.toString(),
          subTitle: office.stnNameA.toString(),
          products: productsDisplay.toString(),
          latitude: double.parse(office.latitude),
          longitude: double.parse(office.longitude),
          coordinates: office.stnNameE,
        );
      },
    );
  }

  void _showStationByFuelTypeBottomSheet(
      BuildContext context, StationsByFuelType office) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      backgroundColor: AppColor.cWhite,
      builder: (context) {
        List<String> availableProducts = [];

        if (office.stnPetrol91 != null && office.stnPetrol91! > 0) {
          availableProducts.add("STN_PETROL91");
        }
        if (office.stnPetrol95 != null && office.stnPetrol95! > 0) {
          availableProducts.add("STN_PETROL95");
        }
        if (office.stnDiesel != null && office.stnDiesel! > 0) {
          availableProducts.add("STN_DIESEL");
        }

        String productsDisplay = availableProducts.isNotEmpty
            ? availableProducts.join(", ")
            : "No products";

        return gasStationsBottomSheetWidget(
          title: office.stnNameE.toString(),
          subTitle: office.stnNameA.toString(),
          products: productsDisplay.toString(),
          latitude: double.parse(office.latitude),
          longitude: double.parse(office.longitude),
          coordinates: office.stnNameE,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    logSuccess('_defaultLatLng ${_defaultLatLng}');
    final isDataLoading = locationsController.isLoading ||
        locationsController.currentStationList.isEmpty;
    logInfo("_initialPosition ${_initialPosition.toString()}");
    return Scaffold(
      backgroundColor: AppColor.cWhite,
      body: Stack(
        children: [
          GoogleMap(
            initialCameraPosition:
                CameraPosition(target: _initialPosition, zoom: 15.0),
            onMapCreated: (GoogleMapController controller) {
              mapController = controller;
            },
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
            markers: Set<Marker>.of(markers.values),
            //..add(userLocationMarker!),
            onCameraMove: (CameraPosition position) {},
            onCameraIdle: () {},
            // buildingsEnabled: true,
            // trafficEnabled: true,
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: 275,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 10),
              child: SingleChildScrollView(
                child: Wrap(
                  // mainAxisSize: MainAxisSize.max,
                  // mainAxisAlignment: MainAxisAlignment.center,
                  //crossAxisAlignment: CrossAxisAlignment.center,
                  alignment: WrapAlignment.center,
                  children: [
                    // Drag Handle
                    Container(
                      width: 50,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    const SizedBox(height: 10),

                    Padding(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          if (isBackBtnActive) ...[
                            InkWell(
                              onTap: () async {
                                setState(() {
                                  isSearchActive = false;
                                  isBackBtnActive = false;
                                });
                              },
                              child: Container(
                                // padding: EdgeInsets.only(left: 15),
                                // margin: EdgeInsets.only(left: 10),
                                height: 40,
                                //width: 90,
                                // decoration: BoxDecoration(
                                //     borderRadius: BorderRadius.circular(5),
                                //     border: Border.all(
                                //       color: AppColor.cBarBlueLine,
                                //     )
                                //     // gradient: const LinearGradient(colors: [
                                //     //   Color(0xff2751A1),
                                //     //   Color(0xff4BB3CF)
                                //     // ])
                                //     ),
                                child: Center(
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.arrow_back_ios,
                                        color: AppColor.cOrangeFont,
                                        size: 20,
                                      ),
                                      Text(
                                        "Back".trr,
                                        style: TextStyle(
                                          color: AppColor.cOrangeFont,
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            // SizedBox(
                            //   width: 50,
                            // ),

                            Text(
                              isFilterApplied && appliedFilter.isNotEmpty
                                  ? appliedFilter.trr
                                  : categories[selectedIndex].trr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  fontSize: 14, fontWeight: FontWeight.w800),
                            ),

                            Icon(
                              Icons.local_gas_station,
                              color: AppColor.cOrangeFont,
                            )
                          ],
                        ],
                      ),
                    ),
                    if (isBackBtnActive == false) ...[
                      Text(
                        isFilterApplied && appliedFilter.isNotEmpty
                            ? appliedFilter.trr
                            : categories[selectedIndex].trr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.w800),
                      ),
                    ],
                    const SizedBox(height: 5),

                    Divider(
                      color: AppColor.cBarBlueLine,
                      thickness: 0.5,
                    ),

                    if (!isSearchActive)
                      ..._buildCategoryList()
                    else
                      ..._buildStationList(),
                  ],
                ),
              ),
            ),
          ),
          if (isDataLoading)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SpinKitDoubleBounce(
                      color: AppColor.themeOrangeColor,
                      //Color(0xff4BB3CF)
                      size: 100.0,
                    ),
                    const SizedBox(height: 10),
                    Text(
                      "Fetching Data".trr,
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<Widget> _buildCategoryList() {
    return [
      ListView.builder(
        shrinkWrap: true,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          return ListTile(
            leading: Icon(
              Icons.location_on,
              color: selectedIndex == index ? Colors.blue : Colors.grey,
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  categories[index].trr,
                  style: TextStyle(
                    color: selectedIndex == index ? Colors.blue : Colors.black,
                    fontWeight: selectedIndex == index
                        ? FontWeight.w800
                        : FontWeight.normal,
                    fontSize: 15,
                  ),
                ),
                const SizedBox(width: 20),
                if (categories[index] == "Aldrees Gas stations" &&
                    selectedIndex == index)
                  isFilterApplied
                      ? locationCustomButton(
                          onTap: () {
                            setState(() {
                              isFilterApplied = false;
                              appliedFilter = "";
                            });
                            showMessage(context, "Filter Cleared".trr);
                          },
                          height: 35,
                          width: 110,
                          text: 'Cancel Filter'.trr,
                        )
                      : locationCustomButton(
                          onTap: () {
                            showFilterDialog(context, categories[index]);
                          },
                          height: 35,
                          width: 110,
                          text: 'Filter'.trr,
                        )
              ],
            ),
            onTap: () async {
              // if (_currentProcess != null && !_currentProcess!.isCompleted) {
              //   _currentProcess!.complete();
              // }

              setState(() {
                selectedIndex = index;
                isNearestStations = true;
                locationsController.isLoading = false;
                isSearchActive = true;
                isBackBtnActive = true;
                isFilterApplied = false;
                //locationsController.currentStationList.clear();
              });

              try {
                List<dynamic> stations = [];
                switch (categories[index]) {
                  case "Aldrees Gas stations":
                    stations = await locationsController.getGasStationsDefault(
                      locationsController.userLatitude!,
                      locationsController.userLongitude!,
                      locationsController.radiusInKm,
                      true,
                    );
                    break;

                  case "WAIE Sales Offices":
                    stations =
                        await locationsController.getSalesOfficeStationsDefault(
                      locationsController.userLatitude!,
                      locationsController.userLongitude!,
                      locationsController.radiusInKm,
                      true,
                    );
                    break;

                  case "Installation centers":
                    stations =
                        await locationsController.getInstallationCenterStations(
                      locationsController.userLatitude!,
                      locationsController.userLongitude!,
                      locationsController.radiusInKm,
                    );
                    break;

                  default:
                    stations = [];
                    break;
                }

                await _addMarkers(stations);

                setState(() {});
              } catch (e) {
                print("Error fetching nearest stations for category: $e");
              } finally {}
            },
          );
        },
      ),
    ];
  }

  int selectedIndexStation = -1;

  List<Widget> _buildStationList() {
    return [
      Obx(
        () {
          return Container(
            height: 180,
            child: ListView.builder(
              shrinkWrap: true,
              physics: BouncingScrollPhysics(),
              itemCount: locationsController.currentStationList.length,
              itemBuilder: (context, index) {
                final station = locationsController.currentStationList[index];
                final isSelected = selectedIndexStation == index;
                final displayText = _getStationDisplayText(station);

                return ListTile(
                  leading: Icon(
                    Icons.place,
                    color: isSelected ? Colors.blue : Colors.grey,
                  ),
                  title: Text(
                    displayText,
                    style: TextStyle(
                      color: isSelected ? Colors.blue : Colors.black,
                      fontWeight:
                          isSelected ? FontWeight.w800 : FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                  onTap: () {
                    setState(() {
                      selectedIndexStation = index;
                      selectedStation = LatLng(
                        double.parse(station.latitude),
                        double.parse(station.longitude),
                      );
                    });

                    final distance = _calculateDistance(
                      _initialPosition.latitude,
                      _initialPosition.longitude,
                      selectedStation!.latitude,
                      selectedStation!.longitude,
                    );

                    showDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          backgroundColor: Colors.white,
                          title: Text(
                            "Station Detail".trr,
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w800),
                          ),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                displayText,
                                style: const TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.w600),
                              ),
                              const SizedBox(height: 10),
                              Text(
                                "Distance".trr +
                                    " "
                                        "${distance.toStringAsFixed(2)}" +
                                    " " +
                                    "km".trr,
                                style: const TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.w500),
                              ),
                            ],
                          ),
                          actions: [
                            TextButton(
                              onPressed: () {
                                _openGoogleMaps(
                                  selectedStation!.latitude,
                                  selectedStation!.longitude,
                                );
                              },
                              child: Text(
                                "Show on Google Maps".trr,
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.w800),
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: Text(
                                "Close".trr,
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.w800),
                              ),
                            ),
                          ],
                        );
                      },
                    );
                  },
                );
              },
            ),
          );
          // : Center(
          //     child: CircularProgressIndicator(
          //       color: AppColor.cOrangeFont,
          //     ),
          //   );
        },
      ),
    ];
  }

  void _openGoogleMaps(double latitude, double longitude) async {
    final googleMapsUrl = "https://www.google.com/maps?q=$latitude,$longitude";

    if (await canLaunch(googleMapsUrl)) {
      await launch(googleMapsUrl);
    } else {
      print("Could not open Google Maps.");
    }
  }

  String _getStationDisplayText(dynamic station) {
    try {
      if (categories[selectedIndex] == "Aldrees Gas stations") {
        if (isFilterApplied && appliedFilter.isNotEmpty) {
          switch (appliedFilter) {
            case "Mosque":
              if (station is MosqueStation) {
                return Get.locale?.languageCode == "ar"
                    ? station.stnNameA?.toString() ?? "مسجد غير معروف".trr
                    : station.stnNameE?.toString() ?? "Unknown Mosque".trr;
              }
              break;

            case "Car Service":
              if (station is CarServiceStation) {
                return Get.locale?.languageCode == "ar"
                    ? station.stnNameA?.toString() ?? "خدمة سيارة غير معروفة".trr
                    : station.stnNameE?.toString() ??
                        "Unknown Service Center".trr;
              }
              break;

            case "Food Resturants":
              if (station is FoodResStation) {
                return Get.locale?.languageCode == "ar"
                    ? station.stnNameA?.toString() ?? "مطعم غير معروف".trr
                    : station.stnNameE?.toString() ?? "Unknown Restaurant".trr;
              }
              break;

            case "Car Rental":
              if (station is CarRentalStation) {
                return Get.locale?.languageCode == "ar"
                    ? station.stnNameA?.toString() ??
                        "تأجير سيارات غير معروف".trr
                    : station.stnNameE?.toString() ?? "Unknown Car Rental".trr;
              }
              break;

            case "Bank ATM":
              if (station is BankAtmStation) {
                return Get.locale?.languageCode == "ar"
                    ? station.stnNameA?.toString() ?? "صراف آلي غير معروف".trr
                    : station.stnNameE?.toString() ?? "Unknown ATM".trr;
              }
              break;

            case "PETROL91":
            case "PETROL95":
            case "DIESEL":
              if (station is StationsByFuelType) {
                return Get.locale?.languageCode == "ar"
                    ? station.stnNameA?.toString() ?? "محطة غير معروفة".trr
                    : station.stnNameE?.toString() ?? "Unknown Station".trr;
              }
              break;
          }
        }

        if (station is GasStationModel) {
          return station.stationName?.toString() ?? "Unknown Station";
        }
      } else if (categories[selectedIndex] == "WAIE Sales Offices") {
        if (station is SalesStationModel) {
          return station.officeDesc?.toString() ?? "Unknown Office";
        }
      } else if (categories[selectedIndex] == "Installation centers") {
        if (station is InstallationCenterModel) {
          return station.stationName?.toString() ?? "Unknown Center";
        }
      }
    } catch (e) {
      print("Error in _getStationDisplayText: $e");
    }

    return "Unknown Location";
  }

  void showFilterDialog(BuildContext context, String selectedCategory) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: Text(
            "Filters".trr,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w800),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: filterOptions.map((option) {
              return ListTile(
                title: Text(
                  option.trr,
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
                onTap: () async {
                  Navigator.of(context).pop();
                  setState(() {
                    isFilterApplied = true;
                    appliedFilter = option;
                    isSearchActive = true;
                    print("filter name $appliedFilter");
                  });
                  showMessage(context, "Filter applied:".trr + "$option".trr);

                  await _handleFilterForCategory(selectedCategory, option);
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text("Close".trr),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleFilterForCategory(
      String category, String filterOption) async {
    setState(() {
      isBackBtnActive = true;
    });

    try {
      List<dynamic> filteredStations = [];

      switch (category) {
        case "Aldrees Gas stations":
          if (filterOption == "Mosque") {
            filteredStations = await locationsController.getMosqueStations(
              locationsController.userLatitude!,
              locationsController.userLongitude!,
              locationsController.radiusInKm,
            );
          } else if (filterOption == "Car Service") {
            filteredStations = await locationsController.getCarServiceStations(
              locationsController.userLatitude!,
              locationsController.userLongitude!,
              locationsController.radiusInKm,
            );
          } else if (filterOption == "Food Resturants") {
            filteredStations = await locationsController.getFoodResStations(
              locationsController.userLatitude!,
              locationsController.userLongitude!,
              locationsController.radiusInKm,
            );
          } else if (filterOption == "Car Rental") {
            filteredStations = await locationsController.getCarRentalStations(
              locationsController.userLatitude!,
              locationsController.userLongitude!,
              locationsController.radiusInKm,
            );
          } else if (filterOption == "Bank ATM") {
            filteredStations = await locationsController.getBankATMStations(
              locationsController.userLatitude!,
              locationsController.userLongitude!,
              locationsController.radiusInKm,
            );
          } else if (filterOption == "PETROL91" ||
              filterOption == "PETROL95" ||
              filterOption == "DIESEL") {
            filteredStations = await locationsController.getStationsByFuelType(
              filterOption,
              locationsController.userLatitude!,
              locationsController.userLongitude!,
              locationsController.radiusInKm,
            );
          }
          break;

        default:
          showMessage(context,
              "No API available for the selected category and filter.");
          return;
      }

      if (filteredStations.isEmpty) {
        showMessage(context, "No stations found for the selected filter.");
        return;
      }

      await _addMarkers(filteredStations);
    } catch (e) {
      print(
          "Error fetching stations for $category with filter $filterOption: $e");
      showMessage(context, "An error occurred while fetching data.");
    } finally {}
  }

  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const earthRadius = 6371;
    final dLat = _degreesToRadians(lat2 - lat1);
    final dLon = _degreesToRadians(lon2 - lon1);
    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }

  void showMessage(BuildContext context, String message) {
    OverlayEntry overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).size.height * 0.4,
        left: MediaQuery.of(context).size.width * 0.1,
        right: MediaQuery.of(context).size.width * 0.1,
        child: Material(
          color: Colors.transparent,
          child: Center(
            child: Container(
              height: 75,
              width: 250,
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context)!.insert(overlayEntry);

    Future.delayed(const Duration(seconds: 1), () {
      overlayEntry.remove();
    });
  }

  Widget locationCustomButton(
      {void Function()? onTap, String? text, double? height, double? width}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: height!,
        width: width!,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          border: Border.all(color: AppColor.cBlueFont),
        ),
        child: Center(
          child: Text(
            text!,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ),
      ),
    );
  }
}
