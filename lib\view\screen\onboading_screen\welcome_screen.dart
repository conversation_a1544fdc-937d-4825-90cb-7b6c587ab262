import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/view/screen/onboading_screen/onboading_screen.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(
      const Duration(seconds: 1),
      () {
        Get.off(() => OnBoardingScreen());
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.themeOrangeColor,
      body: SafeArea(
          child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            assetSvdImageWidget(image: DefaultImages.welcomeLogoImage),
            // verticalSpace(Get.height * 0.15),
            // Padding(
            //   // padding:  EdgeInsets.only(left: 49,right: 49,bottom: Get.height * 0.15,top: Get.height * 0.25),
            //   padding: EdgeInsets.only(
            //       left: 49, right: 49, bottom: 125, top: Get.height * 0.25),
            //   child: Text(
            //     "This is loader. We can animate WAIE circle symbol to show that the app is loading. The animation itself can take just a second in time.".trr,
            //     style: pBold14.copyWith(color: AppColor.cWhiteFont),
            //     textAlign: TextAlign.center,
            //   ),
            // )
          ],
        ),
      )),
    );
  }
}
