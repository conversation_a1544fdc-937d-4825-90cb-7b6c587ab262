import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/login_manager/investor_relation_webview_screen.dart';
import 'package:waie_app/view/screen/login_manager/logistics_transport_webview_screen.dart';
import 'package:waie_app/view/screen/login_manager/petroleum_services_webview_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class MoreScreen extends StatelessWidget {
  const MoreScreen({super.key});

  launchURL(String myUrl) async {
    final Uri url = Uri.parse(myUrl);
    // if (await canLaunchUrl(Uri.parse(myUrl))) {
    //   await launchUrl(Uri.parse(myUrl),
    //       mode: LaunchMode.externalApplication, webViewConfiguration: WebViewConfiguration(enableJavaScript: false));
    // } else {
    //   throw 'Could not launch $url';
    // }

    /// RMV - 04182024
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "More about Aldrees".trr,
              style: pBold28.copyWith(fontSize: 22),
            ),
            verticalSpace(32),
            moreAboutWidget(
                onTap: () async {
                  // await launchURL(AppConstant.petrolUrl);
                  /// RMV - 04182024
                  await Get.to(() => const PetroleumServicesWebviewScreen(
                      url: AppConstant.petrolUrl));
                },
                image: DefaultImages.petroliamServiceIcn,
                title: "Petroleum Services".trr),
            moreAboutWidget(
                onTap: () async {
                  //await launchURL(AppConstant.trransportUrl);
                  /// RMV - 04182024
                  await Get.to(() => const LogisticsTransportWebviewScreen(
                      url: AppConstant.transportUrl));
                },
                image: DefaultImages.logisticsTransportIcn,
                title: "Logistics Transport".trr),
            moreAboutWidget(
                onTap: () async {
                  //await launchURL(AppConstant.stockUrl);
                  /// RMV - 04182024
                  await Get.to(() => const InvestorRelationsWebviewScreen(
                      url: AppConstant.stockUrl));
                },
                image: DefaultImages.investorRelationsIcn,
                title: "Investor Relations".trr),
          ],
        ),
      ),
    );
  }

  Widget moreAboutWidget(
      {required String image,
      required String title,
      required Function() onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                assetSvdImageWidget(image: image),
                horizontalSpace(8),
                Text(
                  title,
                  style: pRegular17,
                )
              ],
            ),
            assetSvdImageWidget(
                image: DefaultImages.nextIcn,
                colorFilter: ColorFilter.mode(AppColor.cText, BlendMode.srcIn))
          ],
        ),
      ),
    );
  }
}
