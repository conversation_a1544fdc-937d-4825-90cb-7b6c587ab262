// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/core/controller/menu_controller/profile_controller/profile_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/prefer.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/login_manager/aldrees_portal.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/company_addreess_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/company_details_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/profile_controller/address_load_controller.dart';
import '../../../../core/controller/menu_controller/profile_controller/new_profile_controller.dart';
import '../../../../utils/validator.dart';
import '../../../widget/common_button.dart';
import '../../../widget/common_drop_down_widget.dart';
import '../../../widget/common_snak_bar_widget.dart';
import '../../../widget/common_text_field.dart';
import '../../auth/login_with_email_screen.dart';
import 'personal_details_screen.dart';

class RestrictProfileScreen extends StatefulWidget {
  const RestrictProfileScreen({super.key});

  @override
  State<RestrictProfileScreen> createState() => _RestrictProfileScreenState();
}

class _RestrictProfileScreenState extends State<RestrictProfileScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  NewProfileController newProfileController = Get.put(NewProfileController());
  Address_Data_Controller addressLoadController =
      Get.put(Address_Data_Controller());

  String errorString = '';
  FilePickerResult? vatDocx;
  FilePickerResult? idDocx;

  bool _isSecondDropdownEnabled = false;

  @override
  void initState() {
    super.initState();
    // if (newProfileController.selectedRegion.value.isNotEmpty) {
    //   print("newProfileController.selectedRegion.value");
    //   print(newProfileController.selectedRegion.value);
    //   setState(() {
    //     addressLoadController
    //         .fetchCityData(newProfileController.selectedRegion.value);
    //   });
    // }
    if (newProfileController.selectedCity.value.isNotEmpty) {
      print("newProfileController.selectedRegion.value");
      print(newProfileController.selectedCity.value);
      _isSecondDropdownEnabled = true;
      addressLoadController.fetchDistData(
          newProfileController.selectedCity.value,
          newProfileController.selectedRegion.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  simpleMyAppBar(
                    title: "Profile".trr,
                    onTap: () async {
                      SharedPreferences sharedProfileDetails =
                          await SharedPreferences.getInstance();
                      SharedPreferences sharedProfileDetail =
                          await SharedPreferences.getInstance();
                      await sharedProfileDetails.clear();
                      await sharedProfileDetail.clear();
                      Get.back();
                    },
                    backString: "Back".trr,
                  ),
                ],
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: Get.width,
                            padding: EdgeInsets.only(
                                right: 24, left: 24, bottom: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Registration Type".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.registrationType
                                      .toString(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                                verticalSpace(12),
                                Text(
                                  "Registration date".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.registrationDate
                                      .toString(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                                verticalSpace(12),
                                Text(
                                  "Account Type".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.accountType.toString(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                                verticalSpace(12),
                                if (newProfileController.regType.value == "C")
                                  Text(
                                    "Company Type".trr,
                                    style: pBold16.copyWith(
                                      color: AppColor.cDarkGreyFont,
                                    ),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  Text(
                                    newProfileController.companyType.toString(),
                                    style: pBold12.copyWith(
                                      color: AppColor.cFont,
                                    ),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(12),
                                if (newProfileController.regType.value == "C")
                                  Text(
                                    "Expro".trr,
                                    style: pBold16.copyWith(
                                      color: AppColor.cDarkGreyFont,
                                    ),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  Text(
                                    newProfileController.expro.toString(),
                                    style: pBold12.copyWith(
                                      color: AppColor.cFont,
                                    ),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(12),
                                Text(
                                  "Customer ID".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.customerID.toString(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                                verticalSpace(12),
                                Text(
                                  "Email ID".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.emailID.toLowerCase(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: Get.width,
                            padding: EdgeInsets.only(
                                right: 24, left: 24, bottom: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "General Info".trr,
                                  style: pBold20.copyWith(
                                    color: AppColor.cText,
                                  ),
                                ),
                                verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .englishCompanyController,
                                    readOnly: true,
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    labelText: '${"English Company Name".trr}*',
                                    hintText: 'English Company Name'.trr,
                                    maxLength: 55,
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'English Company name'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .arabicCompanyController,
                                    readOnly: true,
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    labelText: '${"Arabic Company Name".trr}*',
                                    hintText: 'Arabic Company Name'.trr,
                                    maxLength: 55,
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Arabic Company name'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .contactPersonController,
                                    readOnly: true,
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    labelText: '${'Contact Person'.trr}*',
                                    hintText: 'Contact Person'.trr,
                                    maxLength: 50,
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Contact Person'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .designationController,
                                    readOnly: true,
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    labelText: '${'Designation'.trr}*',
                                    hintText: 'Designation'.trr,
                                    maxLength: 40,
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Designation'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .commercialController,
                                    readOnly: true,
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    labelText:
                                        '${'Commercial Register No'.trr}*',
                                    hintText: 'Commercial Register No'.trr,
                                    maxLength: 10,
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            signed: true, decimal: true),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.isVerified.value ==
                                    "Y")
                                  CommonTextField(
                                    controller:
                                        newProfileController.vatController,
                                    readOnly: true,
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    labelText: 'VAT No'.trr,
                                    hintText: 'VAT No'.trr,
                                  ),
                                verticalSpace(16),
                                Text(
                                  '${"Salesman".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                      .selectedSalesmen.value,
                                  items: addressLoadController.salesmenList
                                      .map((data) {
                                    print("=============selectedSalesmen");
                                    print("data.TYPECODE");
                                    print(
                                        "newProfileController.selectedSalesmen");
                                    print(data.TYPECODE);
                                    print(data.TYPEDESC);
                                    print("=============selectedSalesmen");
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: null,
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    hintText: 'Salesman'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .companyTelController,
                                    readOnly: true,
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    labelText: '${"Company Tel".trr}*',
                                    hintText: 'Company Tel'.trr,
                                    maxLength: 9,
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            signed: true, decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Company Tel'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .companyFaxController,
                                    readOnly: true,
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    labelText: '${"Company Fax".trr}*',
                                    hintText: 'Company Fax'.trr,
                                    maxLength: 9,
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            signed: true, decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Company Fax'.trr);
                                    },
                                  ),
                              ],
                            ),
                          ),
                          Container(
                            width: Get.width,
                            padding: EdgeInsets.only(
                                right: 24, left: 24, bottom: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Address".trr,
                                  style: pBold20.copyWith(
                                    color: AppColor.cText,
                                  ),
                                ),
                                verticalSpace(16),
                                // CommonTextField(
                                //   controller:
                                //       newProfileController.countryController,
                                //   readOnly: true,
                                //   fillColor: AppColor.cFilled,
                                //   filled: true,
                                //   labelText: '${"Country".trr}*',
                                // ),
                                Text(
                                  '${"Country".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                      .selectedCountry.value,
                                  items: addressLoadController.cntryList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: null,
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    hintText: 'Country'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                Text(
                                  '${"Region".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value:
                                      newProfileController.selectedRegion.value,
                                  items: addressLoadController.provList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: null,
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    hintText: 'Region'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                Text(
                                  '${"City".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value:
                                      newProfileController.selectedCity.value,
                                  items: addressLoadController.cityList
                                      .map((data) {
                                    print("=============");
                                    print("data.TYPECODE");
                                    print(data.TYPECODE);
                                    print(data.TYPEDESC);
                                    print(newProfileController.selectedCity
                                        .toString());
                                    print("=============");
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: null,
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    hintText: 'City'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                Text(
                                  '${"District".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                      .selectedDistrict.value,
                                  items: addressLoadController.distList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: null,
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    hintText: 'District'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.streetController,
                                  readOnly: true,
                                  fillColor: AppColor.cFilled,
                                  filled: true,
                                  labelText: '${"Street".trr}*',
                                  hintText: 'Street'.trr,
                                  maxLength: 24,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Street'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.buildingNoController,
                                  readOnly: true,
                                  fillColor: AppColor.cFilled,
                                  filled: true,
                                  labelText: '${'Building No.'.trr}*',
                                  hintText: 'Building No.'.trr,
                                  keyboardType: TextInputType.numberWithOptions(
                                      signed: true, decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  maxLength: 4,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Building No.'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.postalCodeController,
                                  readOnly: true,
                                  fillColor: AppColor.cFilled,
                                  filled: true,
                                  labelText: '${'Postal Code'.trr}*',
                                  hintText: 'Postal Code'.trr,
                                  keyboardType: TextInputType.numberWithOptions(
                                      signed: true, decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  maxLength: 5,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Postal Code'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.poBoxController,
                                  readOnly: true,
                                  fillColor: AppColor.cFilled,
                                  filled: true,
                                  labelText: '${'PO Box'.trr}*',
                                  hintText: 'PO Box'.trr,
                                  keyboardType: TextInputType.numberWithOptions(
                                      signed: true, decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'PO Box'.trr);
                                  },
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: Get.width,
                            padding: EdgeInsets.only(
                                right: 24, left: 24, bottom: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Personal Details".trr,
                                  style: pBold20.copyWith(
                                    color: AppColor.cText,
                                  ),
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.firstnameController,
                                  readOnly: true,
                                  fillColor: AppColor.cFilled,
                                  filled: true,
                                  labelText: '${"First Name".trr}*',
                                  hintText: 'First Name'.trr,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'First Name'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.middlenameController,
                                  readOnly: true,
                                  fillColor: AppColor.cFilled,
                                  filled: true,
                                  labelText: '${"Middle Name".trr}*',
                                  hintText: 'Middle Name'.trr,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Middle Name'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.lastnameController,
                                  readOnly: true,
                                  fillColor: AppColor.cFilled,
                                  filled: true,
                                  labelText: '${"Last Name".trr}*',
                                  hintText: 'Last Name'.trr,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Last Name'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.phoneNoController,
                                  readOnly: true,
                                  fillColor: AppColor.cFilled,
                                  filled: true,
                                  labelText: '${"Phone Number".trr}*',
                                  hintText: '966530609646'.trr,
                                  keyboardType: TextInputType.numberWithOptions(
                                      signed: true, decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Phone Number'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                Text(
                                  '${"Default Language".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                      .selectedLanguage.value,
                                  items: addressLoadController.langList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: null,
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    hintText: 'Default Language'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                // CommonTextField(
                                //   readOnly: true,
                                //   fillColor: AppColor.cFilled,
                                //   filled: true,
                                //   labelText: "How did you know about WAIE?".trr,
                                // ),
                                Text(
                                  '${"How did you know about WAIE?".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value:
                                      newProfileController.selectedWaie.value,
                                  items: addressLoadController.waieList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: null,
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    hintText: 'How did you know about WAIE?'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: CommonButton(
                title: 'Update Profile'.trr,
                onPressed: () {
                  showDialog(
                    barrierDismissible: false,
                    context: context,
                    builder: (context) {
                      return AlertDialog(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        contentPadding: EdgeInsets.all(24),
                        insetPadding: EdgeInsets.all(16),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                    onTap: () {
                                      Get.back();
                                    },
                                    child: assetSvdImageWidget(
                                        image: DefaultImages.cancelIcn)),
                              ],
                            ),
                            verticalSpace(24),
                            Center(
                              child: Text(
                                  "Kindly visit our website waie.aldrees.com to update your profile"
                                      .trr,
                                  style: pBold20,
                                  textAlign: TextAlign.center),
                            ),
                            verticalSpace(34),
                            Row(
                              children: [
                                Expanded(
                                  child: CommonButton(
                                    title: "Cancel".trr,
                                    onPressed: () {
                                      Get.back();
                                    },
                                    textColor: AppColor.cDarkBlueFont,
                                    btnColor: AppColor.cBackGround,
                                    bColor: AppColor.cDarkBlueFont,
                                  ),
                                ),
                                horizontalSpace(16),
                                Expanded(
                                  child: CommonButton(
                                    title: "Visit the website".trr,
                                    onPressed: () async {
                                      await Get.to(() =>
                                          const AldreesPortalScreen(
                                              url: AppConstant.aldreesportal));
                                    },
                                    textColor: AppColor.cWhiteFont,
                                    btnColor: AppColor.themeOrangeColor,
                                    bColor: AppColor.cTransparent,
                                    horizontalPadding: 16,
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                      );
                    },
                  );
                },
                textColor: AppColor.cWhiteFont,
                btnColor: AppColor.themeOrangeColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget dataHeaderWidget({
  String? registrationType,
  String? registrationDate,
  String? accountType,
  String? companyType,
  String? expro,
  String? customerID,
  String? emailID,
}) {
  return Container(
    width: Get.width,
    padding: EdgeInsets.only(right: 24, left: 24, bottom: 24),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Registration Type".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          registrationType!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Registration date".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          registrationDate!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Account Type".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          accountType!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Company Type".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          companyType!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Expro".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          expro!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Customer ID".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          customerID!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Email ID".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          emailID!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
      ],
    ),
  );
}
