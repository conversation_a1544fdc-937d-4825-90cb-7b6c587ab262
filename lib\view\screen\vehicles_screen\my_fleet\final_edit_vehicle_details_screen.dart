// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'dart:convert';

import 'package:chips_choice/chips_choice.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/new_user_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/fleet_structure_controller/edit_vehicle_details_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/overview_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/view_vehicle_details_controller.dart';
import '../../../../core/controller/vehicle_controller/add_pure_dc_vehicle_controller.dart';
import '../../../../utils/validator.dart';
import '../../../widget/common_appbar_widget.dart';
import '../../../widget/common_button.dart';
import '../../../widget/loading_widget.dart';
import '../../../widget/common_drop_down_widget.dart';
import '../../dashboard_manager/dashboard_manager.dart';
import 'action_widget.dart';
import 'add_pure_dc_vehicle_screen.dart';
import 'assign_digital_coupon_widget.dart';
import 'my_fleet_screen.dart';

class FinalEditVehicleDetailsScreen extends StatefulWidget {
  const FinalEditVehicleDetailsScreen({super.key});

  @override
  State<FinalEditVehicleDetailsScreen> createState() =>
      _FinalEditVehicleDetailsScreenState();
}

class _FinalEditVehicleDetailsScreenState
    extends State<FinalEditVehicleDetailsScreen> {
  //VehicleController vehicleController = Get.put(VehicleController());
  //ViewVehicleDetailsController viewVehicleDetailsController =
  //Get.put(ViewVehicleDetailsController());
  //ViewVehicleDetailsController viewVehicleDetailsController = Get.find();

  AddPureDcVehicleController addPureDcVehicleController =
      Get.put(AddPureDcVehicleController());
  EditVehicleDetailsController editVehicleDetailsController =
      Get.put(EditVehicleDetailsController());

  String hidePartOfNumber(String rfid) {
    // Convert the number to a string
    String numberString = rfid.toString();

    // Determine how many digits to hide (in this example, hiding all but the last four digits)
    int digitsToShow = 6;
    int visibleDigits = numberString.length - digitsToShow;

    // Replace the visible digits with asterisks
    String hiddenDigits = 'X' * visibleDigits;

    // Concatenate the visible part of the number with the hidden part
    String maskedNumber = hiddenDigits + numberString.substring(visibleDigits);

    return maskedNumber;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Scaffold(
          backgroundColor: AppColor.cBackGround,
          body: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                simpleMyAppBar(
                    title: "",
                    onTap: () {
                      editVehicleDetailsController.country.refresh();
                      editVehicleDetailsController.vehlicType.refresh();
                      editVehicleDetailsController.plateNoController.clear();
                      editVehicleDetailsController.driverNameController.clear();
                      editVehicleDetailsController.vehicleType.refresh();
                      editVehicleDetailsController.fuelType.refresh();
                      editVehicleDetailsController.offlineController.clear();
                      editVehicleDetailsController.quotaType.refresh();
                      editVehicleDetailsController.quotaClass.refresh();
                      editVehicleDetailsController.quotaValue.refresh();
                      Get.back();
                      addPureDcVehicleController.tagLists.clear();
                      editVehicleDetailsController.vehicleDetails.clear();
                      editVehicleDetailsController.countryList.clear();
                      editVehicleDetailsController.vehlicTypeList.clear();
                      editVehicleDetailsController.vehicleTypeList.clear();
                      editVehicleDetailsController.fuelList.clear();
                      editVehicleDetailsController.quotaTypeList.clear();
                      editVehicleDetailsController.quotaClassList.clear();
                    },
                    backString: "Back".trr),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Obx(() {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            addDcTitleRowWidget(
                              title: "Vehicle details".trr,
                              isSelected: addPureDcVehicleController
                                  .isVehicleDetail.value,
                              onTap: () {
                                addPureDcVehicleController
                                        .isVehicleDetail.value =
                                    !addPureDcVehicleController
                                        .isVehicleDetail.value;
                              },
                            ),
                            verticalSpace(16),
                            addPureDcVehicleController.isVehicleDetail.value
                                ? Container(
                                    padding: const EdgeInsets.only(
                                        right: 16, left: 16),
                                    decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(12)),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Country".trr,
                                          style: pRegular13,
                                        ),
                                        verticalSpace(6),
                                        DropdownButtonFormField(
                                          value: editVehicleDetailsController
                                                  .country.value.isEmpty
                                              ? "KSA"
                                              : editVehicleDetailsController
                                                  .country.value,
                                          items: editVehicleDetailsController
                                              .countryList
                                              .map((data) {
                                            return DropdownMenuItem(
                                              value: data.TYPECODE,
                                              child: Text(
                                                data.TYPEDESC,
                                                style: pMedium12,
                                                textAlign: TextAlign.center,
                                              ),
                                            );
                                          }).toList(),
                                          onChanged: (value) {
                                            print(value);
                                            editVehicleDetailsController.country
                                                .value = value.toString();
                                            // PaginatedController.selectedDiv.value =
                                            //     value.toString();
                                            // reportController.loadBranch(value.toString());
                                            //vehicleList.value = value.toString();
                                          },
                                          style: pRegular14.copyWith(
                                              color: AppColor.cLabel),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          dropdownColor: AppColor.cLightGrey,
                                          icon: assetSvdImageWidget(
                                              image: DefaultImages.dropDownIcn),
                                          decoration: InputDecoration(
                                            hintText: 'Country'.trr,
                                            hintStyle: pRegular14.copyWith(
                                                color: AppColor.cHintFont),
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 16, right: 16),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            disabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                          ),
                                        ),
                                        verticalSpace(16),
                                        Text(
                                          "Plate No".trr,
                                          style: pRegular13,
                                        ),
                                        verticalSpace(6),
                                        DropdownButtonFormField(
                                          value: editVehicleDetailsController
                                                  .vehlicType.value.isEmpty
                                              ? null
                                              : editVehicleDetailsController
                                                  .vehlicType.value,
                                          items: editVehicleDetailsController
                                              .vehlicTypeList
                                              .map((data) {
                                            return DropdownMenuItem(
                                              value: data.TYPECODE,
                                              child: Text(
                                                data.TYPEDESC,
                                                style: pMedium12,
                                                textAlign: TextAlign.center,
                                              ),
                                            );
                                          }).toList(),
                                          onChanged: (value) {
                                            print(value);
                                            editVehicleDetailsController
                                                .vehlicType
                                                .value = value.toString();
                                            // PaginatedController.selectedDiv.value =
                                            //     value.toString();
                                            // reportController.loadBranch(value.toString());
                                            //vehicleList.value = value.toString();
                                          },
                                          style: pRegular14.copyWith(
                                              color: AppColor.cLabel),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          dropdownColor: AppColor.cLightGrey,
                                          icon: assetSvdImageWidget(
                                              image: DefaultImages.dropDownIcn),
                                          decoration: InputDecoration(
                                            hintText: 'Type'.trr,
                                            hintStyle: pRegular14.copyWith(
                                                color: AppColor.cHintFont),
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 16, right: 16),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            disabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                          ),
                                        ),
                                        Gap(4),
                                        CommonTextField(
                                          controller:
                                              editVehicleDetailsController
                                                  .plateNoController,
                                          labelText: ''.trr,
                                          hintText: ''.trr,
                                        ),
                                        //Gap(6),

                                        // Container(
                                        //   height: 44,
                                        //   decoration: BoxDecoration(
                                        //       borderRadius:
                                        //           BorderRadius.circular(6),
                                        //       border: Border.all(
                                        //           color: AppColor.cLightGrey)),
                                        //   child: Row(
                                        //     children: [
                                        //       platDropDownWidget(
                                        //           value: addPureDcVehicleController
                                        //               .selectedPlatValue.value,
                                        //           list: addPureDcVehicleController
                                        //               .plateList,
                                        //           onChanged: (value) {
                                        //             addPureDcVehicleController
                                        //                 .selectedPlatValue
                                        //                 .value = value.toString();
                                        //           }),
                                        //       Expanded(
                                        //         child: plateTextFieldWidget(
                                        //             addPureDcVehicleController
                                        //                 .plateController,
                                        //             'Enter Plate number'.trr),
                                        //       )
                                        //     ],
                                        //   ),
                                        // ),
                                        verticalSpace(16),
                                        Text(
                                          "Vehicle Type".trr,
                                          style: pRegular13,
                                        ),
                                        verticalSpace(6),
                                        DropdownButtonFormField(
                                          value: editVehicleDetailsController
                                                  .vehicleType.value.isEmpty
                                              ? null
                                              : editVehicleDetailsController
                                                  .vehicleType.value,
                                          items: editVehicleDetailsController
                                              .vehicleTypeList
                                              .map((data) {
                                            return DropdownMenuItem(
                                              value: data.TYPECODE,
                                              child: Text(
                                                data.TYPEDESC,
                                                style: pMedium12,
                                                textAlign: TextAlign.center,
                                              ),
                                            );
                                          }).toList(),
                                          onChanged: (value) {
                                            print(value);
                                            editVehicleDetailsController
                                                .vehicleType
                                                .value = value.toString();
                                            // PaginatedController.selectedDiv.value =
                                            //     value.toString();
                                            // reportController.loadBranch(value.toString());
                                            //vehicleList.value = value.toString();
                                          },
                                          style: pRegular14.copyWith(
                                              color: AppColor.cLabel),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          dropdownColor: AppColor.cLightGrey,
                                          icon: assetSvdImageWidget(
                                              image: DefaultImages.dropDownIcn),
                                          decoration: InputDecoration(
                                            hintText: 'Vehicle Type'.trr,
                                            hintStyle: pRegular14.copyWith(
                                                color: AppColor.cHintFont),
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 16, right: 16),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            disabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                          ),
                                        ),
                                        // CommonHintDropdownWidget(
                                        //   hint: "Select vehicle type".trr,
                                        //   labelText: "Vehicle type".trr,
                                        //   value: addPureDcVehicleController
                                        //       .selectedVehicleValue.value,
                                        //   list: addPureDcVehicleController
                                        //       .vehicleTypeList,
                                        //   onChanged: (value) {
                                        //     addPureDcVehicleController
                                        //         .selectedVehicleValue.value = value;
                                        //   },
                                        // ),
                                        verticalSpace(16),
                                        Text(
                                          "Fuel".trr,
                                          style: pRegular13,
                                        ),
                                        verticalSpace(6),
                                        DropdownButtonFormField(
                                          value: editVehicleDetailsController
                                                  .fuelType.value.isEmpty
                                              ? null
                                              : editVehicleDetailsController
                                                  .fuelType.value,
                                          items: editVehicleDetailsController
                                              .fuelList
                                              .map((data) {
                                            return DropdownMenuItem(
                                              value: data.TYPECODE,
                                              child: Text(
                                                data.TYPEDESC,
                                                style: pMedium12,
                                                textAlign: TextAlign.center,
                                              ),
                                            );
                                          }).toList(),
                                          onChanged: (value) {
                                            print(value);
                                            editVehicleDetailsController
                                                .fuelType
                                                .value = value.toString();
                                            // PaginatedController.selectedDiv.value =
                                            //     value.toString();
                                            // reportController.loadBranch(value.toString());
                                            //vehicleList.value = value.toString();
                                          },
                                          style: pRegular14.copyWith(
                                              color: AppColor.cLabel),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          dropdownColor: AppColor.cLightGrey,
                                          icon: assetSvdImageWidget(
                                              image: DefaultImages.dropDownIcn),
                                          decoration: InputDecoration(
                                            hintText: 'Fuel'.trr,
                                            hintStyle: pRegular14.copyWith(
                                                color: AppColor.cHintFont),
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 16, right: 16),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            disabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                          ),
                                        ),
                                        // CommonHintDropdownWidget(
                                        //   hint: "Select Fuel".trr,
                                        //   labelText: "Fuel".trr,
                                        //   list: addPureDcVehicleController.fuelList,
                                        //   value: addPureDcVehicleController
                                        //       .selectedFuelValue.value,
                                        //   onChanged: (value) {
                                        //     addPureDcVehicleController
                                        //         .selectedFuelValue.value = value;
                                        //   },
                                        // ),
                                        verticalSpace(16),
                                        Text(
                                          "Tanks".trr,
                                          style: pRegular13,
                                        ),
                                        verticalSpace(6),
                                        Row(
                                          children: [
                                            Expanded(
                                                flex: 1,
                                                child: CommonIconButton(
                                                  title: '1',
                                                  iconData:
                                                      addPureDcVehicleController
                                                              .isOne.value
                                                          ? DefaultImages
                                                              .checkIcn
                                                          : null,
                                                  btnColor:
                                                      addPureDcVehicleController
                                                              .isOne.value
                                                          ? AppColor
                                                              .themeDarkBlueColor
                                                          : AppColor.cLightGrey,
                                                  textColor:
                                                      addPureDcVehicleController
                                                              .isOne.value
                                                          ? AppColor.cWhiteFont
                                                          : AppColor.cText,
                                                  onPressed: () {
                                                    addPureDcVehicleController
                                                        .isOne.value = true;
                                                    addPureDcVehicleController
                                                        .isTwo.value = false;
                                                    editVehicleDetailsController
                                                        .tanks.value = "1";
                                                  },
                                                )),
                                            horizontalSpace(8),
                                            Expanded(
                                                flex: 1,
                                                child: CommonIconButton(
                                                  title: '2',
                                                  iconData:
                                                      addPureDcVehicleController
                                                              .isTwo.value
                                                          ? DefaultImages
                                                              .checkIcn
                                                          : null,
                                                  btnColor:
                                                      addPureDcVehicleController
                                                              .isTwo.value
                                                          ? AppColor
                                                              .themeDarkBlueColor
                                                          : AppColor.cLightGrey,
                                                  textColor:
                                                      addPureDcVehicleController
                                                              .isTwo.value
                                                          ? AppColor.cWhiteFont
                                                          : AppColor.cText,
                                                  onPressed: () {
                                                    addPureDcVehicleController
                                                        .isOne.value = false;
                                                    addPureDcVehicleController
                                                        .isTwo.value = true;
                                                    editVehicleDetailsController
                                                        .tanks.value = "2";
                                                  },
                                                )),
                                          ],
                                        ),
                                        // verticalSpace(16),
                                        // CommonTextField(
                                        //   controller: addPureDcVehicleController
                                        //       .driverNameController,
                                        //   labelText: 'Driver Name'.trr,
                                        //   hintText: 'Enter driver’s name'.trr,
                                        // ),
                                        verticalSpace(16),
                                        CommonTextField(
                                          controller:
                                              editVehicleDetailsController
                                                  .driverNameController,
                                          labelText: 'Driver Name'.trr,
                                          hintText: 'Enter driver’s name'.trr,
                                        ),
                                        if (editVehicleDetailsController
                                                .isServiceType.value !=
                                            "D")
                                          verticalSpace(16),
                                        if (editVehicleDetailsController
                                                .isServiceType.value !=
                                            "D")
                                          Text(
                                            'Offline limit per refuel'.tr,
                                            style: pRegular13,
                                          ),
                                        if (editVehicleDetailsController
                                                .isServiceType.value !=
                                            "D")
                                          verticalSpace(6),
                                        if (editVehicleDetailsController
                                                .isServiceType.value !=
                                            "D")
                                          SizedBox(
                                            height: 50,
                                            child: SingleChildScrollView(
                                              child: Row(
                                                children: [
                                                  // SizedBox(
                                                  //   width: 20,
                                                  //   height: 20,
                                                  //   child: Checkbox(
                                                  //     value:
                                                  //         editVehicleDetailsController
                                                  //             .isOffline.value,
                                                  //     onChanged: (value) {
                                                  //       setState(() {
                                                  //         editVehicleDetailsController
                                                  //             .isOffline
                                                  //             .value = value!;
                                                  //         print(
                                                  //             editVehicleDetailsController
                                                  //                 .isOffline
                                                  //                 .value);
                                                  //       });
                                                  //     },
                                                  //     shape:
                                                  //         RoundedRectangleBorder(
                                                  //             borderRadius:
                                                  //                 BorderRadius
                                                  //                     .circular(
                                                  //                         4)),
                                                  //     activeColor: AppColor
                                                  //         .themeDarkBlueColor,
                                                  //   ),
                                                  // ),
                                                  // horizontalSpace(10),
                                                  Expanded(
                                                    //flex: 2,
                                                    child: CommonTextField(
                                                        // readOnly:
                                                        //     editVehicleDetailsController
                                                        //                 .isOffline
                                                        //                 .value ==
                                                        //             false
                                                        //         ? true
                                                        //         : false,
                                                        // filled:
                                                        //     editVehicleDetailsController
                                                        //                 .isOffline
                                                        //                 .value ==
                                                        //             false
                                                        //         ? true
                                                        //         : false,
                                                        fillColor:
                                                            AppColor.cLightGrey,
                                                        controller:
                                                            editVehicleDetailsController
                                                                .offlineController,
                                                        labelText: '',
                                                        hintText: '0.00'.tr,
                                                        keyboardType: TextInputType
                                                            .numberWithOptions(
                                                                signed: true,
                                                                decimal: true)),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        // Text(
                                        //   "Offline limit per refuel".trr,
                                        //   style: pRegular13,
                                        // ),
                                        // verticalSpace(6),
                                        // Container(
                                        //   height: 44,
                                        //   decoration: BoxDecoration(
                                        //       borderRadius:
                                        //           BorderRadius.circular(6),
                                        //       border: Border.all(
                                        //           color: AppColor.cLightGrey)),
                                        //   child: Row(
                                        //     children: [
                                        //       platDropDownWidget(
                                        //           value:
                                        //               addPureDcVehicleController
                                        //                   .selectedLimitValue
                                        //                   .value,
                                        //           list: addPureDcVehicleController
                                        //               .limitList,
                                        //           onChanged: (value) {
                                        //             addPureDcVehicleController
                                        //                 .selectedLimitValue
                                        //                 .value = value.toString();
                                        //           }),
                                        //       Expanded(
                                        //         child: plateTextFieldWidget(
                                        //             addPureDcVehicleController
                                        //                 .limitController,
                                        //             '00'),
                                        //       )
                                        //     ],
                                        //   ),
                                        // ),
                                        if (editVehicleDetailsController
                                                .isServiceType.value !=
                                            "D")
                                          verticalSpace(6),
                                        if (editVehicleDetailsController
                                                .isServiceType.value !=
                                            "D")
                                          Text(
                                            "* ${"Offline limit allows driver to top up the tank with the predefined number of litres or within the set amount.".tr}",
                                            style: pRegular10.copyWith(
                                                fontSize: 11),
                                          ),
                                        verticalSpace(16),
                                        CommonTextField(
                                          controller:
                                              editVehicleDetailsController
                                                  .passwordController,
                                          labelText: 'Password'.trr,
                                          hintText: 'Set driver password'.trr,
                                          keyboardType: TextInputType.number,
                                          maxLength: 4,
                                        ),
                                        verticalSpace(16),
                                        CommonTextField(
                                          controller:
                                              editVehicleDetailsController
                                                  .vehicleReferenceController,
                                          labelText:
                                              'Vehicle Reference Name'.trr,
                                          hintText: 'Enter Reference Name'.trr,
                                          maxLength: 20,
                                        ),
                                        verticalSpace(32)
                                      ],
                                    ),
                                  )
                                : SizedBox(),
                            horizontalDivider(),
                            // addDcTitleRowWidget(
                            //   title: "Vehicle level".trr,
                            //   isSelected:
                            //       addPureDcVehicleController.isVehicleLevel.value,
                            //   onTap: () {
                            //     addPureDcVehicleController.isVehicleLevel.value =
                            //         !addPureDcVehicleController
                            //             .isVehicleLevel.value;
                            //   },
                            // ),
                            // verticalSpace(16),
                            // addPureDcVehicleController.isVehicleLevel.value
                            //     ? Column(
                            //         crossAxisAlignment: CrossAxisAlignment.start,
                            //         children: [
                            //           CommonHintDropdownWidget(
                            //             hint: "Select division".trr,
                            //             labelText: "Division".trr,
                            //             value: addPureDcVehicleController
                            //                 .selectDivisionValue.value,
                            //             list: addPureDcVehicleController
                            //                 .divisionList,
                            //             onChanged: (value) {
                            //               addPureDcVehicleController
                            //                   .selectDivisionValue.value = value;
                            //             },
                            //           ),
                            //           verticalSpace(16),
                            //           CommonHintDropdownWidget(
                            //             hint: "Select branch".trr,
                            //             labelText: "Branch".trr,
                            //             list:
                            //                 addPureDcVehicleController.branchList,
                            //             value: addPureDcVehicleController
                            //                 .selectedBranchValue.value,
                            //             onChanged: (value) {
                            //               addPureDcVehicleController
                            //                   .selectedBranchValue.value = value;
                            //             },
                            //           ),
                            //           verticalSpace(16),
                            //           CommonHintDropdownWidget(
                            //             hint: "Select department".trr,
                            //             labelText: "Department".trr,
                            //             list: addPureDcVehicleController
                            //                 .departmentList,
                            //             value: addPureDcVehicleController
                            //                 .selectedDepartmentValue.value,
                            //             onChanged: (value) {
                            //               addPureDcVehicleController
                            //                   .selectedDepartmentValue
                            //                   .value = value;
                            //             },
                            //           ),
                            //           verticalSpace(16),
                            //           CommonHintDropdownWidget(
                            //             hint: "Select operation".trr,
                            //             labelText: "Operation".trr,
                            //             list: addPureDcVehicleController
                            //                 .operationList,
                            //             value: addPureDcVehicleController
                            //                 .selectedOperationValue.value,
                            //             onChanged: (value) {
                            //               addPureDcVehicleController
                            //                   .selectedOperationValue
                            //                   .value = value;
                            //             },
                            //           ),
                            //           verticalSpace(32)
                            //         ],
                            //       )
                            //     : SizedBox(),
                            // horizontalDivider(),
                            addDcTitleRowWidget(
                              title: "Refuel limits".trr,
                              isSelected: addPureDcVehicleController
                                  .isRefuelLimits.value,
                              onTap: () {
                                addPureDcVehicleController
                                        .isRefuelLimits.value =
                                    !addPureDcVehicleController
                                        .isRefuelLimits.value;
                              },
                            ),
                            verticalSpace(16),
                            addPureDcVehicleController.isRefuelLimits.value
                                ? Padding(
                                    padding: const EdgeInsets.only(
                                        right: 16, left: 16),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Quota type".trr,
                                          style: pRegular13,
                                        ),
                                        verticalSpace(6),
                                        DropdownButtonFormField(
                                          value: editVehicleDetailsController
                                                  .quotaType.value.isEmpty
                                              ? null
                                              : editVehicleDetailsController
                                                  .quotaType.value,
                                          items: editVehicleDetailsController
                                              .quotaTypeList
                                              .map((data) {
                                            return DropdownMenuItem(
                                              value: data.TYPECODE,
                                              child: Text(
                                                data.TYPEDESC,
                                                style: pMedium12,
                                                textAlign: TextAlign.center,
                                              ),
                                            );
                                          }).toList(),
                                          onChanged: (value) {
                                            print(value);
                                            editVehicleDetailsController
                                                .quotaType
                                                .value = value.toString();
                                            // PaginatedController.selectedDiv.value =
                                            //     value.toString();
                                            // reportController.loadBranch(value.toString());
                                            //vehicleList.value = value.toString();
                                          },
                                          style: pRegular14.copyWith(
                                              color: AppColor.cLabel),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          dropdownColor: AppColor.cLightGrey,
                                          icon: assetSvdImageWidget(
                                              image: DefaultImages.dropDownIcn),
                                          decoration: InputDecoration(
                                            hintText: 'Quota type'.trr,
                                            hintStyle: pRegular14.copyWith(
                                                color: AppColor.cHintFont),
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 16, right: 16),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            disabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                          ),
                                        ),
                                        // CommonDropdownButtonWidget(
                                        //   labelText: "Quota type".trr,
                                        //   value: addPureDcVehicleController
                                        //       .selectedQuotaTypeValue.value,
                                        //   list: addPureDcVehicleController
                                        //       .quotaTypeList,
                                        //   onChanged: (value) {
                                        //     addPureDcVehicleController
                                        //         .selectedQuotaTypeValue
                                        //         .value = value;
                                        //   },
                                        // ),
                                        verticalSpace(16),
                                        Text(
                                          "Quota class".trr,
                                          style: pRegular13,
                                        ),
                                        verticalSpace(6),
                                        DropdownButtonFormField(
                                          value: editVehicleDetailsController
                                                  .quotaClass.value.isEmpty
                                              ? null
                                              : editVehicleDetailsController
                                                  .quotaClass.value,
                                          items: editVehicleDetailsController
                                              .quotaClassList
                                              .map((data) {
                                            return DropdownMenuItem(
                                              value: data.TYPECODE,
                                              child: Text(
                                                data.TYPEDESC,
                                                style: pMedium12,
                                                textAlign: TextAlign.center,
                                              ),
                                            );
                                          }).toList(),
                                          onChanged: (value) {
                                            print(value);
                                            editVehicleDetailsController
                                                .quotaClass
                                                .value = value.toString();
                                            // PaginatedController.selectedDiv.value =
                                            //     value.toString();
                                            // reportController.loadBranch(value.toString());
                                            //vehicleList.value = value.toString();
                                          },
                                          style: pRegular14.copyWith(
                                              color: AppColor.cLabel),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          dropdownColor: AppColor.cLightGrey,
                                          icon: assetSvdImageWidget(
                                              image: DefaultImages.dropDownIcn),
                                          decoration: InputDecoration(
                                            hintText: 'Quota class'.trr,
                                            hintStyle: pRegular14.copyWith(
                                                color: AppColor.cHintFont),
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 16, right: 16),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            disabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: BorderSide(
                                                color: AppColor.cBorder,
                                              ),
                                            ),
                                          ),
                                        ),
                                        // CommonDropdownButtonWidget(
                                        //   labelText: "Quota class".trr,
                                        //   value: addPureDcVehicleController
                                        //       .selectedQuotaLimitValue.value,
                                        //   list: addPureDcVehicleController
                                        //       .quotaLimitList,
                                        //   onChanged: (value) {
                                        //     addPureDcVehicleController
                                        //         .selectedQuotaLimitValue
                                        //         .value = value;
                                        //   },
                                        //   filledColor: AppColor.lightBlueColor,
                                        // ),
                                        verticalSpace(16),
                                        CommonTextField(
                                            controller:
                                                editVehicleDetailsController
                                                    .quotaValController,
                                            labelText: "WAIE Quota Value".trr,
                                            hintText: '0'.trr,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                    signed: true,
                                                    decimal: true)),
                                        verticalSpace(16),
                                        CommonTextField(
                                          readOnly: true,
                                          controller:
                                              editVehicleDetailsController
                                                  .quotaBalController,
                                          labelText: "WAIE Quota Balance".trr,
                                          filled: true,
                                          fillColor: AppColor.cLightGrey,
                                          hintText: '0'.trr,
                                        ),
                                        verticalSpace(32),
                                        /* CommonDropdownButtonWidget(
                                          labelText: "Quota value".trr,
                                          value: addPureDcVehicleController
                                              .selectedQuotaValue.value,
                                          list: addPureDcVehicleController
                                              .quotaValueList,
                                          onChanged: (value) {
                                            addPureDcVehicleController
                                                .selectedQuotaValue.value = value;
                                          },
                                          filledColor: AppColor.lightBlueColor,
                                        ),*/
                                        //verticalSpace(16),
                                        // CommonTextField(
                                        //   controller: addPureDcVehicleController
                                        //       .WAIEquotaValueController,
                                        //   labelText: "WAIE quota balance".trr,
                                        //   hintText: 'e.g. 00'.trr,
                                        // ),
                                        /* CommonDropdownButtonWidget(
                                          labelText: "WAIE quota balance".trr,
                                          value: addPureDcVehicleController
                                              .selectedQuotaBalanceValue.value,
                                          list: addPureDcVehicleController
                                              .quotaBalanceList,
                                          onChanged: (value) {
                                            addPureDcVehicleController
                                                .selectedQuotaBalanceValue
                                                .value = value;
                                          },
                                          filledColor: AppColor.lightBlueColor,
                                        ),*/
                                        if (Constants.isDCShow == 'Y')
                                          editVehicleDetailsController
                                                      .isDCActive.value ==
                                                  true
                                              ? Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 16,
                                                      vertical: 16),
                                                  child: Row(
                                                    children: [
                                                      SizedBox(
                                                        height: 24,
                                                        width: 24,
                                                        child: Checkbox(
                                                          value: editVehicleDetailsController
                                                                      .isDC
                                                                      .value ==
                                                                  'Y'
                                                              ? addPureDcVehicleController
                                                                  .isDigitalCoupon
                                                                  .value = true
                                                              : addPureDcVehicleController
                                                                  .isDigitalCoupon
                                                                  .value,
                                                          onChanged:
                                                              editVehicleDetailsController
                                                                          .isDC
                                                                          .value ==
                                                                      'Y'
                                                                  ? null // Disables the checkbox when isDCActive is true
                                                                  : (value) {
                                                                      addPureDcVehicleController
                                                                          .isDigitalCoupon
                                                                          .value = value!;
                                                                      if (value ==
                                                                          false) {
                                                                        addPureDcVehicleController
                                                                            .isSticker
                                                                            .value = false;
                                                                      } else {
                                                                        if (addPureDcVehicleController.isSticker.value ==
                                                                            true) {
                                                                          addPureDcVehicleController
                                                                              .coupType
                                                                              .value = "S";
                                                                        } else {
                                                                          addPureDcVehicleController
                                                                              .coupType
                                                                              .value = "Q";
                                                                        }
                                                                      }
                                                                    },
                                                          shape: RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          6)),
                                                          activeColor: AppColor
                                                              .themeDarkBlueColor,
                                                        ),
                                                      ),
                                                      horizontalSpace(6),
                                                      Text(
                                                        "Can use Digital Coupon (DC)"
                                                            .tr,
                                                        style: pRegular13,
                                                      )
                                                    ],
                                                  ),
                                                )
                                              : SizedBox(),
                                        addPureDcVehicleController
                                                    .isDigitalCoupon.value ==
                                                true
                                            ? Column(
                                                children: [
                                                  Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 16,
                                                        vertical: 16),
                                                    child: Row(
                                                      children: [
                                                        SizedBox(
                                                          height: 24,
                                                          width: 24,
                                                          child: Checkbox(
                                                            value: editVehicleDetailsController
                                                                            .isCoup
                                                                            .value ==
                                                                        "S" ||
                                                                    editVehicleDetailsController
                                                                            .isCoup
                                                                            .value ==
                                                                        "B"
                                                                ? addPureDcVehicleController
                                                                        .isSticker
                                                                        .value =
                                                                    true
                                                                : addPureDcVehicleController
                                                                    .isSticker
                                                                    .value,
                                                            onChanged:
                                                                editVehicleDetailsController
                                                                            .isDC
                                                                            .value ==
                                                                        'Y'
                                                                    ? null
                                                                    : (value) {
                                                                        addPureDcVehicleController
                                                                            .isSticker
                                                                            .value = value!;
                                                                        print(
                                                                            "isSticker>>>>>>> ${value}");
                                                                        print(
                                                                            "editVehicleDetailsController.isCoup.value>>>>>>> ${editVehicleDetailsController.isCoup.value}");
                                                                        print(
                                                                            "editVehicleDetailsController.isCoup.value>>>>>>> ${editVehicleDetailsController.isCoup}");
                                                                        editVehicleDetailsController
                                                                            .coupType
                                                                            .value = value ==
                                                                                true
                                                                            ? "S"
                                                                            : "Q";
                                                                      },
                                                            shape: RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            6)),
                                                            activeColor: AppColor
                                                                .themeDarkBlueColor,
                                                          ),
                                                        ),
                                                        horizontalSpace(6),
                                                        Text(
                                                          "Sticker".tr,
                                                          style: pRegular13,
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                  addPureDcVehicleController
                                                              .isSticker
                                                              .value ==
                                                          false
                                                      ? CommonTextField(
                                                          controller:
                                                              editVehicleDetailsController
                                                                  .driverMobileNoController,
                                                          labelText:
                                                              "Mobile number"
                                                                  .tr,
                                                          hintText:
                                                              'XXX XXX XXXX',
                                                          keyboardType:
                                                              TextInputType
                                                                  .phone,
                                                          maxLength: 10,
                                                          validator: (value) {
                                                            return Validator
                                                                .validateMobile(
                                                                    value);
                                                          },
                                                        )
                                                      : SizedBox(),
                                                  verticalSpace(16),
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                          child:
                                                              CommonTextField(
                                                        controller:
                                                            editVehicleDetailsController
                                                                .dcFuelQuotaController,
                                                        labelText:
                                                            'DC fuel quota (liters)'
                                                                .tr,
                                                        hintText:
                                                            editVehicleDetailsController
                                                                        .dcFuelQuotaController ==
                                                                    ""
                                                                ? '0'
                                                                : '',
                                                        keyboardType:
                                                            TextInputType
                                                                .number,
                                                      )),
                                                      horizontalSpace(16),
                                                      Expanded(
                                                          child:
                                                              CommonTextField(
                                                        readOnly: true,
                                                        controller:
                                                            editVehicleDetailsController
                                                                .dcQuotaRemController,
                                                        labelText:
                                                            'DC fuel balance (liters)'
                                                                .tr,
                                                        hintText:
                                                            editVehicleDetailsController
                                                                        .dcQuotaRemController ==
                                                                    ""
                                                                ? '0'
                                                                : '',
                                                        keyboardType:
                                                            TextInputType
                                                                .number,
                                                        filled: true,
                                                        fillColor:
                                                            AppColor.cLightGrey,
                                                        // filled: true,
                                                        // fillColor:
                                                        //     AppColor.lightBlueColor,
                                                      )),
                                                    ],
                                                  ),
                                                  verticalSpace(16),
                                                ],
                                              )
                                            : SizedBox()
                                      ],
                                    ),
                                  )
                                : SizedBox(),
                            horizontalDivider(),
                            addDcTitleRowWidget(
                              title: "Filling days".trr,
                              isSelected: addPureDcVehicleController
                                  .isFillingDays.value,
                              onTap: () {
                                addPureDcVehicleController.isFillingDays.value =
                                    !addPureDcVehicleController
                                        .isFillingDays.value;
                              },
                            ),
                            verticalSpace(16),
                            addPureDcVehicleController.isFillingDays.value
                                ? Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ChipsChoice<int>.multiple(
                                        value: addPureDcVehicleController
                                            .tag.value,
                                        onChanged: (val) {
                                          addPureDcVehicleController.tagLists
                                              .clear();
                                          addPureDcVehicleController.tag.value =
                                              val;
                                          print(
                                              "addPureDcVehicleController.tag.value");
                                          print(addPureDcVehicleController
                                              .tag.value);
                                          addPureDcVehicleController.tags = val
                                              .map((i) => i.toString())
                                              .join(",");
                                        },
                                        choiceItems:
                                            C2Choice.listFrom<int, String>(
                                          source: addPureDcVehicleController
                                              .daysList,
                                          value: (i, v) => i,
                                          label: (i, v) => v,
                                        ),
                                        choiceBuilder: (item, i) {
                                          return customChip(
                                            label: item.label.toString().trr,
                                            selected: item.selected,
                                            onSelect: item.select!,
                                          );
                                        },
                                        wrapped: true,
                                      ),
                                      verticalSpace(32)
                                    ],
                                  )
                                : SizedBox(),
                            verticalSpace(16),
                            CommonTextField(
                              readOnly: true,
                              filled: true,
                              fillColor: AppColor.cLightGrey,
                              controller: editVehicleDetailsController
                                  .insTermDateController,
                              labelText: editVehicleDetailsController
                                          .serviceStatus.value !=
                                      "T" //"Division".trr,
                                  ? editVehicleDetailsController
                                              .serviceStatus.value ==
                                          "T"
                                      ? "Installation Date".trr
                                      : "Activation Date".trr
                                  : "Terminated Date".trr,
                              hintText: '',
                            ),
                            verticalSpace(16),
                            CommonTextField(
                              readOnly: true,
                              filled: true,
                              fillColor: AppColor.cLightGrey,
                              controller: editVehicleDetailsController
                                  .serialXIDController,
                              labelText: editVehicleDetailsController
                                          .serviceType.value ==
                                      "C" //"Division".trr,
                                  ? "Card No"
                                  : "RFID",
                              hintText: '',
                            ),
                          ],
                        );
                      }),
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(color: AppColor.cLightGrey),
            child: Row(
              children: [
                Expanded(
                  child: CommonButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      editVehicleDetailsController.country.refresh();
                      editVehicleDetailsController.vehlicType.refresh();
                      editVehicleDetailsController.plateNoController.clear();
                      editVehicleDetailsController.driverNameController.clear();
                      editVehicleDetailsController.vehicleType.refresh();
                      editVehicleDetailsController.fuelType.refresh();
                      editVehicleDetailsController.offlineController.clear();
                      editVehicleDetailsController.quotaType.refresh();
                      editVehicleDetailsController.quotaClass.refresh();
                      editVehicleDetailsController.quotaValue.refresh();
                      addPureDcVehicleController.tagLists.clear();
                      editVehicleDetailsController.vehicleDetails.clear();
                      editVehicleDetailsController.countryList.clear();
                      editVehicleDetailsController.vehlicTypeList.clear();
                      editVehicleDetailsController.vehicleTypeList.clear();
                      editVehicleDetailsController.fuelList.clear();
                      editVehicleDetailsController.quotaTypeList.clear();
                      editVehicleDetailsController.quotaClassList.clear();
                      Get.back();
                    },
                    textColor: AppColor.cText,
                    btnColor: AppColor.cBackGround,
                  ),
                ),
                horizontalSpace(16),
                Expanded(
                  child: CommonButton(
                    title: 'Save'.trr,
                    onPressed: () {
                      // Loader.showLoader();
                      // Future.delayed(const Duration(seconds: 3), () {
                      //   Loader.hideLoader();
                      //   Get.back();
                      // });
                      if (editVehicleDetailsController
                          .plateNoController.text.isEmpty) {
                        if (editVehicleDetailsController.isPlateNo !=
                            editVehicleDetailsController.isSerialID) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: AppColor.themeOrangeColor,
                              content: Center(
                                child: Text("Plate No. is required.",
                                    style: pBold14.copyWith(
                                        color: AppColor.cBlackFont)),
                              ),
                            ),
                          );
                        } else {
                          editVehicleDetailsController.saveVehicle();
                        }
                      }
                      // else if (editVehicleDetailsController.isOffline.value ==
                      //             true &&
                      //         editVehicleDetailsController
                      //             .offlineController.text.isEmpty ||
                      //     editVehicleDetailsController.isOffline.value ==
                      //             true &&
                      //         editVehicleDetailsController
                      //                 .offlineController.text ==
                      //             "0") {
                      //   ScaffoldMessenger.of(context).showSnackBar(
                      //     SnackBar(
                      //       backgroundColor: AppColor.themeOrangeColor,
                      //       content: Center(
                      //         child: Text("Invalid offline limit!",
                      //             style: pBold14.copyWith(
                      //                 color: AppColor.cBlackFont)),
                      //       ),
                      //     ),
                      //   );
                      // }
                      else if (Constants.isDCShow == 'Y' &&
                          editVehicleDetailsController.isDC.value == 'Y' &&
                          addPureDcVehicleController.isSticker.value == true) {
                        if (editVehicleDetailsController
                                    .dcFuelQuotaController.text
                                    .toString() ==
                                "0" ||
                            editVehicleDetailsController
                                    .dcFuelQuotaController.text
                                    .toString() ==
                                "0.0") {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: AppColor.themeOrangeColor,
                              content: Center(
                                child: Text(
                                    "Please set amount of DC fuel quote",
                                    style: pBold14.copyWith(
                                        color: AppColor.cBlackFont)),
                              ),
                            ),
                          );
                        } else if (editVehicleDetailsController
                                .passwordController.text.isEmpty ||
                            editVehicleDetailsController
                                    .passwordController.text ==
                                "") {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: AppColor.themeOrangeColor,
                              content: Center(
                                child: Text("Please set password",
                                    style: pBold14.copyWith(
                                        color: AppColor.cBlackFont)),
                              ),
                            ),
                          );
                        } else {
                          editVehicleDetailsController.saveVehicle();
                        }
                      } else if (editVehicleDetailsController.isDC.value ==
                              'N' ||
                          editVehicleDetailsController.isDC.isEmpty) {
                        if (editVehicleDetailsController
                                .passwordController.text.isEmpty ||
                            editVehicleDetailsController
                                    .passwordController.text ==
                                "") {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: AppColor.themeOrangeColor,
                              content: Center(
                                child: Text("Please set password",
                                    style: pBold14.copyWith(
                                        color: AppColor.cBlackFont)),
                              ),
                            ),
                          );
                        } else if (editVehicleDetailsController
                                .offlineController.text.isEmpty ||
                            editVehicleDetailsController
                                    .offlineController.text ==
                                "0" ||
                            editVehicleDetailsController
                                    .offlineController.text ==
                                "0.00") {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: AppColor.themeOrangeColor,
                              content: Center(
                                child: Text("Invalid offline limit!",
                                    style: pBold14.copyWith(
                                        color: AppColor.cBlackFont)),
                              ),
                            ),
                          );
                        } else {
                          editVehicleDetailsController.saveVehicle();
                        }
                      } else {
                        editVehicleDetailsController.saveVehicle();
                      }

                      // else if (editVehicleDetailsController
                      //     .driverNameController.text.isEmpty) {
                      //   ScaffoldMessenger.of(context).showSnackBar(
                      //     SnackBar(
                      //       backgroundColor: AppColor.themeOrangeColor,
                      //       content: Center(
                      //         child: Text("Driver Name is required.",
                      //             style: pBold14.copyWith(
                      //                 color: AppColor.cBlackFont)),
                      //       ),
                      //     ),
                      //   );
                      // }

                      // else if (editVehicleDetailsController
                      //         .passwordController.value ==
                      //     "") {
                      //   ScaffoldMessenger.of(context).showSnackBar(
                      //     SnackBar(
                      //       backgroundColor: AppColor.themeOrangeColor,
                      //       content: Text("Password is required.",
                      //           style:
                      //               pBold14.copyWith(color: AppColor.cBlackFont)),
                      //     ),
                      //   );
                      // } else if (editVehicleDetailsController
                      //     .vehicleReferenceController.text.isEmpty) {
                      //   ScaffoldMessenger.of(context).showSnackBar(
                      //     SnackBar(
                      //       backgroundColor: AppColor.themeOrangeColor,
                      //       content: Text("Vehicle Reference Name is required.",
                      //           style:
                      //               pBold14.copyWith(color: AppColor.cBlackFont)),
                      //     ),
                      //   );
                      // }

                      // var zero = "0";
                      // var one = "0";
                      // var two = "0";
                      // var three = "0";
                      // var four = "0";
                      // var five = "0";
                      // var six = "0";
                      // print(addPureDcVehicleController.tags);
                      // final split = addPureDcVehicleController.tags.split(',');
                      // for (int i = 0; i <= split.length; i++) {
                      //   var char = split[i];
                      //   print(split[i]);
                      //   if (char == "0") {
                      //     zero = "1";
                      //   } else if (char == "1") {
                      //     one = "1";
                      //   } else if (char == "2") {
                      //     two = "1";
                      //   } else if (char == "3") {
                      //     three = "1";
                      //   } else if (char == "4") {
                      //     four = "1";
                      //   } else if (char == "5") {
                      //     five = "1";
                      //   } else {
                      //     six = "0";
                      //   }
                      //   // List<String> vehicleserialids = [];
                      //   // vehicleserialids.add(vehicleSerialID.toString());
                      //   // String vehicleserialIDList = vehicleserialids.join(",");
                      //   // print(char);
                      //   // addPureDcVehicleController.tagList.add('1');
                      // }
                      // String list = "$zero,$one,$two,$three,$four,$five,$six";
                      // print(list);
                      //=======================================
                      //print(addPureDcVehicleController.tagList);
                      // vehicleController.myFleetList.value =
                      //     vehicleController.dummyFleetList.value;
                      // vehicleController.myFleetList.refresh();
                      // Get.back();
                      //addPureDcVehicleController.addFleets(1);
                    },
                    textColor: AppColor.cWhiteFont,
                    btnColor: AppColor.themeOrangeColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget customChip({
    required final String label,
    required final bool selected,
    required final Function(bool selected) onSelect,
  }) {
    return GestureDetector(
      onTap: () => onSelect(!selected),
      child: Container(
        height: 44,
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color:
                selected ? AppColor.themeDarkBlueColor : AppColor.cLightGrey),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(label,
                style: pRegular14.copyWith(
                    color: selected ? AppColor.cWhiteFont : AppColor.cText)),
            horizontalSpace(8),
            assetSvdImageWidget(
                image:
                    selected ? DefaultImages.cancelIcn : DefaultImages.addIcn,
                colorFilter: ColorFilter.mode(
                    selected ? AppColor.cWhiteFont : AppColor.cText,
                    BlendMode.srcIn),
                height: 16,
                width: 16),
          ],
        ),
      ),
    );
  }

  Widget platDropDownWidget(
      {List? list, String? value, Function(Object?)? onChanged}) {
    return Container(
      width: 120,
      decoration: BoxDecoration(
          color: AppColor.cLightGrey,
          borderRadius: BorderRadius.horizontal(left: Radius.circular(6)),
          border: Border.all(color: AppColor.cLightGrey)),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: DropdownButton(
        items: list!.map((data) {
          return DropdownMenuItem(
            value: data,
            child: Text(
              data,
              style: pRegular13.copyWith(color: AppColor.cText),
              overflow: TextOverflow.ellipsis,
            ),
          );
        }).toList(),
        onChanged: onChanged,
        value: value,
        dropdownColor: AppColor.cLightGrey,
        icon: assetSvdImageWidget(
            image: DefaultImages.blueArrowDownIcn,
            colorFilter: ColorFilter.mode(AppColor.cText, BlendMode.srcIn)),
        padding: EdgeInsets.zero,
        isExpanded: true,
        underline: Container(),
      ),
    );
  }

  Widget plateTextFieldWidget(TextEditingController controller, String hint) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: TextField(
        controller: controller,
        cursorColor: AppColor.cDarkGreyFont,
        style: pRegular13,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 10),
        ),
      ),
    );
  }
}

Widget availableStationTitleRowWidget(
    {required String title, required bool isSelected, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      color: AppColor.cBackGround,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pBold20,
          ),
          // assetSvdImageWidget(
          //     image: isSelected == true
          //         ? DefaultImages.arrowUpIcn
          //         : DefaultImages.dropDownIcn,
          //     width: 24,
          //     height: 24)
        ],
      ),
    ),
  );
}

Widget addDcTitleRowWidget(
    {required String title, required bool isSelected, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      color: AppColor.cBackGround,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pBold20,
          ),
          assetSvdImageWidget(
              image: isSelected == true
                  ? DefaultImages.arrowUpIcn
                  : DefaultImages.dropDownIcn,
              width: 24,
              height: 24)
        ],
      ),
    ),
  );
}
