import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:waie_app/models/loadplaces.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/utils/api_endpoints.dart';

class VehicleController extends GetxController {
  RxBool isMyFleet = true.obs;
  RxBool isTagInstallation = false.obs;
  RxBool isTagTransfer = false.obs;
  RxList myFleetList = [].obs;
  RxList dummyFleetList = [
    {
      "value": false.obs,
      "code": "Plate #",
      "status": "New",
      "title": "",
      "type": "",
      "driver": "",
      "quotaTotal": "",
      "quotaString": '',
      "division": "",
    }.obs,
    {
      "value": false.obs,
      "code": "0411XTA",
      "status": "Active",
      "title": "Nissan NV300",
      "type": "Diesel",
      "driver": "<PERSON> Manson",
      "quotaTotal": "50 / 300",
      "quotaString": 'Liters Monthly',
      "division": "Tabuk /\nAuto Parts /\nSales /\nCouriers",
    }.obs,
    {
      "value": false.obs,
      "code": "9886KTR",
      "status": "Inactive",
      "title": "Nissan NV300",
      "type": "Diesel",
      "driver": "Mark Manson",
      "quotaTotal": "50 / 300",
      "quotaString": 'Liters Monthly',
      "division": "Tabuk /\nAuto Parts /\nSales /\nCouriers",
    }.obs
  ].obs;
  RxList filterValueList = [
    {
      'title': 'Service type',
      "isExpand": false.obs,
      "subtitle": ["Service type1", 'Service type2', 'Service type3'],
    }.obs,
    {
      'title': 'Title',
      "isExpand": false.obs,
      "subtitle": ["Title1", 'Title2', 'Title3'],
    }.obs,
    {
      'title': 'Branch',
      "isExpand": false.obs,
      "subtitle": ["Branch1", 'Branch2', 'Branch3'],
    }.obs,
    {
      'title': 'Department',
      "isExpand": false.obs,
      "subtitle": ["Department1", 'Department2', 'Department3'],
    }.obs,
    {
      'title': 'Operation',
      "isExpand": false.obs,
      "subtitle": ["Operation1", 'Operation2', 'Operation3'],
    }.obs,
    {
      'title': 'Offline limit',
      "isExpand": false.obs,
      "subtitle": ["All limits", 'Offline limit1', 'Offline limit2'],
    }.obs,
    {
      'title': 'Tanks',
      "isExpand": false.obs,
      "subtitle": ["All Tanks", 'Tanks1', 'Tanks2'],
    }.obs,
    {
      'title': 'Make',
      "isExpand": false.obs,
      "subtitle": ["All Make", 'Make1', 'Make2'],
    }.obs,
    {
      'title': 'Model',
      "isExpand": false.obs,
      "subtitle": ["All Model", 'Model1', 'Model2'],
    }.obs,
    {
      'title': 'Filling days',
      "isExpand": false.obs,
      "subtitle": ["All Day", 'Day1', 'Day2'],
    }.obs,
  ].obs;
  RxString limitType = 'All limits'.obs;
  RxList selectedFleetList = [].obs;
  RxList selectedVehicleList = [].obs;
  RxList selectedSerialList = [].obs;
  RxBool isOverview = true.obs;
  RxBool isFuelHistory = false.obs;
  RxList vehicleList = [
    {
      "value": false.obs,
      "code": "0411XTA",
      "status": "Active",
      "title": "Nissan NV300",
      "type": "Diesel",
      "driver": "Mark Manson",
      "vehicleType": "Car",
      "tanks": "1",
      "offlineLimit": "20 Litres",
      "quotaTotal": "50 / 300",
      "quotaString": 'Liters Monthly',
      "division": "Tabuk /\nAuto Parts /\nSales /\nCouriers",
      "password": "1234",
      'day_data': [
        'Mon',
        'Tue',
        'Wed',
        'Thu',
        'Fri',
        'Sat',
        'Sun',
      ],
      // 'day_data': [
      //   'Sun',
      //   'Sat',
      //   'Fri',
      //   'Thu',
      //   'Wed',
      //   'Tue',
      //   'Mon',
      // ]
    }.obs,
  ].obs;

  RxBool isAvailableStations = true.obs;
  RxList availableStationList = [
    {"value": true.obs, "title": "WAIE stations"}.obs,
    // {"value": false.obs, "title": "Non-WAIE stations"}.obs,
    // {"value": false.obs, "title": "Sub-contractors"}.obs,
  ].obs;
  RxList multipleSelected = [].obs;
  RxBool isSelected = false.obs;
  RxBool isPlaceSelected = false.obs;
  RxBool isStationSelected = false.obs;
  RxBool ismultipleSelected = false.obs;
  RxList stationList = [
    {
      "id": 0,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Khobar",
      "data": [
        {'value': false.obs, "title": "Al Khobar 204850"},
        {'value': false.obs, "title": "Al Khobar 201940"},
      ]
    },
    {
      "id": 1,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Abha",
      "data": [
        {'value': false.obs, "title": "Abha 204850"},
        {'value': false.obs, "title": "Abha 201940"},
      ]
    },
    {
      "id": 2,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Addam",
      "data": [
        {'value': false.obs, "title": "Addam 204850"},
        {'value': false.obs, "title": "Addam 201940"},
      ]
    },
    {
      "id": 3,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Addam",
      "data": [
        {'value': false.obs, "title": "Addam 204850"},
        {'value': false.obs, "title": "Addam 201940"},
      ]
    },
    {
      "id": 4,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Afif",
      "data": [
        {'value': false.obs, "title": "Afif 204850"},
        {'value': false.obs, "title": "Afif 201940"},
      ]
    },
    {
      "id": 5,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Ahsa",
      "data": [
        {'value': false.obs, "title": "Al Ahsa 204850"},
        {'value': false.obs, "title": "Al Ahsa 201940"},
      ]
    },
    {
      "id": 6,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Aqiq",
      "data": [
        {'value': false.obs, "title": "Al Aqiq 204850"},
        {'value': false.obs, "title": "Al Aqiq 201940"},
      ]
    },
    {
      "id": 7,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Artawia",
      "data": [
        {'value': false.obs, "title": "Al Artawia 204850"},
        {'value': false.obs, "title": "Al Artawia 201940"},
      ]
    },
    {
      "id": 8,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Artawia",
      "data": [
        {'value': false.obs, "title": "Al Artawia 204850"},
        {'value': false.obs, "title": "Al Artawia 201940"},
      ]
    },
  ].obs;
  RxList fuelDetailList = [
    {
      "date": "Fri, Apr 14, 2023 12:58",
      "driver": "Mark Manson",
      "amount": "50.00 SAR",
      "location": "Old Industrial87, Riyadh, Saudi Arabia",
      "price": "2.00 SAR",
      "litres": "10",
      "service": "Tag",
      "isShowMore": true.obs,
    }.obs,
    {
      "date": "Fri, Apr 14, 2023 12:58",
      "driver": "Dan Brown",
      "amount": "50.00",
      "location": "Old Industrial87, Riyadh, Saudi Arabia",
      "price": "2.00 SAR",
      "litres": "10",
      "service": "Tag",
      "isShowMore": false.obs,
    }.obs,
  ].obs;

  RxBool isScheduleInstallation = false.obs;

  RxList rowAdjustmentList = [
    {
      "isStatus": true.obs,
      "title": "Plate",
    },
    {
      "isStatus": true.obs,
      "title": "Driver",
    },
    {
      "isStatus": false.obs,
      "title": "Tanks",
    },
    {
      "isStatus": true.obs,
      "title": "Division",
    },
    {
      "isStatus": true.obs,
      "title": "Quota",
    },
    {
      "isStatus": true.obs,
      "title": "Fuel",
    },
    {
      "isStatus": true.obs,
      "title": "Status",
    },
    {
      "isStatus": false.obs,
      "title": "Filling days",
    },
    {
      "isStatus": false.obs,
      "title": "Branch",
    },
    {
      "isStatus": false.obs,
      "title": "Department",
    },
    {
      "isStatus": false.obs,
      "title": "Operation",
    },
    {
      "isStatus": false.obs,
      "title": "Make",
    },
    {
      "isStatus": false.obs,
      "title": "Model",
    },
  ].obs;
}
