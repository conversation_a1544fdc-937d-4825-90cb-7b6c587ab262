// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/graphview_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../utils/images.dart';
import '../../../widget/common_button.dart';

class GraphViewScreen extends StatelessWidget {
  GraphViewScreen({super.key});
  GraphviewController graphviewController = Get.put(GraphviewController());
  List<_SalesData> data = [
    _SalesData('January', 8),
    _SalesData('February', 24),
    _SalesData('March', 22),
    _SalesData('April', 30),
    _SalesData('May', 25),
    _SalesData('June', 8),
    _SalesData('July', 24),
    _SalesData('August', 22),
    _SalesData('September', 30),
    _SalesData('October', 8),
    _SalesData('November', 24),
    _SalesData('December', 25),
  ];
  List<_SalesData> month = [
    _SalesData('January', 8),
    _SalesData('February', 14),
    _SalesData('March', 11),
    _SalesData('April', 20),
    _SalesData('May', 11),
    _SalesData('June', 15),
    _SalesData('July', 8),
    _SalesData('August', 14),
    _SalesData('September', 11),
    _SalesData('October', 20),
    _SalesData('November', 11),
    _SalesData('December', 20),
  ];
  List<_SalesData> year = [
    _SalesData('January', 35),
    _SalesData('February', 15),
    _SalesData('March', 45),
    _SalesData('April', 55),
    _SalesData('May', 15),
    _SalesData('June', 45),
    _SalesData('July', 55),
    _SalesData('August', 5),
    _SalesData('September', 35),
    _SalesData('October', 15),
    _SalesData('November', 45),
    _SalesData('December', 55),
  ];

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Quota change, SAR".trr,
                style: pBold14.copyWith(fontSize: 13),
              ),
              Row(
                children: [
                  GestureDetector(
                      onTap: () {
                        graphviewController.isPreviousYear.value = true;
                        graphviewController.isCurrantYear.value = false;
                      },
                      child: yearWidget(
                          graphviewController.isPreviousYear.value
                              ? AppColor.cGreen
                              : AppColor.cLightGrey,
                          "2022")),
                  horizontalSpace(24),
                  GestureDetector(
                      onTap: () {
                        graphviewController.isPreviousYear.value = false;
                        graphviewController.isCurrantYear.value = true;
                      },
                      child: yearWidget(
                          graphviewController.isCurrantYear.value
                              ? AppColor.cGreen
                              : AppColor.cLightGrey,
                          "2023"))
                ],
              )
            ],
          ),
          verticalSpace(16),
          CommonDropdownButtonWidget(
            labelText: '',
            list: graphviewController.valueList,
            value: graphviewController.selectedValue.value,
            onChanged: (value) {
              graphviewController.selectedValue.value = value;
            },
          ),
          verticalSpace(16),
          // Container(
          //   height: 450,
          //   padding: EdgeInsets.all(16),
          //   decoration: BoxDecoration(
          //       color: AppColor.cLightGrey,
          //       borderRadius: BorderRadius.circular(6)),
          //   child: SfCartesianChart(
          //     enableAxisAnimation: true,
          //     plotAreaBorderWidth: 0,
          //     legend: Legend(isVisible: false),
          //     zoomPanBehavior: ZoomPanBehavior(
          //       enablePanning: true,
          //     ),
          //     primaryXAxis: CategoryAxis(
          //       borderWidth: 0,
          //       autoScrollingMode: AutoScrollingMode.start,
          //       autoScrollingDelta: 7,
          //       maximumLabels: 100,
          //       majorGridLines: MajorGridLines(color: AppColor.cTransparent),
          //       axisLine: AxisLine(width: 0, color: AppColor.cTransparent),
          //       interval: 1,
          //       majorTickLines: MajorTickLines(width: 0),
          //       axisBorderType: AxisBorderType.withoutTopAndBottom,
          //     ),
          //     primaryYAxis: NumericAxis(
          //         borderWidth: 0,
          //         majorGridLines: MajorGridLines(color: AppColor.cBorder),
          //         axisLine: AxisLine(width: 0, color: AppColor.cTransparent),
          //         majorTickLines: MajorTickLines(width: 0)),
          //     // Enable tooltip
          //     tooltipBehavior: TooltipBehavior(enable: false),
          //     series: <ChartSeries<_SalesData, String>>[
          //       StackedLineSeries<_SalesData, String>(
          //           dataSource: data,
          //           xValueMapper: (_SalesData sales, _) =>
          //               sales.year.toString().trr,
          //           yValueMapper: (_SalesData sales, _) => sales.sales,
          //           color: AppColor.cBarBlueLine,
          //           width: 2.5,
          //           dataLabelSettings: DataLabelSettings(isVisible: false)),
          //       LineSeries<_SalesData, String>(
          //           dataSource: month,
          //           xValueMapper: (_SalesData sales, _) =>
          //               sales.year.toString().trr,
          //           yValueMapper: (_SalesData sales, _) => sales.sales,
          //           color: AppColor.cLightOrange,
          //           width: 2.5,
          //           dataLabelSettings: DataLabelSettings(isVisible: false)),
          //       LineSeries<_SalesData, String>(
          //           dataSource: year,
          //           xValueMapper: (_SalesData sales, _) =>
          //               sales.year.toString().trr,
          //           yValueMapper: (_SalesData sales, _) => sales.sales,
          //           color: AppColor.cLightGreenBarLine,
          //           width: 2.5,
          //           dataLabelSettings: DataLabelSettings(isVisible: false))
          //     ],
          //   ),
          // ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: CommonIconButton(
              onPressed: () {},
              iconData: DefaultImages.downloadReportIcn,
              title: 'Download report'.trr,
              btnColor: AppColor.themeOrangeColor,
            ),
          ),
        ],
      );
    });
  }

  Row yearWidget(Color color, String text) {
    return Row(
      children: [
        Container(
          height: 18,
          width: 18,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        horizontalSpace(8),
        Text(
          text,
          style: pRegular14.copyWith(color: AppColor.cLabel),
        )
      ],
    );
  }
}

class _SalesData {
  _SalesData(this.year, this.sales);

  final String year;
  final double sales;
}
