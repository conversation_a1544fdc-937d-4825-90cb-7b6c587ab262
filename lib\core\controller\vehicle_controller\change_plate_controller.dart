import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:quickalert/quickalert.dart';

import '../../../utils/colors.dart';
import '../../../utils/constants.dart';
import '../../../utils/text_style.dart';
import '../../../view/widget/common_button.dart';
import '../../../view/widget/common_space_divider_widget.dart';
import '../../../view/widget/loading_widget.dart';

class ChangePlateController extends GetxController {
  VehicleController vehicleController = Get.put(VehicleController());
  GetStorage userStorage = GetStorage('User');
  final vehicle = GetStorage();
  final isPinblock = GetStorage();
  final isDCBlock = GetStorage();
  TextEditingController oldPlateController = TextEditingController();
  TextEditingController newPlateController = TextEditingController();

  changePlate(serialid, oldPlate) async {
    // showDialog(
    //     context: Get.context!,
    //     builder: (context) {
    //       return const Center(
    //         child: CircularProgressIndicator(),
    //       );
    //     });
    Loader.showLoader();
    print("===============================================================");
    print("serialid>>>>>>> $serialid");
    print("oldPlate>>>>>>> $oldPlate");
    print("oldPlateController>>>>>>> ${oldPlateController.text}");
    print("newPlateController>>>>>>> ${newPlateController.text}");
    print("===============================================================");

    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");
    var client = http.Client();

    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.requestChangePlate),
          body: {
            "serialid": serialid,
            "oldplate": oldPlate,
            "newplate": newPlateController.text,
            "emailid": emailid,
          });
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        vehicle.remove('vehicleID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('complaintJobID');
        isPinblock.remove('isPinActivate');
        isDCBlock.remove('isdcBlock');
        vehicleController.selectedSerialList.clear();
        vehicleController.selectedVehicleList.clear();
        vehicleController.selectedFleetList.clear();
        vehicleController.filterValueList.refresh();
        vehicleController.selectedVehicleList.refresh();
        vehicleController.selectedSerialList.refresh();
        vehicleController.selectedFleetList.refresh();
        // Get.offAll(
        //   () => DashBoardManagerScreen(
        //     currantIndex: 0,
        //   ),
        //   //preventDuplicates: false,
        // );
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.success,
          text:
              'Request to change plate submitted,\nplease wait for technician confirmation.',
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
      } else {
        print('Failed');
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          title: 'Error',
          text: 'Failed to load data. Please check internet connection.',
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
      }
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  cancelChangePlate(serialid) async {
    // showDialog(
    //     context: Get.context!,
    //     builder: (context) {
    //       return const Center(
    //         child: CircularProgressIndicator(),
    //       );
    //     });
    Loader.showLoader();
    print("===============================================================");
    print("serialid>>>>>>> $serialid");
    print("oldPlateController>>>>>>> ${oldPlateController.text}");
    print("newPlateController>>>>>>> ${newPlateController.text}");
    print("===============================================================");

    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");
    var client = http.Client();

    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.cancelChangePlate),
          body: {
            "serialid": serialid,
          });
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["MessageType"]}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["Message"]}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        vehicle.remove('vehicleID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('complaintJobID');
        isPinblock.remove('isPinActivate');
        isDCBlock.remove('isdcBlock');
        vehicleController.selectedSerialList.clear();
        vehicleController.selectedVehicleList.clear();
        vehicleController.selectedFleetList.clear();
        vehicleController.filterValueList.refresh();
        vehicleController.selectedVehicleList.refresh();
        vehicleController.selectedSerialList.refresh();
        vehicleController.selectedFleetList.refresh();
        if (jsonDecode(response.body)["MessageType"] == "success") {
          await QuickAlert.show(
            context: Get.context!,
            type: QuickAlertType.success,
            text: jsonDecode(response.body)["Message"],
          );
          Get.offAll(
            () => DashBoardManagerScreen(
              currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
            ),
            //preventDuplicates: false,
          );
          // showDialog(
          //   barrierDismissible: false,
          //   context: Get.context!,
          //   builder: (context) {
          //     return AlertDialog(
          //       insetPadding: const EdgeInsets.all(16),
          //       contentPadding: const EdgeInsets.all(24),
          //       shape: RoundedRectangleBorder(
          //           borderRadius: BorderRadius.circular(12)),
          //       content: Column(
          //         mainAxisSize: MainAxisSize.min,
          //         children: [
          //           Text(
          //             jsonDecode(response.body)["Message"],
          //             style: pBold20,
          //             textAlign: TextAlign.center,
          //           ),
          //           verticalSpace(24),
          //           CommonButton(
          //             title: "OK".tr,
          //             onPressed: () async {
          //               await Get.offAll(
          //                 () => DashBoardManagerScreen(
          //                   currantIndex: 0,
          //                 ),
          //                 //preventDuplicates: false,
          //               );
          //             },
          //             btnColor: AppColor.themeOrangeColor,
          //           )
          //         ],
          //       ),
          //     );
          //   },
          // );
        } else {
          Loader.hideLoader();
          await QuickAlert.show(
            context: Get.context!,
            type: QuickAlertType.error,
            title: 'Error',
            text: jsonDecode(response.body)["Message"],
          );
          Get.offAll(
            () => DashBoardManagerScreen(
              currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
            ),
            //preventDuplicates: false,
          );
          // showDialog(
          //   barrierDismissible: false,
          //   context: Get.context!,
          //   builder: (context) {
          //     return AlertDialog(
          //       insetPadding: const EdgeInsets.all(16),
          //       contentPadding: const EdgeInsets.all(24),
          //       shape: RoundedRectangleBorder(
          //           borderRadius: BorderRadius.circular(12)),
          //       content: Column(
          //         mainAxisSize: MainAxisSize.min,
          //         children: [
          //           Text(
          //             jsonDecode(response.body)["Message"],
          //             style: pBold20,
          //             textAlign: TextAlign.center,
          //           ),
          //           verticalSpace(24),
          //           CommonButton(
          //             title: "OK".tr,
          //             onPressed: () async {
          //               await Get.offAll(
          //                 () => DashBoardManagerScreen(
          //                   currantIndex: 0,
          //                 ),
          //                 //preventDuplicates: false,
          //               );
          //             },
          //             btnColor: AppColor.themeOrangeColor,
          //           )
          //         ],
          //       ),
          //     );
          //   },
          // );
        }
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
