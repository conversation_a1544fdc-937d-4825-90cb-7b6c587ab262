// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/order_history_service.dart';
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/refund_menu_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../utils/constants.dart';
import '../../../../view/widget/loading_widget.dart';

class OrderHistoryCancelController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');

  cancelOrderHistory(reqId) async {
    Loader.showLoader();
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = jsonEncode(custsData.read('custData'));
    print("OrderHistoryCancelController custid>>>>>>> $custid");
    print("OrderHistoryCancelController emailid>>>>>>> $emailid");
    print("OrderHistoryCancelController custData>>>>>>> $custData");
    print("OrderHistoryCancelController reqId>>>>>>> $reqId");
    Navigator.of(Get.context!).pop();
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.cancelOrderHistory),
          body: {
            "custdata": custData,
            "reqId": reqId,
            "IsAR": Constants.IsAr_App,
          });
      var parsedJson = jsonDecode(response.body);
      if (parsedJson['response']['Action'] == "EXCEPTION") {
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    parsedJson['response']['Message'].toString(),
                    style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.offAll(() => const RefundMenuScreen());
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
      if (parsedJson['response']['Action'] == "POPUP") {
        Loader.hideLoader();
        var msg = "";
        if (parsedJson['message'] != null) {
          msg = parsedJson['message'].toString();
        } else {
          msg = parsedJson['response']['Message'].toString();
        }
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    msg,
                    style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.offAll(() => const RefundMenuScreen());
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
      print(parsedJson);
      print(parsedJson['message']);
      print(parsedJson['response']['Action']);
      print(parsedJson['response']['Message']);

      return response;
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  cancelTopupHistory(reqId) async {
    Loader.showLoader();
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = jsonEncode(custsData.read('custData'));
    print("OrderHistoryCancelController custid>>>>>>> $custid");
    print("OrderHistoryCancelController emailid>>>>>>> $emailid");
    print("OrderHistoryCancelController custData>>>>>>> $custData");
    print("OrderHistoryCancelController reqId>>>>>>> $reqId");
    //Navigator.of(Get.context!).pop();
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.cancelTopupHistory),
          body: {
            "custdata": custData,
            "reqId": reqId,
            "IsAR": 'false',
          });
      Loader.hideLoader();
      print("response.body======="+response.body.toString());
      var parsedJson = jsonDecode(response.body);
      if (parsedJson['response']['Action'] == "EXCEPTION") {
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    parsedJson['response']['Message'].toString(),
                    style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.offAll(() => const RefundMenuScreen());
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
      if (parsedJson['response']['Action'] == "POPUP") {
        Loader.hideLoader();
        var msg = "";
        if (parsedJson['message'] != null) {
          msg = parsedJson['message'].toString();
        } else {
          msg = parsedJson['response']['Message'].toString();
        }
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    msg,
                    style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.offAll(() => const RefundMenuScreen());
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
     /* else
        {
          Get.back();
        }*/
      print(parsedJson);
      print(parsedJson['message']);
      print(parsedJson['response']['Action']);
      print(parsedJson['response']['Message']);

      return response;
    } catch (e) {
      Loader.hideLoader();
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      Loader.hideLoader();
      // Then finally destroy the client.
      client.close();
    }
  }
}
