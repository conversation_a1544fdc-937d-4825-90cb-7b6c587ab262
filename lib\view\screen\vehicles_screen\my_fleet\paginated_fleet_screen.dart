import 'dart:developer';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/activate_pinblock_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/add_pure_dc_vehicle_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/bulk_actions_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/change_status_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/file_complaint_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/plate_change_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/set_quota_limits_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/transfer_tags_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'dart:convert';
import '../../../../core/controller/vehicle_controller/file_complaint_controller.dart';
import '../../../../core/controller/vehicle_controller/paginated_controller.dart';
import '../../../../core/controller/vehicle_controller/vehicle_controller.dart';
import '../../../../models/newfleet.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/colors.dart';
import '../../../widget/loading_widget.dart';
import '../../dashboard_manager/dashboard_manager.dart';

class PaginatedDataTableView extends StatefulWidget {
  const PaginatedDataTableView({super.key});

  @override
  State<PaginatedDataTableView> createState() => _PaginatedDataTableViewState();
}

class _PaginatedDataTableViewState extends State<PaginatedDataTableView> {
  VehicleController vehicleController = Get.put(VehicleController());
  FileComplaintController fileComplaintController =
      Get.put(FileComplaintController());
  PaginatedController paginatedController = Get.put(PaginatedController());
  int _currentPage = 1;
  int _pageSize = 10;
  final List<ServiceObj> _data = [];
  RxInt counts = 0.obs;
  bool _isLoading = false;
  GetStorage userStorage = GetStorage('User');
  RxBool isFilterAccess = false.obs;
  RxString serviceList = 'PL'.obs;
  RxString vehicleList = ''.obs;
  RxString statusList = ''.obs;
  RxString fuelList = ''.obs;
  RxString tp = '100'.obs;
  RxInt skp = 0.obs;
  TextEditingController searchController = TextEditingController();
  int _pageIndex = 0; // track current page index

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  Future<void> sample() async {
    var searchBy =
        "${searchController.text == "" ? "" : searchController.text},${vehicleList.value},${statusList.value},${fuelList.value},,,,,,,,,${serviceList.value}";
    print("======================================");
    print(searchBy);
    print(skp.value.toString());
    print(tp.value.toString());
    print("======================================");
  }

  Future<void> fetchData() async {
    _data.clear();
    var searchBy =
        "${searchController.text == "" ? "" : searchController.text},${vehicleList.value},${statusList.value},${fuelList.value},,,,,,,,,${serviceList.value}";
    print("======================================");
    print(searchBy);
    print(skp.value.toString());
    print(tp.value.toString());
    print("======================================");
    var client = http.Client();
    var custid = userStorage.read('custid');
    setState(() {
      _isLoading = true;
    });

    // final response = await http.get(Uri.parse(
    //     '${ApiEndpoint.users}?page=$_currentPage')); //&pageSize=$_pageSize

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map body = {
      "SEARCHBY": searchBy,
      "TOP": tp.value.toString(),
      "SKIP": skp.value.toString(),
      "CUSTID": custid, //"000003944",
      "SERIALID": ""
    };
    final response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
        body: jsonEncode(body),
        headers: {
          'authorization': basicAuth,
          "Content-Type": "application/json",
        });

    if (response.statusCode == 200) {
      _data.clear();
      searchController.clear();
      serviceList.value = 'PL';
      vehicleList.value = '';
      statusList.value = '';
      fuelList.value = '';
      tp.value = '100';
      skp.value = 0;
      final jsonData = json.decode(response.body);
      // print("===================================");
      // print(json.decode(response.body));
      // print("===================================");
      // print(jsonData);
      // print("===================================");
      inspect(jsonData);
      final dataList = jsonData["serviceObj"] as List<dynamic>;
      int countList = jsonData["totalCount"] as int;

      final List<ServiceObj> newData =
          dataList.map((item) => ServiceObj.fromJson(item)).toList();

      setState(() {
        _data.addAll(newData);
        counts.value = countList;
        _isLoading = false;

        print("_data.length");
        print(_data.length);
        if (_data.isEmpty) {
          //Loader.showLoader();
          setState(() {
            searchController.clear();
            serviceList.value = 'PL';
            vehicleList.value = '';
            statusList.value = '';
            fuelList.value = '';
            tp.value = '100';
            skp.value = 0;
          });
          //Get.off(() => const PaginatedDataTableView());
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'No data available in table',
                      style: pSemiBold17,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".trr,
                      onPressed: () async {
                        print("===========================");
                        print(searchController.text);
                        print(serviceList.value);
                        print(vehicleList.value);
                        print(statusList.value);
                        print(fuelList.value);
                        print(tp.value);
                        print(skp.value);
                        print("===========================");
                        //Loader.hideLoader();
                        // Get.off(() => DashBoardManagerScreen(
                        //       currantIndex: 1,
                        //     ));
                        fetchData();
                        Get.back();

                        //Get.off(() => const PaginatedDataTableView());
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          if (_pageSize > _data.length) {
            _pageSize = _data.length;
          }
        }
      });
    } else {
      setState(() {
        _isLoading = false;
      });
      throw Exception('Failed to fetch data');
    }
  }

  void _loadMoreData() {
    if (!_isLoading) {
      setState(() {
        _currentPage++;
      });
      fetchData();
    }
  }

  @override
  Widget build(BuildContext context) {
    print("paginatedController.serviceTypeList");
    print(paginatedController.serviceTypeList);
    return Scaffold(
      body: SafeArea(
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.only(right: 16, left: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.offAll(() => DashBoardManagerScreen(
                              currantIndex: 0,
                            ));
                      },
                      child: Container(
                        padding: const EdgeInsets.only(
                          top: 15,
                          bottom: 16,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.backIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cDarkBlueFont, BlendMode.srcIn)),
                            horizontalSpace(10),
                            Text(
                              "Back".trr,
                              style: pRegular18.copyWith(
                                  color: AppColor.cDarkBlueFont, fontSize: 17),
                              textAlign: TextAlign.start,
                            )
                          ],
                        ),
                      ),
                    ),
                    // horizontalSpace(35),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "Vehicle".trr,
                          style: pBold20,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              verticalSpace(10),
              // Padding(
              //   padding: const EdgeInsets.only(right: 16, left: 16),
              //   child: GestureDetector(
              //     onTap: () {
              //       Get.to(() => AddPureDCVehicleScreen(
              //             title: "Add DC vehicle".trr,
              //           ));
              //     },
              //     child: Container(
              //       padding:
              //           const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              //       decoration: BoxDecoration(
              //         borderRadius: BorderRadius.circular(6),
              //         border: Border.all(color: AppColor.themeDarkBlueColor),
              //       ),
              //       child: Row(
              //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //         children: [
              //           Row(
              //             children: [
              //               assetSvdImageWidget(
              //                   image: DefaultImages.scannerIcn),
              //               horizontalSpace(8),
              //               Text(
              //                 "Add digital coupon vehicle".trr,
              //                 style: pRegular16.copyWith(
              //                   color: AppColor.cDarkBlueFont,
              //                 ),
              //               )
              //             ],
              //           ),
              //           // assetSvdImageWidget(
              //           //   image: DefaultImages.blueArrowDownIcn,
              //           //   colorFilter: ColorFilter.mode(AppColor.cDarkGreyFont, BlendMode.srcIn),
              //           // ),
              //         ],
              //       ),
              //     ),
              //   ),
              // ),
              // verticalSpace(10),
              Padding(
                padding: const EdgeInsets.only(right: 16, left: 16),
                child: menuTitleRowWidget(
                  title: "Filters".trr,
                  isSelected: isFilterAccess.value,
                  onTap: () {
                    isFilterAccess.value = !isFilterAccess.value;
                  },
                ),
              ),
              verticalSpace(10),
              isFilterAccess.value
                  ? Container(
                      padding: const EdgeInsets.only(right: 16, left: 16),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonTextField(
                            controller: searchController,
                            labelText: '',
                            hintText: "Search here".trr,
                          ),
                          const Gap(16),
                          Row(
                            children: [
                              Expanded(
                                child: DropdownButtonFormField(
                                  items: paginatedController.serviceTypeList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data["TYPECODE"],
                                      child: Text(
                                        data["TYPEDESC"],
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    print(value);
                                    // PaginatedController.selectedDiv.value =
                                    //     value.toString();
                                    // reportController.loadBranch(value.toString());
                                    serviceList.value = value.toString();
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'Plate #'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const Gap(10),
                              Expanded(
                                child: DropdownButtonFormField(
                                  items: paginatedController.vehicleTypeList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data["TYPECODE"],
                                      child: Text(
                                        data["TYPEDESC"],
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    print(value);
                                    // PaginatedController.selectedDiv.value =
                                    //     value.toString();
                                    // reportController.loadBranch(value.toString());
                                    vehicleList.value = value.toString();
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'All Vehicle Type'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Gap(16),
                          Row(
                            children: [
                              Expanded(
                                child: DropdownButtonFormField(
                                  items: paginatedController.statusTypeList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data["TYPECODE"],
                                      child: Text(
                                        data["TYPEDESC"],
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    print(value);
                                    // PaginatedController.selectedDiv.value =
                                    //     value.toString();
                                    // reportController.loadBranch(value.toString());
                                    statusList.value = value.toString();
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'All Status'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const Gap(10),
                              Expanded(
                                child: DropdownButtonFormField(
                                  items: paginatedController.fuelTypeList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data["TYPECODE"],
                                      child: Text(
                                        data["TYPEDESC"],
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    print(value);
                                    // PaginatedController.selectedDiv.value =
                                    //     value.toString();
                                    // reportController.loadBranch(value.toString());
                                    fuelList.value = value.toString();
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'All Fuel Type'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Gap(24),
                          Row(
                            children: [
                              // Expanded(
                              //     child: CommonButton(
                              //   title: 'Back'.trr,
                              //   onPressed: () {
                              //     Get.back();
                              //     // Get.offAll(
                              //     //   () => DashBoardManagerScreen(
                              //     //     currantIndex: 0,
                              //     //   ),
                              //     //   //preventDuplicates: false,
                              //     // );
                              //   },
                              //   btnColor: AppColor.cBackGround,
                              //   bColor: AppColor.themeDarkBlueColor,
                              //   textColor: AppColor.cDarkBlueFont,
                              // )),
                              // horizontalSpace(16),
                              Expanded(
                                child: CommonButton(
                                  title: 'Search'.trr,
                                  onPressed: () async {
                                    setState(() {
                                      isFilterAccess.value = false;
                                    });
                                    await fetchData();
                                    //await sample();
                                  },
                                  btnColor: AppColor.themeOrangeColor,
                                  horizontalPadding: 16,
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    )
                  : const SizedBox(),
              verticalSpace(10),
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: SizedBox(
                          width: double.infinity,
                          child: PaginatedDataTable(
                            // actions: [
                            //   GestureDetector(
                            //     onTap: () {
                            //       fetchData();
                            //     },
                            //     child: const Text("SAMPLE"),
                            //   )
                            // ],
                            // header: const Text("SAMPLE"),
                            dragStartBehavior: DragStartBehavior.start,
                            rowsPerPage: _pageSize,
                            availableRowsPerPage: [
                              _pageSize,
                              _pageSize * 2,
                              _pageSize * 5,
                              _pageSize * 10,
                              _pageSize * 20,
                              _pageSize * 30,
                              _pageSize * 40,
                              _pageSize * 50,
                              _pageSize * 60,
                              _pageSize * 70,
                              _pageSize * 80,
                              _pageSize * 90,
                              _pageSize * 100,
                            ],
                            onRowsPerPageChanged: (value) async {
                              setState(() {
                                _pageSize = value!;
                                tp.value = value.toString();
                                print(tp.value);
                                print('Rows ${tp.value} loaded.');
                                print('Page $_pageSize loaded.');
                                // RxString tp = '10'.obs;
                                // RxString skp = '0'.obs;
                              });
                              await fetchData();
                            },
                            columns: const [
                              DataColumn(label: Text('#')),
                              DataColumn(label: Text('Plate No / Card No')),
                              DataColumn(label: Text('Service Type')),
                              DataColumn(label: Text('Driver')),
                              DataColumn(label: Text('Division')),
                              DataColumn(label: Text('Quota')),
                              DataColumn(label: Text('Fuel')),
                              DataColumn(label: Text('DC')),
                              DataColumn(label: Text('Tank No')),
                              DataColumn(label: Text('Status')),
                              DataColumn(label: Text(' ')),
                            ],
                            source:
                                _DataSource(data: _data, count: counts.value),
                            showCheckboxColumn: false,
                            onPageChanged: (int index) async {
                              setState(() {
                                print("index");
                                print(index);
                                _pageIndex = index;
                                skp++;
                                print(skp.value);
                                print('SKIP ${skp.value} loaded.');
                                // Call your function here when page changes
                                print('Page $_pageIndex loaded.');
                                // Your function call goes here
                                // callYourFunction();
                              });
                              //await fetchData();
                            },
                            columnSpacing: 20,
                            horizontalMargin: 30,
                            showFirstLastButtons: true,
                          ),
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DataSource extends DataTableSource {
  VehicleController vehicleController = Get.put(VehicleController());
  FileComplaintController fileComplaintController =
      Get.put(FileComplaintController());
  final List<ServiceObj> data;
  int count;

  _DataSource({
    required this.data,
    required this.count,
  });

  @override
  DataRow? getRow(int index) {
    if (index >= data.length) {
      return null;
    }

    final item = data[index];

    return DataRow(
        color: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
          // Check if the row is even or odd and set colors accordingly
          if (item.servicestatus == "A") {
            return AppColor.cLightGreen; // Example: Light grey for even rows
          } else if (item.servicestatus == "T") {
            return AppColor.cMediumGreyContainer; // Example: White for odd rows
          } else if (item.servicestatus == "N") {
            return AppColor.cLightBlueContainer; // Example: White for odd rows
          } else {
            return AppColor.cLightRedContainer; //
          }
        }),
        cells: [
          DataCell(Text((index + 1).toString())),
          DataCell(Text(item.plateno)),
          DataCell(Text(item.servicetypeDisp)),
          DataCell(Text(item.driver)),
          DataCell(Text(item.divisionname)),
          DataCell(Text(item.quotaDisp)),
          DataCell(Text(item.fueltypeDisp)),
          DataCell(
            Tooltip(
              triggerMode: TooltipTriggerMode.tap,
              message:
                  'DC fuel quota (litres)  ${item.dcQuotavalue.round()}\nDC fuel balance (litres)  ${item.dcQuotarem.round()}',
              child: assetSvdImageWidget(
                  image: DefaultImages.scannerIcn,
                  colorFilter: ColorFilter.mode(
                      item.isdc == "Y" || item.servicetype == "D"
                          ? AppColor.cText
                          : AppColor.cLightBlueFont,
                      BlendMode.srcIn)),
            ),
          ),
          // DataCell(assetSvdImageWidget(
          //     image: DefaultImages.scannerIcn,
          //     colorFilter: ColorFilter.mode(
          //         item.isdc == "Y" || item.servicetype == "D"
          //             ? AppColor.cText
          //             : AppColor.cLightBlueFont,
          //         BlendMode.srcIn))),
          DataCell(Text(item.tankNo)),
          DataCell(Text(item.servicestatusDisp)),
          DataCell(GestureDetector(
            onTap: () {
              showModalBottomSheet(
                isDismissible: false,
                context: Get.context!,
                shape: const RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16))),
                backgroundColor: AppColor.cBackGround,
                barrierColor: AppColor.cBlackOpacity,
                isScrollControlled: true,
                builder: (context) {
                  var isPin = item.pinblock == "Y" ? true : false;
                  var isdcBlock = item.servicetype == "D" || item.isdc == "Y"
                      ? true
                      : false;
                  return Container(
                    decoration: const BoxDecoration(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(16))),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Row(
                            children: [
                              GestureDetector(
                                  onTap: () {
                                    vehicleController.selectedSerialList
                                        .clear();
                                    vehicleController.selectedVehicleList
                                        .clear();
                                    vehicleController.selectedFleetList.clear();
                                    vehicleController.filterValueList.refresh();
                                    vehicleController.selectedVehicleList
                                        .refresh();
                                    vehicleController.selectedSerialList
                                        .refresh();
                                    vehicleController.selectedFleetList
                                        .refresh();
                                    Get.back();
                                    // Get.offAll(
                                    //   () => DashBoardManagerScreen(
                                    //     currantIndex: 0,
                                    //   ),
                                    //   //preventDuplicates: false,
                                    // );
                                  },
                                  child: CircleAvatar(
                                    radius: 20,
                                    backgroundColor:
                                        AppColor.cLightBlueContainer,
                                    child: Center(
                                        child: assetSvdImageWidget(
                                            image: DefaultImages.backIcn)),
                                  )),
                              Expanded(
                                child: Align(
                                  alignment: Alignment.center,
                                  child: Center(
                                    child: Text(
                                      "Actions".trr,
                                      style: pBold20,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        verticalSpace(22),
                        if (isPin == true && isdcBlock == false)
                          bulkActionWidget(
                            title: "PINBLOCK".trr,
                            onTap: () {
                              Get.back();
                              showModalBottomSheet(
                                isDismissible: false,
                                context: context,
                                shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16))),
                                backgroundColor: AppColor.cBackGround,
                                barrierColor: AppColor.cBlackOpacity,
                                isScrollControlled: true,
                                builder: (context) {
                                  return ActivatePinblockWidget(
                                    serialid: item.serialid,
                                    isPin: isPin,
                                  );
                                },
                              );
                            },
                          ),
                        bulkActionWidget(
                          title: "Change status".trr,
                          onTap: () {
                            bool btnActivate =
                                item.fleetaction["BTNACTIVATESTATS"] == true
                                    ? true
                                    : false;
                            bool btnDeactivate =
                                item.fleetaction["BTNDEACTIVATESTATS"] == true
                                    ? true
                                    : false;
                            Get.back();
                            showModalBottomSheet(
                              isDismissible: false,
                              context: context,
                              shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(16))),
                              backgroundColor: AppColor.cBackGround,
                              barrierColor: AppColor.cBlackOpacity,
                              isScrollControlled: true,
                              builder: (context) {
                                return ChangeStatusWidget(
                                  serviceStatus: item.servicestatus,
                                  vehicleID: item.vehicleid,
                                  serialID: item.serialid,
                                  btnActivate: btnActivate,
                                  btnDeactivate: btnDeactivate,
                                );
                              },
                            );
                          },
                        ),
                        if (item.fleetaction["BTNSETQUOTALIMIT"] == true)
                          bulkActionWidget(
                            title: "Set quota limits".trr,
                            onTap: () {
                              Get.back();
                              showModalBottomSheet(
                                isDismissible: false,
                                context: context,
                                shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16))),
                                backgroundColor: AppColor.cBackGround,
                                barrierColor: AppColor.cBlackOpacity,
                                isScrollControlled: true,
                                builder: (context) {
                                  return SetQuotaLimitsWidget(
                                    serialID: item.serialid,
                                  );
                                },
                              );
                            },
                          ),
                        if (item.fleetaction["BTNCHANGEPLATE"] == true)
                          bulkActionWidget(
                            title: "Change Plate".trr,
                            onTap: () {
                              Get.back();
                              showModalBottomSheet(
                                isDismissible: false,
                                context: context,
                                shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16))),
                                backgroundColor: AppColor.cBackGround,
                                barrierColor: AppColor.cBlackOpacity,
                                isScrollControlled: true,
                                builder: (context) {
                                  return PlateChangeWidget(
                                    code: item.plateno,
                                    serialid: item.serialid,
                                  );
                                },
                              );
                            },
                          ),
                        if (isdcBlock == false)
                          bulkActionWidget(
                            title: "Transfer tag".trr,
                            onTap: () {
                              Get.back();
                              showModalBottomSheet(
                                context: context,
                                shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16))),
                                backgroundColor: AppColor.cBackGround,
                                barrierColor: AppColor.cBlackOpacity,
                                isScrollControlled: true,
                                builder: (context) {
                                  return TransferTagsWidget(
                                    serialid: item.serialid,
                                  );
                                },
                              );
                            },
                          ),
                        if (isdcBlock == false)
                          bulkActionWidget(
                            title: item.fleetaction["BTNTAGCOMPLAIN"] == true
                                ? "File complaint".trr
                                : "Cancel complaint".trr,
                            onTap: () {
                              Get.back();
                              if (item.fleetaction["BTNTAGCOMPLAIN"] == true) {
                                Get.to(() => FileComplaintScreen(
                                      code: item.plateno,
                                      serialid: item.serialid,
                                    ));
                              } else {
                                showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (context) {
                                    return AlertDialog(
                                      insetPadding: const EdgeInsets.all(16),
                                      contentPadding: const EdgeInsets.all(24),
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12)),
                                      content: cancelComplaintWidget(
                                        code: item.plateno,
                                        onTap: () {
                                          // final serialid =
                                          //     vehicle.read('singleVehicleSerialID');
                                          // print(
                                          //     "singleVehicleSerialID >>>>> $serialid");

                                          vehicleController.myFleetList
                                              .refresh();
                                          fileComplaintController
                                              .cancelComplaint(item.serialid);
                                          // Get.back();
                                          // for (var element in vehicleController.myFleetList) {
                                          //   if (element['code'] == code) {
                                          //     element['status'] = 'Active';
                                          //   }
                                          // }
                                          // vehicleController.myFleetList.refresh();
                                        },
                                      ),
                                    );
                                  },
                                );
                              }
                            },
                          ),
                      ],
                    ),
                  );
                },
              );
            },
            child: assetSvdImageWidget(image: DefaultImages.verticleMoreIcn),
          )),
        ]);
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => data.length; //count;

  @override
  int get selectedRowCount => 0;
}

cancelComplaintWidget({required String code, required Function() onTap}) {
  return Container(
    decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: () {
            Get.back();
            // Get.offAll(
            //   () => DashBoardManagerScreen(
            //     currantIndex: 0,
            //   ),
            //   //preventDuplicates: false,
            // );
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [assetSvdImageWidget(image: DefaultImages.cancelIcn)],
          ),
        ),
        verticalSpace(24),
        Text.rich(
            TextSpan(
                text: 'Do you want to cancel your complaint for'.trr,
                style: pRegular17,
                children: [TextSpan(text: '  $code?', style: pSemiBold17)]),
            textAlign: TextAlign.center),
        verticalSpace(8),
        Text(
            "You can't undo this. You'll need to submit another complaint if the issue hasn't been resolved."
                .trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
            textAlign: TextAlign.center),
        verticalSpace(24),
        Row(
          children: [
            Expanded(
                child: CommonButton(
              title: 'Back'.trr,
              onPressed: () {
                Get.back();
                // Get.offAll(
                //   () => DashBoardManagerScreen(
                //     currantIndex: 0,
                //   ),
                //   //preventDuplicates: false,
                // );
              },
              btnColor: AppColor.cBackGround,
              bColor: AppColor.themeDarkBlueColor,
              textColor: AppColor.cDarkBlueFont,
            )),
            horizontalSpace(16),
            Expanded(
              child: CommonButton(
                title: 'Cancel complaint'.trr,
                onPressed: onTap,
                btnColor: AppColor.themeOrangeColor,
                horizontalPadding: 16,
              ),
            ),
          ],
        )
      ],
    ),
  );
}

Widget menuTitleRowWidget(
    {required String title, required bool isSelected, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      //color: AppColor.themeOrangeColor,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
            color: AppColor.themeDarkBlueColor), //AppColor.themeDarkBlueColor
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pBold20.copyWith(
              color: AppColor.cDarkBlueFont,
            ),
          ),
          assetSvdImageWidget(
            image: isSelected == true
                ? DefaultImages.arrowUpIcn
                : DefaultImages.dropDownIcn,
            width: 24,
            height: 24,
          )
        ],
      ),
    ),
  );
}
