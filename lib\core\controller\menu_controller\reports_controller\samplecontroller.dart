import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LazyListController extends GetxController {
  List<String> myList = [];
  ScrollController scrollController = ScrollController();
  int currentMax = 10;

  @override
  void onInit() {
    super.onInit();
    myList = List.generate(10, (i) => "Item : ${i + 1}");
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent * 0.8) {
        _getMoreData();
      }
    });
  }

  void _getMoreData() {
    List<String> list = [];

    for (int i = currentMax; i < currentMax + 10; i++) {
      if (i == currentMax) print('CURRENT MAX: $currentMax');
      list.add("Item : ${i + 1}");
    }

    myList.addAll(list);
    print('myList: $myList');

    currentMax = currentMax + 10;
    update();
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }
}
