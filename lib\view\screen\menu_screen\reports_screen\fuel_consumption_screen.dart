// ignore_for_file: prefer_const_constructors

import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/fuel_consuption_by_customer.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/reports_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../widget/common_appbar_widget.dart';

class FuelConsumptionScreen extends StatefulWidget {
  final String title;

  const FuelConsumptionScreen({super.key, required this.title});

  @override
  State<FuelConsumptionScreen> createState() => _FuelConsumptionScreenState();
}

class _FuelConsumptionScreenState extends State<FuelConsumptionScreen> {
  FuelConsuptionByCustomerController reportController =
      Get.put((FuelConsuptionByCustomerController()));

  pickDate() async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColor.themeOrangeColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor:
                    AppColor.themeDarkBlueColor, // button text color
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (pickedDate != null) {
      print(pickedDate);
      //String formattedDate = DateFormat('MM/dd/yy').format(pickedDate);
      String formattedDate = DateFormat('MM/yyyy').format(pickedDate);
      print(formattedDate);

      reportController.datePickerFleetFromController.text = "01/$formattedDate";
    } else {
      print("Date is not selected");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            simpleMyAppBar(
                title: "".trr,
                backString: "Back".trr,
                onTap: () {
                  Get.back();
                },
                backColor: AppColor.cBlueFont),
            horizontalSpace(45),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    /* simpleMyAppBar(
                        title: "".trr,
                        backString: "Back".trr,
                        onTap: () {
                          Get.back();
                        },
                        backColor: AppColor.cBlueFont),
                    horizontalSpace(45),*/
                    Text(
                      widget.title,
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    Obx(() {
                      return ListView(
                        padding:
                            EdgeInsets.only(left: 16, right: 16, bottom: 16),
                        physics: BouncingScrollPhysics(),
                        shrinkWrap: true,
                        children: [
                          /* Expanded(
                      flex: 2,*/
                          CommonTextField(
                            controller:
                                reportController.datePickerFleetFromController,
                            labelText: '${"Period".trr}*',
                            suffix: assetSvdImageWidget(
                                image: DefaultImages.calendarIcn),
                            fillColor: AppColor.cWhite,
                            filled: true,
                            readOnly: true,
                            onTap: pickDate,
                          ),
                          //  ),
                          Gap(16),
                          CommonDropdownButtonWidget(
                            hint: 'Connection Status'.trr,
                            labelText: 'Connection Status'.trr,
                            list: reportController.connectionStatusList,
                            // Your adjusted list
                            value: reportController
                                .selectedconnectionStatusList.value,
                            // Currently selected value
                            onChanged: (value) {
                              if (value != null) {
                                // Assuming onChanged provides a non-null value
                                reportController.selectedConnection.value = value
                                    .toString(); // Update the selected value
                              }
                            },
                            fontColor: AppColor.cDarkGreyFont,
                            filledColor: AppColor.cFilled,
                          ),
                          Gap(16),
                          Row(
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: Checkbox(
                                  value: reportController.isGroupByPlate.value,
                                  onChanged: (value) {
                                    reportController.isGroupByPlate.value =
                                        value!;
                                  },
                                  activeColor: AppColor.themeBlueColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  materialTapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                ),
                              ),
                              horizontalSpace(8),
                              Text(
                                'Group By Plate'.trr,
                                style: pRegular12,
                              )
                            ],
                          ),
                          Gap(16),
                          // Row(
                          //   children: [
                          //     SizedBox(
                          //       width: 20,
                          //       height: 20,
                          //       child: Checkbox(
                          //         value: reportController.isShiftClose.value,
                          //         onChanged: (value) {
                          //           reportController.isShiftClose.value =
                          //               value!;
                          //           print(reportController.isShiftClose);
                          //         },
                          //         activeColor: AppColor.themeBlueColor,
                          //         shape: RoundedRectangleBorder(
                          //           borderRadius: BorderRadius.circular(4),
                          //         ),
                          //         materialTapTargetSize:
                          //             MaterialTapTargetSize.shrinkWrap,
                          //       ),
                          //     ),
                          //     horizontalSpace(8),
                          //     Text(
                          //       'Shift Closing'.trr,
                          //       style: pRegular14,
                          //     )
                          //   ],
                          // ),
                          // Gap(16),
                          Text(
                            "Division".trr,
                            style: pRegular12,
                          ),
                          DropdownButtonFormField(
                            items: reportController.reportDivList2.map((data) {
                              return DropdownMenuItem(
                                value: data.TYPECODE,
                                child: Text(
                                  data.TYPEDESC,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              reportController.selectedDiv.value =
                                  value.toString();
                              reportController.loadBranch(value.toString());
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Division'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                          Gap(16),
                          Text(
                            "Branch".trr,
                            style: pRegular12,
                          ),
                          DropdownButtonFormField(
                            items:
                                reportController.reportBranchList.map((data) {
                              return DropdownMenuItem(
                                value: data.BRANCHCODE,
                                child: Text(
                                  data.BRANCHNAME,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              reportController.selectedBranch.value =
                                  value.toString();
                              reportController.loadDept(value.toString());
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Branch'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                          Gap(16),
                          Text(
                            "Department".trr,
                            style: pRegular12,
                          ),
                          DropdownButtonFormField(
                            items: reportController.reportDeptList.map((data) {
                              return DropdownMenuItem(
                                value: data.DEPTCODE,
                                child: Text(
                                  data.DEPTNAME,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              reportController.selectedDepartment.value =
                                  value.toString();
                              reportController.loadOperation(value.toString());
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Department'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                          Gap(16),
                          Text(
                            "Operation".trr,
                            style: pRegular12,
                          ),
                          DropdownButtonFormField(
                            items: reportController.reportOprList.map((data) {
                              return DropdownMenuItem(
                                value: data.TYPECODE,
                                child: Text(
                                  data.TYPEDESC,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              reportController.selectedOperation.value =
                                  value.toString();
                              //reportController.loadStation(value.toString());
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Operation'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                          Gap(16),
                          Text(
                            "Station".trr,
                            style: pRegular12,
                          ),
                          DropdownButtonFormField(
                            items: reportController.reportSTNList.map((data) {
                              return DropdownMenuItem(
                                value: data.STATIONCODE,
                                child: Text(
                                  data.STATION,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              reportController.selectedStn.value =
                                  value.toString();
                              //reportController.loadDriver(value.toString());
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Station'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                          Gap(16),
                          Text(
                            "Driver".trr,
                            style: pRegular12,
                          ),
                          DropdownButtonFormField(
                            items:
                                reportController.reportDriverList.map((data) {
                              return DropdownMenuItem(
                                value: data.DRIVERCODE,
                                child: Text(
                                  data.DRIVER,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              reportController.selectedDriver.value =
                                  value.toString();
                              //reportController.loadPlateNo(value.toString());
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Driver'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                          Gap(16),
                          Text(
                            "Plate No".trr,
                            style: pRegular12,
                          ),
                          DropdownButtonFormField(
                            items:
                                reportController.reportPlatesList.map((data) {
                              return DropdownMenuItem(
                                value: data.PLATENOCODE,
                                child: Text(
                                  data.PLATENO,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              reportController.selectedPlateNo.value =
                                  value.toString();
                              //reportController.loadVehicle(value.toString());
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Plate No'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                          Gap(16),
                          Text(
                            "Vehicle".trr,
                            style: pRegular12,
                          ),
                          DropdownButtonFormField(
                            items:
                                reportController.reportVehicleList.map((data) {
                              return DropdownMenuItem(
                                value: data.CODE,
                                child: Text(
                                  data.TYPEDESC,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              reportController.selectedVehicle.value =
                                  value.toString();
                              //reportController.loadProducts(value.toString());
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Vehicle'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                          Gap(16),
                          Text(
                            "Products".trr,
                            style: pRegular12,
                          ),
                          DropdownButtonFormField(
                            items:
                                reportController.reportProductList.map((data) {
                              return DropdownMenuItem(
                                value: data.CODE,
                                child: Text(
                                  data.TYPEDESC,
                                  style: pMedium12,
                                  textAlign: TextAlign.center,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              reportController.selectedProduct.value =
                                  value.toString();
                            },
                            style: pRegular14.copyWith(color: AppColor.cLabel),
                            borderRadius: BorderRadius.circular(6),
                            dropdownColor: AppColor.cLightGrey,
                            icon: assetSvdImageWidget(
                                image: DefaultImages.dropDownIcn),
                            decoration: InputDecoration(
                              hintText: 'Products'.trr,
                              hintStyle: pRegular14.copyWith(
                                  color: AppColor.cHintFont),
                              contentPadding:
                                  EdgeInsets.only(left: 16, right: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                                borderSide: BorderSide(
                                  color: AppColor.cBorder,
                                ),
                              ),
                            ),
                          ),
                          Gap(16),
                          CommonDropdownButtonWidget(
                            hint: 'Services'.trr,
                            labelText: 'Services'.trr,
                            list: reportController.ServicesList,
                            // Your adjusted list
                            value: reportController.selectedServicesList.value,
                            // Currently selected value
                            onChanged: (value) {
                              if (value != null) {
                                // Assuming onChanged provides a non-null value
                                reportController.selectedService.value =
                                    value; // Update the selected value
                              }
                            },
                            fontColor: AppColor.cDarkGreyFont,
                            filledColor: AppColor.cFilled,
                          ),
                        ],
                      );
                    }),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        //  child: Expanded(
        child: CommonButton(
          title: 'SUBMIT'.trr,
          onPressed: () {
            reportController.reportRequestSubmit();
          },
          textColor: AppColor.cWhiteFont,
          btnColor: AppColor.themeOrangeColor,
        ),
        //  ),
      ),
    );
  }
}
