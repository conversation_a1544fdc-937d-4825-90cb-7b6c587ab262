import 'dart:convert';

class ServiceknownModel {
  final bool disabled;
  final String group;
  final bool selected;
  final String text;
  final String value;
  ServiceknownModel({
    required this.disabled,
    required this.group,
    required this.selected,
    required this.text,
    required this.value,
  });

  Map<String, dynamic> toMap() {
    return {
      'Disabled': disabled,
      'Group': group,
      'Selected': selected,
      'Text': text,
      'Value': value,
    };
  }

  factory ServiceknownModel.fromMap(Map<String, dynamic> map) {
    return ServiceknownModel(
      disabled: map['Disabled'] ?? '',
      group: map['Group'] ?? '',
      selected: map['Selected'] ?? '',
      text: map['Text'] ?? '',
      value: map['Value'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory ServiceknownModel.fromJson(String source) =>
      ServiceknownModel.fromMap(json.decode(source));
}
