import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../utils/colors.dart';
import '../../../../utils/images.dart';
import '../../../../utils/text_style.dart';
import '../../../widget/icon_and_image.dart';

menuItemWidget({
  required String title,
  required Function() onTap,
  TextStyle? textStyle,
  String? icon,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        width: Get.width,
        color: AppColor.cBackGround,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: textStyle ?? pRegular13,
            ),
            assetSvdImageWidget(
              image: icon ?? DefaultImages.nextIcn,
              colorFilter: icon != null
                  ? null
                  : ColorFilter.mode(
                AppColor.cText,
                BlendMode.srcIn,
              ),
            ),
          ],
        ),
      ),
    ),
  );
}