// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/core/controller/menu_controller/help_center_controller/help_center_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/help_center_screen/download_documents_screen.dart';
import 'package:waie_app/view/screen/menu_screen/help_center_screen/general_question_screen.dart';
import 'package:waie_app/view/screen/menu_screen/help_center_screen/search_video_tutorial_widget.dart';
import 'package:waie_app/view/screen/menu_screen/help_center_screen/video_tutorials_screen.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../widget/common_appbar_widget.dart';
import 'have_issue_screen.dart';

class HelpCenterScreen extends StatelessWidget {
  HelpCenterScreen({Key? key}) : super(key: key);
  HelpCenterController helpCenterController = Get.put(HelpCenterController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: Column(
            children: [
              simpleAppBar(
                  title: "Help center".trr,
                  onTap: () {
                    Get.back();
                  },
                  backString: "Back".trr),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Help center".trr,
                      style: pBold20,
                    ),
                    /* simpleAppBar(
                        title: "Help center".trr,
                        onTap: () {
                          Get.back();
                        },
                        backString: "Back".trr),*/
                    // GestureDetector(
                    //     onTap: () {
                    //       showModalBottomSheet(
                    //         context: context,
                    //         barrierColor: AppColor.cBlackOpacity,
                    //         shape:
                    //             RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
                    //         isScrollControlled: true,
                    //         builder: (context) {
                    //           return SearchVideoTutorialWidget();
                    //         },
                    //       );
                    //     },
                    //     child: assetSvdImageWidget(image: DefaultImages.searchCircleIcn))
                  ],
                ),
              ),
              Obx(() {
                return Expanded(
                  child: ListView(
                    shrinkWrap: true,
                    padding: EdgeInsets.only(
                        top: 8, bottom: 16, right: 16, left: 16),
                    physics: BouncingScrollPhysics(),
                    children: [
                      CommonDropdownButtonWidget(
                        labelText: '',
                        isExpanded: true,
                        list: helpCenterController.helpValueList,
                        value: helpCenterController.helpValue.value,
                        onChanged: (value) {
                          helpCenterController.helpValue.value = value;
                        },
                      ),
                      verticalSpace(24),
                      helpCenterController.helpValue.value ==
                              "General Questions"
                          ? GeneralQuestionScreen(
                              helpCenterController: helpCenterController,
                            )
                          : helpCenterController.helpValue.value ==
                                  'Download Documents'
                              ? DownloadDocumentsScreen()
                              : VideoTutorialScreen(
                                  helpCenterController: helpCenterController,
                                )
                    ],
                  ),
                );
              })
            ],
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12.0),
          child: GestureDetector(
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(12))),
                useSafeArea: false,
                builder: (context) {
                  return HaveIssueScreen(
                    helpCenterController: helpCenterController,
                  );
                },
              );
            },
            child: Container(
              height: 40,
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 30),
              decoration: BoxDecoration(
                color: AppColor.themeOrangeColor,
                borderRadius: BorderRadius.circular(4),
              ),
              child: GestureDetector(
                onTap: () {
                  showDialog(
                    context: Get.context!,
                    builder: (context) {
                      return AlertDialog(
                        insetPadding: const EdgeInsets.all(16),
                        contentPadding: const EdgeInsets.all(16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              "Need help?".trr,
                              style: TextStyle(
                                  fontSize: 20, fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 24),
                            GestureDetector(
                              onTap: () async {
                                String phoneNumber =
                                    "tel://8001228800"; // Replace with your phone number
                                if (await canLaunch(phoneNumber)) {
                                  await launch(phoneNumber);
                                } else {
                                  throw 'Could not launch $phoneNumber';
                                }
                              },
                              child: Text(
                                "Kindly call us on 920002667".trr,
                                style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.blue,
                                    decoration: TextDecoration.underline),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            SizedBox(height: 24),
                          ],
                        ),
                      );
                    },
                  );
                },
                child: Text(
                  "I have an issue / question".trr,
                  style: pRegular13.copyWith(color: AppColor.cWhiteFont),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  TextFormField searchField(
      TextEditingController controller, Function() onTap) {
    return TextFormField(
      controller: controller,
      style: pRegular14,
      cursorColor: AppColor.cHintFont,
      readOnly: true,
      onTap: onTap,
      decoration: InputDecoration(
        hintText: 'Search',
        hintStyle: pRegular14,
        prefixIcon: Icon(Icons.search, color: AppColor.cText),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: AppColor.cBorder)),
        disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: AppColor.cBorder)),
        enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: AppColor.cBorder)),
        errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: AppColor.cRedText)),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: AppColor.cFocusedTextField)),
        contentPadding: EdgeInsets.only(left: 16, right: 16),
      ),
    );
  }
}
