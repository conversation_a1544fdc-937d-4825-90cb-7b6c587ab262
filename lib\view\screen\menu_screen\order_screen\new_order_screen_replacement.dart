// ignore_for_file: must_be_immutable, prefer_const_constructors, avoid_print,, sized_box_for_whitespace, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:waie_app/core/controller/menu_controller/order_controller/new_order_replacment_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/order_controller/confirm_order_controller.dart';
import '../../../widget/common_button.dart';
import '../balance_topup_screen/balance_topup_tab_screen.dart';

class NewOrderScreenreplacement extends StatefulWidget {
  const NewOrderScreenreplacement({super.key});

  @override
  State<NewOrderScreenreplacement> createState() =>
      _NewOrderScreenreplacementState();
}

class _NewOrderScreenreplacementState extends State<NewOrderScreenreplacement>
    with SingleTickerProviderStateMixin {
  NewOrderReplacementController newOrderController =
      Get.put(NewOrderReplacementController());
  final ConfirmOrderController confirmOrderController =
      Get.put(ConfirmOrderController());

  // PriceTagCardController priceTagCardController = Get.find();
  //Controller controller = Get.put(Controller());

  GetStorage custsData = GetStorage('custsData');

  var amountOrderUnit = '0';
  var amountOrderTopup = '0';
  double amountValue = 0;
  double initialVat = 0;
  double newVat = 0;
  num vatAmount = 0;
  String vatExclusiveAmount = "0";
  num totalAmount = 0;
  RxString isoCode = 'SA'.obs;

  /// ///////////////////////////////////////
  bool _showOtherOptions = false;
  bool _showHint = true;

  /// ///////////////////////////////////////

  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      callOrderInit();
    });

    print("isAR <> ${Constants.IsAr_App}");
  }

  void callOrderInit() async {
    print(" callOrderInit called=========== ");
    newOrderController.quantity.value = 1;
    newOrderController.isTag.value = true;
    newOrderController.isSmartCard.value = false;
    newOrderController.srvType.value = "T";
    await newOrderController.fetchPriceTagCards();
    newOrderController.serviceTypeController.text = "T";
    amountOrderUnit = newOrderController.unitPrice.value;
    amountOrderTopup = newOrderController.topUpAmount.value;
    print("TAG");
    print("amountOrderUnit ******** $amountOrderUnit");
    print("amountOrderTopup ******** $amountOrderTopup");
    print(
        "newOrderController.vatAmount.value ******** ${newOrderController.vatAmount.value}");
    print(
        "newOrderController.quantity.value ******** ${newOrderController.quantity.value}");
    await _qtyCounter();
  }

  _qtyCounter() {
    setState(() {
      amountValue = double.parse(newOrderController.unitPrice.value == "0"
              ? amountOrderTopup
              : amountOrderUnit) *
          newOrderController.quantity.value;
      double vatExclusivAmt = roundNumber((amountValue / 1.15), 2);

      //vatExclusiveAmount = vatExclusivAmt.toString();
      vatExclusiveAmount = newOrderController.subTotal.value;
      //roundNumber(vatExclusivAmt,2);//,int.parse(AmountValue)-10);//int.parse(AmountValue)-10;
      //vatAmount = roundNumber(amountValue - vatExclusivAmt, 2);
      initialVat = double.parse(newOrderController.vatAmount.value) *
          newOrderController.quantity.value;
      //newVat = amountValue - vatExclusivAmt;
      newVat = double.parse(newOrderController.vatAmount.value);
      vatAmount = roundNumber(initialVat, 2);
      // totalAmount = roundNumber(
      //     double.parse(vatExclusiveAmount.toString()) +
      //         double.parse(newVat.toString()),
      //     2);
      totalAmount =
          roundNumber(double.parse(newOrderController.totalAmount.value), 2);
    });

    print("amountOrderUnit ******** $amountOrderUnit");
    print("amountOrderTopup ******** $amountOrderTopup");

    print(
        "controller.quantity.value.toString() ********** >>>>> ${newOrderController.quantity.value}");

    print("amountValue ********** >>>>> $amountValue");

    print("vatExclusivAmt ********** >>>>> $vatExclusiveAmount");

    print("vatAmount ********** >>>>> $vatAmount");

    print("totalAmount ********** >>>>> $vatAmount");
  }

  @override
  Widget build(BuildContext context) {
    print(amountValue);
    var custData = custsData.read('custData');
    // var currentBalance = custData['BALAMT'];
    var currentBalance = Constants.custBalance;
    print("currentBalance ********** >>>>> $currentBalance");
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              simpleAppBar(
                  title: "REPLACEMENT".trr,
                  onTap: () {
                    Get.back();
                  },
                  backString: "Back".trr),
              Obx(
                () => Expanded(
                  child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: SingleChildScrollView(
                        physics: BouncingScrollPhysics(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'I want to order a'.trr + "...",
                              style: pSemiBold17,
                            ),
                            verticalSpace(24),
                            Column(
                              children: [
                                tagWidget(
                                  onTap: () async {
                                    newOrderController.isTag.value = true;
                                    newOrderController.isSmartCard.value =
                                        false;
                                    newOrderController.srvType.value = "T";
                                    await newOrderController
                                        .fetchPriceTagCards();
                                    newOrderController
                                        .serviceTypeController.text = "T";
                                    amountOrderUnit =
                                        newOrderController.unitPrice.value;
                                    amountOrderTopup =
                                        newOrderController.topUpAmount.value;
                                    print("TAG");
                                    print(
                                        "amountOrderUnit ******** $amountOrderUnit");
                                    print(
                                        "amountOrderTopup ******** $amountOrderTopup");
                                    await _qtyCounter();
                                  },
                                  title: "Tag".trr,
                                  isSelected: newOrderController.isTag.value,
                                  subtitle:
                                      "Applies only to specified cars, installed in the fuel tank and fuel-up by RFID."
                                          .trr,
                                ),
                                if (Constants.SCrbBtn == "Y") verticalSpace(10),
                                if (Constants.SCrbBtn == "Y")
                                  tagWidget(
                                    onTap: () async {
                                      newOrderController.isTag.value = false;
                                      newOrderController.isSmartCard.value =
                                          true;
                                      newOrderController.isDelivery.value =
                                          true;
                                      newOrderController.isPickUp.value = false;
                                      newOrderController.srvType.value = "C";
                                      await newOrderController
                                          .fetchPriceTagCards();
                                      newOrderController
                                          .serviceTypeController.text = "C";
                                      amountOrderUnit =
                                          newOrderController.unitPrice.value;
                                      amountOrderTopup =
                                          newOrderController.topUpAmount.value;
                                      print("CARD");
                                      print(
                                          "amountOrderUnit ******** $amountOrderUnit");
                                      print(
                                          "amountOrderTopup ******** $amountOrderTopup");
                                      //amountOrder = data.cardRate;
                                      await _qtyCounter();
                                    },
                                    title: "Smart Card".trr,
                                    isSelected:
                                        newOrderController.isSmartCard.value,
                                    subtitle:
                                        "No more bills or cash, means faster transaction."
                                            .trr,
                                  ),
                              ],
                            ),
                            verticalSpace(8),
                            /* Text(
                              "Quantity".trr,
                              style: pSemiBold17,
                            ),
                            verticalSpace(8),
                            Row(
                              children: [
                                plusWidget(
                                  image: DefaultImages.plusIcn,
                                  onTap: () {
                                    if (newOrderController.isTag.value ==
                                        false &&
                                        newOrderController.isSmartCard.value ==
                                            false) {
                                      commonToast("Please select ORDER first.");
                                    } else {
                                      newOrderController.quantity.value += 1;
                                      _qtyCounter();
                                    }
                                  },
                                ),
                                Expanded(
                                  child: Padding(
                                    padding:
                                    EdgeInsets.symmetric(horizontal: 12),
                                    child: Container(
                                      height: 44,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                          BorderRadius.circular(4),
                                          border: Border.all(
                                              color: AppColor.cBorder)),
                                      child: Center(
                                        child: Text(
                                          newOrderController.quantity.value
                                              .toString(),
                                          style: pRegular14.copyWith(
                                              color: AppColor.cDarkGreyFont),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                plusWidget(
                                  image: DefaultImages.minusIcn,
                                  onTap: () {
                                    if (newOrderController.isTag.value ==
                                        false &&
                                        newOrderController.isSmartCard.value ==
                                            false) {
                                      commonToast("Please select ORDER first.");
                                    } else {
                                      if (newOrderController.quantity.value >
                                          0) {
                                        newOrderController.quantity.value -= 1;
                                        _qtyCounter();
                                      }
                                    }
                                  },
                                ),
                              ],
                            ),
                            verticalSpace(32),
                            horizontalDivider(),
                            verticalSpace(32),*/
                            /// /////////////////////////////////////////////////////////////////////////////////
                            Text(
                              "Payment options".trr,
                              style: pSemiBold17,
                            ),
                            // // if(Constants.custRegType=="I" && Constants.custAcctType=="C")
                            verticalSpace(16),
                            // //  if(Constants.custRegType=="I" && Constants.custAcctType=="C")
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Stack(
                                    alignment: Alignment.centerRight,
                                    children: [
                                      // Row with Title and Arrow Button
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text("Payment options".trr,
                                              style: pSemiBold17),

                                          // Stack around the button and the hint
                                          Stack(
                                            clipBehavior: Clip.none,
                                            alignment: Alignment.topCenter,
                                            children: [
                                              // Arrow button
                                              IconButton(
                                                onPressed: () {
                                                  setState(() {
                                                    _showOtherOptions =
                                                        !_showOtherOptions;
                                                    _showHint =
                                                        false; // Hide the hint on click
                                                  });
                                                },
                                                icon: Icon(
                                                  _showOtherOptions
                                                      ? Icons
                                                          .keyboard_arrow_up_rounded
                                                      : Icons
                                                          .keyboard_arrow_down_rounded,
                                                ),
                                              ),

                                              // Hint bubble (adjusted for LTR and RTL)
                                              if (_showHint)
                                                Positioned(
                                                  top: -25,
                                                  // Dynamically switch between `left` and `right`
                                                  left: Directionality.of(
                                                              context) ==
                                                          TextDirection.rtl
                                                      ? 0
                                                      : null,
                                                  right: Directionality.of(
                                                              context) ==
                                                          TextDirection.ltr
                                                      ? 0
                                                      : null,
                                                  child: Material(
                                                    color: Colors.transparent,
                                                    child: Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 12,
                                                          vertical: 8),
                                                      decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        border: Border.all(
                                                            color: AppColor
                                                                .cBlueFont),
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color:
                                                                Colors.black12,
                                                            blurRadius: 6,
                                                            offset:
                                                                Offset(2, 2),
                                                          ),
                                                        ],
                                                      ),
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Text(
                                                            "Tap to view more payment methods"
                                                                .trr,
                                                            style: TextStyle(
                                                              color: Colors
                                                                  .black87,
                                                              fontSize: 13,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                            ),
                                                          ),
                                                          SizedBox(width: 6),
                                                          Text("👇",
                                                              style: TextStyle(
                                                                  fontSize:
                                                                      14)),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  // if (Constants.custIs_partner == "N" &&
                                  //     Constants.custBalance != "0")
                                  if (Constants.MadaPayOpt == "Y")
                                    newPaymentOptionDataWidget(
                                        onTap: () {
                                          {
                                            newOrderController
                                                .paymentOptionList[0]['value']
                                                .value = false;
                                            newOrderController
                                                .paymentOptionList[1]['value']
                                                .value = false;
                                            newOrderController
                                                .paymentOptionList[2]['value']
                                                .value = false;
                                            newOrderController
                                                .paymentOptionList[3]['value']
                                                .value = true;
                                            newOrderController
                                                .paymentOptionList[4]['value']
                                                .value = false;
                                            if (Constants.STCEnable == "Y") {
                                              newOrderController
                                                  .paymentOptionList[5]['value']
                                                  .value = false;
                                            }
                                          }
                                          newOrderController.paytype.value =
                                              "D";
                                          newOrderController
                                              .payTypeController.text = "D";
                                          newOrderController
                                              .currantIndex.value = 3;
                                          print(
                                              "newOrderController.payTypeController.text" +
                                                  newOrderController
                                                      .payTypeController.text);
                                          newOrderController.paymentOptionList
                                              .refresh();
                                        },
                                        isWidget: true,
                                        isSelected: newOrderController
                                            .paymentOptionList[3]['value']
                                            .value,
                                        title: "MADA".trr,
                                        image: DefaultImages.madaIMG,
                                        widget: MadaPaymentWidget()),
                                  if (_showOtherOptions)
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        if (newOrderController.useBalanceOTP ==
                                            "Y")
                                          balancePaymentOptionDataWidget(
                                              onTap: () {
                                                {
                                                  newOrderController
                                                      .paymentOptionList[0]
                                                          ['value']
                                                      .value = true;
                                                  newOrderController
                                                      .paymentOptionList[1]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[2]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[3]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[4]
                                                          ['value']
                                                      .value = false;
                                                  if (Constants.STCEnable ==
                                                      "Y") {
                                                    newOrderController
                                                        .paymentOptionList[5]
                                                            ['value']
                                                        .value = false;
                                                  }
                                                }
                                                newOrderController
                                                    .paytype.value = "B";
                                                newOrderController
                                                    .payTypeController
                                                    .text = "B";
                                                newOrderController
                                                    .currantIndex.value = 0;
                                                print(
                                                    "newOrderController.payTypeController.text" +
                                                        newOrderController
                                                            .payTypeController
                                                            .text);
                                                newOrderController
                                                    .paymentOptionList
                                                    .refresh();
                                              },
                                              isWidget: true,
                                              isSelected: newOrderController
                                                  .paymentOptionList[0]['value']
                                                  .value,
                                              // title: "Use my Balance Credits".trr+"                 "+ "Available balance:  "+currentBalance.toString(),
                                              title:
                                                  "Use my Balance Credits".trr,
                                              //currentBalanceWidget(balance: Constants.custBalance =="-9999999" ?"Not Available" :(Constants.custBalance ??"0.00")),
                                              image:
                                                  Constants.IsAr_App == "false"
                                                      ? DefaultImages
                                                          .deductBalanceEN
                                                      : DefaultImages
                                                          .deductBalanceAR,
                                              widget: UseBalanceWidget(
                                                  currentBalance.toString())),
                                        if (Constants.promoPayment == "Y")
                                          partnersPaymentOptionDataWidget(
                                              onTap: () {
                                                {
                                                  newOrderController
                                                      .paymentOptionList[0]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[1]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[2]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[4]
                                                          ['value']
                                                      .value = true;
                                                  newOrderController
                                                      .paymentOptionList[3]
                                                          ['value']
                                                      .value = false;
                                                  if (Constants.STCEnable ==
                                                      "Y") {
                                                    newOrderController
                                                        .paymentOptionList[5]
                                                            ['value']
                                                        .value = false;
                                                  }
                                                }
                                                newOrderController
                                                    .paymentOptionList
                                                    .refresh();
                                              },
                                              isWidget: true,
                                              isSelected: newOrderController
                                                  .paymentOptionList[4]['value']
                                                  .value,
                                              title: "Aldrees promotion".trr,
                                              image:
                                                  Constants.IsAr_App == "false"
                                                      ? DefaultImages
                                                          .aldreesPartnersEN
                                                      : DefaultImages
                                                          .aldreesPartnersAR,
                                              widget: AldreesPromotionWidget()),
                                        cashPaymentOptionDataWidget(
                                            onTap: () {
                                              {
                                                newOrderController
                                                    .paymentOptionList[0]
                                                        ['value']
                                                    .value = false;
                                                newOrderController
                                                    .paymentOptionList[1]
                                                        ['value']
                                                    .value = true;
                                                newOrderController
                                                    .paymentOptionList[2]
                                                        ['value']
                                                    .value = false;
                                                newOrderController
                                                    .paymentOptionList[3]
                                                        ['value']
                                                    .value = false;
                                                newOrderController
                                                    .paymentOptionList[4]
                                                        ['value']
                                                    .value = false;
                                                if (Constants.STCEnable ==
                                                    "Y") {
                                                  newOrderController
                                                      .paymentOptionList[5]
                                                          ['value']
                                                      .value = false;
                                                }
                                              }
                                              newOrderController.paytype.value =
                                                  "C";
                                              newOrderController
                                                  .payTypeController.text = "C";
                                              newOrderController
                                                  .currantIndex.value = 1;
                                              print(
                                                  "newOrderController.payTypeController.text" +
                                                      newOrderController
                                                          .payTypeController
                                                          .text);
                                              newOrderController
                                                  .paymentOptionList
                                                  .refresh();
                                            },
                                            isWidget: true,
                                            isSelected: newOrderController
                                                .paymentOptionList[1]['value']
                                                .value,
                                            title: "Cash".trr,
                                            image: Constants.IsAr_App == "false"
                                                ? DefaultImages.cashEN
                                                : DefaultImages.cashAR,
                                            widget: cashPaymentNoteWidget()),
                                        if (Constants.STCEnable == "Y")
                                          stcPayPaymentOptionDataWidget(
                                              onTap: () {
                                                {
                                                  newOrderController
                                                      .paymentOptionList[0]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[1]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[2]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[3]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[4]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[5]
                                                          ['value']
                                                      .value = true;
                                                }
                                                newOrderController
                                                    .paytype.value = "Y";
                                                newOrderController
                                                    .payTypeController
                                                    .text = "Y";
                                                newOrderController
                                                    .currantIndex.value = 5;
                                                print(
                                                    "newOrderController.payTypeController.text" +
                                                        newOrderController
                                                            .payTypeController
                                                            .text);
                                                newOrderController
                                                    .paymentOptionList
                                                    .refresh();
                                              },
                                              isWidget: true,
                                              isSelected: newOrderController
                                                  .paymentOptionList[5]['value']
                                                  .value,
                                              title: "STC Pay".tr,
                                              image: DefaultImages.stcBank,
                                              widget: stcPayPaymentWidget()),
                                        if (Constants.ETrans == "Y")
                                          eTransferPaymentOptionDataWidget(
                                              onTap: () {
                                                {
                                                  newOrderController
                                                      .paymentOptionList[0]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[1]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[2]
                                                          ['value']
                                                      .value = true;
                                                  newOrderController
                                                      .paymentOptionList[3]
                                                          ['value']
                                                      .value = false;
                                                  newOrderController
                                                      .paymentOptionList[4]
                                                          ['value']
                                                      .value = false;
                                                  if (Constants.STCEnable ==
                                                      "Y") {
                                                    newOrderController
                                                        .paymentOptionList[5]
                                                            ['value']
                                                        .value = false;
                                                  }
                                                }
                                                newOrderController
                                                    .paytype.value = "E";
                                                newOrderController
                                                    .payTypeController
                                                    .text = "E";
                                                newOrderController
                                                    .currantIndex.value = 2;
                                                print(
                                                    "newOrderController.payTypeController.text" +
                                                        newOrderController
                                                            .payTypeController
                                                            .text);
                                                newOrderController
                                                    .paymentOptionList
                                                    .refresh();
                                              },
                                              isWidget: true,
                                              isSelected: newOrderController
                                                  .paymentOptionList[2]['value']
                                                  .value,
                                              title: "E. Transfer".trr,
                                              image: Constants.IsAr_App ==
                                                      "false"
                                                  ? DefaultImages.eTransferEN
                                                  : DefaultImages.eTransferAR,
                                              widget: eTransferWidget()),
                                        if (Constants.newETrans == "Y")
                                          newETransferPaymentOptionDataWidget(
                                              title: "E. Transfer".trr,
                                              image: Constants.IsAr_App ==
                                                      "false"
                                                  ? DefaultImages.eTransferEN
                                                  : DefaultImages.eTransferAR,
                                              widget: newETransferWidget()),
                                        newOrderController.isSmartCard.value ==
                                                true
                                            ? SizedBox()
                                            : SizedBox(),
                                      ],
                                    )
                                ]),

                            /// /////////////////////////////////////////////////////////////////////////////////
                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Stack(
                            //       alignment: Alignment.centerRight,
                            //       children: [
                            //         Row(
                            //           mainAxisAlignment:
                            //               MainAxisAlignment.spaceBetween,
                            //           children: [
                            //             Text(
                            //               "Payment Options".tr,
                            //               style: pSemiBold17,
                            //             ),
                            //             Stack(
                            //               clipBehavior: Clip.none,
                            //               alignment: Alignment.topCenter,
                            //               children: [
                            //                 IconButton(
                            //                   onPressed: () {
                            //                     setState(() {
                            //                       _showOtherOptions =
                            //                           !_showOtherOptions;
                            //                       _showHint = false;
                            //                     });
                            //                   },
                            //                   icon: Icon(
                            //                     _showOtherOptions
                            //                         ? Icons
                            //                             .keyboard_arrow_up_rounded
                            //                         : Icons
                            //                             .keyboard_arrow_down_rounded,
                            //                     // color:
                            //                     //     AppColor.themeOrangeColor,
                            //                   ),
                            //                 ),
                            //                 if (_showHint)
                            //                   Positioned(
                            //                     top: -25,
                            //                     right: 0,
                            //                     child: SlideTransition(
                            //                       position: _bounceAnimation,
                            //                       child: FadeTransition(
                            //                         opacity: _fadeAnimation,
                            //                         child: Material(
                            //                           color: Colors.transparent,
                            //                           child: Container(
                            //                             padding:
                            //                                 const EdgeInsets
                            //                                     .symmetric(
                            //                                     horizontal: 12,
                            //                                     vertical: 8),
                            //                             decoration:
                            //                                 BoxDecoration(
                            //                               color: Colors.white,
                            //                               borderRadius:
                            //                                   BorderRadius
                            //                                       .circular(12),
                            //                               border: Border.all(
                            //                                   color: AppColor
                            //                                       .cBlueFont),
                            //                               boxShadow: [
                            //                                 BoxShadow(
                            //                                   color: Colors
                            //                                       .black12,
                            //                                   blurRadius: 6,
                            //                                   offset:
                            //                                       Offset(2, 2),
                            //                                 ),
                            //                               ],
                            //                             ),
                            //                             child: Row(
                            //                               mainAxisSize:
                            //                                   MainAxisSize.min,
                            //                               children: [
                            //                                 Text(
                            //                                   "Tap to view more payment methods",
                            //                                   style: TextStyle(
                            //                                     color: Colors
                            //                                         .black87,
                            //                                     fontSize: 13,
                            //                                     fontWeight:
                            //                                         FontWeight
                            //                                             .w500,
                            //                                   ),
                            //                                 ),
                            //                                 SizedBox(width: 6),
                            //                                 Text("👇",
                            //                                     style: TextStyle(
                            //                                         fontSize:
                            //                                             14)),
                            //                               ],
                            //                             ),
                            //                           ),
                            //                         ),
                            //                       ),
                            //                     ),
                            //                   ),
                            //               ],
                            //             ),
                            //           ],
                            //         ),
                            //       ],
                            //     ),
                            //     SizedBox(height: 16),
                            //     if (Constants.MadaPayOpt == "Y")
                            //       newPaymentOptionDataWidget(
                            //         onTap: () {
                            //           newOrderController.paymentOptionList
                            //               .forEach((opt) {
                            //             opt['value'].value = false;
                            //           });
                            //           newOrderController
                            //               .paymentOptionList[3]['value']
                            //               .value = true;
                            //           newOrderController.paytype.value = "D";
                            //           newOrderController
                            //               .payTypeController.text = "D";
                            //           newOrderController.currantIndex.value = 3;
                            //           newOrderController.paymentOptionList
                            //               .refresh();
                            //           print(
                            //               "newOrderController.payTypeController.text: ${newOrderController.payTypeController.text}");
                            //         },
                            //         isWidget: true,
                            //         isSelected: newOrderController
                            //             .paymentOptionList[3]['value'].value,
                            //         title: "MADA".tr,
                            //         image: DefaultImages.madaIMG,
                            //         widget: MadaPaymentWidget(),
                            //       ),
                            //     if (_showOtherOptions)
                            //       ConstrainedBox(
                            //         constraints: BoxConstraints(
                            //           maxHeight:
                            //               MediaQuery.of(context).size.height *
                            //                   0.4,
                            //         ),
                            //         child: IntrinsicHeight(
                            //           child: SingleChildScrollView(
                            //             child: Column(
                            //               crossAxisAlignment:
                            //                   CrossAxisAlignment.start,
                            //               children: [
                            //                 if (newOrderController
                            //                         .useBalanceOTP ==
                            //                     "Y")
                            //                   balancePaymentOptionDataWidget(
                            //                     onTap: () {
                            //                       newOrderController
                            //                           .paymentOptionList
                            //                           .forEach((opt) {
                            //                         opt['value'].value = false;
                            //                       });
                            //                       newOrderController
                            //                           .paymentOptionList[0]
                            //                               ['value']
                            //                           .value = true;
                            //                       newOrderController
                            //                           .paytype.value = "B";
                            //                       newOrderController
                            //                           .payTypeController
                            //                           .text = "B";
                            //                       newOrderController
                            //                           .currantIndex.value = 0;
                            //                       newOrderController
                            //                           .paymentOptionList
                            //                           .refresh();
                            //                       print(
                            //                           "newOrderController.payTypeController.text: ${newOrderController.payTypeController.text}");
                            //                     },
                            //                     isWidget: true,
                            //                     isSelected: newOrderController
                            //                         .paymentOptionList[0]
                            //                             ['value']
                            //                         .value,
                            //                     title:
                            //                         "Use my Balance Credits".tr,
                            //                     image: Constants.IsAr_App ==
                            //                             "false"
                            //                         ? DefaultImages
                            //                             .deductBalanceEN
                            //                         : DefaultImages
                            //                             .deductBalanceAR,
                            //                     widget: UseBalanceWidget(
                            //                         currentBalance.toString()),
                            //                   ),
                            //                 if (Constants.promoPayment == "Y")
                            //                   partnersPaymentOptionDataWidget(
                            //                     onTap: () {
                            //                       newOrderController
                            //                           .paymentOptionList
                            //                           .forEach((opt) {
                            //                         opt['value'].value = false;
                            //                       });
                            //                       newOrderController
                            //                           .paymentOptionList[4]
                            //                               ['value']
                            //                           .value = true;
                            //                       newOrderController
                            //                           .paytype.value = "PRM";
                            //                       newOrderController
                            //                           .payTypeController
                            //                           .text = "PRM";
                            //                       newOrderController
                            //                           .currantIndex.value = 4;
                            //                       newOrderController
                            //                           .paymentOptionList
                            //                           .refresh();
                            //                       print(
                            //                           "newOrderController.payTypeController.text: ${newOrderController.payTypeController.text}");
                            //                     },
                            //                     isWidget: true,
                            //                     isSelected: newOrderController
                            //                         .paymentOptionList[4]
                            //                             ['value']
                            //                         .value,
                            //                     title: "Aldrees promotion".tr,
                            //                     image: Constants.IsAr_App ==
                            //                             "false"
                            //                         ? DefaultImages
                            //                             .aldreesPartnersEN
                            //                         : DefaultImages
                            //                             .aldreesPartnersAR,
                            //                     widget:
                            //                         AldreesPromotionWidget(),
                            //                   ),
                            //                 cashPaymentOptionDataWidget(
                            //                   onTap: () {
                            //                     newOrderController
                            //                         .paymentOptionList
                            //                         .forEach((opt) {
                            //                       opt['value'].value = false;
                            //                     });
                            //                     newOrderController
                            //                         .paymentOptionList[1]
                            //                             ['value']
                            //                         .value = true;
                            //                     newOrderController
                            //                         .paytype.value = "C";
                            //                     newOrderController
                            //                         .payTypeController
                            //                         .text = "C";
                            //                     newOrderController
                            //                         .currantIndex.value = 1;
                            //                     newOrderController
                            //                         .paymentOptionList
                            //                         .refresh();
                            //                     print(
                            //                         "newOrderController.payTypeController.text: ${newOrderController.payTypeController.text}");
                            //                   },
                            //                   isWidget: true,
                            //                   isSelected: newOrderController
                            //                       .paymentOptionList[1]['value']
                            //                       .value,
                            //                   title: "Cash".tr,
                            //                   image:
                            //                       Constants.IsAr_App == "false"
                            //                           ? DefaultImages.cashEN
                            //                           : DefaultImages.cashAR,
                            //                   widget: cashPaymentNoteWidget(),
                            //                 ),
                            //                 if (Constants.STCEnable == "Y")
                            //                   stcPayPaymentOptionDataWidget(
                            //                     onTap: () {
                            //                       newOrderController
                            //                           .paymentOptionList
                            //                           .forEach((opt) {
                            //                         opt['value'].value = false;
                            //                       });
                            //                       newOrderController
                            //                           .paymentOptionList[5]
                            //                               ['value']
                            //                           .value = true;
                            //                       newOrderController
                            //                           .paytype.value = "Y";
                            //                       newOrderController
                            //                           .payTypeController
                            //                           .text = "Y";
                            //                       newOrderController
                            //                           .currantIndex.value = 5;
                            //                       newOrderController
                            //                           .paymentOptionList
                            //                           .refresh();
                            //                       print(
                            //                           "newOrderController.payTypeController.text: ${newOrderController.payTypeController.text}");
                            //                     },
                            //                     isWidget: true,
                            //                     isSelected: newOrderController
                            //                         .paymentOptionList[5]
                            //                             ['value']
                            //                         .value,
                            //                     title: "STC Pay".tr,
                            //                     image: DefaultImages.stcBank,
                            //                     widget: stcPayPaymentWidget(),
                            //                   ),
                            //                 if (Constants.ETrans == "Y")
                            //                   eTransferPaymentOptionDataWidget(
                            //                     onTap: () {
                            //                       newOrderController
                            //                           .paymentOptionList
                            //                           .forEach((opt) {
                            //                         opt['value'].value = false;
                            //                       });
                            //                       newOrderController
                            //                           .paymentOptionList[2]
                            //                               ['value']
                            //                           .value = true;
                            //                       newOrderController
                            //                           .paytype.value = "E";
                            //                       newOrderController
                            //                           .payTypeController
                            //                           .text = "E";
                            //                       newOrderController
                            //                           .currantIndex.value = 2;
                            //                       newOrderController
                            //                           .paymentOptionList
                            //                           .refresh();
                            //                       print(
                            //                           "newOrderController.payTypeController.text: ${newOrderController.payTypeController.text}");
                            //                     },
                            //                     isWidget: true,
                            //                     isSelected: newOrderController
                            //                         .paymentOptionList[2]
                            //                             ['value']
                            //                         .value,
                            //                     title: "E. Transfer".tr,
                            //                     image: Constants.IsAr_App ==
                            //                             "false"
                            //                         ? DefaultImages.eTransferEN
                            //                         : DefaultImages.eTransferAR,
                            //                     widget: eTransferWidget(),
                            //                   ),
                            //                 if (Constants.newETrans == "Y")
                            //                   newETransferPaymentOptionDataWidget(
                            //                     title: "E. Transfer".tr,
                            //                     image: Constants.IsAr_App ==
                            //                             "false"
                            //                         ? DefaultImages.eTransferEN
                            //                         : DefaultImages.eTransferAR,
                            //                     widget: newETransferWidget(),
                            //                   ),
                            //               ],
                            //             ),
                            //           ),
                            //         ),
                            //       ),
                            //   ],
                            // ),

                            /// /////////////////////////////////////////////////////////////////////////////////
                            verticalSpace(8),
                            Container(
                              width: Get.width,
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColor.cLightGrey,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Totals".trr,
                                    style: pBold24.copyWith(fontSize: 22),
                                  ),
                                  verticalSpace(16),
                                  totalsDataWidget(
                                      title: "Unit price".trr,
                                      value: "$amountOrderUnit SAR"),
                                  verticalSpace(16),
                                  totalsDataWidget(
                                      title: "Quantity".trr,
                                      value:
                                          "${newOrderController.quantity.value}"),
                                  // if (amountOrderUnit != 0) verticalSpace(16),
                                  // if (amountOrderUnit != 0)
                                  //   totalsDataWidget(
                                  //       title: "Topup".trr,
                                  //       value:
                                  //           "${roundNumber(amountValue, 2)} SAR"),
                                  verticalSpace(16),
                                  totalsDataWidget(
                                      title: "Total w/o VAT".trr,
                                      //value: "$vatExclusiveAmount SAR"),
                                      value: double.parse(vatExclusiveAmount)
                                              .toStringAsFixed(2) +
                                          " SAR"),
                                  verticalSpace(16),
                                  totalsDataWidget(
                                      title: "VAT".trr,
                                      value:
                                          "${roundNumber(newVat, 2).toStringAsFixed(2)} SAR"),
                                  verticalSpace(16),
                                  horizontalDivider(),
                                  verticalSpace(16),
                                  totalsDataWidget(
                                      title: "Total".trr,
                                      //value: "$totalAmount SAR",
                                      value: totalAmount.toStringAsFixed(2) +
                                          " SAR",
                                      fontSize: 17,
                                      fontColor: AppColor.cDarkFont),
                                  // verticalSpace(16),
                                  //  if(Constants.custIsVerified=='Y')
                                  /// //////////
                                  // CommonButton(
                                  //   title: "PLACE ORDER".trr,
                                  //   btnColor: AppColor.themeOrangeColor,
                                  //   onPressed: () {
                                  //     if (newOrderController.isTag.value ==
                                  //             true &&
                                  //         newOrderController.quantity.value
                                  //                 .toInt() >
                                  //             1000) {
                                  //       showDialog(
                                  //         barrierDismissible: false,
                                  //         context: Get.context!,
                                  //         builder: (context) {
                                  //           return AlertDialog(
                                  //             insetPadding:
                                  //                 const EdgeInsets.all(16),
                                  //             contentPadding:
                                  //                 const EdgeInsets.all(24),
                                  //             shape: RoundedRectangleBorder(
                                  //                 borderRadius:
                                  //                     BorderRadius.circular(
                                  //                         12)),
                                  //             content: Column(
                                  //               mainAxisSize: MainAxisSize.min,
                                  //               children: [
                                  //                 Text(
                                  //                   "The maximum quantity per order 1000 per order",
                                  //                   style: pBold20,
                                  //                   textAlign: TextAlign.center,
                                  //                 ),
                                  //                 verticalSpace(24),
                                  //                 CommonButton(
                                  //                   title: "BACK".trr,
                                  //                   onPressed: () {
                                  //                     Get.back();
                                  //                   },
                                  //                   btnColor: AppColor
                                  //                       .themeOrangeColor,
                                  //                 )
                                  //               ],
                                  //             ),
                                  //           );
                                  //         },
                                  //       );
                                  //     } else if (newOrderController
                                  //                 .phoneNumber !=
                                  //             9 &&
                                  //         newOrderController
                                  //             .paymentOptionList[4]['value']
                                  //             .value) {
                                  //       showDialog(
                                  //         barrierDismissible: false,
                                  //         context: Get.context!,
                                  //         builder: (context) {
                                  //           return AlertDialog(
                                  //             insetPadding:
                                  //                 const EdgeInsets.all(16),
                                  //             contentPadding:
                                  //                 const EdgeInsets.all(24),
                                  //             shape: RoundedRectangleBorder(
                                  //                 borderRadius:
                                  //                     BorderRadius.circular(
                                  //                         12)),
                                  //             content: Column(
                                  //               mainAxisSize: MainAxisSize.min,
                                  //               children: [
                                  //                 Text(
                                  //                   "Enter Valid Mobile Number"
                                  //                       .trr,
                                  //                   style: pBold20,
                                  //                   textAlign: TextAlign.center,
                                  //                 ),
                                  //                 verticalSpace(24),
                                  //                 CommonButton(
                                  //                   title: "BACK".trr,
                                  //                   onPressed: () {
                                  //                     Get.back();
                                  //                   },
                                  //                   btnColor: AppColor
                                  //                       .themeOrangeColor,
                                  //                 )
                                  //               ],
                                  //             ),
                                  //           );
                                  //         },
                                  //       );
                                  //     } else if (newOrderController
                                  //                 .isSmartCard.value ==
                                  //             true &&
                                  //         newOrderController.quantity.value
                                  //                 .toInt() >
                                  //             100) {
                                  //       showDialog(
                                  //         barrierDismissible: false,
                                  //         context: Get.context!,
                                  //         builder: (context) {
                                  //           return AlertDialog(
                                  //             insetPadding:
                                  //                 const EdgeInsets.all(16),
                                  //             contentPadding:
                                  //                 const EdgeInsets.all(24),
                                  //             shape: RoundedRectangleBorder(
                                  //                 borderRadius:
                                  //                     BorderRadius.circular(
                                  //                         12)),
                                  //             content: Column(
                                  //               mainAxisSize: MainAxisSize.min,
                                  //               children: [
                                  //                 Text(
                                  //                   "The maximum quantity per order 100 per order",
                                  //                   style: pBold20,
                                  //                   textAlign: TextAlign.center,
                                  //                 ),
                                  //                 verticalSpace(24),
                                  //                 CommonButton(
                                  //                   title: "BACK".trr,
                                  //                   onPressed: () {
                                  //                     Get.back();
                                  //                   },
                                  //                   btnColor: AppColor
                                  //                       .themeOrangeColor,
                                  //                 )
                                  //               ],
                                  //             ),
                                  //           );
                                  //         },
                                  //       );
                                  //     } else {
                                  //       if (newOrderController.quantity.value >
                                  //               0 ||
                                  //           newOrderController.isTag.value ==
                                  //                   true &&
                                  //               newOrderController
                                  //                       .payTypeController
                                  //                       .text !=
                                  //                   "") {
                                  //         print("============================");
                                  //         print(amountValue.toString());
                                  //         print(newOrderController
                                  //             .currantIndex.value);
                                  //         // if (newOrderController
                                  //         //         .currantIndex.value ==
                                  //         //     0) {
                                  //         //   // BALANCE CREDITS Payment Option
                                  //         //   newOrderController
                                  //         //       .payTypeController.text = "B";
                                  //         // } else if (newOrderController
                                  //         //         .currantIndex.value ==
                                  //         //     1) {
                                  //         //   // CASH Payment Option
                                  //         //   newOrderController
                                  //         //       .payTypeController.text = "C";
                                  //         // } else if (newOrderController
                                  //         //         .currantIndex.value ==
                                  //         //     2) {
                                  //         //   // E.trrANSFER Payment Option
                                  //         //   newOrderController
                                  //         //       .payTypeController.text = "E";
                                  //         // }
                                  //
                                  //         // if (newOrderController.isTag.value =
                                  //         //     true) {
                                  //         //   newOrderController
                                  //         //       .serviceTypeController
                                  //         //       .text = "T";
                                  //         // } else {
                                  //         //   newOrderController
                                  //         //       .serviceTypeController
                                  //         //       .text = "C";
                                  //         // }
                                  //
                                  //         newOrderController
                                  //                 .qtyController.text =
                                  //             newOrderController.quantity.value
                                  //                 .toString();
                                  //
                                  //         newOrderController.topUpAmtController
                                  //             .text = newOrderController
                                  //                     .unitPrice.value ==
                                  //                 "0"
                                  //             ? amountOrderTopup
                                  //             : amountOrderUnit;
                                  //
                                  //         newOrderController
                                  //             .purchaseTotalController
                                  //             .text = amountValue.toString();
                                  //
                                  //         if (newOrderController.currantIndex.value == 0 ||
                                  //             newOrderController
                                  //                     .currantIndex.value ==
                                  //                 1 ||
                                  //             newOrderController
                                  //                     .currantIndex.value ==
                                  //                 2) {
                                  //           newOrderController.orderContinue();
                                  //
                                  //           newOrderController
                                  //                   .amountValue.value =
                                  //               vatExclusiveAmount.toString();
                                  //           newOrderController.vatAmount.value =
                                  //               roundNumber(newVat, 2)
                                  //                   .toString();
                                  //           newOrderController.totalAmount
                                  //               .value = totalAmount.toString();
                                  //         } else if (newOrderController
                                  //                 .currantIndex.value ==
                                  //             4) {
                                  //           newOrderController
                                  //               .createPromotionOrder();
                                  //         } else if (newOrderController
                                  //                 .currantIndex.value ==
                                  //             5) {
                                  //           if (confirmOrderController
                                  //                   .stcPayPhoneNumber.length !=
                                  //               13) {
                                  //             showDialog(
                                  //               barrierDismissible: false,
                                  //               context: Get.context!,
                                  //               builder: (context) {
                                  //                 return AlertDialog(
                                  //                   insetPadding:
                                  //                       const EdgeInsets.all(
                                  //                           16),
                                  //                   contentPadding:
                                  //                       const EdgeInsets.all(
                                  //                           24),
                                  //                   shape:
                                  //                       RoundedRectangleBorder(
                                  //                           borderRadius:
                                  //                               BorderRadius
                                  //                                   .circular(
                                  //                                       12)),
                                  //                   content: Column(
                                  //                     mainAxisSize:
                                  //                         MainAxisSize.min,
                                  //                     children: [
                                  //                       Text(
                                  //                         "Enter Valid Mobile Number"
                                  //                             .trr,
                                  //                         style: pBold20,
                                  //                         textAlign:
                                  //                             TextAlign.center,
                                  //                       ),
                                  //                       verticalSpace(24),
                                  //                       CommonButton(
                                  //                         title: "BACK".trr,
                                  //                         onPressed: () {
                                  //                           Get.back();
                                  //                         },
                                  //                         btnColor: AppColor
                                  //                             .themeOrangeColor,
                                  //                       )
                                  //                     ],
                                  //                   ),
                                  //                 );
                                  //               },
                                  //             );
                                  //           } else {
                                  //             var replacement = "true";
                                  //             var code = custData['CUSTID'];
                                  //             var unitPrice = newOrderController
                                  //                         .unitPrice.value ==
                                  //                     "0"
                                  //                 ? amountOrderTopup
                                  //                 : amountOrderUnit;
                                  //             var qtys = newOrderController
                                  //                 .quantity.value
                                  //                 .toString();
                                  //             var subTotal =
                                  //                 amountValue.toString();
                                  //             var vat = vatAmount.toString();
                                  //             var totalAmnt =
                                  //                 totalAmount.toString();
                                  //             var serviceType =
                                  //                 newOrderController.isSmartCard
                                  //                             .value ==
                                  //                         true
                                  //                     ? 'C'
                                  //                     : 'T';
                                  //             confirmOrderController
                                  //                 .newServiceOrder(
                                  //               serviceType,
                                  //               qtys,
                                  //               newOrderController
                                  //                   .paytype.value,
                                  //               replacement,
                                  //               subTotal,
                                  //               vat,
                                  //               totalAmnt,
                                  //             );
                                  //           }
                                  //         } else {
                                  //           var replacement = "true";
                                  //           var code = custData['CUSTID'];
                                  //           var unitPrice = newOrderController
                                  //                       .unitPrice.value ==
                                  //                   "0"
                                  //               ? amountOrderTopup
                                  //               : amountOrderUnit;
                                  //           var qtys = newOrderController
                                  //               .quantity.value
                                  //               .toString();
                                  //           var subTotal =
                                  //               amountValue.toString();
                                  //           var vat = vatAmount.toString();
                                  //           var totalAmnt =
                                  //               totalAmount.toString();
                                  //           var serviceType = newOrderController
                                  //                       .isSmartCard.value ==
                                  //                   true
                                  //               ? 'C'
                                  //               : 'T';
                                  //
                                  //           if (Constants.AlrajhiMADAEnable ==
                                  //               "Y") {
                                  //             print(
                                  //                 "SERVICETYPE ===============${serviceType}");
                                  //             print(
                                  //                 "QTY =============== ${newOrderController.quantity.value.toString()}");
                                  //             print(
                                  //                 "PAYTYPE ===============${newOrderController.paytype.value}");
                                  //
                                  //             confirmOrderController
                                  //                 .newServiceOrder(
                                  //               serviceType,
                                  //               qtys,
                                  //               newOrderController
                                  //                   .paytype.value,
                                  //               replacement,
                                  //               subTotal,
                                  //               vat,
                                  //               totalAmnt,
                                  //             );
                                  //           } else {
                                  //             confirmOrderController
                                  //                 .prepareMADA(
                                  //                     code,
                                  //                     unitPrice,
                                  //                     qtys,
                                  //                     subTotal,
                                  //                     vat,
                                  //                     totalAmnt,
                                  //                     serviceType,
                                  //                     replacement);
                                  //           }
                                  //         }
                                  //       } else {
                                  //         print(amountValue.toString());
                                  //         // commonToast(
                                  //         //     "Minimum Order amount".trr +
                                  //         //         ": 1 SAR");
                                  //         showDialog(
                                  //           barrierDismissible: false,
                                  //           context: Get.context!,
                                  //           builder: (context) {
                                  //             return AlertDialog(
                                  //               insetPadding:
                                  //                   const EdgeInsets.all(16),
                                  //               contentPadding:
                                  //                   const EdgeInsets.all(24),
                                  //               shape: RoundedRectangleBorder(
                                  //                   borderRadius:
                                  //                       BorderRadius.circular(
                                  //                           12)),
                                  //               content: Column(
                                  //                 mainAxisSize:
                                  //                     MainAxisSize.min,
                                  //                 children: [
                                  //                   Text(
                                  //                     "Minimum Order amount"
                                  //                             .trr +
                                  //                         ": 1 SAR and make sure to select payment option.",
                                  //                     style: pBold20,
                                  //                     textAlign:
                                  //                         TextAlign.center,
                                  //                   ),
                                  //                   verticalSpace(24),
                                  //                   CommonButton(
                                  //                     title: "BACK".trr,
                                  //                     onPressed: () {
                                  //                       Get.back();
                                  //                     },
                                  //                     btnColor: AppColor
                                  //                         .themeOrangeColor,
                                  //                   )
                                  //                 ],
                                  //               ),
                                  //             );
                                  //           },
                                  //         );
                                  //       }
                                  //       print(
                                  //           "newOrderController.currantIndex.value =*=*=*=*=*=*=*=*=*=*=>>>>> ${newOrderController.currantIndex.value}");
                                  //     }
                                  //
                                  //     //}
                                  //   },
                                  // ),
                                ],
                              ),
                            ),
                            verticalSpace(4),
                            // Padding(
                            //   padding: const EdgeInsets.symmetric(
                            //       horizontal: 16, vertical: 4),
                            //   child: Text(
                            //     "* " +
                            //         "Volume discount for Monthly Service Charge may change as Diesel vehicles will be charged SAR10 each.."
                            //             .trr,
                            //     style: pRegular13.copyWith(
                            //         color: AppColor.cDarkGreyFont),
                            //   ),
                            // ),
                            // Padding(
                            //   padding: const EdgeInsets.symmetric(
                            //       horizontal: 16, vertical: 4),
                            //   child: Text(
                            //     "* " +
                            //         "The availability of stock is upon confirmation of payment.."
                            //             .trr,
                            //     style: pRegular13.copyWith(
                            //         color: AppColor.cDarkGreyFont),
                            //   ),
                            // ),
                            // Padding(
                            //   padding: const EdgeInsets.symmetric(
                            //       horizontal: 16, vertical: 4),
                            //   child: Text(
                            //     "* " +
                            //         "Enable popups for this page to view/print Order Reports."
                            //             .trr,
                            //     style: pRegular13.copyWith(
                            //         color: AppColor.cDarkGreyFont),
                            //   ),
                            // ),
                          ],
                        ),
                      )),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: CommonButton(
                  title: "PLACE ORDER".trr,
                  btnColor: AppColor.themeOrangeColor,
                  onPressed: () {
                    if (newOrderController.isTag.value == true &&
                        newOrderController.quantity.value.toInt() > 1000) {
                      showDialog(
                        barrierDismissible: false,
                        context: Get.context!,
                        builder: (context) {
                          return AlertDialog(
                            insetPadding: const EdgeInsets.all(16),
                            contentPadding: const EdgeInsets.all(24),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  "The maximum quantity per order 1000 per order",
                                  style: pBold20,
                                  textAlign: TextAlign.center,
                                ),
                                verticalSpace(24),
                                CommonButton(
                                  title: "BACK".trr,
                                  onPressed: () {
                                    Get.back();
                                  },
                                  btnColor: AppColor.themeOrangeColor,
                                )
                              ],
                            ),
                          );
                        },
                      );
                    } else if (newOrderController.phoneNumber != 9 &&
                        newOrderController
                            .paymentOptionList[4]['value'].value) {
                      showDialog(
                        barrierDismissible: false,
                        context: Get.context!,
                        builder: (context) {
                          return AlertDialog(
                            insetPadding: const EdgeInsets.all(16),
                            contentPadding: const EdgeInsets.all(24),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  "Enter Valid Mobile Number".trr,
                                  style: pBold20,
                                  textAlign: TextAlign.center,
                                ),
                                verticalSpace(24),
                                CommonButton(
                                  title: "BACK".trr,
                                  onPressed: () {
                                    Get.back();
                                  },
                                  btnColor: AppColor.themeOrangeColor,
                                )
                              ],
                            ),
                          );
                        },
                      );
                    } else if (newOrderController.isSmartCard.value == true &&
                        newOrderController.quantity.value.toInt() > 100) {
                      showDialog(
                        barrierDismissible: false,
                        context: Get.context!,
                        builder: (context) {
                          return AlertDialog(
                            insetPadding: const EdgeInsets.all(16),
                            contentPadding: const EdgeInsets.all(24),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  "The maximum quantity per order 100 per order",
                                  style: pBold20,
                                  textAlign: TextAlign.center,
                                ),
                                verticalSpace(24),
                                CommonButton(
                                  title: "BACK".trr,
                                  onPressed: () {
                                    Get.back();
                                  },
                                  btnColor: AppColor.themeOrangeColor,
                                )
                              ],
                            ),
                          );
                        },
                      );
                    } else {
                      if (newOrderController.quantity.value > 0 ||
                          newOrderController.isTag.value == true &&
                              newOrderController.payTypeController.text != "") {
                        print("============================");
                        print(amountValue.toString());
                        print(newOrderController.currantIndex.value);
                        // if (newOrderController
                        //         .currantIndex.value ==
                        //     0) {
                        //   // BALANCE CREDITS Payment Option
                        //   newOrderController
                        //       .payTypeController.text = "B";
                        // } else if (newOrderController
                        //         .currantIndex.value ==
                        //     1) {
                        //   // CASH Payment Option
                        //   newOrderController
                        //       .payTypeController.text = "C";
                        // } else if (newOrderController
                        //         .currantIndex.value ==
                        //     2) {
                        //   // E.trrANSFER Payment Option
                        //   newOrderController
                        //       .payTypeController.text = "E";
                        // }

                        // if (newOrderController.isTag.value =
                        //     true) {
                        //   newOrderController
                        //       .serviceTypeController
                        //       .text = "T";
                        // } else {
                        //   newOrderController
                        //       .serviceTypeController
                        //       .text = "C";
                        // }

                        newOrderController.qtyController.text =
                            newOrderController.quantity.value.toString();

                        newOrderController.topUpAmtController.text =
                            newOrderController.unitPrice.value == "0"
                                ? amountOrderTopup
                                : amountOrderUnit;

                        newOrderController.purchaseTotalController.text =
                            amountValue.toString();

                        if (newOrderController.currantIndex.value == 0 ||
                            newOrderController.currantIndex.value == 1 ||
                            newOrderController.currantIndex.value == 2) {
                          newOrderController.orderContinue();

                          newOrderController.amountValue.value =
                              vatExclusiveAmount.toString();
                          newOrderController.vatAmount.value =
                              roundNumber(newVat, 2).toString();
                          newOrderController.totalAmount.value =
                              totalAmount.toString();
                        } else if (newOrderController.currantIndex.value == 4) {
                          newOrderController.createPromotionOrder();
                        } else if (newOrderController.currantIndex.value == 5) {
                          if (confirmOrderController.stcPayPhoneNumber.length !=
                              13) {
                            showDialog(
                              barrierDismissible: false,
                              context: Get.context!,
                              builder: (context) {
                                return AlertDialog(
                                  insetPadding: const EdgeInsets.all(16),
                                  contentPadding: const EdgeInsets.all(24),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12)),
                                  content: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        "Enter Valid Mobile Number".trr,
                                        style: pBold20,
                                        textAlign: TextAlign.center,
                                      ),
                                      verticalSpace(24),
                                      CommonButton(
                                        title: "BACK".trr,
                                        onPressed: () {
                                          Get.back();
                                        },
                                        btnColor: AppColor.themeOrangeColor,
                                      )
                                    ],
                                  ),
                                );
                              },
                            );
                          } else {
                            var replacement = "true";
                            var code = custData['CUSTID'];
                            var unitPrice =
                                newOrderController.unitPrice.value == "0"
                                    ? amountOrderTopup
                                    : amountOrderUnit;
                            var qtys =
                                newOrderController.quantity.value.toString();
                            var subTotal = amountValue.toString();
                            var vat = vatAmount.toString();
                            var totalAmnt = totalAmount.toString();
                            var serviceType =
                                newOrderController.isSmartCard.value == true
                                    ? 'C'
                                    : 'T';
                            confirmOrderController.newServiceOrder(
                              serviceType,
                              qtys,
                              newOrderController.paytype.value,
                              replacement,
                              subTotal,
                              vat,
                              totalAmnt,
                            );
                          }
                        } else {
                          var replacement = "true";
                          var code = custData['CUSTID'];
                          var unitPrice =
                              newOrderController.unitPrice.value == "0"
                                  ? amountOrderTopup
                                  : amountOrderUnit;
                          var qtys =
                              newOrderController.quantity.value.toString();
                          var subTotal = amountValue.toString();
                          var vat = vatAmount.toString();
                          var totalAmnt = totalAmount.toString();
                          var serviceType =
                              newOrderController.isSmartCard.value == true
                                  ? 'C'
                                  : 'T';

                          if (Constants.AlrajhiMADAEnable == "Y") {
                            print("SERVICETYPE ===============${serviceType}");
                            print(
                                "QTY =============== ${newOrderController.quantity.value.toString()}");
                            print(
                                "PAYTYPE ===============${newOrderController.paytype.value}");

                            confirmOrderController.newServiceOrder(
                              serviceType,
                              qtys,
                              newOrderController.paytype.value,
                              replacement,
                              subTotal,
                              vat,
                              totalAmnt,
                            );
                          } else {
                            confirmOrderController.prepareMADA(
                                code,
                                unitPrice,
                                qtys,
                                subTotal,
                                vat,
                                totalAmnt,
                                serviceType,
                                replacement);
                          }
                        }
                      } else {
                        print(amountValue.toString());
                        // commonToast(
                        //     "Minimum Order amount".trr +
                        //         ": 1 SAR");
                        showDialog(
                          barrierDismissible: false,
                          context: Get.context!,
                          builder: (context) {
                            return AlertDialog(
                              insetPadding: const EdgeInsets.all(16),
                              contentPadding: const EdgeInsets.all(24),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    "Minimum Order amount".trr +
                                        ": 1 SAR and make sure to select payment option.",
                                    style: pBold20,
                                    textAlign: TextAlign.center,
                                  ),
                                  verticalSpace(24),
                                  CommonButton(
                                    title: "BACK".trr,
                                    onPressed: () {
                                      Get.back();
                                    },
                                    btnColor: AppColor.themeOrangeColor,
                                  )
                                ],
                              ),
                            );
                          },
                        );
                      }
                      print(
                          "newOrderController.currantIndex.value =*=*=*=*=*=*=*=*=*=*=>>>>> ${newOrderController.currantIndex.value}");
                    }

                    //}
                  },
                ),
              ),
              verticalSpace(20),
            ],
          ),
        ),
      ),
    );
  }

  Widget newETransferWidget() {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: Column(
        children: [
          if (Constants.virtualAccountBTN == 'Y')
            CommonButton(
              title: "Generate".trr,
              btnColor: AppColor.themeOrangeColor,
              onPressed: () {
                if (confirmOrderController.bankCode.isEmpty) {
                  showDialog(
                    barrierDismissible: false,
                    context: Get.context!,
                    builder: (context) {
                      return AlertDialog(
                        insetPadding: const EdgeInsets.all(16),
                        contentPadding: const EdgeInsets.all(24),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              "Please make sure to Select Bank.".trr,
                              style: pBold20,
                              textAlign: TextAlign.center,
                            ),
                            verticalSpace(24),
                            CommonButton(
                              title: "OK".trr,
                              onPressed: () async {
                                Get.back();
                              },
                              btnColor: AppColor.themeOrangeColor,
                            )
                          ],
                        ),
                      );
                    },
                  );
                } else {
                  confirmOrderController.generateVirtualAccount();
                }
              },
            ),
          if (Constants.virtualAccountBTN == 'Y') verticalSpace(16),
          if (newOrderController.CustIBAN != "")
            CommonTextField(
              controller: newOrderController.CustIBAN,
              labelText: 'IBAN # *'.trr,
              readOnly: true,
              fillColor: AppColor.cBorder,
              filled: true,
            ),
          verticalSpace(16),
          if (Constants.virtualAccountBTN == 'N')
            CommonTextField(
              readOnly: true,
              fillColor: AppColor.cBorder,
              filled: true,
              labelText: 'Select Bank'.trr,
              hintText: Constants.custB2B_BANK == "ARB"
                  ? "(ARB) AL-RAJHI BANK / مصرف الراجحي"
                  : Constants.custB2B_BANK == "NCB"
                      ? "(NCB) AL AHLI BANK / البنك الأهلي التجاري"
                      : "RIYADH BANK / بنك الرياض",
            ),
          // CommonDropdownButtonWidget(
          //   labelText: "Select Bank".trr,
          //   list: newOrderController.itemList,
          //   value: newOrderController.selectedItem.value,
          //   onChanged: (value) {
          //     newOrderController.selectedItem.value = value;
          //   },
          //   filledColor: AppColor.cBorder,
          // ),
          if (Constants.virtualAccountBTN == 'Y')
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Select Bank".trr,
                  style: pRegular12,
                ),
                SizedBox(
                  height: 44,
                  child: DropdownButtonFormField(
                    value: newOrderController.bankList.isNotEmpty
                        ? newOrderController.bankList.first.bankcode
                        : null,
                    items: newOrderController.bankList.map((data) {
                      return DropdownMenuItem(
                        value: data.bankcode,
                        child: Text(
                          data.bankdesc.trr,
                          style: pRegular13,
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    dropdownColor: AppColor.cLightGrey,
                    icon: Padding(
                      padding: const EdgeInsets.only(right: 0),
                      child:
                          assetSvdImageWidget(image: DefaultImages.dropDownIcn),
                    ),
                    onChanged: (value) {
                      print("Select Bank >>>> $value");
                      confirmOrderController.bankCode.value = value.toString();
                    },
                    validator: (value) {
                      return Validator.validateRequired(value.toString());
                    },
                    style: pMedium14.copyWith(
                      color: AppColor.cWhiteFont,
                    ),
                    isExpanded: true,
                    decoration: InputDecoration(
                      fillColor: AppColor.cFilled,
                      filled: true,
                      hintText: "Select Bank".trr,
                      hintStyle: pMedium12.copyWith(color: AppColor.cHintFont),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      contentPadding: EdgeInsets.only(left: 16, right: 16),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      disabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          verticalSpace(8),
          Text(
            "• " +
                "Please transfer to your designated Riyadh/NCB IBAN account."
                    .trr +
                "\n• " +
                "For Riyadh Bank customer kindly use the last 13 digits of the IBAN account."
                    .trr +
                "\n• " +
                "For NCB Bank customer kindly use the last 14 digits of the IBAN account."
                    .trr,
            style: pRegular12,
          )
        ],
      ),
    );
  }

  Widget stcPayPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(7),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: 60,
                    ),
                    // horizontalSpace(5),
                    // Text(
                    //   title,
                    //   style: pRegular13,
                    // ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget stcPayPaymentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //verticalSpace(20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Phone Number".trr,
              style: pRegular14,
            ),
            newOrderController.isSTCPaySubmit.value ||
                    newOrderController.isSTCPayValidate.value
                ? SizedBox()
                : Text(
                    "Not Confirmed".trr,
                    style: pSemiBold12.copyWith(color: AppColor.cRedText),
                  ),
          ],
        ),
        verticalSpace(6),
        Directionality(
          textDirection:
              TextDirection.ltr, // Set your desired text direction here
          child: Container(
            height: 44,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                    color: newOrderController.isSTCPaySubmit.value == true ||
                            newOrderController.isSTCPayValidate.value == true
                        ? AppColor.cBorder
                        : AppColor.cRedText)),
            child: InternationalPhoneNumberInput(
              onInputChanged: (PhoneNumber num) {
                print("====>${num.phoneNumber}");
                print("---->${num.isoCode}");
                newOrderController.isoCode.value = num.isoCode!;
                confirmOrderController.stcPayPhoneNumber =
                    num.phoneNumber.toString();
              },
              onInputValidated: (bool value) {
                print(value);
              },
              cursorColor: AppColor.cHintFont,
              selectorConfig: SelectorConfig(
                selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                leadingPadding: 16,
                setSelectorButtonAsPrefixIcon: true,
              ),
              ignoreBlank: false,
              autoValidateMode: AutovalidateMode.disabled,
              textStyle: pRegular14.copyWith(color: AppColor.cLabel),
              initialValue: PhoneNumber(
                  isoCode: newOrderController.isoCode.value,
                  dialCode: newOrderController.isoCode.value),
              inputBorder: OutlineInputBorder(),
              keyboardAction: TextInputAction.done,
              scrollPadding: EdgeInsets.zero,
              selectorTextStyle:
                  pRegular14.copyWith(color: AppColor.cLabel, fontSize: 14),
              textAlign: TextAlign.start,
              textAlignVertical: TextAlignVertical.center,
              inputDecoration: InputDecoration(
                  contentPadding: EdgeInsets.only(left: 16, bottom: 8),
                  isDense: true,
                  prefixText: "|  ",
                  prefixStyle: TextStyle(fontSize: 30, color: AppColor.cBorder),
                  counterText: '',
                  hintText: " " + 'Please enter here'.trr,
                  counterStyle: TextStyle(fontSize: 0, height: 0),
                  errorStyle: TextStyle(fontSize: 0, height: 0),
                  hintStyle: pRegular14.copyWith(
                    color: AppColor.cHintFont,
                  ),
                  border: InputBorder.none),
              onSaved: (PhoneNumber num) {
                print('On Saved: $num');
                print('On Saved:111:: ${num.dialCode}');
                print('On Saved:2222: ${num.phoneNumber}');
              },
            ),
          ),
        ),
        verticalSpace(8),
        Text(
          "• " + "Complete the order details and confirm payment.".trr,
          style: pRegular14,
        ),
      ],
    );
  }

  Widget eTransferWidget() {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: Column(
        children: [
          verticalSpace(20),
          if (newOrderController.CustIBAN != "")
            CommonTextField(
              controller: newOrderController.CustIBAN,
              labelText: 'IBAN # *'.trr,
              readOnly: true,
            ),
          verticalSpace(16),
          CommonTextField(
            readOnly: true,
            fillColor: AppColor.cBorder,
            filled: true,
            labelText: 'Select Bank'.trr,
            hintText: Constants.custB2B_BANK == "ARB"
                ? "(ARB) AL-RAJHI BANK / مصرف الراجحي"
                : Constants.custB2B_BANK == "NCB"
                    ? "(NCB) AL AHLI BANK / البنك الأهلي التجاري"
                    : "RIYADH BANK / بنك الرياض",
          ),
          // CommonDropdownButtonWidget(
          //   labelText: "Select Bank".trr,
          //   list: newOrderController.itemList,
          //   value: newOrderController.selectedItem.value,
          //   onChanged: (value) {
          //     newOrderController.selectedItem.value = value;
          //   },
          // ),
          verticalSpace(8),
          Text(
            "• " +
                "Select the items and the quantity you require..".trr +
                "\n• " +
                "Enter the TOPUP Amount (if any).".trr +
                "\n• " +
                "Please transfer to your designated Riyadh/NCB IBAN account."
                    .trr +
                "\n• " +
                "For Riyadh Bank customer kindly use the last 13 digits of the IBAN account."
                    .trr +
                "\n• " +
                "For NCB Bank customer kindly use the last 14 digits of the IBAN account."
                    .trr +
                "\n• " +
                "Please make sure that your transfer amount is sufficient against your order."
                    .trr +
                "\n• " +
                "System will automatically allocate the amount to orders which has total payment not exceeding the transferred amount : [ First Ordered, First Paid ] ."
                    .trr +
                "\n• " +
                "Please wait for a call from the technician for installation."
                    .trr,
            style: pRegular12,
          )
        ],
      ),
    );
  }

  Column cashDetailWidget(data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSpace(15),
        Text(
          data,
          style: pSemiBold14,
        ),
        verticalSpace(8),
        Text(
          "• " +
              "Fill up with fuel or pick up the items you need.".trr +
              "\n• " +
              "Go to the cashier.".trr +
              "\n• " +
              'Tell the cashier your user account ID.'.trr,
          style: pRegular14,
        ),
      ],
    );
  }

  Column applePayWidget(data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSpace(15),
        Text(
          data,
          style: pSemiBold14,
        ),
        verticalSpace(8),
        Text(
          "• " +
              "Complete the order details and confirm payment.".trr +
              "\n• " +
              "You will be re-directed to Riyad Bank Online Payment Portal."
                  .trr +
              "\n• " +
              'Please proceed to WAIE branch to claim/activate the cards (for smart cards only).'
                  .trr,
          style: pRegular12,
        ),
      ],
    );
  }

  Column madaDetailWidget(data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSpace(15),
        Text(
          data,
          style: pSemiBold14,
        ),
        verticalSpace(8),
        Text(
          "• " +
              "Complete the order details and confirm payment.".trr +
              "\n• " +
              "You will be re-directed to Riyad Bank Online Payment Portal."
                  .trr +
              "\n• " +
              'Print Receipt.'.trr +
              "\n• " +
              'Please proceed to WAIE branch to claim/activate the cards (for smart cards only).'
                  .trr,
          style: pRegular12,
        ),
      ],
    );
  }

  Widget tagWidget({
    required Function() onTap,
    required String title,
    required String subtitle,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: Get.width,
        decoration: BoxDecoration(
          color: isSelected ? AppColor.themeDarkBlueColor : AppColor.cLightGrey,
          borderRadius: BorderRadius.circular(6),
        ),
        padding: EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "ORDER".trr,
                  style: pSemiBold12.copyWith(
                    color: isSelected
                        ? AppColor.cLightBlueBorder
                        : AppColor.cDarkGreyFont,
                  ),
                ),
                isSelected
                    ? assetSvdImageWidget(
                        image: DefaultImages.whiteCheckCircleIcn)
                    : SizedBox()
              ],
            ),
            Text(
              title,
              style: pBold28.copyWith(
                color: isSelected ? AppColor.cWhiteFont : AppColor.cText,
              ),
            ),
            verticalSpace(21),
            Text(
              subtitle,
              style: pBold12.copyWith(
                  color:
                      isSelected ? AppColor.cLightBlueBorder : AppColor.cFont,
                  fontSize: 10),
            ),
            // Text(
            //   price,
            //   style: pSemiBold17.copyWith(
            //       color: isSelected ? AppColor.cWhiteFont : AppColor.cText),
            // ),
            // verticalSpace(10),
            // Text(
            //   "*" + "Money will be transferred to your balance".trr,
            //   style: pRegular10.copyWith(
            //       color: isSelected
            //           ? AppColor.cLightGreyFont
            //           : AppColor.cDarkGreyFont),
            // )
          ],
        ),
      ),
    );
  }

  Widget MadaPaymentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSpace(15),
        Text(
          "Online Payment Note :".trr,
          style: pSemiBold14,
        ),
        verticalSpace(8),
        Text(
          "• " +
              "Complete the order details and confirm payment.".trr +
              "\n• " +
              "You will be re-directed to Riyad Bank Online Payment Portal."
                  .trr +
              "\n• " +
              'Print Receipt.'.trr +
              "\n• " +
              'Please proceed to WAIE branch to claim/activate the cards (for smart cards only).'
                  .trr,
          style: pRegular12,
        ),
      ],
    );
  }

  /* Widget UseBalanceWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
    );
  }*/
  Widget UseBalanceWidget(String balance) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSpace(15),
        Text(
          "Available Balance :".trr +
              (balance.toString() == "-9999999"
                  ? "Not Available"
                  : (balance.toString() ?? "0.00")),
          style: pSemiBold14,
        ),
        /* verticalSpace(8),
        Text((balance.toString() == "-9999999" ? "Not Available": (balance.toString() ?? "0.00")),
          style: pRegular12,
        ),*/
      ],
    );
  }

  Widget AldreesPromotionWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSpace(20),
        Text(
          "Aldrees promotion".trr,
          style: pRegular14,
        ),
        verticalSpace(8),
        Row(
          children: [
            promotionWidget(
              title: "Al-Rajhi Mokafaa".trr,
              image: DefaultImages.alrajhiIMG,
              isSelected: newOrderController.isMokafa.value,
              onTap: () {
                newOrderController.prmType = "J";
                newOrderController.prmPayType.value = "J";
                newOrderController.isMokafa.value = true;
                newOrderController.isQitaf.value = false;
                newOrderController.isANB.value = false;
                newOrderController.isALINMA.value = false;
              },
            ),
            horizontalSpace(12),
            promotionWidget(
              title: "STC Qitaf".trr,
              image: DefaultImages.stcIMG,
              isSelected: newOrderController.isQitaf.value,
              onTap: () {
                newOrderController.prmPayType.value = "S";
                newOrderController.prmType = "S";
                newOrderController.isMokafa.value = false;
                newOrderController.isQitaf.value = true;
                newOrderController.isANB.value = false;
                newOrderController.isALINMA.value = false;
              },
            ),
            if (Constants.IsAlinmaEnable == 'N') horizontalSpace(12),
            if (Constants.IsAlinmaEnable == 'N')
              promotionWidget(
                title: "ANB".trr,
                image: DefaultImages.anbIMG,
                isSelected: newOrderController.isANB.value,
                onTap: () {
                  newOrderController.prmPayType.value = "N";
                  newOrderController.prmType = "N";
                  newOrderController.isMokafa.value = false;
                  newOrderController.isQitaf.value = false;
                  newOrderController.isANB.value = true;
                  newOrderController.isALINMA.value = false;
                },
              ),
          ],
        ),
        if (Constants.IsAlinmaEnable == 'Y') verticalSpace(12),
        if (Constants.IsAlinmaEnable == 'Y')
          Row(
            children: [
              promotionWidget(
                title: "ANB".trr,
                image: DefaultImages.anbIMG,
                isSelected: newOrderController.isANB.value,
                onTap: () {
                  newOrderController.prmPayType.value = "N";
                  newOrderController.prmType = "N";
                  newOrderController.isMokafa.value = false;
                  newOrderController.isQitaf.value = false;
                  newOrderController.isANB.value = true;
                  newOrderController.isALINMA.value = false;
                },
              ),
              horizontalSpace(12),
              promotionWidget(
                title: "ALINMA".trr,
                image: DefaultImages.alinmaIMG,
                isSelected: newOrderController.isALINMA.value,
                onTap: () {
                  newOrderController.prmPayType.value = "L";
                  newOrderController.prmType = "L";
                  newOrderController.isMokafa.value = false;
                  newOrderController.isQitaf.value = false;
                  newOrderController.isANB.value = false;
                  newOrderController.isALINMA.value = true;
                },
              ),
            ],
          ),
        verticalSpace(16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Phone Number".trr,
              style: pRegular14,
            ),
            newOrderController.isSubmit.value ||
                    newOrderController.isValidate.value
                ? SizedBox()
                : Text(
                    "Not Confirmed".trr,
                    style: pSemiBold12.copyWith(color: AppColor.cRedText),
                  ),
          ],
        ),
        verticalSpace(6),
        Container(
          height: 44,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                  color: newOrderController.isSubmit.value == true ||
                          newOrderController.isValidate.value == true
                      ? AppColor.cBorder
                      : AppColor.cRedText)),
          child: InternationalPhoneNumberInput(
            onInputChanged: (PhoneNumber number) {
              print("====>${number.phoneNumber}");
              print("---->${number.isoCode}");
              // newOrderController.isoCode.value = number.isoCode!;
              newOrderController.phoneNumber = number.phoneNumber.toString();
            },
            onInputValidated: (bool value) {
              print("onInputValidated==========" + value.toString());
            },
            cursorColor: AppColor.cHintFont,
            selectorConfig: SelectorConfig(
              selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
              leadingPadding: 16,
              setSelectorButtonAsPrefixIcon: true,
            ),
            ignoreBlank: false,
            autoValidateMode: AutovalidateMode.disabled,
            textStyle: pRegular14.copyWith(color: AppColor.cLabel),
            /* initialValue: PhoneNumber(
                isoCode: newOrderController.isoCode.value,
                dialCode: newOrderController.isoCode.value),*/
            inputBorder: OutlineInputBorder(),
            keyboardAction: TextInputAction.done,
            scrollPadding: EdgeInsets.zero,
            selectorTextStyle:
                pRegular14.copyWith(color: AppColor.cLabel, fontSize: 14),
            textAlign: TextAlign.start,
            textAlignVertical: TextAlignVertical.center,
            inputDecoration: InputDecoration(
                contentPadding: EdgeInsets.only(left: 16, bottom: 8),
                isDense: true,
                prefixText: "|  ",
                prefixStyle: TextStyle(fontSize: 30, color: AppColor.cBorder),
                counterText: '',
                hintText: " " + 'Please enter here'.trr,
                counterStyle: TextStyle(fontSize: 0, height: 0),
                errorStyle: TextStyle(fontSize: 0, height: 0),
                hintStyle: pRegular14.copyWith(
                  color: AppColor.cHintFont,
                ),
                border: InputBorder.none),
            onSaved: (PhoneNumber number) {},
          ),
        ),
        verticalSpace(8),
        if (newOrderController.isMokafa.value == true)
          Text(
            "• " +
                "TOPUP through Al-Rajhi Mokafaa cannot be refunded for cash."
                    .trr +
                "\n• " +
                "265 Al-Rajhi Mokafaa points is equal to 1 SAR.".trr +
                "\n• " +
                "Decimal point is not allowed in this payment type.".trr,
            style: pRegular12,
          ),
        if (newOrderController.isQitaf.value == true)
          Text(
            "• " +
                "TOPUP through STC Qitaf, cannot be refunded for cash.".trr +
                "\n• " +
                "5 STC Qitaf is equal to 1 SAR.".trr +
                "\n• " +
                "Decimal point is not allowed in this payment type.".trr,
            style: pRegular12,
          ),
        if (newOrderController.isANB.value == true)
          Text(
            "• " +
                "TOPUP through ANB Rewards points cannot be refunded for cash."
                    .trr +
                "\n• " +
                "Decimal point is not allowed in this payment type.".trr,
            style: pRegular12,
          ),
        verticalSpace(15),
        /*newOrderController.isValidate.value
            ? Row(
                children: [
                  assetSvdImageWidget(
                      image: DefaultImages.checkCircleIcn,
                      colorFilter:
                          ColorFilter.mode(AppColor.cGreen, BlendMode.srcIn)),
                  horizontalSpace(12),
                  Text(
                    "CONFIRMED".trr,
                    style: pSemiBold12.copyWith(color: AppColor.cGreen),
                  )
                ],
              )
            */ /*: CommonButton(
          title: newOrderController.isSubmit.value ==true
              ? "Submit".trr
              : "Try again".trr,
          onPressed: () {
            if (newOrderController.isSubmit.value == false) {
              newOrderController.isValidate.value = true;
            } else {
              newOrderController.isSubmit.value = false;
            }
          },
          btnColor: AppColor.themeOrangeColor,
        ),*/ /*
            : CommonButton(
                title: newOrderController.phoneNumber.length > 8
                    ? "Submit".trr
                    : "Try again".trr,
                onPressed: () {
                  */ /* if (newOrderController.isSubmit.value == false) {
              newOrderController.isValidate.value = true;
            } else {
              newOrderController.isSubmit.value = false;
            }*/ /*
                  if (newOrderController.phoneNumber.length > 8) {
                    newOrderController.isValidate.value = true;
                    newOrderController.isSubmit.value == true;
                  } else {
                    newOrderController.isValidate.value = false;
                    newOrderController.isSubmit.value == false;
                  }
                },
                btnColor: AppColor.themeOrangeColor,
              ),*/
      ],
    );
  }

  Widget cashPaymentNoteWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Cash Payment Note".trr,
          style: pSemiBold14,
        ),
        verticalSpace(8),
        Text(
          "• " +
              "Enter the TOPUP Amount and click Confirm..".trr +
              "\n• " +
              "Print your Order from the TOPUP History for reference.".trr +
              "\n• " +
              "Proceed to the Cashier for payment.".trr,
          style: pRegular14,
        ),
      ],
    );
  }

  Widget paymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(8),
                    Text(
                      title,
                      style: pRegular13,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget cashPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(6),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: 45,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget newPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(7),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: 60,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget newETransferPaymentOptionDataWidget({
    required String title,
    required Widget widget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Container(
          width: Get.width,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: AppColor.cBorder)),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
          child: Column(
            children: [
              Row(
                children: [
                  horizontalSpace(5),
                  Image.asset(
                    image,
                    fit: BoxFit.contain,
                    width: Constants.IsAr_App == "false" ? 80 : 110,
                  ),
                ],
              ),
              verticalSpace(20),
              widget,
            ],
          ),
        ),
      );

  Widget eTransferPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(5),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: Constants.IsAr_App == "false" ? 80 : 110,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget balancePaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(5),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: 60,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget partnersPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(5),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: Constants.IsAr_App == "false" ? 120 : 110,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );
}

Widget expiryMonthWidget(
    {void Function()? onTap, required String label, required String month}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        label,
        style: pRegular12,
      ),
      verticalSpace(6),
      GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: AppColor.cBorder)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(month, style: pRegular17),
              assetSvdImageWidget(image: DefaultImages.dropDownIcn)
            ],
          ),
        ),
      ),
    ],
  );
}

Widget promotionWidget({
  required String title,
  required bool isSelected,
  required Function() onTap,
  required String image,
}) {
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
              color: isSelected ? AppColor.cLinkText : AppColor.cBorder,
              width: isSelected ? 1 : 1),
        ),
        padding: EdgeInsets.symmetric(vertical: 12),
        child: Column(
          children: [
            // Center(
            //   child: Text(title,
            //       style: isSelected
            //           ? pSemiBold14
            //           : pRegular14.copyWith(color: AppColor.cDarkGreyFont)),
            // ),
            // verticalSpace(8),
            Image.asset(
              image,
              fit: BoxFit.contain,
              width: 60,
            ),
          ],
        ),
      ),
    ),
  );
}

/*Widget paymentOptionDataWidget({
  required Function() onTap,
  required bool isSelected,
  required String title,
  required String balance,
  required Widget widget,
}) =>
    Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: Get.width,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                  color: isSelected
                      ? AppColor.themeDarkBlueColor
                      : AppColor.cBorder)),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      assetSvdImageWidget(
                          image: isSelected == true
                              ? DefaultImages.checkCircleIcn
                              : DefaultImages.circleIcn),
                      horizontalSpace(8),
                      Text(
                        title,
                        style: pRegular13,
                      ),
                    ],
                  ),
                  balance == "0" || balance == ''
                      ? SizedBox()
                      : isSelected == true
                          ? Text(
                              "Balance".trr + ": $balance",
                              style: pRegular10.copyWith(
                                  fontSize: 11, color: AppColor.cDarkGreyFont),
                            )
                          : SizedBox()
                ],
              ),
              isSelected == true ? widget : SizedBox(),
            ],
          ),
        ),
      ),
    );*/

Widget plusWidget({String? image, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      height: 44,
      width: 44,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: AppColor.cLightBlueBorder)),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Center(
        child: assetSvdImageWidget(image: image),
      ),
    ),
  );
}

Widget totalsDataWidget(
    {required String title,
    required String value,
    double? fontSize,
    Color? fontColor}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(
        title,
        style: pRegular13.copyWith(
            fontSize: fontSize ?? 13, color: fontColor ?? AppColor.cText),
      ),
      Text(
        value,
        // double.parse(value).toStringAsFixed(2),
        style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
      ),
    ],
  );
}
