// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/current_balance_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_current_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_reserved_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/submit_order_refund_service_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/logger_extention/logger_extension.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_cards_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_current_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_reserved_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_tags_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/refund_history_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/submit_topup_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/topup_refund_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../location_screen/location_screen.dart';
import 'refund_search_widget.dart';
import 'submit_refund_screen.dart';

class OrderRefundTopupMenuScreen extends StatelessWidget {
  OrderRefundTopupMenuScreen({super.key});
  RefundsController refundsController = Get.put(RefundsController());
  SubmitOrderRefundServiceController submitOrderRefundServiceController =
      Get.put(SubmitOrderRefundServiceController());
  CurrentBalanceController currentBalanceController =
      Get.put(CurrentBalanceController());
  final tagOrderRefund = GetStorage();

  @override
  Widget build(BuildContext context) {
    logInfo("home screen reserved amount check ${Constants.custResrvBal}");

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 16, right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                          top: 15,
                          bottom: 15,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.backIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cDarkBlueFont, BlendMode.srcIn)),
                            horizontalSpace(10),
                            Text(
                              "Back".trr,
                              style: pRegular18.copyWith(
                                  color: AppColor.cDarkBlueFont, fontSize: 17),
                              textAlign: TextAlign.start,
                            )
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "Topup Refunds".trr,
                          style: pBold20,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Obx(() {
                  return Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      // shrinkWrap: true,
                      // padding: EdgeInsets.all(16),
                      children: [
                        SizedBox(
                          //height: Get.height * 0.06,
                          child: Row(
                            children: [
                              tabWidget(
                                  title: 'Topup'.trr,
                                  onTap: () {
                                    refundsController.isOrderRefund.value =
                                        true;
                                    refundsController.isTopUpRefund.value =
                                        false;
                                    refundsController.isRefundHistory.value =
                                        false;
                                  },
                                  isSelected:
                                      refundsController.isOrderRefund.value),
                              if (Constants.custResrvBal >
                                  0) //comment only to check and fix things 5-11-2025
                                tabWidget(
                                    title: 'Reserved'.trr,
                                    onTap: () {
                                      refundsController.isOrderRefund.value =
                                          false;
                                      refundsController.isTopUpRefund.value =
                                          true;
                                      refundsController.isRefundHistory.value =
                                          false;
                                    },
                                    isSelected:
                                        refundsController.isTopUpRefund.value),
                            ],
                          ),
                        ),
                        refundsController.isOrderRefund.value == true
                            ? OrderRefundCurrentScreen()
                            : OrderRefundReservedScreen(),
                      ],
                    ),
                  );
                }),
              )
            ],
          ),
        ),
//added by fuzail 5-15-2025
        bottomNavigationBar: Obx(() {
          log("Topup count: ${refundsController.selectedTopupList}");
          log("Reserved  count: ${refundsController.selectedReservedList}");

          // Check if there are any selected orders in the respective tab
          return (refundsController.isOrderRefund.value &&
                      refundsController.selectedTopupList.isEmpty) ||
                  (refundsController.isTopUpRefund.value &&
                      refundsController.selectedReservedList.isEmpty)
              ? SizedBox()
              : Container(
                  padding: EdgeInsets.fromLTRB(24, 0, 24, 12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text.rich(
                        TextSpan(
                          text:
                              '${refundsController.isOrderRefund.value ? refundsController.selectedTopupList.length : refundsController.selectedReservedList.length} ' +
                                  "orders".trr +
                                  " ",
                          style: pBold14,
                          children: <TextSpan>[
                            TextSpan(
                                text: 'selected'.trr,
                                style: pRegular14.copyWith(
                                    color: AppColor.cDarkGreyFont)),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          CommonButton(
                            height: 40,
                            width: 130,
                            title: "Refund_selected".trr,
                            horizontalPadding: 8,
                            btnColor: AppColor.themeOrangeColor,
                            onPressed: () {
                              log("refund selected button tapped ${refundsController.isOrderRefund.value}");
                              Get.to(SubmitRefundScreen(
                                routedScreen: "OrderRefundTopupMenuScreen",
                              ));
                            },
                          ),
                          horizontalSpace(24),
                          //added by fuzail 5-20-2025
                          GestureDetector(
                            onTap: () {
                              submitOrderRefundServiceController.clear();

                              // Remove stored keys, logging omitted for brevity
                              tagOrderRefund
                                  .remove('orderRefundTagSerialIDList');
                              tagOrderRefund
                                  .remove('orderRefundTagOrderIDList');
                              tagOrderRefund.remove('tAmt');
                              tagOrderRefund.remove('tVAT');
                              tagOrderRefund.remove('orderType');

                              refundsController
                                  .selectedOrderRefundCurrentSerialIDList
                                  .clear();
                              refundsController
                                  .selectedOrderRefundReservedOrderIDList
                                  .clear();

                                           refundsController
                                  .selectedOrderRefundCurrentOrderIDList.clear();


                              if (refundsController.isOrderRefund.value) {
                                refundsController.selectedTopupList.clear();

                                final currentController =
                                    Get.find<OrderRefundCurrentController>();
                                for (var item in currentController
                                    .refundableCurrentTopups) {
                                  item.isvalue = false;
                                }
                                currentController.refundableCurrentTopups
                                    .refresh();

                                // RESET totals here
                                currentController.topupTotaltAmt = 0.0;
                                currentController.topupTotaltVAT = 0.0;
                              } else {
                                refundsController.selectedReservedList.clear();

                                final reservedController =
                                    Get.find<OrderRefundReservedController>();
                                for (var item in reservedController
                                    .refundableReservedTopups) {
                                  item.isvalue = false;
                                }
                                reservedController.refundableReservedTopups
                                    .refresh();

                                reservedController.topupTotaltAmt = 0.0;
                                reservedController.topupTotaltVAT = 0.0;
                              }
                            },
                            child: assetSvdImageWidget(
                                image: DefaultImages.cancelIcn),
                          )
                        ],
                      )
                    ],
                  ),
                );
        }),

        //added this bottomNavigationBar button by fuzail 5-13-2025
        // bottomNavigationBar: Obx(() {
        //   final topupList = refundsController.selectedTopupList;
        //   final reservedList = refundsController.selectedReservedList;
        //   final isTopupTab = refundsController.isOrderRefund.value;
        //   final isReservedTab = refundsController.isTopUpRefund.value;
        //   print("Topup count: ${refundsController.selectedTopupList.length}");
        //   print(
        //       "Reserved count: ${refundsController.selectedReservedList.length}");

        //   final hasSelection = (isTopupTab && topupList.isNotEmpty) ||
        //       (isReservedTab && reservedList.isNotEmpty);

        //   if (!hasSelection) return SizedBox();

        //   final totalSelected =
        //       isTopupTab ? topupList.length : reservedList.length;

        //   return Container(
        //     padding: EdgeInsets.fromLTRB(24, 0, 24, 12),
        //     child: Row(
        //       crossAxisAlignment: CrossAxisAlignment.center,
        //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //       children: [
        //         Text.rich(
        //           TextSpan(
        //             text: '$totalSelected  ${"orders".trr} ',
        //             style: pBold14,
        //             children: <TextSpan>[
        //               TextSpan(
        //                 text: 'selected'.trr,
        //                 style: pRegular14.copyWith(
        //                   color: AppColor.cDarkGreyFont,
        //                 ),
        //               ),
        //             ],
        //           ),
        //         ),
        //         Row(
        //           children: [
        //             CommonButton(
        //               height: 40,
        //               width: 130,
        //               title: "Refund_selected".trr,
        //               horizontalPadding: 8,
        //               btnColor: AppColor.themeOrangeColor,
        //               onPressed: () {
        //                 Get.to(SubmitTopupScreen());
        //               },
        //             ),
        //             horizontalSpace(24),
        //             GestureDetector(
        //               onTap: () {
        //                 if (isTopupTab) {
        //                   for (var item
        //                       in Get.find<OrderRefundCurrentController>()
        //                           .refundableCurrentTopups) {
        //                     item.isvalue = false;
        //                   }
        //                   refundsController.selectedTopupList.clear();
        //                 } else if (isReservedTab) {
        //                   for (var item
        //                       in Get.find<OrderRefundReservedController>()
        //                           .refundableReservedTopups) {
        //                     item.isvalue = false;
        //                   }
        //                   refundsController.selectedReservedList.clear();
        //                 }
        //               },
        //               child:
        //                   assetSvdImageWidget(image: DefaultImages.cancelIcn),
        //             ),
        //           ],
        //         )
        //       ],
        //     ),
        //   );
        // })

        //commented by fuzail
        // bottomNavigationBar: Obx(() {
        //   return refundsController.selectedOrderRefundList.isEmpty
        //       ? SizedBox()
        //       : refundsController.isOrderRefund.value == true
        //           ? Container(
        //               // padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
        //               padding: EdgeInsets.fromLTRB(24, 0, 24, 12),
        //               child: Row(
        //                 crossAxisAlignment: CrossAxisAlignment.center,
        //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //                 children: [
        //                   Text.rich(
        //                     TextSpan(
        //                       text:
        //                           '${refundsController.selectedOrderRefundList.length}  ' +
        //                               "orders".trr +
        //                               " ",
        //                       style: pBold14,
        //                       children: <TextSpan>[
        //                         TextSpan(
        //                             text: 'selected'.trr,
        //                             style: pRegular14.copyWith(
        //                                 color: AppColor.cDarkGreyFont)),
        //                       ],
        //                     ),
        //                   ),
        //                   Row(
        //                     children: [
        //                       CommonButton(
        //                         height: 40,
        //                         width: 130,
        //                         title: "Refund_selected".trr,
        //                         horizontalPadding: 8,
        //                         btnColor: AppColor.themeOrangeColor,
        //                         onPressed: () {
        //                           Get.to(SubmitTopupScreen());
        //                         },
        //                       ),
        //                       horizontalSpace(24),
        //                       GestureDetector(
        //                           onTap: () {
        //                             refundsController.selectedOrderRefundList
        //                                 .clear();
        //                             for (var element
        //                                 in refundsController.orderRefundList) {
        //                               print("object $element");
        //                               element['value'].value = false;
        //                             }
        //                             refundsController.orderRefundList.refresh();
        //                           },
        //                           child: assetSvdImageWidget(
        //                               image: DefaultImages.cancelIcn))
        //                     ],
        //                   )
        //                 ],
        //               ))
        //           : SizedBox();
        // }),
      ),
    );
  }
}

Widget tabWidget({
  String? title,
  // Color? indicatorColor,
  // Color? fontColor,
  // double? indicatorSize,
  Function()? onTap,
  bool? isSelected,
}) {
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          FittedBox(
            child: Text(
              title!,
              style: pSemiBold17.copyWith(
                  color: isSelected == true
                      ? AppColor.cText
                      : AppColor.cDarkGreyFont),
            ),
          ),
          verticalSpace(8),
          Container(
            // width: Get.width/2,
            height: isSelected == true ? 3 : 1,
            color: isSelected == true
                ? AppColor.themeOrangeColor
                : AppColor.cIndicator,
          )
        ],
      ),
    ),
  );
}
