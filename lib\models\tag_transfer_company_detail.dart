import 'dart:convert';

List<TagTransferCompanyDetailModel> tagTransferCompanyDetailModelFromJson(
        String str) =>
    List<TagTransferCompanyDetailModel>.from(
        json.decode(str).map((x) => TagTransferCompanyDetailModel.fromJson(x)));

String tagTransferCompanyDetailModelToJson(
        List<TagTransferCompanyDetailModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TagTransferCompanyDetailModel {
  String reqId;
  String reqDate;
  String custid;
  String customer;

  TagTransferCompanyDetailModel({
    required this.reqId,
    required this.reqDate,
    required this.custid,
    required this.customer,
  });

  factory TagTransferCompanyDetailModel.fromMap(Map<String, dynamic> map) {
    return TagTransferCompanyDetailModel(
      reqId: map["REQ_ID"] ?? '',
      reqDate: map["REQ_DATE"] ?? '',
      custid: map["CUSTID"] ?? '',
      customer: map["CUSTOMER"] ?? '',
    );
  }

  factory TagTransferCompanyDetailModel.fromJson(Map<String, dynamic> json) =>
      TagTransferCompanyDetailModel(
        reqId: json["REQ_ID"],
        reqDate: json["REQ_DATE"],
        custid: json["CUSTID"],
        customer: json["CUSTOMER"],
      );

  Map<String, dynamic> toJson() => {
        "REQ_ID": reqId,
        "REQ_DATE": reqDate,
        "CUSTID": custid,
        "CUSTOMER": customer,
      };
}
