import 'package:get/get.dart';

class ListviewFilterController extends GetxController{
  RxList filterValueList = [
    {
      'title': 'Service type',
      "isExpand": false.obs,
      "subtitle": ["Service type1", 'Service type2', 'Service type3'],
    }.obs,
    {
      'title': 'Division',
      "isExpand": false.obs,
      "subtitle": ["Division1", 'Division2', 'Division3'],
    }.obs,
    {
      'title': 'Branch',
      "isExpand": false.obs,
      "subtitle": ["Branch1", 'Branch2', 'Branch3'],
    }.obs,
    {
      'title': 'Department',
      "isExpand": false.obs,
      "subtitle": ["Department1", 'Department2', 'Department3'],
    }.obs,
    {
      'title': 'Quota unit',
      "isExpand": false.obs,
      "subtitle": ["Quota unit1", 'Quota unit2', 'Quota unit3'],
    }.obs,
  ].obs;
}