import 'dart:convert';
import 'dart:developer';

import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:waie_app/models/load_data.dart';
import 'package:http/http.dart' as http;

import '../../../../utils/api_endpoints.dart';
import '../../../../utils/constants.dart';

class Data_Load_Controller extends GetxController {
  var client = http.Client();
  RxList<String> salesman = <String>[].obs;
  //RxList<String> serviceknowns = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    print('DataLoadController');
    fetchSalesMan();
  }

  Future<List<Load_Data_Model>> fetchSalesMan() async {
    List<Load_Data_Model> salesmen = [];
    //List<ServiceknownModel> services = [];

    try {
      var salesmanResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "LOOKUPS",
            "searchCode": "SALESMAN",
            "lookupCode": ""
          });

      print("responsegetSalesman===> ${jsonDecode(salesmanResponse.body)}");

      List salesManResult = jsonDecode(salesmanResponse.body);

      for (int i = 0; i < salesManResult.length; i++) {
        Load_Data_Model loadData =
            Load_Data_Model.fromMap(salesManResult[i] as Map<String, dynamic>);
        salesmen.add(loadData);
        //print("loadData ===============${loadData.TYPEDESC}");
      }

      salesman.value = salesmen.map((item) => item.TYPEDESC).toList();
      // serviceknowns.value = services.map((item) => item.text).toList();
      print("SalesMan.value===> $salesman");
      //print("cities.value===> $cities");
      // print("citys===> ${jsonDecode(jsonEncode(cities))}");
      return salesmen;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<List<Load_Data_Model>> fetchAddresData() async {
    List<Load_Data_Model> salesmen = [];
    //List<ServiceknownModel> services = [];

    try {
      var salesmanResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "LOOKUPS",
            "searchCode": "SALESMAN",
            "lookupCode": ""
          });
      /* var serviceResponse = await client.post(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getServiceKnows));*/
      print("responsegetSalesman===> ${jsonDecode(salesmanResponse.body)}");
      // print("responsegetServiceKnows===> ${jsonDecode(serviceResponse.body)}");
      List salesManResult = jsonDecode(salesmanResponse.body);
      // List serviceResult = jsonDecode(serviceResponse.body);

      for (int i = 0; i < salesManResult.length; i++) {
        Load_Data_Model loadData =
            Load_Data_Model.fromMap(salesManResult[i] as Map<String, dynamic>);
        salesmen.add(loadData);
        //print("loadData ===============${loadData.TYPEDESC}");
      }

      /*for (int i = 0; i < serviceResult.length; i++) {
        ServiceknownModel service =
        ServiceknownModel.fromMap(serviceResult[i] as Map<String, dynamic>);
        services.add(service);
      }*/

      salesman.value = salesmen.map((item) => item.TYPEDESC).toList();
      // serviceknowns.value = services.map((item) => item.text).toList();
      print("SalesMan.value===> $salesman");
      //print("cities.value===> $cities");
      // print("citys===> ${jsonDecode(jsonEncode(cities))}");
      return salesmen;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
