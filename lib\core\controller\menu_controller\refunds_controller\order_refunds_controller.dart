// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../../utils/constants.dart';

class OrderRefundsController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  var refundablesServices = <RefundableService>[].obs;

  Future<List<RefundableService>> getAllRefundablesService() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    print("OrderRefundsController custid>>>>>>> $custid");
    print("OrderRefundsController emailid>>>>>>> $emailid");
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getAllRefundablesService),
          body: {
            "custid": "000000054A",
            "IsAR": Constants.IsAr_App,
          });
      List result = jsonDecode(response.body);

      print("OrderRefundsController result >>>>> $result");
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        RefundableService order =
            RefundableService.fromJson(result[i] as Map<String, dynamic>);
        refundablesServices.add(order);
      }
      print("===============================================================");
      print(
          "loadPlacesloadPlacesloadPlacesloadPlaces >>>>> ${jsonDecode(jsonEncode(refundablesServices))}");
      print("===============================================================");

      return refundablesServices;
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('OrderRefundsController');
    await getAllRefundablesService();
    //Navigator.of(Get.context!).pop();
  }
}
