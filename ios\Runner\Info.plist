<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>API_KEY</key>
    <string>AIzaSyCEzkhV-KjrWED6H7wVDuta3SlL8b1RFR8</string>
    <key>GCM_SENDER_ID</key>
    <string>270928518814</string>
    <key>PLIST_VERSION</key>
    <string>1</string>
    <key>BUNDLE_ID</key>
    <string>com.waie.aldrees.mobile</string>
    <key>PROJECT_ID</key>
    <string>aldrees-push-notification</string>
    <key>STORAGE_BUCKET</key>
    <string>aldrees-push-notification.appspot.com</string>
    <key>IS_ADS_ENABLED</key>
    <false/>
    <key>IS_ANALYTICS_ENABLED</key>
    <false/>
    <key>IS_APPINVITE_ENABLED</key>
    <true/>
    <key>IS_GCM_ENABLED</key>
    <true/>
    <key>IS_SIGNIN_ENABLED</key>
    <true/>
    <key>GOOGLE_APP_ID</key>
    <string>1:270928518814:ios:04581d5b69f94da40aa58f</string>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>Aldrees | الدريس</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>com.waie.aldrees.mobile</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>waie_app</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>
   <!-- add by fuzail 
    -->
    <key>GMSApiKey</key>
    <string>$(GOOGLE_MAPS_API_KEY)</string>

    <!-- 2-03-2025
    -->
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>NSAppTransportSecurity</key>
    <dict>
     <!-- added by fuzail
     -->
      <!-- <key>NSAllowsArbitraryLoads</key>
        <true/> -->
        
        <key>NSAllowsArbitraryLoads</key>
        <false/> 
 <!-- 24-02-2025
  -->

    </dict>
    <key>NSCameraUsageDescription</key>
    <string>Used to demonstrate image picker plugin</string>
    <key>NSFaceIDUsageDescription</key>
    <string>This app uses Face ID to provide secure authentication</string>
    <!-- <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>Always and when in use!</string> -->
    <!-- <key>NSLocationAlwaysUsageDescription</key>
    <string>Can I haz location always?</string> -->
    <!-- <key>NSLocationUsageDescription</key>
    <string>Older devices need location.</string> -->
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>We use your location to show the nearest stations and provide location based features while you are using the app.</string>
    <!-- <key>NSMicrophoneUsageDescription</key>
    <string>Used to capture audio for image picker plugin</string> -->
    <key>NSPhotoLibraryUsageDescription</key>
    <string>We use your photo library so you can select photos to upload or share in the app.</string>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
    <key>UIBackgroundModes</key>
    <array>
        <string>fetch</string>
        <string>remote-notification</string>
    </array>
    <key>UNNotificationExtensionUserInteractionEnabled</key>
    <true/>

    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>UIStatusBarStyle</key>
    <string>UIStatusBarStyleDefault</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationPortraitUpsideDown</string>
    </array>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false/>
    <key>io.flutter.embedded_views_preview</key>
    <true/>
    <key>UNUserNotificationCenterDelegate</key>
    <true/>
    <key>FirebaseMessagingAutoInitEnabled</key>
    <true/>
</dict>
</plist>
