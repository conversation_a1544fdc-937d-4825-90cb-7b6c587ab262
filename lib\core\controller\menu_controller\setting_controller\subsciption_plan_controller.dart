import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
//import 'package:waie_app/models/subcriptions.dart';

import '../../../../models/subcriptions.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/constants.dart';

class Subscription_Plan_Controller extends GetxController {
  RxBool basicPlan = false.obs;
  RxBool premiumPlan = false.obs;
  RxList data = [].obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    getPlanStatus();
  }

  getPlanStatus() async {
    await fetchSubsStatus();
  }

  void updatePlansVisibility(bool hasFreePlan, bool hasPremiumPlan) {
    basicPlan.value = hasFreePlan;
    premiumPlan.value = hasPremiumPlan;
  }

  Future<dynamic> fetchSubsStatus() async {
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    var userid = sharedUser.getString('userid');
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.hasSubscrition),
          body: {"custid": userid});
      bool status = jsonDecode(response.body);
      bool hasFreePlan = false;
      bool hasPremiumPlan = false;
      if (status) {
        hasFreePlan = true;
        hasPremiumPlan = false;
      } else {
        hasFreePlan = false;
        hasPremiumPlan = true;
      }

      print("basic Plane========$hasFreePlan");
      print("premium Plane========$hasPremiumPlan");
      updatePlansVisibility(hasFreePlan, hasPremiumPlan);
       Subscription subscription =
           new Subscription(basic: hasFreePlan, premium: hasPremiumPlan);
       await Future.delayed(
           const Duration(seconds: 0), () => data.add(subscription));
    } catch (e) {
      log("Service Fee Error  : $e");
    }
  }

  updateSubsPlan(bool premium) async {
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    var userid = sharedUser.getString('userid');
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.activatePremiumSubs),
          body: {
            "custid": userid,
            "enable": premium.toString(),
            "IsAR": Constants.IsAr_App
          });
      String status = response.body;
      fetchSubsStatus();
      if (status != null) {
        print("Sucessfully Activated");
      } else {
        print(status);
      }
    } catch (e) {
      log("Service Fee Error  : $e");
    }
  }
}
