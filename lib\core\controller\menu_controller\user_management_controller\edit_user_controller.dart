import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class EditUserController extends GetxController {
  var email = ''.obs;
  var name = ''.obs;
  List statusList = ['Active', "In-Active"];
  RxBool isUserDetail = true.obs;
  RxBool isAssignVehicle = false.obs;

  List rcvEmailList = ['Yes'.tr, "No".tr];
  List twoFAList = ['Yes'.tr, "No".tr];
  RxString statusValue = 'Active'.obs;
  RxString rcvEmailValue = 'Yes'.tr.obs;
  RxString twoFAValue = 'Yes'.tr.obs;
  RxBool isPassword = true.obs;
  RxList selectedMenuList = [].obs;
  RxList selectedOrgList = [].obs;
  RxList vehiclePlateList = [TextEditingController()].obs;
  RxBool isMenuAccess = true.obs;
  RxBool isOrgLevel = true.obs;
  RxBool isView = true.obs;
  TextEditingController usernameController = TextEditingController();
  TextEditingController useremailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  // Function to update user data
  void updateUser(String updatedEmail, String updatedName) {
    email.value = updatedEmail;
    name.value = updatedName;
    // Here you would add your logic to update the user data in your database or API
    Get.back(); // Close the EditUserScreen after updating
  }
}