import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class NoTagTransferWidgetScreen extends StatelessWidget {
  NoTagTransferWidgetScreen({Key? key}) : super(key: key);
  VehicleController vehicleController = Get.put(VehicleController());

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          verticalSpace(66),
          assetSvdImageWidget(image: DefaultImages.upcomingImage),
          verticalSpace(32),
          Text(
            "You don't have any Tag Transfer History",
            style: pBold18,
          ),
        ],
      ),
    );
  }
}
