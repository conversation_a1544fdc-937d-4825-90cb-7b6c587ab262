// ignore_for_file: prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import 'cash_balance_screen.dart';

class WireTransferBalanceScreen extends StatelessWidget {
  const WireTransferBalanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              simpleAppBar(
                  title: "Balance top up".trr,
                  onTap: () {
                    Get.back();
                  },
                  backString: "Back".trr),
              Expanded(
                child: SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                            child: Text(
                          "Balance top-up receipt".trr,
                          style: pBold20,
                        )),
                        verticalSpace(32),
                        Center(
                          child: Text(
                            "your order invoice".trr,
                            style: pBold20,
                          ),
                        ),
                        verticalSpace(20),
                        Center(child: Image.asset(DefaultImages.invoiceImg, width: 128, height: 181)),
                        verticalSpace(20),
                        Center(
                          child: Text(
                            "Pay by the invoice at bank office, or share it by email with someone who will pay it for you.".trr,
                            style: pRegular14,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        verticalSpace(20),
                        CommonIconButton(
                          iconData: DefaultImages.whiteDownloadIcn,
                          title: 'Download invoice'.trr,
                          onPressed: () {},
                          btnColor: AppColor.themeOrangeColor,
                        ),
                        verticalSpace(16),
                        Center(
                          child: Text(
                            "or share this receipt by email".trr,
                            style: pRegular14,
                          ),
                        ),
                        verticalSpace(16),
                        Text('Recepient email'.trr, style: pRegular8.copyWith(fontSize: 11)),
                        verticalSpace(6),
                        Container(
                          height: 44,
                          decoration: BoxDecoration(
                              border: Border.all(color: AppColor.cBorder), borderRadius: BorderRadius.circular(6)),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 3,
                                child: TextFormField(
                                  style: pRegular14,
                                  keyboardType: TextInputType.emailAddress,
                                  cursorColor: AppColor.cBorder,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Enter email'.trr,
                                      hintStyle: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                                      contentPadding: EdgeInsets.only(left: 16, right: 16, bottom: 8)),
                                ),
                              ),
                              sendEmailBtn(() {}),
                            ],
                          ),
                        ),
                        verticalSpace(10),
                        Center(child: Text("Invoice was sent to".trr+" <EMAIL> ",style: pRegular14,))
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
        floatingActionButton: purchaseHistoryTextWidget(),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      ),
    );
  }


}
