import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/fleet_structure.dart';

import '../../../../utils/api_endpoints.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/text_style.dart';
import '../../../../view/screen/menu_screen/fleet_structure_screen/branch_fleet_structure_screen.dart';
import '../../../../view/screen/menu_screen/fleet_structure_screen/department_fleet_structure_screen.dart';
import '../../../../view/screen/menu_screen/fleet_structure_screen/fleet_structure_screen.dart';
import '../../../../view/screen/menu_screen/fleet_structure_screen/operation_fleet_structure_screen.dart';
import '../../../../view/widget/common_button.dart';
import '../../../../view/widget/common_space_divider_widget.dart';
import '../../../../view/widget/loading_widget.dart';

class FleetStructureController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  RxList fleetStructureList = [].obs;
  List dummyFleetStructureList = [
    {
      'title': "Logistics Division",
      'status': 'Active',
      'division_title': 'Division',
      'division': '0281935',
      'isSelected': false.obs,
      'branch': {
        'title': "Logistics Canada",
        'status': 'Active',
        'division_title': 'Branch',
        'isBranch': false.obs,
      },
      'department': {
        'title': "Regional",
        'status': 'Active',
        'division_title': 'Department',
        'isDepartment': false.obs,
        'operations': [
          {
            'title': "Quebec",
            'status': 'Active',
            'division_title': 'Operation',
          },
          {
            'title': "Ottawa",
            'status': 'Active',
            'division_title': 'Operation',
          },
          {
            'title': "Toronto",
            'status': 'Active',
            'division_title': 'Operation',
          },
        ]
      }
    },
    {
      'title': "Delivery Division",
      'status': 'Active',
      'division_title': 'Division',
      'division': '0281935',
      'isSelected': false.obs,
      'branch': {
        'title': "Logistics Canada",
        'status': 'Active',
        'division_title': 'Branch',
        'isBranch': false.obs,
      },
      'department': {
        'title': "Regional",
        'status': 'Active',
        'division_title': 'Department',
        'isDepartment': false.obs,
        'operations': [
          {
            'title': "Quebec",
            'status': 'Active',
            'division_title': 'Operation',
          },
          {
            'title': "Ottawa",
            'status': 'Active',
            'division_title': 'Operation',
          },
          {
            'title': "Toronto",
            'status': 'Active',
            'division_title': 'Operation',
          },
        ]
      }
    },
  ];
  RxList subCategoriesList = ["Branch", "Department", "Operation"].obs;
  RxString selectedCategory = ''.obs;
  TextEditingController subCategoryNameController = TextEditingController();
  TextEditingController branchNameController = TextEditingController();
  TextEditingController editDivisionNameController = TextEditingController();
  TextEditingController divisionNameController = TextEditingController();
  TextEditingController groupProcessNameController = TextEditingController();
  TextEditingController editGroupProcessNameController =
      TextEditingController();
  TextEditingController departmentNameController = TextEditingController();
  RxList statusList = ["Active", "Pushed"].obs;
  RxString selectedStatus = ''.obs;
  TextEditingController operationNameController = TextEditingController();
  RxList operationStatusList = ["Active", "Pushed"].obs;
  RxString operationSelectedStatus = ''.obs;
  RxList itemList = [].obs;
  var searchController = TextEditingController().obs;
  RxList divisionList = <FleetStructureModel>[].obs;
  RxList branchList = <FleetStructureModel>[].obs;
  RxList departmentList = <FleetStructureModel>[].obs;
  RxList operationList = <FleetStructureModel>[].obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    searchController.value = TextEditingController();
    getDatas();
  }

  getDatas() async {
    await loadDivision();
  }

  loadDivision() async {
    divisionList.clear();
    print("loadDivision");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadDivision),
          body: {
            "custid": userid,
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      List result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        FleetStructureModel structures =
            FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
        divisionList.add(structures);
      }
      print("===============================================================");

      return divisionList;
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  loadBranch(parentID) async {
    branchList.clear();
    Loader.showLoader();
    print("loadBranch");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadBranch),
          body: {
            "custid": userid,
            "parentid": parentID,
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      // Map<String, dynamic> result = jsonDecode(response.body);
      List result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      /// print("result .>>>>> $result");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        FleetStructureModel structures =
            FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
        branchList.add(structures);
      }
      print("===============================================================");

      if (response.statusCode == 200) {
        Loader.hideLoader();
        Get.to(() => BranchFleetStructureScreen(
              parentID: parentID,
            ));
      }

      return branchList;
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  loadDepartment(parentID) async {
    departmentList.clear();
    Loader.showLoader();
    print("loadDepartment");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadDepartment),
          body: {
            "custid": userid,
            "parentid": parentID,
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      List result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        FleetStructureModel structures =
            FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
        departmentList.add(structures);
      }
      print("===============================================================");

      if (response.statusCode == 200) {
        Loader.hideLoader();
        Get.to(() => DepartmentFleetStructureScreen(
              parentID: parentID,
            ));
      }

      return departmentList;
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  loadOperation(parentID) async {
    operationList.clear();
    Loader.showLoader();
    print("loadOperation");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadOperation),
          body: {
            "custid": userid,
            "parentid": parentID,
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      List result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        FleetStructureModel structures =
            FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
        operationList.add(structures);
      }
      print("===============================================================");

      if (response.statusCode == 200) {
        Loader.hideLoader();
        Get.to(() => OperationFleetStructureScreen(
              parentID: parentID,
            ));
      }

      return operationList;
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  groupingProcessAdd(typeid, parentid) async {
    Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var custData = jsonEncode(custsData.read('custData'));
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.groupingProcessAdd),
          body: {
            "custdata": custData,
            "typeid": typeid,
            "description": groupProcessNameController.text,
            "parentid": parentid,
          });
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["MessageType"]}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["Message"]}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        print(
            "groupingProcessAdd response.statusCode>>>>>>> ${response.statusCode}");
        clear();
        if (jsonDecode(response.body)["MessageType"] == "success") {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        branchList.refresh();
                        departmentList.refresh();
                        operationList.refresh();
                        getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          Loader.hideLoader();
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        branchList.refresh();
                        departmentList.refresh();
                        operationList.refresh();
                        getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
        //await Get.off(() => UserManagementScreen(), preventDuplicates: false);
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  addDivision() async {
    Loader.showLoader();
    var client = http.Client();
    var custData = jsonEncode(custsData.read('custData'));
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.groupingProcessAdd),
          body: {
            "custdata": custData,
            "typeid": "CUSTDIVISION",
            "description": divisionNameController.text,
            "parentid": "NA",
          });
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["MessageType"]}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["Message"]}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        print(
            "addDivisionaddDivision response.statusCode>>>>>>> ${response.statusCode}");
        divisionNameController.clear();
        branchNameController.clear();
        departmentNameController.clear();
        operationNameController.clear();
        if (jsonDecode(response.body)["MessageType"] == "success") {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        await getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          Loader.hideLoader();
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        await getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
        //await Get.off(() => UserManagementScreen(), preventDuplicates: false);
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  groupingProcessEdit(typecode, typeid, typedesc, parentid) async {
    Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var custData = jsonEncode(custsData.read('custData'));
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.groupingProcessEdit),
          body: {
            "custdata": custData,
            "typecode": typecode,
            "typeid": typeid,
            "description": editGroupProcessNameController.text,
            "parentid": parentid,
          });
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["MessageType"]}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["Message"]}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        print(
            "groupingProcessEdit response.statusCode>>>>>>> ${response.statusCode}");
        clear();
        if (jsonDecode(response.body)["MessageType"] == "success") {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        branchList.refresh();
                        departmentList.refresh();
                        operationList.refresh();
                        getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          Loader.hideLoader();
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        branchList.refresh();
                        departmentList.refresh();
                        operationList.refresh();
                        getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
        //await Get.off(() => UserManagementScreen(), preventDuplicates: false);
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  groupingProcessDelete(typecode, typeid, typedesc, parentid) async {
    Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var custData = jsonEncode(custsData.read('custData'));
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.groupingProcessDelete),
          body: {
            "custdata": custData,
            "typecode": typecode,
            "typeid": typeid,
            "description": typedesc,
            "parentid": parentid,
          });
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["MessageType"]}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["Message"]}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        print(
            "groupingProcessEdit response.statusCode>>>>>>> ${response.statusCode}");
        clear();
        if (jsonDecode(response.body)["MessageType"] == "success") {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        branchList.refresh();
                        departmentList.refresh();
                        operationList.refresh();
                        await getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          Loader.hideLoader();
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        branchList.refresh();
                        departmentList.refresh();
                        operationList.refresh();
                        await getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
        //await Get.off(() => UserManagementScreen(), preventDuplicates: false);
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  holdingProcess(typecode, typeid, isHold) async {
    Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var custData = jsonEncode(custsData.read('custData'));
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.holdingProcess),
          body: {
            "custdata": custData,
            "typecode": typecode,
            "typeid": typeid,
            "isHold": isHold,
          });
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["MessageType"]}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["Message"]}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        print(
            "groupingProcessEdit response.statusCode>>>>>>> ${response.statusCode}");
        clear();
        if (jsonDecode(response.body)["MessageType"] == "success") {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        branchList.refresh();
                        departmentList.refresh();
                        operationList.refresh();
                        getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          Loader.hideLoader();
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        divisionList.refresh();
                        branchList.refresh();
                        departmentList.refresh();
                        operationList.refresh();
                        getDatas();
                        await Get.offAll(
                          () => const FleetStructureScreen(),
                        );
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
        //await Get.off(() => UserManagementScreen(), preventDuplicates: false);
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  clear() {
    subCategoryNameController.clear();
    branchNameController.clear();
    editDivisionNameController.clear();
    divisionNameController.clear();
    groupProcessNameController.clear();
    editGroupProcessNameController.clear();
    departmentNameController.clear();
    operationNameController.clear();
  }
}
