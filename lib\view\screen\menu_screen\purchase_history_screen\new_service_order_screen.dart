// ignore_for_file: prefer_const_constructors, must_be_immutable
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/models/newpurchasehistory.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/bulk_actions_widget.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../../core/controller/menu_controller/purchase_history_controller/order_controller.dart';
import '../../../../models/order_history.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/images.dart';
import '../../../widget/common_space_divider_widget.dart';
import '../../../widget/icon_and_image.dart';
import '../company_affiliates_screen/request_history_screen.dart';
import '../user_management_screen/user_management_screen.dart';
import '../../../../models/profile.dart';

class NewServiceOrderScreen extends StatefulWidget {
  const NewServiceOrderScreen({super.key});

  @override
  State<NewServiceOrderScreen> createState() => _NewServiceOrderScreenState();
}

class _NewServiceOrderScreenState extends State<NewServiceOrderScreen> {
  OrderController orderController = Get.put(OrderController());
  int fromData = 1;
  int toData = 10;
  int skpPlus = 0;
  int totalCount = 0;
  bool _isLoading = false;
  final List<NewPurchaseHistory> _data = [];
  RxInt counts = 0.obs;
  RxString tp = '10'.obs;
  RxInt skp = 0.obs;
  int pageNo = 1;

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  Future<void> fetchData() async {
    _data.clear();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    setState(() {
      _isLoading = true;
    });

    // Map body = {
    //   "userid": userid,
    //   "serviceType": "A",
    //   "orderType": "S",
    //   "PageNo": "1",
    //   "orderstatus": "",
    //   "paytype": "",
    //   "IsAr": "false"
    // };

    final response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl +
            ApiEndPoints.authEndpoints.getPurchaseHistory),
        body: {
          "userid": userid,
          "serviceType": "A",
          "orderType": "S",
          "skip": fromData < 10 ? "0" : skp.value.toString(),
          "top": tp.value.toString(),
          "orderstatus": "",
          "paytype": "",
          "IsAr": "false"
        });

    print("fetchData ===============================");
    print(jsonDecode(response.body));
    print("fetchData ===============================");

    if (response.statusCode == 200) {
      _data.clear();
      tp.value = '10';
      skp.value = 0;

      final jsonData = json.decode(response.body);
      inspect(jsonData);
      final dataList = jsonData["OrderHistoryDT"] as List<dynamic>;
      int countList = jsonData["totalCount"] as int;

      final List<NewPurchaseHistory> newData =
          dataList.map((item) => NewPurchaseHistory.fromJson(item)).toList();

      print("newData");
      print("newData =============================== $newData");

      setState(() {
        _data.addAll(newData);
        counts.value = countList;
        _isLoading = false;
        _data.length == 10 ? counts.value : _data.length;

        if (totalCount == 0) {
          totalCount = _data.length == 10 ? counts.value : _data.length;
        } else {
          totalCount = counts.value;
        }
        if (totalCount < toData) {
          toData = totalCount;
        }
      });
    } else {
      setState(() {
        _isLoading = false;
      });
      throw Exception('Failed to fetch data');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.only(right: 16, left: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: const EdgeInsets.only(
                          top: 15,
                          bottom: 16,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.backIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cDarkBlueFont, BlendMode.srcIn)),
                            horizontalSpace(10),
                            Text(
                              "Back".trr,
                              style: pRegular18.copyWith(
                                  color: AppColor.cDarkBlueFont, fontSize: 17),
                              textAlign: TextAlign.start,
                            )
                          ],
                        ),
                      ),
                    ),
                    // horizontalSpace(35),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "Service Order".trr,
                          style: pBold20,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              verticalSpace(10),
              Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: CommonIconButton(
                          title: 'Previous Page'.trr,
                          textStyle: pRegular14.copyWith(
                              color: _isLoading
                                  ? AppColor.cBlack
                                  : AppColor.cWhite,
                              fontSize: 12),
                          height: 30,
                          width: 40,
                          onPressed: () async {
                            if (fromData < 10 || _isLoading) {
                              null;
                            } else {
                              print("Previous Page");
                              setState(() {
                                if (fromData > 10) {
                                  if (toData == totalCount) {
                                    var tmp = toData - fromData;
                                    var tmpTotal = (toData - tmp) - 1;
                                    toData = tmpTotal;
                                    fromData = fromData - 10;
                                  } else {
                                    fromData = fromData - 10;
                                    toData = toData - 10;
                                  }
                                }
                                if (fromData < 10) {
                                  skpPlus = 0;
                                  pageNo = 1;
                                } else {
                                  skpPlus = skpPlus - 10;
                                  skp.value = skpPlus;
                                  pageNo = pageNo - 1;
                                }
                                print("------------------------");
                                print(fromData);
                                print(toData);
                                print(skp.value);
                                print(skpPlus);
                                print("------------------------");
                              });
                              await fetchData();
                              await orderController.fetchOrderHistory(pageNo);
                              // Get.to(() => NewUserScreen());
                            }
                          },
                          btnColor: _isLoading
                              ? AppColor.cLightGrey
                              : AppColor.themeOrangeColor,
                          radius: 6),
                    ),
                  ),
                  Expanded(
                    child: Center(
                      child: Directionality(
                        textDirection: TextDirection.ltr,
                        child: Text(
                          //"Showing $fromData to $toData of $totalCount entries",
                          "$toData ${"of".trr} $totalCount",
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: CommonIconButton(
                          title: 'Next Page'.trr,
                          textStyle: pRegular14.copyWith(
                              color: _isLoading
                                  ? AppColor.cBlack
                                  : AppColor.cWhite,
                              fontSize: 12),
                          height: 30,
                          width: 40,
                          onPressed: () async {
                            if (toData == totalCount ||
                                totalCount < toData ||
                                _isLoading) {
                              null;
                            } else {
                              setState(() {
                                print("Next Page");
                                var tempToSum = toData + 10;
                                if (tempToSum > totalCount) {
                                  var tmp = toData - totalCount;
                                  var tmpTotal = toData - tmp;
                                  toData = tmpTotal;
                                  fromData = fromData + 10;
                                } else {
                                  fromData = fromData + 10;
                                  toData = toData + 10;
                                }
                                skpPlus = skpPlus + 10;
                                skp.value = skpPlus;
                                pageNo = pageNo + 1;
                                print("++++++++++++++++++++++++");
                                print(fromData);
                                print(toData);
                                print(skp.value);
                                print(skpPlus);
                                print("++++++++++++++++++++++++");
                              });
                              await fetchData();
                              await orderController.fetchOrderHistory(pageNo);
                              // Get.to(() => NewUserScreen());
                            }
                          },
                          btnColor: _isLoading
                              ? AppColor.cLightGrey
                              : AppColor.themeOrangeColor,
                          radius: 6),
                    ),
                  ),
                ],
              ),
              verticalSpace(10),
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            right: 16, left: 16, bottom: 16),
                        child: SingleChildScrollView(
                          physics: BouncingScrollPhysics(),
                          child: Column(
                            children: [
                              ListView.builder(
                                shrinkWrap: true,
                                itemCount:
                                    orderController.orderHistoryList.length,
                                physics: NeverScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  var data =
                                      orderController.orderHistoryList[index];
                                  print(
                                      "DATA ORDER HISTORY==========${data.ORDERID}");
                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 8.0),
                                    child: orderWidget(
                                        code: data.ORDERID.toString(),
                                        orderDate: data.ORDERDATE.toString(),
                                        orderType: data.ORDERTYPE.toString().trr,
                                        serviceType:
                                            data.SERVICETYPE.toString().trr,
                                        status: data.STATUS.toString().trr,
                                        paymentMethod:
                                            data.PAYMODE.toString().trr,
                                        //paymentMethod2:data['paymentMethod2'].toString().trr,
                                        paymentMethod2: "",
                                        // price: "",//data['price'],
                                        //  quantity: "",//data['quantity'],
                                        totalValue: data.TOTPAYAMT
                                            .toString(), //data['totalValue'],
                                        color:
                                            data.STATUS.toString() == "Pending"
                                                ? AppColor.cLightGrey
                                                : AppColor.cLightGreen,
                                        textColor:
                                            data.STATUS.toString() == "Pending"
                                                ? AppColor.cDarkGreyFont
                                                : AppColor.cGreen,
                                        cancelPOOnTap: () {
                                          // await QuickAlert.show(
                                          //   context: context,
                                          //   type: QuickAlertType.confirm,
                                          //   text:
                                          //       'You want to cancel this order #${data.ORDERID.toString()}?',
                                          //   confirmBtnText: 'Yes',
                                          //   cancelBtnText: 'No',
                                          //   confirmBtnColor: AppColor.themeOrangeColor,
                                          // );
                                          showDialog(
                                            context: context,
                                            builder: (context) {
                                              return AlertDialog(
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12)),
                                                contentPadding:
                                                    EdgeInsets.all(24),
                                                insetPadding:
                                                    EdgeInsets.all(16),
                                                content: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment.end,
                                                      children: [
                                                        GestureDetector(
                                                            onTap: () {
                                                              Get.back();
                                                            },
                                                            child: assetSvdImageWidget(
                                                                image: DefaultImages
                                                                    .cancelIcn)),
                                                      ],
                                                    ),
                                                    verticalSpace(24),
                                                    Center(
                                                      child: Text(
                                                          "Cancel Order".trr,
                                                          style: pBold20,
                                                          textAlign:
                                                              TextAlign.center),
                                                    ),
                                                    verticalSpace(14),
                                                    Center(
                                                        child: Text(
                                                            "${"Are you sure you want to cancel this order".trr} ${data.ORDERID.toString()}?"
                                                                .trr,
                                                            style: pRegular13,
                                                            textAlign: TextAlign
                                                                .center)),
                                                    verticalSpace(24),
                                                    Row(
                                                      children: [
                                                        Expanded(
                                                          child: CommonButton(
                                                            title: "NO".trr,
                                                            onPressed: () {
                                                              Get.back();
                                                            },
                                                            textColor: AppColor
                                                                .cDarkBlueFont,
                                                            btnColor: AppColor
                                                                .cBackGround,
                                                            bColor: AppColor
                                                                .cDarkBlueFont,
                                                          ),
                                                        ),
                                                        horizontalSpace(16),
                                                        Expanded(
                                                          child: CommonButton(
                                                            title: "Yes".trr,
                                                            onPressed: () {
                                                              //Get.back();
                                                              Loader
                                                                  .showLoader();
                                                              orderController
                                                                  .cancelPurchaseHistory(data
                                                                      .ORDERID
                                                                      .toString());
                                                              //Get.back();
                                                            },
                                                            textColor: AppColor
                                                                .cWhiteFont,
                                                            btnColor: AppColor
                                                                .cRedText,
                                                            bColor: AppColor
                                                                .cTransparent,
                                                            horizontalPadding:
                                                                16,
                                                          ),
                                                        ),
                                                      ],
                                                    )
                                                  ],
                                                ),
                                              );
                                            },
                                          );
                                        }
                                        /*onTap: () {
                                  data.STATUS.toString() == "FOR PAYMENT"
                                      ? showModalBottomSheet(
                                          context: context,
                                          shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.vertical(
                                                  top: Radius.circular(16))),
                                          builder: (context) {
                                            return orderActionWidget(
                                              code: data.ORDERID.toString(),
                                              // isShowCancelOrder:
                                              //     data['status'] == "Claimed",
                                              // printOrder: () {},
                                              // downloadOrder: () {},
                                              cancelOrder: () {
                                                orderController
                                                    .cancelPurchaseHistory(
                                                        data.ORDERID.toString());
                                              },
                                            );
                                          },
                                        )
                                      : SizedBox();
                                }, */
                                        ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget orderWidget(
    {required String code,
    required String status,
    Color? color,
    Color? textColor,
    required String orderType,
    required String serviceType,
    required String orderDate,
    required String paymentMethod,
    required String paymentMethod2,
    // required String price,
    // required String quantity,
    required String totalValue,
    Function()? cancelPOOnTap,
    Function()? onTap}) {
  return Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(4),
      border: Border.all(color: AppColor.cLightGrey),
    ),
    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.cLightBlueBorder),
                        borderRadius: BorderRadius.circular(4)),
                    padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 13, color: AppColor.cDarkBlueText),
                      ),
                    )),
                horizontalSpace(8),
                newWidget(text: status, color: color, textColor: textColor),
              ],
            ),
            // status == "FOR PAYMENT"
            //     ? GestureDetector(
            //         onTap: onTap,
            //         child: assetSvdImageWidget(
            //             image: DefaultImages.verticleMoreIcn))
            //     : SizedBox()
          ],
        ),
        verticalSpace(18),
        userDataRowWidget(
            title: "Order type".trr,
            value: orderType == "S" ? "Services" : "TopUp"),
        verticalSpace(18),
        userDataRowWidget(
            title: "Service type".trr,
            value: serviceType == "T" ? "Tag" : "Card"),
        verticalSpace(12),
        userDataRowWidget(title: "Order date".trr, value: orderDate),
        verticalSpace(12),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                width: 140,
                child: Text(
                  "Payment method".trr,
                  style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                ),
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  text: paymentMethod,
                  style: pRegular14.copyWith(fontSize: 13),
                  children: <TextSpan>[
                    TextSpan(
                        text: paymentMethod2,
                        style: pBold14.copyWith(fontSize: 13)),
                  ],
                ),
                maxLines: 2,
                softWrap: true,
              ),
            ),
          ],
        ),
        /* verticalSpace(12),
        userDataRowWidget(title: "Price".trr, value: price),
        verticalSpace(12),
        userDataRowWidget(title: "Quantity".trr, value: quantity),*/
        verticalSpace(12),
        userTotalValueWidget(title: "Total value".trr, value: totalValue),
        verticalSpace(status == "FOR PAYMENT".trr ? 14 : 0),
        status == "FOR PAYMENT".trr
            ? CommonIconBorderButton(
                iconData: DefaultImages.cancelRequestIcn,
                title: "Cancel Order".trr,
                onPressed: cancelPOOnTap,
              )
            : SizedBox()
      ],
    ),
  );
}
