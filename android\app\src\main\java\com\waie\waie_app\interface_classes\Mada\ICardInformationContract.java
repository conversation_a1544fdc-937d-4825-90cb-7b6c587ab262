package com.waie.aldrees.mobile.interface_classes.Mada;

public interface ICardInformationContract {
    interface View {
        void onLoadedSuccess();// Success

        void onLoadedFailure(final String errorMessage); // Error

        void onUpdateMada(Mada mada);

    }

    interface UserActionsListener {
        void prepareMada(String customerId, String orderType, String serviceType, String qty, String totalAmount);

        void createSession(String amount, String orderId, String transactionId);

        void updateCardInfo(Mada mada, String cardName, String cardNumber, String cardExpiryMonth,
                String cardExpiryYear, String cardCvv);

    }
}
