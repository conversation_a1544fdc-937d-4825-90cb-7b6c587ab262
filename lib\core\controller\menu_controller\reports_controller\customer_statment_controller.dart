import 'dart:convert';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/reports/report_dept.dart';
import 'package:waie_app/models/reports/report_division.dart';
import 'package:waie_app/models/reports/report_places.dart';
import 'package:waie_app/models/reports/report_product.dart';
import 'package:waie_app/models/reports/report_stations.dart';
import 'package:waie_app/models/reports/report_vehicle.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';

import '../../../../models/profile.dart';
import '../../../../models/reports/report_branch.dart';
import '../../../../models/reports/report_driver.dart';
import '../../../../models/reports/report_plates.dart';
import '../../../../models/usemenulist.dart';
import '../../../../utils/api_endpoints.dart';

class CustomerStatmentController extends GetxController {


  bool isCheckedGroupBy=false;

  final TextEditingController datePickerController = TextEditingController();
  RxBool isGroupByPlate = false.obs;

  //Fleet Wise Fuel Usage
  final TextEditingController datePickerFleetFromController =
  TextEditingController();
  final TextEditingController datePickerFleetToController =
  TextEditingController();

  List<String> connectionStatusList = [
    "All",
    "Online",
    "Offline",
  ];
  RxString selectedconnectionStatusList = 'All'.obs;
  RxString selectedServicesList = 'All'.obs;
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  reportRequestSubmit() async{
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    String sorted="";



    String grp="N";
    String shift="N";
    String con="A";
    if(isGroupByPlate.value)
    {
      grp="Y";
    }

    if(selectedconnectionStatusList.value=="All")
    {
      con="2";
    }else if(selectedconnectionStatusList.value=="Online")
    {
      con="0";
    }
    else if(selectedconnectionStatusList.value=="Offline")
    {
      con="1";
    }
    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map dataBody = {};
    dataBody = {
      "CUSTID":userid,
      "PeriodFrom":"01/"+datePickerFleetFromController.text,
      "GroupByPlate":grp,
      "ShiftClose":shift,
      "ConnectionStatus":con,
      "EMAILID":userData.auCust!.emailid!,
      "REPORTTYPE":"CustomerStatement"};

    print("Json Value=================="+dataBody.toString());
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportReqSubmit),
          //  body: jsonEncode(dataBody));
          //body: dataBody,
          body: jsonEncode(dataBody),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          }
      );
      print("REPORT RESPONSE============"+response.body);
      //List result = jsonDecode(response.body);
      commonToast(response.body);
      return "";
    } catch (e) {
      log(e.toString());
      commonToast(e.toString());
      Get.back();
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}