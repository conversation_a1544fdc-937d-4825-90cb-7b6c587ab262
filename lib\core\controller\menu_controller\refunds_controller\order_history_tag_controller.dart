// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/order_history_service.dart';
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../../utils/constants.dart';

class OrderHistoryTagController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  GetStorage usersData = GetStorage('usersData');
  var loadOrderHistoryTagServices = <OrderHistoryServiceModel>[].obs;

  getloadOrderHistoryTagServices() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = jsonEncode(custsData.read('custData'));
    var userData = jsonDecode(jsonEncode(usersData.read('usrData')));
    print("OrderHistoryCardController custid>>>>>>> $custid");
    print("OrderHistoryCardController emailid>>>>>>> $emailid");
    print("OrderHistoryCardController custData>>>>>>> $custData");
    print("OrderHistoryCardController userData>>>>>>> ${userData['USERNAME']}");
    var client = http.Client();
    try {
      if (loadOrderHistoryTagServices.isEmpty) {
        var response = await client.post(
            Uri.parse(ApiEndPoints.baseUrl +
                ApiEndPoints.authEndpoints.loadOrderHistoryService),
            body: {
              "custdata": custData,
              "stype": "T",
              "IsAR": Constants.IsAr_App,
              "pageNo": "1",
              "username": userData['USERNAME'],
            });
        List result = jsonDecode(response.body);
        print(
            "OrderHistoryTagController response >>>>> ${jsonDecode(response.body)}");
        print("OrderHistoryTagController STATUS >>>>> ${response.statusCode}");

        print("OrderHistoryTagController result >>>>> $result");
        print("OrderHistoryTagController COUNT >>>>> ${result.length}");
        print(
            "===============================================================");
        print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
        print(
            "===============================================================");

        for (int i = 0; i < result.length; i++) {
          OrderHistoryServiceModel history = OrderHistoryServiceModel.fromJson(
              result[i] as Map<String, dynamic>);
          loadOrderHistoryTagServices.add(history);
        }
        print(
            "===============================================================");
        print(
            "OrderHistoryTagController >>>>> ${jsonDecode(jsonEncode(loadOrderHistoryTagServices))}");
        print(
            "===============================================================");

        return loadOrderHistoryTagServices;
      }
      print("ERROR: NO DATA");
      return [];
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('OrderHistoryTagController');
    print(jsonDecode(jsonEncode(loadOrderHistoryTagServices)));
    if (loadOrderHistoryTagServices.isEmpty) {
      print("sulod");
      await getloadOrderHistoryTagServices();
    }
    //await getloadOrderHistoryServices();
    //Navigator.of(Get.context!).pop();
  }
}
