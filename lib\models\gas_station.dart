import 'dart:convert';

List<GasStationModel> gasStationModelFromJson(String str) =>
    List<GasStationModel>.from(
        json.decode(str).map((x) => GasStationModel.fromJson(x)));

String gasStationModelToJson(List<GasStationModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class GasStationModel {
  int srNo;
  double stnNo;
  String stationName;
  String placeDesc;
  String areaName;
  String stationStatus;
  String district;
  dynamic stnOwners;
  String stnAllCustFlag;
  String stationAddress;
  String stationFrom;
  String stationTo;
  String isOffice;
  String latitude;
  String longitude;
  String stnMosque;
  String stationCoordinates;
  String placeCode;
  String mainbranch;
  String branchName;
  String products;
  String prods;
  String stnCarService;
  String stnFoodRest;
  String stnCarRent;
  String stnAtm;

  GasStationModel({
    required this.srNo,
    required this.stnNo,
    required this.stationName,
    required this.placeDesc,
    required this.areaName,
    required this.stationStatus,
    required this.district,
    required this.stnOwners,
    required this.stnAllCustFlag,
    required this.stationAddress,
    required this.stationFrom,
    required this.stationTo,
    required this.isOffice,
    required this.latitude,
    required this.longitude,
    required this.stnMosque,
    required this.stationCoordinates,
    required this.placeCode,
    required this.mainbranch,
    required this.branchName,
    required this.products,
    required this.prods,
    required this.stnCarService,
    required this.stnFoodRest,
    required this.stnCarRent,
    required this.stnAtm,
  });

  factory GasStationModel.fromJson(Map<String, dynamic> json) =>
      GasStationModel(
        srNo: json["SR_NO"] ?? "",
        stnNo: json["STN_NO"] ?? 0.00,
        stationName: json["STATION_NAME"] ?? "",
        placeDesc: json["PLACE_DESC"] ?? "",
        areaName: json["AREA_NAME"] ?? "",
        stationStatus: json["STATION_STATUS"] ?? "",
        district: json["DISTRICT"] ?? "",
        stnOwners: json["STN_OWNERS"] ?? "",
        stnAllCustFlag: json["STN_ALL_CUST_FLAG"] ?? "",
        stationAddress: json["STATION_ADDRESS"] ?? "",
        stationFrom: json["STATION_FROM"] ?? "",
        stationTo: json["STATION_TO"] ?? "",
        isOffice: json["IS_OFFICE"] ?? "",
        latitude: json["LATITUDE"] == "" ||
                json["LATITUDE"] == null ||
                json["LATITUDE"] == " "
            ? ""
            : json["LATITUDE"].trim(),
        longitude: json["LONGITUDE"] == "" ||
                json["LONGITUDE"] == null ||
                json["LONGITUDE"] == " "
            ? ""
            : json["LONGITUDE"].trim(),
        stnMosque: json["STN_MOSQUE"] ?? "",
        stationCoordinates: json["STATION_COORDINATES"] ?? "",
        placeCode: json["PLACE_CODE"] ?? "",
        mainbranch: json["MAINBRANCH"] ?? "",
        branchName: json["BRANCH_NAME"] ?? "",
        products: json["PRODUCTS"] ?? "",
        prods: json["PRODS"] ?? "",
        stnCarService: json["STN_CAR_SERVICE"] ?? "",
        stnFoodRest: json["STN_FOOD_REST"] ?? "",
        stnCarRent: json["STN_CAR_RENT"] ?? "",
        stnAtm: json["STN_ATM"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "SR_NO": srNo,
        "STN_NO": stnNo,
        "STATION_NAME": stationName,
        "PLACE_DESC": placeDesc,
        "AREA_NAME": areaName,
        "STATION_STATUS": stationStatus,
        "DISTRICT": district,
        "STN_OWNERS": stnOwners,
        "STN_ALL_CUST_FLAG": stnAllCustFlag,
        "STATION_ADDRESS": stationAddress,
        "STATION_FROM": stationFrom,
        "STATION_TO": stationTo,
        "IS_OFFICE": isOffice,
        "LATITUDE": latitude,
        "LONGITUDE": longitude,
        "STN_MOSQUE": stnMosque,
        "STATION_COORDINATES": stationCoordinates,
        "PLACE_CODE": placeCode,
        "MAINBRANCH": mainbranch,
        "BRANCH_NAME": branchName,
        "PRODUCTS": products,
        "PRODS": prods,
        "STN_CAR_SERVICE": stnCarService,
        "STN_FOOD_REST": stnFoodRest,
        "STN_CAR_RENT": stnCarRent,
        "STN_ATM": stnAtm,
      };
}
