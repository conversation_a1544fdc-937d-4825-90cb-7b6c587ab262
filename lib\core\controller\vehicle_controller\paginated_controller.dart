import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/models/load_data.dart';
import 'package:http/http.dart' as http;
import '../../../../utils/api_endpoints.dart';

class PaginatedController extends GetxController {
  RxInt pgIndex = 0.obs; // track current page index
  RxList serviceTypeList = [
    {
      "TYPECODE": "PL",
      "TYPEDESC": "Plate #".tr,
    },
    {
      "TYPECODE": "CN",
      "TYPEDESC": "Card No".tr,
    },
    {
      "TYPECODE": "DR",
      "TYPEDESC": "Driver".tr,
    },
  ].obs;
  RxList vehicleTypeList = [
    {
      "TYPECODE": "",
      "TYPEDESC": "All Vehicle Type".tr,
    },
    {
      "TYPECODE": "C",
      "TYPEDESC": "CAR".tr,
    },
    {
      "TYPECODE": "T",
      "TYPEDESC": "TRUCK".tr,
    },
    {
      "TYPECODE": "P",
      "TYPEDESC": "PICKUP".tr,
    },
    {
      "TYPECODE": "V",
      "TYPEDESC": "VAN".tr,
    },
    {
      "TYPECODE": "S",
      "TYPEDESC": "SUV".tr,
    },
    {
      "TYPECODE": "M",
      "TYPEDESC": "MOTORCYCLE".tr,
    },
    {
      "TYPECODE": "N",
      "TYPEDESC": "MINIVAN".tr,
    },
    {
      "TYPECODE": "B",
      "TYPEDESC": "BUS".tr,
    },
    {
      "TYPECODE": "R",
      "TYPEDESC": "TRAILER".tr,
    },
    {
      "TYPECODE": "F",
      "TYPEDESC": "FOUR-WHEEL DRIVE".tr,
    },
    {
      "TYPECODE": "J",
      "TYPEDESC": "JEEP".tr,
    },
  ].obs;
  RxList statusTypeList = [
    {
      "TYPECODE": "",
      "TYPEDESC": "All Status".tr,
    },
    {
      "TYPECODE": "N",
      "TYPEDESC": "New".tr,
    },
    {
      "TYPECODE": "I",
      "TYPEDESC": "In-Active".tr,
    },
    {
      "TYPECODE": "T",
      "TYPEDESC": "Terminated".tr,
    },
    {
      "TYPECODE": "A",
      "TYPEDESC": "Active".tr,
    },
  ].obs;
  RxList fuelTypeList = [
    {
      "TYPECODE": "",
      "TYPEDESC": "All Fuel Type".tr,
    },
    {
      "TYPECODE": "PESE0",
      "TYPEDESC": "PETROL95".tr,
    },
    {
      "TYPECODE": "PE910",
      "TYPEDESC": "PETROL91".tr,
    },
    {
      "TYPECODE": "DISE0",
      "TYPEDESC": "DIESEL".tr,
    },
    {
      "TYPECODE": "BIDI0",
      "TYPEDESC": "BIO DIESEL".tr,
    },
  ].obs;
}
