import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:waie_app/models/banklist.dart';
import 'package:http/http.dart' as http;

import '../../../../utils/api_endpoints.dart';
import '../../../../utils/constants.dart';

class BankListController extends GetxController {
  final bankLists = <BankListModel>[].obs;
  TextEditingController bankController = TextEditingController();
  //RxList<String> serviceknowns = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    print('BankListController');
    fetchBanks();
  }

  Future<List<BankListModel>> fetchBanks() async {
    var client = http.Client();
    List<BankListModel> banks = [];
    //List<ServiceknownModel> services = [];

    try {
      var banksResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "LOOKUPS",
            "searchCode": "WAIEBANK",
            "lookupCode": ""
          });

      print("responsegetSalesman===> ${jsonDecode(banksResponse.body)}");

      List bankListResult = jsonDecode(banksResponse.body);

      for (int i = 0; i < bankListResult.length; i++) {
        BankListModel loadData =
            BankListModel.fromMap(bankListResult[i] as Map<String, dynamic>);
        banks.add(loadData);
        print(
            "bankLists ===============${loadData.typecode} ${loadData.typedesc}");
      }

      bankLists.value = banks; //.map((item) => item.typedesc).toList();
      // serviceknowns.value = services.map((item) => item.text).toList();
      print("bankLists.value===> $bankLists");
      return bankLists;
    } catch (e) {
      log(e.toString());
      print("e.toString() ++++++++ ${e.toString()}");
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  submitRefundFormService() {
    print("bankLists.value===> $bankLists");
  }
}
