import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/purchse_history_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class CardSuccessBalanceScreen extends StatelessWidget {
  const CardSuccessBalanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            simpleAppBar(
                title: "Balance top up".trr,
                onTap: () {
                  Get.back();
                },
                backString: "Back".trr),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                  child: Container(
                    height: Get.height-150,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4), border: Border.all(color: AppColor.cBorder)),
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Center(child: assetSvdImageWidget(image: DefaultImages.circleCheckIcn)),
                        verticalSpace(24),
                        Center(
                          child: Text(
                            "Your top-up was successful".trr,
                            style: pBold20,
                          ),
                        ),
                        verticalSpace(24),
                        Center(
                          child: Text(
                            "${"Topped-up balance".trr}:",
                            style: pRegular16,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        verticalSpace(4),
                        Center(
                          child: Text(
                            "5000.00 SAR",
                            style: pRegular16,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        verticalSpace(24),
                        CommonButton(
                            title: "go to home".trr.toUpperCase(),
                            onPressed: () {
                              Get.offAll(DashBoardManagerScreen(currantIndex: 1));
                            },
                            btnColor: AppColor.themeOrangeColor),
                        verticalSpace(24),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 20),
                          child: Text.rich(TextSpan(
                              text: '${'You can view the details of your order in'.trr}\n ',
                              style: pRegular12.copyWith( ),
                              children: [
                                TextSpan(
                                  text: 'Purchase History'.trr,
                                  style: pBold12.copyWith( decoration: TextDecoration.underline),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      Get.to(PurchaseHistoryScreen());
                                    },
                                )
                              ]),textAlign: TextAlign.center),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
