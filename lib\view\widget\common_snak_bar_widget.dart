import 'package:fluttertoast/fluttertoast.dart';
import '../../utils/colors.dart';

Future<void> commonToast(String msg, {ToastGravity? gravity}) {
  return Fluttertoast.showToast(
      msg: msg,
      toastLength: Toast.LENGTH_SHORT,
      gravity: gravity ?? ToastGravity.CENTER,
      timeInSecForIosWeb: 2,
      backgroundColor: AppColor.cOrangeFont,
      textColor: AppColor.cWhite,
      fontSize: 16.0);
}
