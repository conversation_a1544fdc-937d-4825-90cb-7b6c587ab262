import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/prefer.dart';
import 'package:waie_app/utils/regDB.dart';

import '../../../models/profile.dart';
import '../../../utils/api_endpoints.dart';
import '../../../utils/colors.dart';
import '../../../utils/locale_string.dart';
import '../../../utils/text_style.dart';
import '../../../view/screen/auth/forgot_password_screen/reset_password_otp_screen.dart';
import '../../../view/screen/auth/login_with_email_screen.dart';
import '../../../view/widget/common_button.dart';
import '../../../view/widget/common_snak_bar_widget.dart';
import '../../../view/widget/common_space_divider_widget.dart';
import '../../../view/widget/loading_widget.dart';

class ForgotPasswordController extends GetxController {
  var emailController = TextEditingController().obs;
  var mobileController = TextEditingController().obs;
  var useridController = TextEditingController().obs;
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  //TextEditingController useridController = TextEditingController();
  RxBool isPassword = true.obs;
  RxBool isConfirmPass = true.obs;
  RxBool isEmail = true.obs;
  RxBool isPhone = false.obs;
  RxBool isValidate = false.obs;
  RxBool isReSetPassword = false.obs;
  RxString verificationCode = ''.obs;
  RxString userid = ''.obs;
  String pattern =
      r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{12,}$';
  // String pattern = r'(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])';
  RxBool isPatternMatch = false.obs;

  RegisterDatabase db = RegisterDatabase();
  // reference the hive register box
  final _isReg = Hive.box('isReg_DB');

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    emailController.value = TextEditingController();
    mobileController.value = TextEditingController();
    useridController.value = TextEditingController();
  }

  forgotPassword() async {
    Loader.showLoader();
    print("forgotPassword");
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.forgotPassword),
          body: {
            "userid": useridController.value.text,
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      Map<String, dynamic> result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      // print(
      //     "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      // for (int i = 0; i < result.length; i++) {
      //   FleetStructureModel structures =
      //       FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
      //   divisionList.add(structures);
      // }
      // print("===============================================================");
      if (jsonDecode(response.body)["MessageType"] == "success") {
        Loader.hideLoader();
        db.deleteDatabase();
        Get.to(() => ResetPasswordOtpScreen(
            mobileno: jsonDecode(response.body)["mobileno"]));
      } else {
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    jsonDecode(response.body)["message"],
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }

      // return divisionList;
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  clearStorage() {
    GetStorage.init('User');
    GetStorage.init('usersData');
    GetStorage.init('custsData');
    GetStorage.init();
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');
    final tagOrderRefund = GetStorage();
    final topupOrderRefund = GetStorage();
    final vehicle = GetStorage();
    final getOTPDataVerify = GetStorage();
    final isPinblock = GetStorage();
    final isDCBlock = GetStorage();
    Prefs.clear();
    //Get.offAll(() => LoginManagerScreen());
    vehicle.erase();
    tagOrderRefund.erase();
    topupOrderRefund.erase();
    getOTPDataVerify.erase();
    isPinblock.erase();
    isDCBlock.erase();
    userStorage.erase();
    custsData.erase();
    usersData.erase();
  }

  resendCode() async {
    Loader.showLoader();
    print("resendCode");
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.forgotPassword),
          body: {
            "userid": userid.value.toString(),
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      Map<String, dynamic> result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      // print(
      //     "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      // for (int i = 0; i < result.length; i++) {
      //   FleetStructureModel structures =
      //       FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
      //   divisionList.add(structures);
      // }
      // print("===============================================================");
      if (jsonDecode(response.body)["MessageType"] == "error") {
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    jsonDecode(response.body)["message"],
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        Loader.hideLoader();
      }

      // return divisionList;
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  login() async {
    Loader.showLoader();
    clearStorage();
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');

    try {
      var client = http.Client();
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      print("username${userid.value}");
      print("password${verificationCode.value}");
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.signInEmail),
          body: {
            "username": userid.value.toString(),
            "password": verificationCode.value.toString(),
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      Map<String, dynamic> result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      // print(
      //     "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");
      if (response.statusCode == 200) {
        Map cardInfo = json.decode(response.body);
        print("json.decode(response.body) ${json.decode(response.body)}");
        print('1');
        print(cardInfo);
        Profile userData = Profile.fromJson(cardInfo);
        print('2');

        if (userData.returnMessage!.isValidTransaction!) {
          if (userData.returnMessage!.action == "POPUP") {
            loginFailWidget(userData.returnMessage!.message);
            commonToast(userData.returnMessage!.message);
          } else {
            print(userData.auUsers?.custid);
            var custData = jsonDecode(jsonEncode(userData.auCust));
            var usrData = jsonDecode(jsonEncode(userData.auUsers));
            print(custData);
            print(custData['MOBILENO']);
            print(usrData['CUSTID']);
            print(usrData['EMAILID']);
            print(usrData['USERNAME']);
            print("usrData $usrData");
            SharedPreferences sharedUser =
                await SharedPreferences.getInstance();
            // Map decode_options = jsonDecode(jsonString);
            String user = jsonEncode(cardInfo);
            sharedUser.setString('user', user);
            sharedUser.setString('userid', userData.auCust!.custid!);
            userStorage.writeIfNull('custid', userData.auCust!.custid!);
            userStorage.writeIfNull('emailid', userData.auCust!.emailid!);
            userStorage.writeIfNull('accttype', userData.auCust?.accttype);
            usersData.writeIfNull('usrData', usrData);
            custsData.writeIfNull('custData', custData);
            /* Get.to(() => DashBoardManagerScreen(
                currantIndex: 0,
              ));*/
            authController.getUserAccess();
          }
        } else {
          print("Invalid ID Password");
          loginFailWidget("Invalid ID or password");
          commonToast("Invalid ID or password");
        }
        return userData;
      } else {
        print('Login Fail');
        commonToast('Login Fail');
        print(response.statusCode.toString());
        loginFailWidget(response.statusCode.toString());
      }
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }
}
