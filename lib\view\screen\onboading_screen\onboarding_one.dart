import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class OnBoardingOne extends StatelessWidget {
  const OnBoardingOne({super.key});

  @override
  Widget build(BuildContext context) {
    return commonObBoardingWidget(
      image: DefaultImages.companyProfileImage,
      title: "First, set up your company profile.".trr,
      subTitle:
          "We need to verify some information about your company to validate security of your account".trr,
    );
  }
}

class OnBoardingTwo extends StatelessWidget {
  const OnBoardingTwo({super.key});

  @override
  Widget build(BuildContext context) {
    return commonObBoardingWidget(
      image: DefaultImages.smartCardImage,
      title: "Order your tags or smart cards".trr,
      subTitle:
          "Next you'll order your fuel tags or smart cards, so it's fast and easy to fill up.".trr,
    );
  }
}

class OnBoardingThree extends StatelessWidget {
  const OnBoardingThree({super.key});

  @override
  Widget build(BuildContext context) {
    return commonObBoardingWidget(
      image: DefaultImages.yourFleetImage,
      title: "Add vehicles to your fleet".trr,
      subTitle:
          "Once you've ordered your tags or cards, you can start the details of your vehicles.".trr,
    );
  }
}

class OnBoardingFour extends StatelessWidget {
  const OnBoardingFour({super.key});

  @override
  Widget build(BuildContext context) {
    return commonObBoardingWidget(
      image: DefaultImages.tagInstallationImage,
      title: "Book your tag installations".trr,
      subTitle:
          "The next step is to make an appointment for us to install your tags on your vehicles. Choose a date, time and WAIE center, bring your vehicle to us, and we'll do the rest.".trr,
    );
  }
}

class OnBoardingFive extends StatelessWidget {
  const OnBoardingFive({super.key});

  @override
  Widget build(BuildContext context) {
    return commonObBoardingWidget(
      image: DefaultImages.yourBalanceImage,
      title: "Top-up your balance".trr,
      subTitle:
          "Top up your account balance to pay for your vehicle's fueling or purchase new tags or smart cards".trr,
    );
  }
}

Widget commonObBoardingWidget(
    {String? image, String? title, String? subTitle}) {
  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      // assetSvdImageWidget(image: image),
      Image.asset(image!),
      verticalSpace(31),
      Text(
        title!,
        style: pBold20.copyWith(color: AppColor.cDarkBlueFont),
      ),
      verticalSpace(20),
      Padding(
        padding: const EdgeInsets.only(left: 28, right: 27),
        child: Text(
          subTitle!,
          style: pRegular13,
          textAlign: TextAlign.center,
        ),
      ),
    ],
  );
}
