import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_structure_controller.dart';

class GroupProcessHoldWidget extends StatefulWidget {
  String name;
  String typecode;
  String typeid;
  String typedesc;
  String isHold;
  GroupProcessHoldWidget(
      {super.key,
      required this.name,
      required this.typecode,
      required this.typeid,
      required this.typedesc,
      required this.isHold});

  @override
  State<GroupProcessHoldWidget> createState() => _GroupProcessHoldWidgetState();
}

class _GroupProcessHoldWidgetState extends State<GroupProcessHoldWidget> {
  FleetStructureController fleetStructureController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Center(
              child: widget.isHold == "Y"
                  ? Text("Hold ${widget.name}".trr, style: pSemiBold17)
                  : Text("UnHold ${widget.name}".trr, style: pSemiBold17)),
          Padding(
            padding:
                const EdgeInsets.only(top: 24, left: 24, right: 24, bottom: 10),
            child: Center(
                child: widget.isHold == "Y"
                    ? Text(
                        "${"Are you sure you want to hold this".trr} ${widget.typedesc} ${widget.name}?",
                        style: pSemiBold14,
                        textAlign: TextAlign.center,
                      )
                    : Text(
                        "${"Are you sure you want to unhold this".trr} ${widget.typedesc} ${widget.name}?",
                        style: pSemiBold14,
                        textAlign: TextAlign.center,
                      )),
          ),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonBorderButton(
                title: 'No'.trr,
                onPressed: () {
                  Get.back();
                },
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(8),
              Expanded(
                  child: CommonButton(
                title: 'Yes'.trr,
                onPressed: () async {
                  if (widget.typeid == "CUSTDIVISION") {
                    fleetStructureController.divisionList.refresh();
                  } else if (widget.typeid == "CUSTBRANCH") {
                    fleetStructureController.branchList.refresh();
                  } else if (widget.typeid == "CUSTDEPT") {
                    fleetStructureController.departmentList.refresh();
                  } else {
                    fleetStructureController.operationList.refresh();
                  }
                  await fleetStructureController.holdingProcess(
                    widget.typecode,
                    widget.typeid,
                    widget.isHold,
                  );
                },
                btnColor: AppColor.cRedText,
              ))
            ],
          ),
          verticalSpace(16),
        ],
      ),
    );
  }
}
