// ignore_for_file: must_be_immutable

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/user_management_screen.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/core/controller/menu_controller/purchase_history_controller/balancetopup_history_controller.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

class BalanceTopupHistoryScreen extends StatelessWidget {
  BalanceTopupHistoryScreen({super.key});
  BalanceTopUpHistoryController balanceTopUpHistoryController =
      Get.put(BalanceTopUpHistoryController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => balanceTopUpHistoryController.balanceTopUpHistoryList1.isNotEmpty
          ? Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  children: [
                    ListView.builder(
                      shrinkWrap: true,
                      itemCount: balanceTopUpHistoryController
                          .balanceTopUpHistoryList1.length,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = balanceTopUpHistoryController
                            .balanceTopUpHistoryList1[index];
                        //print("History Data==========="+data['code']);
                        print("History Data===========${data.ORDERID}");
                        //print("History Data==========="+data.code);
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: balanceTopUpWidget(
                              code: data.ORDERID.toString(),
                              orderDate: data.ORDERDATE.toString(),
                              status: data.STATUS.toString(),
                              paymentMethod: data.PAYMODE.toString(),
                              paymentMethod2: data.CUSTID.toString(),
                              amount: data.TOTPAYAMT.toString(),
                              color: AppColor.cLightGreen,
                              textColor: AppColor.cGreen,
                              cancelPOOnTap: () {
                                // await QuickAlert.show(
                                //   context: context,
                                //   type: QuickAlertType.confirm,
                                //   text:
                                //       'You want to cancel this order #${data.ORDERID.toString()}?',
                                //   confirmBtnText: 'Yes',
                                //   cancelBtnText: 'No',
                                //   confirmBtnColor: AppColor.themeOrangeColor,
                                // );
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return AlertDialog(
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12)),
                                      contentPadding: const EdgeInsets.all(24),
                                      insetPadding: const EdgeInsets.all(16),
                                      content: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              GestureDetector(
                                                  onTap: () {
                                                    Get.back();
                                                  },
                                                  child: assetSvdImageWidget(
                                                      image: DefaultImages
                                                          .cancelIcn)),
                                            ],
                                          ),
                                          verticalSpace(24),
                                          Center(
                                            child: Text("Cancel Order".trr,
                                                style: pBold20,
                                                textAlign: TextAlign.center),
                                          ),
                                          verticalSpace(14),
                                          Center(
                                              child: Text(
                                                  "${"Are you sure you want to cancel this order".trr} ${data.ORDERID.toString()}?"
                                                      .trr,
                                                  style: pRegular13,
                                                  textAlign: TextAlign.center)),
                                          verticalSpace(24),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: CommonButton(
                                                  title: "NO".trr,
                                                  onPressed: () {
                                                    Get.back();
                                                  },
                                                  textColor:
                                                      AppColor.cDarkBlueFont,
                                                  btnColor:
                                                      AppColor.cBackGround,
                                                  bColor:
                                                      AppColor.cDarkBlueFont,
                                                ),
                                              ),
                                              horizontalSpace(16),
                                              Expanded(
                                                child: CommonButton(
                                                  title: "Yes".trr,
                                                  onPressed: () {
                                                    //Get.back();
                                                    Loader.showLoader();
                                                    balanceTopUpHistoryController
                                                        .cancelPurchaseHistory(
                                                            data.ORDERID
                                                                .toString());
                                                    //Get.back();
                                                  },
                                                  textColor:
                                                      AppColor.cWhiteFont,
                                                  btnColor: AppColor.cRedText,
                                                  bColor: AppColor.cTransparent,
                                                  horizontalPadding: 16,
                                                ),
                                              ),
                                            ],
                                          )
                                        ],
                                      ),
                                    );
                                  },
                                );
                              }
                              /*onTap: () {
                                showModalBottomSheet(
                                  context: context,
                                  shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                                  builder: (context) {
                                    return orderActionWidget(
                                      code: data.CUSTID.toString(),
                                      isShowCancelOrder: data.CUSTID.toString() == "Claimed",
                                      printOrder: () {},
                                      downloadOrder: () {},
                                      cancelOrder: () {},
                                    );
                                  },
                                );
                              },*/
                              ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            )
          : const Center(child: CircularProgressIndicator()),
    );
  }
}

Widget balanceTopUpWidget({
  required String code,
  required String status,
  Color? color,
  Color? textColor,
  required String orderDate,
  required String paymentMethod,
  required String paymentMethod2,
  required String amount,
  Function()? onTap,
  Function()? cancelPOOnTap,
}) {
  return Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(4),
      border: Border.all(color: AppColor.cLightGrey),
    ),
    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.cLightBlueBorder),
                        borderRadius: BorderRadius.circular(4)),
                    padding:
                        const EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 13, color: AppColor.cDarkBlueText),
                      ),
                    )),
                horizontalSpace(8),
                newWidget(text: status, color: color, textColor: textColor),
              ],
            ),
            // GestureDetector(onTap: onTap, child: assetSvdImageWidget(image: DefaultImages.verticleMoreIcn))
          ],
        ),
        verticalSpace(18),
        userDataRowWidget(title: "Order date".trr, value: orderDate),
        verticalSpace(12),
        Row(
          children: [
            SizedBox(
              width: 140,
              child: Text(
                "Payment method".trr,
                style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  text: paymentMethod,
                  style: pRegular14.copyWith(fontSize: 13),
                  /*children: <TextSpan>[
                    TextSpan(
                        text: paymentMethod2,
                        style: pBold14.copyWith(fontSize: 13)),
                  ],*/
                ),
                maxLines: 2,
                softWrap: true,
              ),
            ),
          ],
        ),
        verticalSpace(12),
        userDataRowWidget(title: "Amount".trr, value: amount),
        verticalSpace(status == "FOR PAYMENT" ? 14 : 0),
        status == "FOR PAYMENT"
            ? CommonIconBorderButton(
                iconData: DefaultImages.cancelRequestIcn,
                title: "Cancel Order".trr,
                onPressed: cancelPOOnTap,
              )
            : const SizedBox()
      ],
    ),
  );
}
