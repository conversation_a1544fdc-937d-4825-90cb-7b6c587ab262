// ignore_for_file: must_be_immutable

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/new_user_screen.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/text_style.dart';
import '../../../widget/common_appbar_widget.dart';
import '../../../widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/menu_controller/setting_controller/email_notification_controller.dart';

class EmailNotificationScreen extends StatelessWidget {
  EmailNotificationScreen({super.key});

  EmailNotificationController emailNotificationController = Get.put(EmailNotificationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            simpleMyAppBar(
                title: "Email notifications".trr,
                onTap: () {
                  Get.back();
                },
                backString: "Back".trr),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 16),
                  child: Obx(() {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "We may send you important notifications about your account outside of your notification bar".trr,
                          style: pRegular14,
                        ),
                        verticalSpace(24),
                        Text(
                          "Station Status Notifications".trr,
                          style: pSemiBold16,
                        ),
                        verticalSpace(14),
                        tagSwitchWidget(
                          value: emailNotificationController.notiForOpenStn.value,
                          onChanged: (bool value) {
                            emailNotificationController.notiForOpenStn.value = value;
                            emailNotificationController.updateCustNoty();
                          },
                          label: 'Notification For Open Station'.trr,
                          onTap: () {
                            emailNotificationController.notiForOpenStn.value =
                            !emailNotificationController.notiForOpenStn.value;
                            emailNotificationController.updateCustNoty();
                          },
                        ),
                       // verticalSpace(14),
                        tagSwitchWidget(
                          value: emailNotificationController.notiForTempClosedStn.value,
                          onChanged: (bool value) {
                            emailNotificationController.notiForTempClosedStn.value = value;
                            emailNotificationController.updateCustNoty();
                          },
                          label: 'Notification For Temporary Closed Station'.trr,
                          onTap: () {
                            emailNotificationController.notiForTempClosedStn.value =
                                !emailNotificationController.notiForTempClosedStn.value;
                            emailNotificationController.updateCustNoty();
                          },
                        ),
                        tagSwitchWidget(
                          value: emailNotificationController.notiForClosedStn.value,
                          onChanged: (bool value) {
                            emailNotificationController.notiForClosedStn.value = value;
                            emailNotificationController.updateCustNoty();
                          },
                          label: 'Notification For Closed Station'.trr,
                          onTap: () {
                            emailNotificationController.notiForClosedStn.value =
                                !emailNotificationController.notiForClosedStn.value;
                            emailNotificationController.updateCustNoty();
                          },
                        ),
                        tagSwitchWidget(
                          value: emailNotificationController.notiForTagInstallation.value,
                          onChanged: (bool value) {
                            emailNotificationController.notiForTagInstallation.value = value;
                            emailNotificationController.updateCustNoty();
                          },
                          label: 'Notification For Tag Installation Schedule'.trr,
                          onTap: () {
                            emailNotificationController.notiForTagInstallation.value =
                                !emailNotificationController.notiForTagInstallation.value;
                            emailNotificationController.updateCustNoty();
                          },
                        ),
                      ],
                    );
                  }),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  tagSwitchWidget({
    required bool value,
    required void Function(bool)? onChanged,
    required String label,
    required Function() onTap,
  }) {
    return ListTile(
      title: Text(label, style: pRegular14),
      leading: CustomSwitch(
        value: value,
        onChanged: onChanged!,
      ),
      horizontalTitleGap: 8,
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }
}
