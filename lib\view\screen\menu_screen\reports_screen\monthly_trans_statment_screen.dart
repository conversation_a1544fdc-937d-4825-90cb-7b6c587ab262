// ignore_for_file: prefer_const_constructors

import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import 'package:waie_app/core/controller/menu_controller/reports_controller/reports_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/reports_controller/monthly_trans_statment_controller.dart';
import '../../../widget/common_appbar_widget.dart';

class MonthlyTransStatmentScreen extends StatefulWidget {
  final String title;
  const MonthlyTransStatmentScreen({super.key, required this.title});

  @override
  State<MonthlyTransStatmentScreen> createState() =>
      _MonthlyTransStatmentScreen();
}

class _MonthlyTransStatmentScreen extends State<MonthlyTransStatmentScreen> {
  MonthlyTransStatmentController reportController =
      Get.put((MonthlyTransStatmentController()));
  bool isCheckedGroupBy = false;
  bool isCheckedSummary = false;
  pickDate() async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary:
                  AppColor.themeOrangeColor, // Picker header background color
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor:
                    AppColor.themeDarkBlueColor, // Button text color
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      print(pickedDate); // Outputs the full date (year, month, day)
      // Format the date to only show the month and year
      String formattedDate = DateFormat('MM/yyyy').format(pickedDate);
      print(formattedDate); // Outputs the formatted date (month/year)

      reportController.datePickerFleetFromController.text =
          formattedDate; // Set the formatted date
    } else {
      print("Date is not selected");
    }
  }

  /* pickMonthYear(BuildContext context) async {
    final DateTime? pickedDate = await showMonthPicker(
      context: context,
      firstDate: DateTime(DateTime.now().year - 1, 5),
      lastDate: DateTime(DateTime.now().year + 1, 9),
      initialDate: DateTime.now(),
      locale: Locale("en"),
    );
    if (pickedDate != null) {
      String formattedDate = DateFormat('MM/yyyy').format(pickedDate);
      print(formattedDate); // Outputs the month and year

      reportController.datePickerFleetFromController.text = formattedDate;
    } else {
      print("Month/Year is not selected");
    }
  }*/

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  simpleMyAppBar(
                      title: "".trr,
                      backString: "Back".trr,
                      onTap: () {
                        Get.back();
                      },
                      backColor: AppColor.cBlueFont),
                ],
              ),
            ),
            Center(
              child: Text(
                widget.title,
                style: pBold20,
                textAlign: TextAlign.center,
              ),
            ),
            Gap(16),
            Expanded(
              child: ListView(
                padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                children: [
                  CommonTextField(
                    controller: reportController.datePickerFleetFromController,
                    labelText: '${"Period".trr}*',
                    suffix:
                        assetSvdImageWidget(image: DefaultImages.calendarIcn),
                    fillColor: AppColor.cWhite,
                    filled: true,
                    readOnly: true,
                    onTap: pickDate,
                  ),
                  Gap(16),
                  CommonDropdownButtonWidget(
                    hint: '',
                    labelText: 'Connection Status'.trr,
                    list: reportController
                        .connectionStatusList, // Your adjusted list
                    value: reportController.selectedconnectionStatusList
                        .value, // Currently selected value
                    onChanged: (value) {
                      if (value != null) {
                        // Assuming onChanged provides a non-null value
                        reportController.selectedconnectionStatusList.value =
                            value; // Update the selected value
                      }
                    },
                    fontColor: AppColor.cDarkGreyFont,
                    filledColor: AppColor.cFilled,
                  ),
                  Gap(16),
                  Row(
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: Checkbox(
                          value: reportController.isCheckedGroupBy,
                          onChanged: (bool? value) {
                            // This is where we update the state of isChecked!
                            setState(() {
                              reportController.isCheckedGroupBy = value!;
                            });
                          },
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4)),
                          activeColor: AppColor.themeBlueColor,
                        ),
                      ),
                      horizontalSpace(10),
                      Text("Group By Plate".trr)
                    ],
                  ),
                  Gap(16),
                  Row(
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: Checkbox(
                          value: reportController.isCheckedSummary,
                          onChanged: (bool? value) {
                            // This is where we update the state of isChecked!
                            setState(() {
                              reportController.isCheckedSummary = value!;
                            });
                          },
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4)),
                          activeColor: AppColor.themeBlueColor,
                        ),
                      ),
                      horizontalSpace(10),
                      Text("Summary".trr)
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: CommonButton(
          title: 'SUBMIT'.trr,
          onPressed: () {
            reportController.reportRequestSubmit();
          },
          textColor: AppColor.cWhiteFont,
          btnColor: AppColor.themeOrangeColor,
        ),
      ),
    );
  }
}
