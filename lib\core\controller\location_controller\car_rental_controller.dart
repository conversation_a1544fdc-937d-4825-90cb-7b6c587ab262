// ignore_for_file: prefer_const_constructors

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:waie_app/models/car_rental_station.dart';
import 'package:waie_app/models/gas_station.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/refundable_service.dart';
import 'dart:math';

class CarRentalController extends GetxController {
  RxBool isGasStations = false.obs;
  RxBool isSalesOffice = false.obs;
  RxBool isInstallationCenters = false.obs;
  RxBool isStations = false.obs;
  RxBool isCarService = false.obs;
  RxBool isMosque = false.obs;
  RxBool isFoodResturant = false.obs;
  RxBool isCarRental = true.obs;
  RxBool isDefaultMap = true.obs;

  RxBool isMap = true.obs;
  RxBool isList = false.obs;
  List<String> saudiArabiaCities = [
    'Riyadh',
    'Jeddah',
    'Mecca',
    'Medina',
    'Dammam',
    'Taif',
    'Tabuk',
    'Al Khobar',
    'Yanbu',
    'Abha',
    // Add more cities as needed
  ];

  RxString selectedCity = ''.obs;

  void setSelectedCity(String city) {
    selectedCity.value = city;
  }

  List<LatLng> latLongList = [
    LatLng(21.44484793949768, 53.295109691390245),
    LatLng(20.582120409829233, 44.96018559757371),
    LatLng(23.899725409740984, 54.046233681296975),
    LatLng(22.302502578679334, 47.422575301577865),
    LatLng(25.169169066706516, 38.833075072755804),
    LatLng(23.359337577784572, 45.01153009917833),
    LatLng(20.950188013577115, 49.39292756943918),
  ];
  List<LatLng> salesOfficeList = [
    LatLng(21.44484793949768, 53.295109691390245),
    LatLng(20.582120409829233, 44.96018559757371),
    LatLng(23.899725409740984, 54.046233681296975),
    LatLng(22.302502578679334, 47.422575301577865),
    LatLng(25.169169066706516, 38.833075072755804),
    LatLng(23.359337577784572, 45.01153009917833),
    LatLng(20.950188013577115, 49.39292756943918),
  ];
  List<LatLng> installationList = [
    LatLng(21.44484793949768, 53.295109691390245),
    LatLng(20.582120409829233, 44.96018559757371),
    LatLng(23.899725409740984, 54.046233681296975),
    LatLng(22.302502578679334, 47.422575301577865),
    LatLng(25.169169066706516, 38.833075072755804),
    LatLng(23.359337577784572, 45.01153009917833),
    LatLng(20.950188013577115, 49.39292756943918),
  ];
  List listData = [
    {
      'data': "Riyadh",
      'list': [
        {
          "title": "Al Reef",
          'subTitle': "RAFB7699، 7699 Riyadh 13314, Saudi Arabia",
        },
        {
          "title": "Asment Exit - 18",
          'subTitle': "REFA7322, 7322 Mahail, 4063",
        },
      ]
    },
    {
      'data': "Al Ahsa",
      'list': [
        {
          "title": "Al Makhaita 2",
          'subTitle':
              "Al Qadisiyah, Al Mubarraz 36422, Saudi Arabia ا,،,لملك سعود",
        },
      ]
    },
    {
      'data': "Duraidah",
      'list': [
        {
          "title": "Al Faizy",
          'subTitle':
              "QBWE7235، 7235 عمر بن الخطاب، 2639، حي النهضة, Buraydah 52388, Saudi Arabia",
        },
      ]
    },
  ];
  RxList itemList = [].obs;

  var carRentalstationsList = <CarRentalStation>[].obs;
  bool isLoading = false;

  List<LatLng> carRentalStats = [];

  getAllCarRentalStations() async {
    carRentalstationsList.clear();
    carRentalStats.clear();
    List<CarRentalStation> stations = [];
    var client = http.Client();

    try {
      print("Fetching Stations...");
      var response = await client.get(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnCarRental));

      //log("getstations API Response: ${jsonDecode(response.body)}");

      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        CarRentalStation station =
            CarRentalStation.fromJson(result[i] as Map<String, dynamic>);

        if (station.latitude.isNotEmpty && station.longitude.isNotEmpty) {
          stations.add(station);
          carRentalStats.add(LatLng(
              double.parse(station.latitude), double.parse(station.longitude)));
        }
      }

      carRentalstationsList.value = stations;

      return carRentalstationsList;
    } catch (e) {
      //log("Error fetching stations: $e");
      return [];
    } finally {
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('GasStationController');
    if (carRentalstationsList.isEmpty) {
      print("sulod");
      // await getAllCarRentalStations();
    }
    //Navigator.of(Get.context!).pop();
  }

  double? userLatitude;
  double? userLongitude;
  double radiusInKm = 200.0;

  List<CarRentalStation> gasStationListFiltered = [];

  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    print('distance method lat1 ${lat1}');
    print('distance method long1 ${lon1}');
    print('distance method lat2 ${lat2}');
    print('distance method lon2 ${lon2}');

    const double R = 6371;
    final double dLat = _degToRad(lat2 - lat1);
    final double dLon = _degToRad(lon2 - lon1);
    print('distance method lat2 ${dLat}');
    print('distance method lon2 ${dLon}');
    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degToRad(lat1)) *
            cos(_degToRad(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return R * c;
  }

  double _degToRad(double deg) => deg * (pi / 180);

  var allStationsList = <CarRentalStation>[].obs;

  getCarRentalStations200kmRadius(
      double userLatitude, double userLongitude, double radiusInKm) async {
    List<CarRentalStation> gasses = [];
    List<CarRentalStation> allStations = [];
    print('yahan aya 200km');
    var client = http.Client();
    carRentalStats.clear();
    carRentalstationsList.clear();
    allStationsList.clear();
    isLoading = true;

    try {
      var response = await client.get(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnCarRental));
      List result = jsonDecode(response.body);
      print('resultofstations $result');
      for (int i = 0; i < result.length; i++) {
        //print('loop start');

        CarRentalStation gas =
            CarRentalStation.fromJson(result[i] as Map<String, dynamic>);
        //print('after model');
        //print('lat ${gas.latitude}');
        //print('long ${gas.longitude}');

        if (gas.latitude != "" && gas.longitude != "") {
          double stationLatitude = double.parse(gas.latitude);
          double stationLongitude = double.parse(gas.longitude);
          //print('if lat ${gas.latitude}');
          //print('if long ${gas.longitude}');
          allStations.add(gas);
          //  print('all stations $allStations');
          print('userlat $userLatitude');
          print('userlon $userLongitude');

          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);
          print('calculated distance $distance');
          print('distance check $distance');
          print('radius check $radiusInKm');
          print(distance <= radiusInKm);

          if (distance <= radiusInKm) {
            print('distance <= radius ');
            gasses.add(gas);
            print('gasses added $gasses');
            //carRentalStats.add(LatLng(stationLatitude, stationLongitude));
          }
        }
      }

      // if (gasses.isEmpty) {
      //   print('No stations within the 200km radius.');
      //   isMap.value = false;
      // }

      carRentalstationsList.value = gasses;
      //print('after loop $carRentalstationsList');
      allStationsList.value = allStations;
      //print('after loop all stations $allStationsList');
      //print("Filtered gasStats >>>>> ${jsonEncode(carRentalStats)}");
      // isLoading = false;
      // isMap.value=true;
      return carRentalstationsList;
    } catch (e) {
      print("getAllGasStations Error: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }
}
