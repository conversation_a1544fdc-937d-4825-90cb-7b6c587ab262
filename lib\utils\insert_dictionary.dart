import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/locale_string.dart';
import 'package:http/http.dart' as http;

class TranslationManager {
  static final Map<String, String> _missingTranslations = {};
  static Timer? _timer;
  static Completer<void>? _completer;

  static Future<String> translate(String key) async {
    // Check if the key exists in both ar_AR and en_US
    bool arExists = LocaleString.localizedStrings['ar_AR']!.containsKey(key);
    bool enExists = LocaleString.localizedStrings['en_US']!.containsKey(key);

    log("json AR check  $arExists AR key $key");
    log("json EN check  $enExists EN KEY $key");

    if (!arExists && !enExists) {
      await _logMissingTranslation(key);
      log('this one should returned $key');
      return key;
    }

    if (arExists && enExists) {
      //return LocaleString.localizedStrings['en_US']![key]!;
    }

    if (arExists) {
      // return LocaleString.localizedStrings['ar_AR']![key]!;
    } else if (enExists) {
      //return LocaleString.localizedStrings['en_US']![key]!;
    }

    return key;
  }

  static Future<void> _logMissingTranslation(String key) async {
    if (!_missingTranslations.containsKey(key)) {
      _missingTranslations[key] = key;
      log("missing keys ${_missingTranslations[key].toString()}");
      _scheduleSend();
      // _sendMissingTranslations();
    }
  }

  static void _scheduleSend() {
    _timer?.cancel();
    _timer = Timer(Duration(seconds: 5), () => _sendMissingTranslations());
  }

  static Future<void> _sendMissingTranslations() async {
    if (_missingTranslations.isEmpty || _completer != null) return;

    _completer = Completer<void>();
    log('missing keys Api Hit $_missingTranslations');

    try {
      List<Map<String, String>> missingKeysList =
          _missingTranslations.keys.map((key) => {"name": key}).toList();
      final String url =
          "${ApiEndPoints.baseUrl}${ApiEndPoints.authEndpoints.insertDictionary}";
      log("insert dictionary url $url");
      log("json check before send ${jsonEncode(missingKeysList)}");
      //"https://devinttest.aldrees.com/api/INSERTDICTIONARY";

      log(url.toString());
      final response = await http.post(
        Uri.parse(url),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(missingKeysList),
      );
      log(response.body.toString());
      log(response.statusCode.toString());

      if (response.statusCode == 200) {
        _missingTranslations.clear();
      } else {
        log('Failed to send missing translations: ${response.statusCode}');
      }
    } catch (e) {
      log('Error sending missing translations: $e');
    } finally {
      _completer?.complete();
      _completer = null;
    }
  }
}

extension CustomTranslation on String {
  String get trr {
    TranslationManager.translate(this);
    return Get.find<LocaleString>().keys[Get.locale.toString()]?[this] ?? this;
  }
}
