// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:waie_app/utils/insert_dictionary.dart';

import 'edit_tag_installation.dart';
import 'upcoming_screen.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/vehicle_controller/tag_installation_controller.dart';

class HistoryScreen extends StatelessWidget {
  HistoryScreen({Key? key}) : super(key: key);
  TagInstallationController tagInstallationController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            verticalSpace(24),
            ListView.builder(
              itemCount:
                  tagInstallationController.tagInstallationHistoryList.length,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                var data =
                    tagInstallationController.tagInstallationHistoryList[index];
                return upcomingDataWidget(
                  title: data['title'],
                  totalVehicle: data['totalVehicle'],
                  type: data['type'].toString().trr,
                  scheduledOn: data['scheduledOn'],
                  location: data['location'],
                  referenceId: data['referenceId'],
                  onTap: () {
                    MapsLauncher.launchCoordinates(
                        data["latlng"].latitude, data["latlng"].longitude);
                  },
                  moreOnTap: () {
                    showModalBottomSheet(
                      context: context,
                      shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.vertical(top: Radius.circular(16))),
                      builder: (context) {
                        return tagInstallationActionWidget(
                          title: data['title'],
                          date: data['scheduledOn'],
                          editOnTap: () {
                            Get.back();
                            Get.to(EditTagInstallationWidget());
                          },
                          deleteOnTap: () {
                            Get.back();
                            showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                                  insetPadding: EdgeInsets.all(16),
                                  contentPadding: EdgeInsets.all(16),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12)),
                                  content: cancelDialogWidget(
                                    cancelTap: () {
                                      Get.back();
                                    },
                                    deleteTap: () {
                                      Get.back();
                                    },
                                  ),
                                );
                              },
                            );
                          },
                        );
                      },
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
