// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_history_cancel_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_history_tag_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_history_topup_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_tag_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/loading_widget.dart';
import '../user_management_screen/user_management_screen.dart';
import 'no_refund_found_screen.dart';

class OrderHistoryTopupScreen extends StatefulWidget {
  const OrderHistoryTopupScreen({super.key});

  @override
  State<OrderHistoryTopupScreen> createState() =>
      _OrderHistoryTopupScreenState();
}

class _OrderHistoryTopupScreenState extends State<OrderHistoryTopupScreen> {
  double totaltAmt = 0.0;
  double totaltVAT = 0.0;
  RefundsController refundsController = Get.put(RefundsController());

  OrderHistoryTopupController orderHistoryTopupController =
      Get.put(OrderHistoryTopupController());

  OrderHistoryCancelController orderHistoryCancelController =
      Get.put(OrderHistoryCancelController());

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () => orderHistoryTopupController.loadOrderHistoryTopups.isEmpty
                  ? NoRefundFoundScreen()
                  : ListView.builder(
                      itemCount: orderHistoryTopupController
                          .loadOrderHistoryTopups.length,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = orderHistoryTopupController
                            .loadOrderHistoryTopups[index];
                        // print(
                        //     "orderRefundTagController.refundableTagServices.length ${orderRefundTagController.refundableTagServices.length}");
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: orderRefundDataWidget(
                            value: data.isvalue,
                            code: data.reqId,
                            status: data.status,
                            reqDate: data.reqDate,
                            amount: data.acctCode.toString(),
                            textColor: data.status == "CANCELLED"
                                ? AppColor.cDarkBlueFont
                                : AppColor.cDarkBlueFont,
                            color: data.status == "CANCELLED"
                                ? AppColor.cLightBlueContainer
                                : AppColor.cLightBlueContainer,
                            isShowButton: data.status == "APPROVED"
                                ? false
                                : data.status == "CANCELLED"
                                    ? false
                                    : true,
                            cancelReqOnTap: () {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return AlertDialog(
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(12)),
                                    contentPadding: EdgeInsets.all(24),
                                    insetPadding: EdgeInsets.all(16),
                                    content: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.end,
                                          children: [
                                            GestureDetector(
                                                onTap: () {
                                                  Get.back();
                                                },
                                                child: assetSvdImageWidget(
                                                    image: DefaultImages
                                                        .cancelIcn)),
                                          ],
                                        ),
                                        verticalSpace(24),
                                        Text(
                                            "${"Are you sure you want to cancel refund request".trr} ${data.reqId}?",
                                            style: pBold20,
                                            textAlign: TextAlign.center),
                                        verticalSpace(14),
                                        Center(
                                            child: Text(
                                                "You can't undo this. If you still need a problem to be resolved, you'll need to submit a new refund request."
                                                    .trr,
                                                style: pRegular13,
                                                textAlign: TextAlign.center)),
                                        verticalSpace(24),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: CommonButton(
                                                title: "NO".trr,
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                textColor:
                                                    AppColor.cDarkBlueFont,
                                                btnColor: AppColor.cBackGround,
                                                bColor: AppColor.cDarkBlueFont,
                                              ),
                                            ),
                                            horizontalSpace(16),
                                            Expanded(
                                              child: CommonButton(
                                                title: "Yes, cancel".trr,
                                                onPressed: () {
                                                  //Get.back();
                                                  //Loader.showLoader();
                                                  orderHistoryCancelController
                                                      .cancelTopupHistory(
                                                          data.reqId);
                                                  //Get.back();
                                                },
                                                textColor: AppColor.cWhiteFont,
                                                btnColor: AppColor.cRedText,
                                                bColor: AppColor.cTransparent,
                                                horizontalPadding: 16,
                                              ),
                                            ),
                                          ],
                                        )
                                      ],
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget orderRefundDataWidget({
  required String code,
  String? status,
  Color? color,
  Color? textColor,
  bool? value,
  ValueChanged<bool?>? onChanged,
  String? orderType,
  String? plate,
  String? vehicleType,
  required String reqDate,
  required String amount,
  String? vat,
  bool? isShowButton = false,
  bool? isShowCheckBox = false,
  Function()? cancelReqOnTap,
}) {
  return Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(4),
      border: Border.all(
          color: value == true ? AppColor.cDarkBlueFont : AppColor.cLightGrey),
    ),
    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                isShowCheckBox == false
                    ? SizedBox()
                    : SizedBox(
                        height: 24,
                        width: 24,
                        child: Checkbox(
                          value: value,
                          onChanged: onChanged,
                          activeColor: AppColor.themeDarkBlueColor,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                              side: BorderSide(color: AppColor.cBorder)),
                        ),
                      ),
                horizontalSpace(isShowCheckBox == false ? 0 : 8),
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        border: Border.all(
                          color: color ?? AppColor.cLightBlueContainer,
                        ),
                        borderRadius: BorderRadius.circular(4)),
                    padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 13,
                            color: textColor ?? AppColor.cDarkBlueFont),
                      ),
                    )),
                horizontalSpace(8),
                newWidget(text: status, color: color, textColor: textColor),
              ],
            ),
            // assetSvdImageWidget(image: DefaultImages.verticleMoreIcn)
          ],
        ),
        verticalSpace(18),
        // userDataRowWidget(
        //   title: "Order type".trr,
        //   value: orderType == "T" ? "Tag" : "Smart Card",
        // ),
        // verticalSpace(12),
        // userDataRowWidget(
        //     title: "${"Plate".trr} #",
        //     value: plate,
        //     textColor: AppColor.cDarkBlueText),
        // verticalSpace(12),
        // userDataRowWidget(title: "Vehicle type".trr, value: vehicleType),
        // verticalSpace(12),
        userTotalValueWidget(title: "Req date".trr, value: reqDate),
        verticalSpace(12),
        userTotalValueWidget(title: "Amount".trr, value: amount),
        verticalSpace(12),
        // userDataRowWidget(title: "VAT".trr, value: vat),
        verticalSpace(isShowButton == true ? 14 : 0),
        isShowButton == true
            ? CommonIconBorderButton(
                iconData: DefaultImages.cancelRequestIcn,
                title: "Cancel request".trr,
                onPressed: cancelReqOnTap,
              )
            : SizedBox()
      ],
    ),
  );
}
