plugins {
    id "com.android.application"
    id "org.jetbrains.kotlin.android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

android {
    namespace "com.waie.aldrees.mobile"
    compileSdk 35

    defaultConfig {
        applicationId "com.waie.aldrees.mobile"
        minSdk 21
        targetSdk 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            if (keystorePropertiesFile.exists()) {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
            }
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            // enable shrinking/obfuscation as needed
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main.java.srcDirs += 'src/main/java'
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }
}

flutter {
    source '../..'
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode') ?: '13'
def flutterVersionName = localProperties.getProperty('flutter.versionName') ?: '1.0.8'

def keystorePropertiesFile = rootProject.file('key.properties')
def keystoreProperties = new Properties()
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

dependencies {
    implementation 'com.squareup.retrofit2:converter-gson:2.4.0'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'com.mastercard.gateway:gateway-android:1.1.6'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation platform("com.google.firebase:firebase-bom:32.7.0")
    implementation "com.google.firebase:firebase-analytics-ktx"
    implementation "org.jetbrains.kotlin:kotlin-stdlib"
    implementation "androidx.multidex:multidex:2.0.1"
}


// def localProperties = new Properties()
// def localPropertiesFile = rootProject.file('local.properties')
// if (localPropertiesFile.exists()) {
//     localPropertiesFile.withReader('UTF-8') { reader ->
//         localProperties.load(reader)
//     }
// }

// def keystorePropertiesFile = rootProject.file('key.properties')
// def keystoreProperties = new Properties()
// if (keystorePropertiesFile.exists()) {
//     keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
// }


// def flutterRoot = localProperties.getProperty('flutter.sdk')
// /*if (flutterRoot == null) {
//     throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
// }*/


// def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
// if (flutterVersionCode == null) {
//     flutterVersionCode = '13'
// }

// def flutterVersionName = localProperties.getProperty('flutter.versionName')
// if (flutterVersionName == null) {
//     flutterVersionName = '1.0.8'
// }
// apply plugin: 'com.android.application'
// //apply plugin: 'kotlin-android'
// apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

// android {
//     //namespace "com.waie.waie_app"
//     //compileSdkVersion flutter.compileSdkVersion
//     compileSdkVersion 35
//     ndkVersion flutter.ndkVersion

//     compileOptions {
//         sourceCompatibility JavaVersion.VERSION_1_8
//         targetCompatibility JavaVersion.VERSION_1_8
//     }

//     signingConfigs {
//         release {
//             if (keystorePropertiesFile.exists()) {
//                 keyAlias keystoreProperties['keyAlias']
//                 keyPassword keystoreProperties['keyPassword']
//                 storeFile file(keystoreProperties['storeFile'])
//                 storePassword keystoreProperties['storePassword']
//             }
//         }
//     }

//     buildTypes {
//         release {
//             signingConfig signingConfigs.release
//         }
//     }

//    /* kotlinOptions {
//         jvmTarget = '1.8'
//     }*/

//     sourceSets {
//         //main.java.srcDirs += 'src/main/kotlin'
//         main.java.srcDirs += 'src/main/java'
//     }

//     defaultConfig {
//         // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
//        // applicationId "com.waie.waie_app"
//         applicationId "com.waie.aldrees.mobile"
//         // You can update the following values to match your application needs.
//         // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
//         minSdkVersion 21
//         multiDexEnabled true
//         targetSdkVersion 35
//         versionCode flutterVersionCode.toInteger()
//         versionName flutterVersionName
//     }

//     /*buildTypes {
//         release {
//             // TODO: Add your own signing config for the release build.
//             // Signing with the debug keys for now, so `flutter run --release` works.
//             signingConfig signingConfigs.debug
//         }
//     }*/
// }

// flutter {
//     source '../..'
// }
// apply plugin: 'com.google.gms.google-services'

// dependencies {
//   //  implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
//     implementation 'com.squareup.retrofit2:converter-gson:2.4.0'
//     implementation 'com.google.code.gson:gson:2.8.5'
//     implementation 'com.mastercard.gateway:gateway-android:1.1.6'
//     implementation 'com.google.android.material:material:1.11.0'
//     implementation 'androidx.appcompat:appcompat:1.6.1'
//     implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
//     implementation platform("com.google.firebase:firebase-bom:32.7.0")
//     implementation "com.google.firebase:firebase-analytics-ktx"
//     implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
//     implementation "androidx.multidex:multidex:2.0.1"

// }
