import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/splash_controller/splash_controller.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/balance_topup_screen/balance_topup_tab_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class BalanceScreen extends StatefulWidget {
  const BalanceScreen({super.key});

  @override
  State<BalanceScreen> createState() => _BalanceScreenState();
}

class _BalanceScreenState extends State<BalanceScreen> {
  @override
  void initState() {
    super.initState();
    _loadHomeImage();
  }

  SplashController splashController = Get.put(SplashController());
  String homeLogo = '';
  Future<void> _loadHomeImage() async {
    String path = await splashController.loadImage('MOBLOGOIMGS');
    setState(() {
      homeLogo = path;
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 7),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Center(
            //       child: Image.asset(
            //        'asset/image/image/loginnationaldaylogo.jpg',
            //        height: 35,
            //        fit: BoxFit.cover,
            //        ),
            //     ),

            Center(
              child: homeLogo.isNotEmpty
                  ? Image.network(
                      homeLogo,
                      height: 35,
                      fit: BoxFit.cover,
                    )
                  : Image.asset(
                      'asset/image/image/logotransparent.png',
                      height: 35,
                      fit: BoxFit.cover,
                    ),
            ),
            // Text(
            //   "Top up balance".tr,
            //   style: pBold20,
            // ),
            // verticalSpace(16),
            const BalanceTopUpTabScreen()
          ],
        ),
      ),
    );
  }
}
