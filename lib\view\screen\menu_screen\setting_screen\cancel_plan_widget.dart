// ignore_for_file: prefer_const_constructors

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/setting_screen/basic_subscription_plan_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class CancelPlanWidget extends StatelessWidget {
  const CancelPlanWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.start, children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Center(child: assetSvdImageWidget(image: DefaultImages.cancelIcn))),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: Center(
            child: Text("Are you sure you want to cancel your premium subscription plan?".trr,
                style: pBold16, textAlign: TextAlign.center),
          ),
        ),
        Center(
          child: Text("${"You can reactivate it by".trr} 14.04.2023 ${"for free".trr}",
              style: pRegular13.copyWith(color: AppColor.cDarkGreyFont), textAlign: TextAlign.center),
        ),
        verticalSpace(24),
        Row(
          children: [
            Expanded(
                child: CommonBorderButton(
              title: 'No'.trr,
              onPressed: () {
                Get.back();
              },
              bColor: AppColor.themeDarkBlueColor,
              textColor: AppColor.cDarkBlueFont,
            )),
            horizontalSpace(8),
            Expanded(
                child: CommonButton(
              title: 'Yes, cancel'.trr,
              onPressed: () {
                Get.back();
                Get.off(() => BasicSubscriptionPlanScreen());
              },
              btnColor: AppColor.cRedText,
            ))
          ],
        ),
        verticalSpace(16),
      ]),
    );
  }
}
