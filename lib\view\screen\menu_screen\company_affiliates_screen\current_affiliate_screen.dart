// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:waie_app/core/controller/menu_controller/company_affiliate_controller/company_affiliate_current_controller.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/no_affiliates_widget.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/no_refund_found_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/company_affiliate_controller/company_affiliate_controller.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/images.dart';
import '../../../widget/common_button.dart';
import 'new_affiliate_screen.dart';

class CurrentAffiliatesScreen extends StatelessWidget {
  final CompanyAffiliateController companyAffiliateController;

  CurrentAffiliatesScreen(
      {super.key, required this.companyAffiliateController});

  CompanyAffiliateCurrentController companyAffiliateCurrentController =
      Get.put(CompanyAffiliateCurrentController());

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalSpace(12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
               /* Expanded(
                    child: Text(
                  "Team up with other companies to get bigger discounts from WAIE",
                  style: pRegular16,
                )),*/
                horizontalSpace(8),
                assetSvdImageWidget(
                    image: DefaultImages.informationMarkCircleIcn)
              ],
            ),
            CommonIconButton(
              iconData: DefaultImages.circleAddIcn,
              title: 'Add Affiliate'.trr,
              onPressed: () {
                Get.to(() => NewAffiliateScreen());
              },
              btnColor: AppColor.themeOrangeColor,
            ),
            verticalSpace(24),
            Obx(
              () =>
                  companyAffiliateCurrentController.affiliateCurrentList.isEmpty
                      ? NoAffiliatesWidget()
                      : ListView.builder(
                          scrollDirection: Axis.vertical,
                          shrinkWrap: true,
                          itemCount: companyAffiliateCurrentController
                              .affiliateCurrentList.length,
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            var data = companyAffiliateCurrentController
                                .affiliateCurrentList[index];
                            return requestAffiliateDataWidget(
                              name: data.reqId,
                              status: data.status,
                              requestType: data.reqDesc,
                              requestDate: data.reqDate,
                              request: data.reqSubType,
                              isRequest: true,
                              textColor: data.status == "NEW"
                                  ? AppColor.cDarkBlueFont
                                  : AppColor.cGreen,
                              conColor: data.status == "NEW"
                                  ? AppColor.cLightBlueContainer
                                  : AppColor.cLightGreen,
                            );
                          },
                        ),
            )
          ],
        ),
      ),
    );
  }

  Container chooseFileWidget(String name) {
    return Container(
      height: 44,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: AppColor.cBorder)),
      padding: EdgeInsets.symmetric(
        horizontal: 6,
      ),
      child: Row(
        children: [
          Container(
            height: 32,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: AppColor.lightBlueColor,
            ),
            padding: EdgeInsets.only(right: 12, left: 8),
            child: Row(
              children: [
                assetSvdImageWidget(
                  image: DefaultImages.fileIcn,
                ),
                horizontalSpace(8),
                Text(
                  "Choose file".trr,
                  style: pRegular14,
                ),
              ],
            ),
          ),
          horizontalSpace(8),
          Expanded(
            child: Text(
              name,
              style: pRegular14.copyWith(
                color: AppColor.cDarkGreyFont,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          )
        ],
      ),
    );
  }

  Widget requestAffiliateDataWidget({
    String? name,
    String? status,
    String? requestType,
    String? requestDate,
    String? request,
    bool? isRequest,
    bool? isBtn,
    Color? textColor,
    Color? conColor,
    String? btnName,
    String? btnImage,
    Function()? onPressed,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 10),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: AppColor.lightBlueColor,
            border: Border.all(color: AppColor.cLightGrey)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SizedBox(
                  height: 24,
                  child: Center(
                    child: Text(
                      name!,
                      style:
                          pSemiBold17.copyWith(color: AppColor.cDarkBlueFont),
                    ),
                  ),
                ),
                horizontalSpace(8),
                newAffiliateWidget(
                    color: conColor, textColor: textColor, text: status),

                // assetSvdImageWidget(image: DefaultImages.verticleMoreIcn),
              ],
            ),
            verticalSpace(18),
            dataRowAffiliateWidget("Request type".trr, requestType!),
            verticalSpace(12),
            dataRowAffiliateWidget("Request date".trr, requestDate!),
          ],
        ),
      ),
    );
  }

  Row dataRowAffiliateWidget(String title, String data) {
    return Row(
      children: [
        SizedBox(
            width: 156,
            child: Text(
              title,
              style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
            )),
        Text(data, style: pRegular13),
      ],
    );
  }

  Container newAffiliateWidget({Color? color, String? text, Color? textColor}) {
    return Container(
      height: 24,
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: color ?? AppColor.cLightBlueContainer,
      ),
      child: Center(
        child: Text(
          text ?? "New",
          style:
              pSemiBold12.copyWith(color: textColor ?? AppColor.cDarkBlueFont),
        ),
      ),
    );
  }
}
