// ignore_for_file: prefer_const_constructors

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';

class Loader {
  static showLoader() {
    Get.dialog(LoadingWidget());
  }

  static showBlackLoader() {
    Get.dialog(LoadingBlackWidget());
  }

  static hideLoader() {
    //Get.back(closeOverlays: true);
    Get.back();
  }
}

class LoadingWidget extends StatelessWidget {
  const LoadingWidget({Key? key}) : super(key: key);

  /* @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(color: AppColor.themeOrangeColor),

    );
  }*/
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 50, // Adjust the width of the container
      height: 50, // Adjust the height of the container
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5), // Transparent background
        borderRadius: BorderRadius.circular(10), // Rounded corners
      ),
      child: Center(
          child: CircularProgressIndicator(color: AppColor.themeOrangeColor)),
    );
  }
}

class LoadingBlackWidget extends StatelessWidget {
  const LoadingBlackWidget({Key? key}) : super(key: key);

  /* @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(color: AppColor.themeOrangeColor),

    );
  }*/
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 50, // Adjust the width of the container
      height: 50, // Adjust the height of the container
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5), // Transparent background
        borderRadius: BorderRadius.circular(10), // Rounded corners
      ),
      child: Center(child: CircularProgressIndicator(color: Colors.white)),
    );
  }
}
