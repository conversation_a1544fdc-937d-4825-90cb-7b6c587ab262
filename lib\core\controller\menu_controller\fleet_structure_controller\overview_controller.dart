import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/models/loadplaces.dart';
import 'package:waie_app/utils/api_endpoints.dart';

class OverviewController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  GetStorage userStorage = GetStorage('User');
  final fleetList = <FleetModel>[].obs;
  List<LoadPlaces> loadPlaces = [];
  RxList placeList = [].obs;
}
