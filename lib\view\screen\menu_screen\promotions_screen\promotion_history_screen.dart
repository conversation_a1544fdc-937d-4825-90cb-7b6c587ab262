// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/promotion_controller/promotion_controller.dart';

class PromotionHistoryScreen extends StatefulWidget {
  final PromotionController promotionController;

  const PromotionHistoryScreen({Key? key, required this.promotionController})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _PromotionHistoryScreen();
}

class _PromotionHistoryScreen extends State<PromotionHistoryScreen> {
  PromotionController promotionController = Get.put(PromotionController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        verticalSpace(24),
        FutureBuilder<dynamic>(
            future: promotionController.promotionHistoryList(),
            builder: (context, AsyncSnapshot snapshot) {
              if (snapshot.connectionState != ConnectionState.done) {
                return const Center(child: CircularProgressIndicator());
              } else {
                return ListView.builder(
                  itemCount: promotionController.promotionList.length,
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    var data = promotionController.promotionList[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: historyWidget(
                        title: data.PROMO,
                        service: data.PROMO,
                        activationCode: data.ACT_CODE,
                        activationDate: data.ACTDATE,
                        amount: data.AFTER_VAT.toString(),
                      ),
                    );
                  },
                );
              }
            }),

        /* verticalSpace(24),
        ListView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: promotionController.historyList.length,
          itemBuilder: (context, index) {
            var data = promotionController.historyList[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: historyWidget(
                title: data['title'],
                service: data['service'],
                activationCode: data['activationCode'],
                activationDate: data['activationDate'],
                amount: data['amount'],
              ),
            );
          },
        )*/
      ],
    );
  }

  Widget historyWidget({
    required String title,
    required String service,
    required String activationCode,
    required String activationDate,
    required String amount,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColor.cLightGrey,
        borderRadius: BorderRadius.circular(6),
      ),
      padding: EdgeInsets.only(top: 16, bottom: 3, right: 16, left: 16),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          title,
          style: pSemiBold17.copyWith(color: AppColor.cDarkBlueText),
        ),
        verticalSpace(19),
        labelRowWidget("Service".trr, service),
        labelRowWidget("Activation code".trr, activationCode),
        labelRowWidget("Activation date".trr, activationDate),
        labelRowWidget("Amount".trr, amount),
      ]),
    );
  }

  Widget labelRowWidget(String title, String subTitle) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          SizedBox(
              width: 140,
              child: Text(
                title,
                style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
              )),
          Text(
            subTitle,
            style: pRegular13,
          ),
        ],
      ),
    );
  }
}
