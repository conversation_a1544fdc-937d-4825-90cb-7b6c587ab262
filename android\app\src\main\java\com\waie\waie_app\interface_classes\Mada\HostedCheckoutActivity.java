package com.waie.aldrees.mobile.interface_classes.Mada;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.waie.aldrees.mobile.MainActivity;
import com.waie.aldrees.mobile.R;
import com.waie.aldrees.mobile.interface_classes.MadaApiService;

import java.util.Set;

import io.flutter.plugin.common.MethodChannel;

public class HostedCheckoutActivity extends AppCompatActivity {

    /**
     * The HTML used to initialize the WebView. Should be the HTML content returned
     * from the Gateway
     * during the Check 3DS Enrollment call
     */
    public static final String EXTRA_HTML = "com.mastercard.gateway.android.HTML";
    /**
     * An OPTIONAL title to display in the toolbar for this activity
     */
    public static final String EXTRA_TITLE = "com.mastercard.gateway.android.TITLE";

    /**
     * The ACS Result data after performing 3DS
     */
    public static final String EXTRA_ACS_RESULT = "com.mastercard.gateway.android.ACS_RESULT";

    static final String REDIRECT_SCHEME = "android";

    Toolbar toolbar;
    WebView webView;
    String orderId;
    MadaApiService madaApiService = new MadaApiService();
    String prepareJson;
    private Dialog progressDialog;
    Context context;
    Activity activity;

    // ApiService apiService;

    @Override
    @SuppressLint("SetJavaScriptEnabled")
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_hosted_checkout);
        activity = HostedCheckoutActivity.this;
        io.flutter.Log.d("startHostedCheckoutActivity 3 ", "Called");
        // apiService=new ApiService(this);

        // init toolbar
        toolbar = findViewById(R.id.toolbar);
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });

        // init web view
        webView = findViewById(R.id.webview1);

        webView.setWebChromeClient(new WebChromeClient());
        webView.getSettings().setDomStorageEnabled(true);
        webView.getSettings().setJavaScriptEnabled(true);
        webView.setWebViewClient(buildWebViewClient());
        webView.addJavascriptInterface(new WebAppInterface(this), "android");
        io.flutter.Log.d("startHostedCheckoutActivity 4 ", "Called");
        init();
    }

    void init() {
        // init html
        // progressDialog.show();

        String extraHtml = getExtraHtml();
        if (extraHtml == null) {
            onBackPressed();
            return;
        } else {
            setWebViewHtml(extraHtml);
        }

        // init title
        String defaultTitle = getDefaultTitle();
        String extraTitle = getExtraTitle();
        setToolbarTitle(extraTitle != null ? extraTitle : defaultTitle);

    }

    String getDefaultTitle() {
        return "Payment";
    }

    String getExtraTitle() {
        Bundle extras = getIntent().getExtras();
        orderId = extras.getString("ORDERID");
        prepareJson = extras.getString("PREPAREJSON");
        if (extras != null) {
            return extras.getString(EXTRA_TITLE);
        }

        return null;
    }

    String getExtraHtml() {
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            return extras.getString("HTML");
        }

        return null;
    }

    void setToolbarTitle(String title) {
        toolbar.setTitle(title);
    }

    void setWebViewHtml(String html) {
        String encoded = Base64.encodeToString(html.getBytes(), Base64.NO_PADDING | Base64.NO_WRAP);
        webView.loadData(encoded, "text/html", "base64");
    }

    void webViewUrlChanges(Uri uri) {
        String scheme = uri.getScheme();
        if (REDIRECT_SCHEME.equalsIgnoreCase(scheme)) {
            webView.setVisibility(View.GONE);
            onBackPressed();
            // MainActivity.flutterReturn();

        } else if ("mailto".equalsIgnoreCase(scheme)) {
            intentToEmail(uri);
        } else {
            loadWebViewUrl(uri);
        }
    }

    void complete(String acsResult) {
        Intent intent = new Intent();
        complete(acsResult, intent);
    }

    // separate for testability
    void complete(String acsResult, Intent intent) {
        intent.putExtra(EXTRA_ACS_RESULT, acsResult);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    void loadWebViewUrl(Uri uri) {
        webView.loadUrl(uri.toString());
    }

    void intentToEmail(Uri uri) {
        Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
        intentToEmail(uri, emailIntent);
    }

    // separate for testability
    void intentToEmail(Uri uri, Intent intent) {
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setData(uri);

        startActivity(intent);
    }

    String getACSResultFromUri(Uri uri) {
        String result = null;

        Set<String> params = uri.getQueryParameterNames();
        for (String param : params) {
            if ("acsResult".equalsIgnoreCase(param)) {
                result = uri.getQueryParameter(param);
            }
        }

        return result;
    }

    WebViewClient buildWebViewClient() {
        return new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                webViewUrlChanges(Uri.parse(url));
                return true;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                // TODO Auto-generated method stub
                super.onPageFinished(view, url);
                // progressDialog.dismiss();
            }
        };

    }

    public class WebAppInterface {
        Context mContext;

        /** Instantiate the interface and set the context */
        WebAppInterface(Context c) {
            mContext = c;
        }

        /** Show a toast from the web page */
        @JavascriptInterface
        public void goBack() {
            finish();
        }
    }

    // Method to close the activity
    private void closeActivity() {
        Intent intent = new Intent();
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

}
