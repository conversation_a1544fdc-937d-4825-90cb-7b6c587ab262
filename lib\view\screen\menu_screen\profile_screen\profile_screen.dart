// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/menu_controller/profile_controller/profile_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/prefer.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/company_addreess_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/company_details_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/profile_controller/address_load_controller.dart';
import '../../../../core/controller/menu_controller/profile_controller/new_profile_controller.dart';
import '../../../../utils/validator.dart';
import '../../../widget/common_button.dart';
import '../../../widget/common_drop_down_widget.dart';
import '../../../widget/common_snak_bar_widget.dart';
import '../../../widget/common_text_field.dart';
import '../../auth/login_with_email_screen.dart';
import 'personal_details_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  NewProfileController newProfileController = Get.put(NewProfileController());
  Address_Data_Controller addressLoadController =
      Get.put(Address_Data_Controller());

  String errorString = '';
  FilePickerResult? vatDocx;
  FilePickerResult? idDocx;

  bool _isSecondDropdownEnabled = false;

  @override
  void initState() {
    super.initState();
    // if (newProfileController.selectedRegion.value.isNotEmpty) {
    //   print("newProfileController.selectedRegion.value");
    //   print(newProfileController.selectedRegion.value);
    //   setState(() {
    //     addressLoadController
    //         .fetchCityData(newProfileController.selectedRegion.value);
    //   });
    // }
    if (newProfileController.selectedCity.value.isNotEmpty) {
      print("newProfileController.selectedRegion.value");
      print(newProfileController.selectedCity.value);
      _isSecondDropdownEnabled = true;
      addressLoadController.fetchDistData(
          newProfileController.selectedCity.value,
          newProfileController.selectedRegion.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  simpleMyAppBar(
                    title: "Profile".trr,
                    onTap: () async {
                      SharedPreferences sharedProfileDetails =
                          await SharedPreferences.getInstance();
                      SharedPreferences sharedProfileDetail =
                          await SharedPreferences.getInstance();
                      await sharedProfileDetails.clear();
                      await sharedProfileDetail.clear();
                      Get.back();
                    },
                    backString: "Back".trr,
                  ),
                ],
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: Get.width,
                            padding: EdgeInsets.only(
                                right: 24, left: 24, bottom: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Registration Type".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.registrationType
                                      .toString(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                                verticalSpace(12),
                                Text(
                                  "Registration date".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.registrationDate
                                      .toString(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                                verticalSpace(12),
                                Text(
                                  "Account Type".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.accountType.toString(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                                verticalSpace(12),
                                if (newProfileController.regType.value == "C")
                                  Text(
                                    "Company Type".trr,
                                    style: pBold16.copyWith(
                                      color: AppColor.cDarkGreyFont,
                                    ),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  Text(
                                    newProfileController.companyType.toString(),
                                    style: pBold12.copyWith(
                                      color: AppColor.cFont,
                                    ),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(12),
                                if (newProfileController.regType.value == "C")
                                  Text(
                                    "Expro".trr,
                                    style: pBold16.copyWith(
                                      color: AppColor.cDarkGreyFont,
                                    ),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  Text(
                                    newProfileController.expro.toString(),
                                    style: pBold12.copyWith(
                                      color: AppColor.cFont,
                                    ),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(12),
                                Text(
                                  "Customer ID".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.customerID.toString(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                                verticalSpace(12),
                                Text(
                                  "Email ID".trr,
                                  style: pBold16.copyWith(
                                    color: AppColor.cDarkGreyFont,
                                  ),
                                ),
                                Text(
                                  newProfileController.emailID.toLowerCase(),
                                  style: pBold12.copyWith(
                                    color: AppColor.cFont,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // dataHeaderWidget(
                          //   registrationType: newProfileController
                          //       .registrationType
                          //       .toString(),
                          //   registrationDate: newProfileController
                          //       .registrationDate
                          //       .toString(),
                          //   accountType:
                          //       newProfileController.accountType.toString(),
                          //   companyType:
                          //       newProfileController.companyType.toString(),
                          //   expro: newProfileController.expro.toString(),
                          //   customerID:
                          //       newProfileController.customerID.toString(),
                          //   emailID:
                          //       newProfileController.emailID.toLowerCase(),
                          // ),
                          Container(
                            width: Get.width,
                            padding: EdgeInsets.only(
                                right: 24, left: 24, bottom: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "General Info".trr,
                                  style: pBold20.copyWith(
                                    color: AppColor.cText,
                                  ),
                                ),
                                verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .englishCompanyController,
                                    labelText: '${"English Company Name".trr}*',
                                    hintText: 'English Company Name'.trr,
                                    maxLength: 55,
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'English Company name'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .arabicCompanyController,
                                    labelText: '${"Arabic Company Name".trr}*',
                                    hintText: 'Arabic Company Name'.trr,
                                    maxLength: 55,
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Arabic Company name'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .contactPersonController,
                                    labelText: '${'Contact Person'.trr}*',
                                    hintText: 'Contact Person'.trr,
                                    maxLength: 50,
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Contact Person'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .designationController,
                                    labelText: '${'Designation'.trr}*',
                                    hintText: 'Designation'.trr,
                                    maxLength: 40,
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Designation'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .commercialController,
                                    readOnly: newProfileController
                                            .commercialController.text.isEmpty
                                        ? false
                                        : true,
                                    fillColor: newProfileController
                                            .commercialController.text.isEmpty
                                        ? AppColor.cWhite
                                        : AppColor.cFilled,
                                    filled: newProfileController
                                            .commercialController.text.isEmpty
                                        ? false
                                        : true,
                                    labelText:
                                        '${'Commercial Register No'.trr}*',
                                    hintText: 'Commercial Register No'.trr,
                                    maxLength: 10,
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            signed: true, decimal: true),
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.isVerified.value ==
                                    "Y")
                                  CommonTextField(
                                    controller:
                                        newProfileController.vatController,
                                    readOnly: true,
                                    fillColor: AppColor.cFilled,
                                    filled: true,
                                    labelText: 'VAT No'.trr,
                                    hintText: 'VAT No'.trr,
                                  ),
                                if (newProfileController.isVerified.value !=
                                    "Y")
                                  CommonTextField(
                                    controller:
                                        newProfileController.vatController,
                                    labelText: 'VAT No'.trr,
                                    hintText: 'VAT No'.trr,
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            signed: true, decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    maxLength: 15,
                                    onChanged: (value) {
                                      setState(() {
                                        newProfileController.isVat.value =
                                            value;
                                      });
                                    },
                                  ),

                                verticalSpace(16),
                                Text(
                                  '${"Salesman".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                          .selectedSalesmen.value.isEmpty
                                      ? "00000"
                                      : newProfileController
                                          .selectedSalesmen.value,
                                  items: addressLoadController.salesmenList
                                      .map((data) {
                                    print("=============selectedSalesmen");
                                    print("data.TYPECODE");
                                    print(
                                        "newProfileController.selectedSalesmen");
                                    print(data.TYPECODE);
                                    print(data.TYPEDESC);
                                    print("=============selectedSalesmen");
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: newProfileController
                                              .selectedSalesmen.value ==
                                          "00000"
                                      ? (value) {
                                          newProfileController.selectedSalesmen
                                              .value = value.toString();
                                          newProfileController
                                              .salesmanController
                                              .text = value.toString();
                                        }
                                      : null,
                                  validator: (value) {
                                    return Validator.validateRequired(
                                        value.toString());
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'Salesman'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .companyTelController,
                                    labelText: '${"Company Tel".trr}*',
                                    hintText: 'Company Tel'.trr,
                                    maxLength: 9,
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            signed: true, decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Company Tel'.trr);
                                    },
                                  ),
                                if (newProfileController.regType.value == "C")
                                  verticalSpace(16),
                                if (newProfileController.regType.value == "C")
                                  CommonTextField(
                                    controller: newProfileController
                                        .companyFaxController,
                                    labelText: '${"Company Fax".trr}*',
                                    hintText: 'Company Fax'.trr,
                                    maxLength: 9,
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            signed: true, decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    validator: (value) {
                                      return Validator.validateName(
                                          value, 'Company Fax'.trr);
                                    },
                                  ),
                                verticalSpace(16),
                                // if (newProfileController.isVerified.value !=
                                //     "Y")
                                if (newProfileController
                                    .vatController.text.isNotEmpty)
                                  Text(
                                    "VAT No Document *",
                                    style: pRegular12,
                                  ),
                                // if (newProfileController.isVerified.value !=
                                //     "Y")
                                if (newProfileController
                                    .vatController.text.isNotEmpty)
                                  verticalSpace(6),
                                // if (newProfileController.isVerified.value !=
                                //     "Y")
                                if (newProfileController
                                    .vatController.text.isNotEmpty)
                                  GestureDetector(
                                    child: Container(
                                      height: 44,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                              color: AppColor.cBorder)),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 6,
                                      ),
                                      child: Row(
                                        children: [
                                          Container(
                                            height: 32,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              color: AppColor.lightBlueColor,
                                            ),
                                            padding: EdgeInsets.only(
                                                right: 12, left: 8),
                                            child: Row(
                                              children: [
                                                assetSvdImageWidget(
                                                  image: DefaultImages.fileIcn,
                                                ),
                                                horizontalSpace(8),
                                                Text(
                                                  "Choose file".trr,
                                                  style: pRegular14,
                                                ),
                                              ],
                                            ),
                                          ),
                                          horizontalSpace(8),
                                          Expanded(
                                            child: Text(
                                              newProfileController
                                                      .vatDocument.isEmpty
                                                  ? "No file chosen".trr
                                                  : newProfileController
                                                      .vatDocument.value
                                                      .split("/")
                                                      .last,
                                              style: pRegular14.copyWith(
                                                color: AppColor.cDarkGreyFont,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    onTap: () async {
                                      //refundsController.companyReg.value =
                                      //await refundsController.pickImage(ImageSource.gallery);
                                      vatDocx =
                                          await FilePicker.platform.pickFiles(
                                        type: FileType.custom,
                                        allowedExtensions: [
                                          'jpg',
                                          'pdf',
                                          'doc'
                                        ],
                                        withReadStream: true,
                                      );
                                      if (vatDocx == null) {
                                        print("No file selected");
                                      } else {
                                        setState(() {
                                          for (var element in vatDocx!.files) {
                                            print(element.name);
                                            newProfileController.vatDocument
                                                .value = element.name;
                                          }
                                        });
                                      }
                                    },
                                  ),
                                // if (newProfileController.isVerified.value !=
                                //     "Y")
                                if (newProfileController
                                    .vatController.text.isNotEmpty)
                                  verticalSpace(16),
                                // if (newProfileController.isVerified.value !=
                                //     "Y")
                                if (newProfileController
                                    .vatController.text.isNotEmpty)
                                  Text(
                                    "ID Document *",
                                    style: pRegular12,
                                  ),
                                // if (newProfileController.isVerified.value !=
                                //     "Y")
                                if (newProfileController
                                    .vatController.text.isNotEmpty)
                                  verticalSpace(6),
                                // if (newProfileController.isVerified.value !=
                                //     "Y")
                                if (newProfileController
                                    .vatController.text.isNotEmpty)
                                  GestureDetector(
                                    child: Container(
                                      height: 44,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                              color: AppColor.cBorder)),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 6,
                                      ),
                                      child: Row(
                                        children: [
                                          Container(
                                            height: 32,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              color: AppColor.lightBlueColor,
                                            ),
                                            padding: EdgeInsets.only(
                                                right: 12, left: 8),
                                            child: Row(
                                              children: [
                                                assetSvdImageWidget(
                                                  image: DefaultImages.fileIcn,
                                                ),
                                                horizontalSpace(8),
                                                Text(
                                                  "Choose file".trr,
                                                  style: pRegular14,
                                                ),
                                              ],
                                            ),
                                          ),
                                          horizontalSpace(8),
                                          Expanded(
                                            child: Text(
                                              newProfileController
                                                      .idDocument.isEmpty
                                                  ? "No file chosen".trr
                                                  : newProfileController
                                                      .idDocument.value
                                                      .split("/")
                                                      .last,
                                              style: pRegular14.copyWith(
                                                color: AppColor.cDarkGreyFont,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    onTap: () async {
                                      //refundsController.companyReg.value =
                                      //await refundsController.pickImage(ImageSource.gallery);
                                      idDocx =
                                          await FilePicker.platform.pickFiles(
                                        type: FileType.custom,
                                        allowedExtensions: [
                                          'jpg',
                                          'pdf',
                                          'doc'
                                        ],
                                        withReadStream: true,
                                      );
                                      if (idDocx == null) {
                                        print("No file selected");
                                      } else {
                                        setState(() {
                                          for (var element in idDocx!.files) {
                                            print(element.name);
                                            newProfileController.idDocument
                                                .value = element.name;
                                          }
                                        });
                                      }
                                    },
                                  ),
                              ],
                            ),
                          ),
                          Container(
                            width: Get.width,
                            padding: EdgeInsets.only(
                                right: 24, left: 24, bottom: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Address".trr,
                                  style: pBold20.copyWith(
                                    color: AppColor.cText,
                                  ),
                                ),
                                verticalSpace(16),
                                // CommonTextField(
                                //   controller:
                                //       newProfileController.countryController,
                                //   readOnly: true,
                                //   fillColor: AppColor.cFilled,
                                //   filled: true,
                                //   labelText: '${"Country".trr}*',
                                // ),
                                Text(
                                  '${"Country".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                          .selectedCountry.value.isEmpty
                                      ? "SA"
                                      : newProfileController
                                          .selectedCountry.value,
                                  items: addressLoadController.cntryList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    print("COUNTRY >>>> $value");
                                    newProfileController.selectedCountry.value =
                                        value.toString();
                                    newProfileController.countryController
                                        .text = value.toString();
                                  },
                                  validator: (value) {
                                    return Validator.validateRequired(
                                        value.toString());
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'Country'.trr,
                                    // newProfileController
                                    //             .selectedRegion.value ==
                                    //         ''
                                    //     ? 'Region'.trr
                                    //     : newProfileController
                                    //         .selectedRegion.value,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                Text(
                                  '${"Region".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                          .selectedRegion.value.isEmpty
                                      ? null
                                      : newProfileController
                                          .selectedRegion.value,
                                  items: addressLoadController.provList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    newProfileController.selectedRegion.value =
                                        value.toString();
                                    newProfileController.regionController.text =
                                        value.toString();

                                    // setState(() {
                                    //   newProfileController.selectedCity.value =
                                    //       "";
                                    //   newProfileController
                                    //       .selectedDistrict.value = "";
                                    // addressLoadController
                                    //     .fetchCityData(value.toString());
                                    // });
                                  },
                                  validator: (value) {
                                    return Validator.validateRequired(
                                        value.toString());
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'Region'.trr,
                                    // newProfileController
                                    //             .selectedRegion.value ==
                                    //         ''
                                    //     ? 'Region'.trr
                                    //     : newProfileController
                                    //         .selectedRegion.value,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                Text(
                                  '${"City".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                          .selectedCity.value.isEmpty
                                      ? null
                                      : newProfileController.selectedCity.value,
                                  items: addressLoadController.cityList
                                      .map((data) {
                                    print("=============");
                                    print("data.TYPECODE");
                                    print(data.TYPECODE);
                                    print(data.TYPEDESC);
                                    print(newProfileController.selectedCity
                                        .toString());
                                    print("=============");
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      newProfileController.selectedCity.value =
                                          value.toString();
                                      newProfileController.cityController.text =
                                          value.toString();
                                      addressLoadController.distList.clear();
                                      addressLoadController.distList.refresh();
                                      _isSecondDropdownEnabled = true;
                                      addressLoadController.fetchDistData(
                                          value.toString(),
                                          newProfileController
                                              .selectedRegion.value);
                                    });
                                    // setState(() {
                                    //   newProfileController
                                    //       .selectedDistrict.value = "";
                                    // addressLoadController.fetchDistData(
                                    //     value.toString(),
                                    //     newProfileController
                                    //         .regionController.text);
                                    // });
                                  },
                                  validator: (value) {
                                    return Validator.validateRequired(
                                        value.toString());
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'City'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                Text(
                                  '${"District".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                          .selectedDistrict.value.isEmpty
                                      ? null
                                      : newProfileController
                                          .selectedDistrict.value,
                                  items: addressLoadController.distList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: _isSecondDropdownEnabled
                                      ? (value) {
                                          newProfileController.selectedDistrict
                                              .value = value.toString();
                                          newProfileController
                                              .districtController
                                              .text = value.toString();
                                        }
                                      : null,
                                  validator: (value) {
                                    return Validator.validateRequired(
                                        value.toString());
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'District'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.streetController,
                                  labelText: '${"Street".trr}*',
                                  hintText: 'Street'.trr,
                                  maxLength: 24,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Street'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.buildingNoController,
                                  labelText: '${'Building No.'.trr}*',
                                  hintText: 'Building No.'.trr,
                                  keyboardType: TextInputType.numberWithOptions(
                                      signed: true, decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  maxLength: 4,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Building No.'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.postalCodeController,
                                  labelText: '${'Postal Code'.trr}*',
                                  hintText: 'Postal Code'.trr,
                                  keyboardType: TextInputType.numberWithOptions(
                                      signed: true, decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  maxLength: 5,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Postal Code'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.poBoxController,
                                  labelText: '${'PO Box'.trr}*',
                                  hintText: 'PO Box'.trr,
                                  keyboardType: TextInputType.numberWithOptions(
                                      signed: true, decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'PO Box'.trr);
                                  },
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: Get.width,
                            padding: EdgeInsets.only(
                                right: 24, left: 24, bottom: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Personal Details".trr,
                                  style: pBold20.copyWith(
                                    color: AppColor.cText,
                                  ),
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.firstnameController,
                                  labelText: '${"First Name".trr}*',
                                  hintText: 'First Name'.trr,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'First Name'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.middlenameController,
                                  labelText: '${"Middle Name".trr}*',
                                  hintText: 'Middle Name'.trr,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Middle Name'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.lastnameController,
                                  labelText: '${"Last Name".trr}*',
                                  hintText: 'Last Name'.trr,
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Last Name'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                CommonTextField(
                                  controller:
                                      newProfileController.phoneNoController,
                                  labelText: '${"Phone Number".trr}*',
                                  hintText: '966530609646'.trr,
                                  keyboardType: TextInputType.numberWithOptions(
                                      signed: true, decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  validator: (value) {
                                    return Validator.validateName(
                                        value, 'Phone Number'.trr);
                                  },
                                ),
                                verticalSpace(16),
                                // CommonDropdownButtonWidget(
                                //   labelText: '${"Default Language".trr}*',
                                //   list: newProfileController.languages,
                                //   value: newProfileController
                                //       .selectedLanguage.value,
                                //   onChanged: (value) {
                                //     newProfileController
                                //         .selectedLanguage.value = value;
                                //   },
                                // ),
                                Text(
                                  '${"Default Language".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                          .selectedLanguage.value.isEmpty
                                      ? "E"
                                      : newProfileController
                                          .selectedLanguage.value,
                                  items: addressLoadController.langList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    newProfileController.selectedLanguage
                                        .value = value.toString();
                                    newProfileController.langController.text =
                                        value.toString();
                                  },
                                  validator: (value) {
                                    return Validator.validateRequired(
                                        value.toString());
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'Default Language'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                                verticalSpace(16),
                                // CommonTextField(
                                //   readOnly: true,
                                //   fillColor: AppColor.cFilled,
                                //   filled: true,
                                //   labelText: "How did you know about WAIE?".trr,
                                // ),
                                Text(
                                  '${"How did you know about WAIE?".trr}*',
                                  style: pRegular13,
                                ),
                                verticalSpace(10),
                                DropdownButtonFormField(
                                  isExpanded: true,
                                  value: newProfileController
                                          .selectedWaie.value.isEmpty
                                      ? "A"
                                      : newProfileController.selectedWaie.value,
                                  items: addressLoadController.waieList
                                      .map((data) {
                                    return DropdownMenuItem(
                                      value: data.TYPECODE,
                                      child: Text(
                                        data.TYPEDESC,
                                        style: pMedium12,
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    newProfileController.selectedWaie.value =
                                        value.toString();
                                    newProfileController.serviceKnownController
                                        .text = value.toString();
                                  },
                                  validator: (value) {
                                    return Validator.validateRequired(
                                        value.toString());
                                  },
                                  style: pRegular14.copyWith(
                                      color: AppColor.cLabel),
                                  borderRadius: BorderRadius.circular(6),
                                  dropdownColor: AppColor.cLightGrey,
                                  icon: assetSvdImageWidget(
                                      image: DefaultImages.dropDownIcn),
                                  decoration: InputDecoration(
                                    hintText: 'How did you know about WAIE?'.trr,
                                    hintStyle: pRegular14.copyWith(
                                        color: AppColor.cHintFont),
                                    contentPadding:
                                        EdgeInsets.only(left: 16, right: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: BorderSide(
                                        color: AppColor.cBorder,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: CommonButton(
                title: 'Cancel'.trr,
                onPressed: () {
                  Get.back();
                },
                textColor: AppColor.cText,
                btnColor: AppColor.cBackGround,
              ),
            ),
            horizontalSpace(16),
            Expanded(
              child: CommonButton(
                title: 'Save'.trr,
                onPressed: () {
                  if (newProfileController.vatNoController.text != "") {
                    /*commonToast(
                          'FIRST NAME/MIDDLE NAME/LAST NAME SHOULD BE ARABIC!');*/
                    if (vatDocx == null) {
                      commonToast('Please Upload VAT No Document');
                    } else if (idDocx == null) {
                      commonToast('Please Upload ID Document');
                    } else {
                      PlatformFile invVatdocx = vatDocx!.files.first;
                      PlatformFile invIddocx = idDocx!.files.first;
                      newProfileController.updateProfile(invVatdocx, invIddocx);
                    }
                  } else {
                    showDialog(
                      barrierDismissible: false,
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          contentPadding: EdgeInsets.all(24),
                          insetPadding: EdgeInsets.all(16),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  GestureDetector(
                                      onTap: () {
                                        Get.back();
                                      },
                                      child: assetSvdImageWidget(
                                          image: DefaultImages.cancelIcn)),
                                ],
                              ),
                              verticalSpace(24),
                              Center(
                                child: Text("Are you sure you want proceed?".trr,
                                    style: pBold20,
                                    textAlign: TextAlign.center),
                              ),
                              verticalSpace(34),
                              Row(
                                children: [
                                  Expanded(
                                    child: CommonButton(
                                      title: "No".trr,
                                      onPressed: () {
                                        Get.back();
                                      },
                                      textColor: AppColor.cDarkBlueFont,
                                      btnColor: AppColor.cBackGround,
                                      bColor: AppColor.cDarkBlueFont,
                                    ),
                                  ),
                                  horizontalSpace(16),
                                  Expanded(
                                    child: CommonButton(
                                      title: "Yes".trr,
                                      onPressed: () {
                                        newProfileController
                                            .updateProfileNoVat();
                                      },
                                      textColor: AppColor.cWhiteFont,
                                      btnColor: AppColor.themeOrangeColor,
                                      bColor: AppColor.cTransparent,
                                      horizontalPadding: 16,
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                        );
                      },
                    );
                  }
                },
                textColor: AppColor.cWhiteFont,
                btnColor: AppColor.themeOrangeColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget dataHeaderWidget({
  String? registrationType,
  String? registrationDate,
  String? accountType,
  String? companyType,
  String? expro,
  String? customerID,
  String? emailID,
}) {
  return Container(
    width: Get.width,
    padding: EdgeInsets.only(right: 24, left: 24, bottom: 24),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Registration Type".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          registrationType!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Registration date".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          registrationDate!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Account Type".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          accountType!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Company Type".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          companyType!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Expro".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          expro!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Customer ID".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          customerID!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
        verticalSpace(12),
        Text(
          "Email ID".trr,
          style: pBold17.copyWith(
            color: AppColor.cText,
          ),
        ),
        Text(
          emailID!,
          style: pBold12.copyWith(
            color: AppColor.cFont,
          ),
        ),
      ],
    ),
  );
}
