// ignore_for_file: prefer_const_constructors, prefer_const_constructors_in_immutables

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/core/controller/location_controller/location_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/location_screen/installation_center_screen.dart';
import 'package:waie_app/view/screen/location_screen/sales_office_screen.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_order_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../../../core/controller/location_controller/gas_station_controller.dart';
import '../../../core/controller/location_controller/installation_center_controller.dart';
import '../../../core/controller/location_controller/sales_office_controller.dart';
import 'gase_station_screen.dart';

class SearchLocationWidget extends StatefulWidget {
  String isActive;
  SearchLocationWidget({
    super.key,
    required this.isActive,
  });

  @override
  State<SearchLocationWidget> createState() => _SearchLocationWidgetState();
}

class _SearchLocationWidgetState extends State<SearchLocationWidget> {
  LocationController locationController = Get.find();
  GasStationController gasStationController = Get.put(GasStationController());
  SalesOfficeController salesOfficeController =
      Get.put(SalesOfficeController());
  InstallationCenterController installationCenterController =
      Get.put(InstallationCenterController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    locationController.itemList.clear();
  }

  void filterSearchResults(String query) {
    widget.isActive == "isGasStations"
        ? locationController.itemList.value = gasStationController.gasStationList
            .where((item) =>
                item.placeDesc.toLowerCase().contains(query.toLowerCase()))
            .toList()
        : widget.isActive == "isSalesOffice"
            ? locationController.itemList.value = salesOfficeController
                .salesStationList
                .where((item) =>
                    item.placeDesc.toLowerCase().contains(query.toLowerCase()))
                .toList()
            : widget.isActive == "isInstallationCenters"
                ? locationController.itemList.value =
                    installationCenterController.instCenterList
                        .where((item) => item.placeDesc
                            .toLowerCase()
                            .contains(query.toLowerCase()))
                        .toList()
                : locationController.itemList.value = locationController
                    .listData
                    .where((item) =>
                        item['data'].toLowerCase().contains(query.toLowerCase()))
                    .toList();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        height: Get.height - 60,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        padding: EdgeInsets.all(16),
        child: Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              verticalSpace(16),
              Row(
                children: [
                  Expanded(
                    child: CommonTextField(
                      controller: locationController.searchController.value,
                      labelText: '',
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(12),
                        child: assetSvdImageWidget(
                            image: DefaultImages.searchIcn,
                            width: 24,
                            height: 24),
                      ),
                      hintText: 'Search'.trr,
                      onChanged: (value) {
                        if (value.isEmpty) {
                          locationController.itemList.clear();
                          locationController.itemList.refresh();
                        } else {
                          locationController.searchController.refresh();
                          filterSearchResults(value);
                        }
                      },
                    ),
                  ),
                  locationController.searchController.value.text.isEmpty
                      ? SizedBox()
                      : cancelButton(
                          () {
                            locationController.searchController.value.clear();
                            locationController.searchController.refresh();
                            locationController.itemList.clear();
                            locationController.itemList.refresh();
                          },
                        )
                ],
              ),
              verticalSpace(16),
              locationController.itemList.isEmpty
                  ? Expanded(
                      child: Center(
                          child: Text(
                      "No matches".trr,
                      style:
                          pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                    )))
                  : Expanded(
                      child: ListView.builder(
                        itemCount: locationController.itemList.length,
                        scrollDirection: Axis.vertical,
                        shrinkWrap: true,
                        physics: BouncingScrollPhysics(),
                        itemBuilder: (context, index) {
                          var data = locationController.itemList[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 22),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                widget.isActive == "isGasStations"
                                    ? Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 8.0),
                                        child: GestureDetector(
                                          onTap: () {
                                            showModalBottomSheet(
                                              context: context,
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.vertical(
                                                          top: Radius.circular(
                                                              12))),
                                              isScrollControlled: true,
                                              builder: (context) {
                                                return gasStationsBottomSheetWidget(
                                                  title: data.placeDesc,
                                                  subTitle: data.stationName,
                                                  status: data.stationStatus,
                                                  products: data.products,
                                                  latitude: double.parse(
                                                      data.latitude),
                                                  longitude: double.parse(
                                                      data.longitude),
                                                  coordinates:
                                                      data.stationCoordinates,
                                                );
                                              },
                                            );
                                          },
                                          child: listDataContainer(
                                            title: data.placeDesc,
                                            subTitle: data.stationName,
                                          ),
                                        ),
                                      )
                                    : widget.isActive == "isSalesOffice"
                                        ? Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 8.0),
                                            child: GestureDetector(
                                              onTap: () {
                                                showModalBottomSheet(
                                                  context: context,
                                                  shape: const RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.vertical(
                                                              top: Radius
                                                                  .circular(
                                                                      12))),
                                                  isScrollControlled: true,
                                                  builder: (context) {
                                                    return salesStationsBottomSheetWidget(
                                                      title: data.placeDesc,
                                                      subTitle: data.officeDesc,
                                                      status: data.offStatus ==
                                                              "Yes"
                                                          ? "Active"
                                                          : "Inactive",
                                                      latitude: double.parse(
                                                          data.latitude),
                                                      longitude: double.parse(
                                                          data.longitude),
                                                      coordinates:
                                                          "https://www.google.com/maps/search/${double.parse(data.latitude)},${double.parse(data.longitude)}",
                                                    );
                                                  },
                                                );
                                              },
                                              child: listDataContainer(
                                                title: data.placeDesc,
                                                subTitle: data.officeDesc,
                                              ),
                                            ),
                                          )
                                        : widget.isActive ==
                                                "isInstallationCenters"
                                            ? Padding(
                                                padding: const EdgeInsets.only(
                                                    bottom: 8.0),
                                                child: GestureDetector(
                                                  onTap: () {
                                                    showModalBottomSheet(
                                                      context: context,
                                                      shape: const RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius.vertical(
                                                                  top: Radius
                                                                      .circular(
                                                                          12))),
                                                      isScrollControlled: true,
                                                      builder: (context) {
                                                        return instCenterBottomSheetWidget(
                                                          title: data.placeDesc,
                                                          subTitle:
                                                              data.stationName,
                                                          status:
                                                              data.stationStatus ==
                                                                      "Yes"
                                                                  ? "Active"
                                                                  : "Inactive",
                                                          latitude:
                                                              double.parse(data
                                                                  .latitude),
                                                          longitude:
                                                              double.parse(data
                                                                  .longitude),
                                                          coordinates:
                                                              "https://www.google.com/maps/search/${double.parse(data.latitude)},${double.parse(data.longitude)}",
                                                        );
                                                      },
                                                    );
                                                  },
                                                  child: listDataContainer(
                                                    title: data.placeDesc,
                                                    subTitle: data.stationName,
                                                  ),
                                                ),
                                              )
                                            : SizedBox(),
                                // Text(
                                //   data['data'],
                                //   style: pBold20,
                                // ),
                                // verticalSpace(11),
                                // ListView.builder(
                                //   itemBuilder: (context, i) {
                                //     var subData = data['list'][i];
                                //     return Padding(
                                //       padding:
                                //           const EdgeInsets.only(bottom: 8.0),
                                //       child: GestureDetector(
                                //         onTap: () {
                                //           showModalBottomSheet(
                                //             context: context,
                                //             shape: RoundedRectangleBorder(
                                //                 borderRadius:
                                //                     BorderRadius.vertical(
                                //                         top: Radius.circular(
                                //                             12))),
                                //             isScrollControlled: true,
                                //             builder: (context) {
                                //               return locationBottomSheetWidget(
                                //                 title: data['data'],
                                //                 subTitle: subData['title'],
                                //                 openNow: "Yes",
                                //                 workingHour: "24/7",
                                //                 phoneNo: "966920002667",
                                //                 latitude: 21.44484793949768,
                                //                 longitude: 53.295109691390245,
                                //               );
                                //             },
                                //           );
                                //         },
                                //         child: listDataContainer(
                                //           title: subData['title'],
                                //           subTitle: subData['subTitle'],
                                //         ),
                                //       ),
                                //     );
                                //   },
                                //   itemCount: data['list'].length,
                                //   shrinkWrap: true,
                                //   physics: NeverScrollableScrollPhysics(),
                                // )
                              ],
                            ),
                          );
                        },
                      ),
                    ),
            ],
          );
        }),
      ),
    );
  }
}
