import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/appointment_data.dart';
import 'package:waie_app/models/load_data.dart';

import '../../../utils/api_endpoints.dart';
import '../../../utils/constants.dart';

class BookTagInstallationController extends GetxController {
  // final List OrderList = ['1000001365','1000001365','1000001365'].obs;
  RxString selectedOrder = ''.obs;
  List placeList = [''];
  RxString selectedPlace = ''.obs;
  List centerList = [''];
  RxString selectedCenter = ''.obs;
  List vehicleTypeList = [''];
  RxString selectedvehicleType = ''.obs;
  List tankTypeList = [''];
  RxString selectedTankType = ''.obs;
  final OrderList = <Load_Data_Model>[].obs;
  List<Load_Data_Model> GetOrderList = [];
  List<Appointment_Data> AppointmentStatusList = [];

  RxString OrderQTYobs=''.obs;
  RxString OrderRemainingobs=''.obs;
  RxString OrderWaitingobs=''.obs;
  RxString OrderAppointedobs=''.obs;

  final TextEditingController WaitingtextController = TextEditingController();
  final TextEditingController datePickerController = TextEditingController();


  @override
  void onInit() {
    super.onInit();
    print('DataLoadController');
    fetchOrderList();
    // fetchCityData("");
    // fetchDistrictData("");
  }

  Future<dynamic> fetchOrderList() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');

    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadOrderList),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
          body: {"custid": userid.toString() });
          //body: {"custid": "000000054A"});
      List result = jsonDecode(response.body);

      print("ORDER LIST RESULT========" + result.toString());

      for (int i = 0; i < result.length; i++) {
        Load_Data_Model loadData =
            Load_Data_Model.fromMap(result[i] as Map<String, dynamic>);
        GetOrderList.add(loadData);
        print("ORDER LIST ===============${loadData.TYPEDESC}");
      }
      OrderList.value = GetOrderList;
      return GetOrderList;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      client.close();
    }
  }

  Future<dynamic> fetchSelectOrderDetails() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');

    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadSelectedOrderList),
         // body: {"custid": "000000054A", "orderid": "2300014"});
          body: {"custid": "000000054A", "orderid": "2300014"});
      List result = jsonDecode(response.body);

      print("ORDER Deytail LIST RESULT========" + result.toString());

      for (int i = 0; i < result.length; i++) {
        Appointment_Data loadData =
        Appointment_Data.fromMap(result[i] as Map<String, dynamic>);
        AppointmentStatusList.add(loadData);
        OrderAppointedobs.value=loadData.APPOINTEDQTY.toString();
        OrderQTYobs.value=loadData.ORDEREDQTY.toString();
        OrderWaitingobs.value=loadData.WAITINGFORUPDATE.toString();
        OrderRemainingobs.value=loadData.REMAININGQY.toString();
        WaitingtextController.text=loadData.WAITINGFORUPDATE.toString() ;
        //_textController.value=loadData.REMAININGQY.toString();
      }

      return AppointmentStatusList;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      client.close();
    }
  }

  Future<dynamic> LoadPlaces() async {
    String dateStr = datePickerController.text;
    DateTime dateObj = DateTime.parse(dateStr);
    print(dateObj.day);
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadSelectedOrderList),
          body: {"selectedday": dateObj.day, "IsAR": Constants.IsAr_App});
      List result = jsonDecode(response.body);

      print("ORDER Deytail LIST RESULT========" + result.toString());

      for (int i = 0; i < result.length; i++) {
        Appointment_Data loadData =
        Appointment_Data.fromMap(result[i] as Map<String, dynamic>);
        AppointmentStatusList.add(loadData);
        OrderAppointedobs.value=loadData.APPOINTEDQTY.toString();
        OrderQTYobs.value=loadData.ORDEREDQTY.toString();
        OrderWaitingobs.value=loadData.WAITINGFORUPDATE.toString();
        OrderRemainingobs.value=loadData.REMAININGQY.toString();
        WaitingtextController.text=loadData.WAITINGFORUPDATE.toString() ;
        //_textController.value=loadData.REMAININGQY.toString();
      }

      return AppointmentStatusList;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      client.close();
    }
  }

  Future<dynamic> LoadPlaceCenter() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadSelectedOrderList),
          body: {"selcteddate": datePickerController.value, "IsAR": Constants.IsAr_App});
      List result = jsonDecode(response.body);

      print("ORDER Deytail LIST RESULT========" + result.toString());

      for (int i = 0; i < result.length; i++) {
        Appointment_Data loadData =
        Appointment_Data.fromMap(result[i] as Map<String, dynamic>);
        AppointmentStatusList.add(loadData);
        OrderAppointedobs.value=loadData.APPOINTEDQTY.toString();
        OrderQTYobs.value=loadData.ORDEREDQTY.toString();
        OrderWaitingobs.value=loadData.WAITINGFORUPDATE.toString();
        OrderRemainingobs.value=loadData.REMAININGQY.toString();
        WaitingtextController.text=loadData.WAITINGFORUPDATE.toString() ;
        //_textController.value=loadData.REMAININGQY.toString();
      }

      return AppointmentStatusList;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      client.close();
    }
  }
}
