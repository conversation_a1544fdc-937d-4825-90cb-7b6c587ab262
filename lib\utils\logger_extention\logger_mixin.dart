import 'logger_extension.dart'; // Import the logger extension.

/// A mixin to add logging capabilities to Flutter Widgets.
mixin LoggerMixin {
  void logInfo(String message) {
    runtimeType.logInfo(message); // Automatically uses the class name of the widget.
  }

  void logError(String message) {
    runtimeType.logError(message); // Automatically uses the class name of the widget.
  }

  void logWarning(String message) {
    runtimeType.logWarning(message); // Automatically uses the class name of the widget.
  }

  void logSuccess(String message) {
    runtimeType.logSuccess(message); // Automatically uses the class name of the widget.
  }
}
