import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/models/tag_transfer_company_detail.dart';
import 'package:waie_app/models/tag_transfer_vehicle_detail.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/incoming_tags_transfer_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/view_tag_transfer_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class ViewTagsTransferController extends GetxController {
  VehicleController vehicleController = Get.put(VehicleController());
  final vehicle = GetStorage();
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  RxList tagTransferVehicelLists = [].obs;

  getDatas(code) async {
    // pop the loading circle
    Navigator.of(Get.context!).pop();
    tagTransferVehicelLists = [].obs;
    await fetchTagTransferVehicleLists(code);
    if (tagTransferVehicelLists.isNotEmpty) {
      showModalBottomSheet(
        isDismissible: false,
        context: Get.context!,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        backgroundColor: AppColor.cBackGround,
        barrierColor: AppColor.cBlackOpacity,
        isScrollControlled: true,
        builder: (context) {
          return ViewTagTransferWidget();
        },
      );
    } else {
      commonToast("No Records");
    }
  }

  Future<dynamic> fetchTagTransferVehicleLists(reqid) async {
    var custid = userStorage.read('custid');
    var client = http.Client();
    print("custID ================== $custid");
    print("reqid ================== $reqid");
    print("PASOK1 ViewTagsTransferController");

    try {
      print("PASOK1 ViewTagsTransferController");
      var reasonResponse = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getTagTransferVehicleLists),
          body: {
            "custid": custid, //custid
            "top": "0",
            "skip": "0",
            "reqid": reqid,
          });
      print("PASOK2");
      List result = jsonDecode(reasonResponse.body);
      print("===============================================================");
      print("result >>>>> $result");
      print("reasonResponseBody >>>>> ${reasonResponse.body}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        TagTransferVehicleDetailModel vehicleInfo =
            TagTransferVehicleDetailModel.fromMap(
                result[i] as Map<String, dynamic>);
        tagTransferVehicelLists.add(vehicleInfo);
      }

      return tagTransferVehicelLists;
    } catch (e) {
      log(e.toString());
      print("e.toString() >>>>> ${e.toString()}");
      print('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
