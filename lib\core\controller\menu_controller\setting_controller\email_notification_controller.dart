// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/load_noty.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../../utils/api_endpoints.dart';

class EmailNotificationController extends GetxController {
  RxBool notiForOpenStn = false.obs;
  RxBool notiForTempClosedStn = false.obs;
  RxBool notiForTagInstallation = false.obs;
  RxBool notiForClosedStn = false.obs;
  RxList emailItemList = [].obs;


  /* RxBool refund = true.obs;
  RxBool fleet = true.obs;
  RxBool user = true.obs;*/

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    //fetchCustNotyList();
    getNotiList();
  }

  getNotiList() async {
    await fetchCustNotyList();
  }

  Future<dynamic> fetchCustNotyList() async {
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    var userid = sharedUser.getString('userid');
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getCustEmailNotyList),
          body: {"custid": userid});
      String notyString = jsonDecode(response.body);
      final split = notyString.split(',');

      for (String notiData in split) {
        if (notiData == "OPSTN") {
          notiForOpenStn.value = true;
        }
        if (notiData == "TMPCSTN") {
          notiForTempClosedStn.value = true;
        }
        if (notiData == "CSTN") {
          notiForClosedStn.value = true;
        }
        if (notiData == "ETIS") {
          notiForTagInstallation.value = true;
        }
        await Future.delayed(
            const Duration(seconds: 0), () => emailItemList.add(notiData));
      }
      print("Load Email Noti List==========" + response.body);

      //"OPSTN,TMPCSTN,CSTN,ETIS"
    } catch (e) {
      log("Email Noty List Error  : " + e.toString());
    }
  }

  void updateCustNoty() async {
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    var userid = sharedUser.getString('userid');
    Loader.showLoader();
    String notiData = "";
    if (notiForOpenStn.value) {
      notiData += 'OPSTN,';
    }
    if (notiForTempClosedStn.value) {
      notiData += 'TMPCSTN,';
    }
    if (notiForClosedStn.value) {
      notiData += 'CSTN,';
    }
    if (notiForTagInstallation.value) {
      notiData += 'ETIS';
    }

    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.updateCustNoty),
          body: {"custid": userid,
          "exNoty":notiData,
          "isAr":"false"});
      print("Load Email Noti List==========" +response.body.toString());
      var notyString = jsonDecode(response.body);
      Loader.hideLoader();
    } catch (e) {
      log("Email Noty Update Error  : " + e.toString());
    }
  }
}
