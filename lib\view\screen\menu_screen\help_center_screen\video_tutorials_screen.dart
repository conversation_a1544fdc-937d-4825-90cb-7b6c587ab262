// ignore_for_file: prefer_const_constructors_in_immutables

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/help_center_controller/help_center_controller.dart';

class VideoTutorialScreen extends StatelessWidget {
  final HelpCenterController helpCenterController;

  VideoTutorialScreen({Key? key, required this.helpCenterController}) : super(key: key);

  _launchURL() async {
    if (Platform.isIOS) {
      if (await canLaunch('youtube://www.youtube.com/channel/UCwXdFgeE9KYzlDdR7TG9cMw')) {
        await launch('youtube://www.youtube.com/channel/UCwXdFgeE9KYzlDdR7TG9cMw', forceSafariVC: false);
      } else {
        if (await canLaunch('https://www.youtube.com/channel/UCwXdFgeE9KYzlDdR7TG9cMw')) {
          await launch('https://www.youtube.com/channel/UCwXdFgeE9KYzlDdR7TG9cMw');
        } else {
          throw 'Could not launch https://www.youtube.com/channel/UCwXdFgeE9KYzlDdR7TG9cMw';
        }
      }
    } else {
      const url = 'https://www.youtube.com';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Video Tutorials".trr, style: pSemiBold17),
        verticalSpace(24),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: helpCenterController.videoList.length,
          itemBuilder: (context, index) {
            var data = helpCenterController.videoList[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: GestureDetector(
                onTap: _launchURL,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    imageWidget(data['image']),
                    verticalSpace(14),
                    Text(
                      data['title'].toString().trr,
                      style: pSemiBold16,
                    )
                  ],
                ),
              ),
            );
          },
        )
      ],
    );
  }

}
Container imageWidget(String image) {
  return Container(
    height: 194,
    width: Get.width,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(6),
      image: DecorationImage(image: AssetImage(image), fit: BoxFit.fill),
    ),
  );
}