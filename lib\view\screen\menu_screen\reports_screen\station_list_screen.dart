// ignore_for_file: prefer_const_constructors

import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/reports_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/reports_controller/station_list_controller.dart';
import '../../../widget/common_appbar_widget.dart';

class StationListScreen extends StatefulWidget {
  final String title;
  const StationListScreen({super.key, required this.title});

  @override
  State<StationListScreen> createState() => _StationListScreenState();
}

class _StationListScreenState extends State<StationListScreen> {
  StationListController stationListController = Get.put((StationListController()));
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  simpleMyAppBar(
                      title: "".trr,
                      backString: "Back".trr,
                      onTap: () {
                        Get.back();
                      },
                      backColor: AppColor.cBlueFont),
                ],
              ),
            ),
            Center(
              child: Text(
                widget.title,
                style: pBold20,
                textAlign: TextAlign.center,
              ),
            ),
            Gap(16),
            Expanded(
              child: Obx(() {
             return  ListView(
                padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                children: [
                  Text("Places".trr),
                  DropdownButtonFormField(
                    items: stationListController.reportPlacesList.map((data) {
                      return DropdownMenuItem(
                        value: data.PLACE_CODE,
                        child: Text(
                          data.PLACE_DESC,
                          style: pMedium12,
                          textAlign: TextAlign.center,
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      stationListController.selectedPlaceController.text =
                          value.toString();
                    },
                    style: pRegular14.copyWith(color: AppColor.cLabel),
                    borderRadius: BorderRadius.circular(6),
                    dropdownColor: AppColor.cLightGrey,
                    icon: assetSvdImageWidget(
                        image: DefaultImages.dropDownIcn),
                    decoration: InputDecoration(
                      hintText: 'Places'.trr,
                      hintStyle:
                      pRegular14.copyWith(color: AppColor.cHintFont),
                      contentPadding:
                      EdgeInsets.only(left: 16, right: 16),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      disabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                    ),
                  ),
                  Gap(16),
                  Text("Product".trr),
                  DropdownButtonFormField(
                    items: stationListController.reportProductsList.map((data) {
                      return DropdownMenuItem(
                        value: data.CODE,
                        child: Text(
                          data.TYPEDESC,
                          style: pMedium12,
                          textAlign: TextAlign.center,
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      stationListController.selectedProductController.text =
                          value.toString();
                    },
                    style: pRegular14.copyWith(color: AppColor.cLabel),
                    borderRadius: BorderRadius.circular(6),
                    dropdownColor: AppColor.cLightGrey,
                    icon: assetSvdImageWidget(
                        image: DefaultImages.dropDownIcn),
                    decoration: InputDecoration(
                      hintText: 'Product'.trr,
                      hintStyle:
                      pRegular14.copyWith(color: AppColor.cHintFont),
                      contentPadding:
                      EdgeInsets.only(left: 16, right: 16),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      disabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                    ),
                  ),
                  Gap(16),
                 /* CommonDropdownButtonWidget(
                    hint: '',
                    labelText: 'Sort By'.trr,
                    list: stationListController.sortByList,
                    value: stationListController.selectedsortByList.value,
                    onChanged: (value) {},
                    fontColor: AppColor.cDarkGreyFont,
                    filledColor: AppColor.cFilled,
                  ),*/
                  CommonDropdownButtonWidget(
                    hint: '',
                    labelText: 'Sort By'.trr,
                    list: stationListController.sortByList, // Your adjusted list
                    value: stationListController.selectedsortByList.value, // Currently selected value
                    onChanged: (value) {
                      if(value != null) { // Assuming onChanged provides a non-null value
                        stationListController.selectedsortByList.value = value; // Update the selected value
                      }
                    },
                    fontColor: AppColor.cDarkGreyFont,
                    filledColor: AppColor.cFilled,
                  ),
                ],
              );
    }),
            ),
          ],
        ),
      ),
     /* bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: Expanded(
          child: CommonButton(
            title: 'SUBMIT'.trr,
            onPressed: () {
              stationListController.reportRequestSubmit();
            },
            textColor: AppColor.cWhiteFont,
            btnColor: AppColor.themeOrangeColor,
          ),
        ),
      ),*/
      bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: CommonButton(  // No need for Expanded here, directly place your button.
          title: 'SUBMIT'.trr,
          onPressed: () {
            stationListController.reportRequestSubmit();
          },
          textColor: AppColor.cWhiteFont,
          btnColor: AppColor.themeOrangeColor,
        ),
      ),
    );
  }
}
