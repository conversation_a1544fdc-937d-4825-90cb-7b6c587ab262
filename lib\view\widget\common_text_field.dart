// ignore_for_file: prefer_const_constructors, avoid_print, must_be_immutable

import '../../utils/colors.dart';
import '../../utils/text_style.dart';
import 'package:flutter/material.dart';
import 'common_space_divider_widget.dart';
import 'package:flutter/services.dart';

class CommonTextField extends StatefulWidget {
  final String? labelText;
  final String? hintText;
  final int? maxLength;
  final int? maxLines;
  final String? obscuringCharacter;
  final String? initialValue;
  final Widget? prefixIcon;
  final Widget? suffix;
  final TextInputType? keyboardType;
  final Function(String v)? validator;
  final Function()? onTap;
  final ValueChanged<String>? onChanged;
  final TextEditingController? controller;
  final List<TextInputFormatter>? inputFormatters;
  bool obscureText;
  bool? filled;
  bool readOnly;
  Color? borderColor;
  Color? focusBorderColor;
  Color? fillColor;

  CommonTextField(
      {Key? key,
      this.labelText,
      this.prefixIcon,
      this.suffix,
      this.keyboardType,
      this.validator,
      this.onChanged,
      this.borderColor,
      this.focusBorderColor,
      this.filled,
      this.readOnly = false,
      this.fillColor,
      this.controller,
      this.obscureText = false,
      this.obscuringCharacter,
      this.maxLength,
      this.hintText,
      this.inputFormatters,
      this.initialValue,
      this.maxLines,
      this.onTap})
      : super(key: key);

  @override
  State<CommonTextField> createState() => _CommonTextFieldState();
}

class _CommonTextFieldState extends State<CommonTextField> {
  // void onChangedHandler(String value) {
  //   if (widget.onChanged != null) {
  //     widget.onChanged!(value);
  //   }
  // }
  String err = '';
  final FocusNode noteFocus = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        widget.labelText == ''
            ? SizedBox(
                height: 0,
              )
            : Text(
                widget.labelText!,
                style: pRegular12,
              ),
        widget.labelText == '' ? verticalSpace(0) : verticalSpace(6),
        SizedBox(
            height: widget.maxLength != null
                ? err == ''
                    ? 44
                    : 50
                : null,
            child: TextFormField(
              focusNode: noteFocus,
              controller: widget.controller,
              onTap: widget.onTap,
              onChanged: widget.onChanged,
              inputFormatters: widget.inputFormatters,
              initialValue: widget.initialValue,
              maxLines: widget.maxLines ?? 1,
              validator: (v) {
                setState(() {
                  err = widget.validator!(v!);
                });
                print("======= $err");
                if (err == '') {
                  return null;
                } else {
                  noteFocus.requestFocus();
                  return err;
                }
              },
              style: pRegular14.copyWith(color: AppColor.cLabel),
              readOnly: widget.readOnly,
              maxLength: widget.maxLength,
              keyboardType: widget.keyboardType,
              obscureText: widget.obscureText,
              cursorColor: AppColor.cHintFont,
              obscuringCharacter: widget.obscuringCharacter ?? ' ',
              textInputAction: TextInputAction.done,
              decoration: InputDecoration(
                prefixIcon: widget.prefixIcon,
                suffixIcon: widget.suffix,
                suffixIconConstraints:
                    BoxConstraints(maxWidth: 45, minWidth: 42),
                errorStyle: TextStyle(
                  height: 0,
                  fontSize: 0,
                  decorationThickness: 0,
                ),
                counterText: '',
                counterStyle: TextStyle(fontSize: 0, height: 0),
                hintText: widget.hintText,
                hintStyle: pRegular14.copyWith(color: AppColor.cHintFont),
                fillColor: widget.fillColor ?? AppColor.cBackGround,
                filled: widget.filled,
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: widget.borderColor ?? AppColor.cBorder)),
                disabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: widget.borderColor ?? AppColor.cBorder)),
                enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: widget.borderColor ?? AppColor.cBorder)),
                errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: widget.borderColor ?? AppColor.cRedText)),
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: widget.focusBorderColor ??
                            AppColor.cFocusedTextField)),
                contentPadding: EdgeInsets.only(
                    left: 16,
                    top: widget.maxLength != null ? 0 : 16,
                    right: 16),
              ),
            )),
        err == ''
            ? SizedBox()
            : Text(err, style: pMedium12.copyWith(color: AppColor.cRedText)),
      ],
    );
  }
}

class CardNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue previousValue,
    TextEditingValue nextValue,
  ) {
    var inputText = nextValue.text;

    if (nextValue.selection.baseOffset == 0) {
      return nextValue;
    }

    var bufferString = StringBuffer();
    for (int i = 0; i < inputText.length; i++) {
      bufferString.write(inputText[i]);
      var nonZeroIndexValue = i + 1;
      if (nonZeroIndexValue % 4 == 0 && nonZeroIndexValue != inputText.length) {
        bufferString.write(' ');
      }
    }

    var string = bufferString.toString();
    return nextValue.copyWith(
      text: string,
      selection: TextSelection.collapsed(
        offset: string.length,
      ),
    );
  }
}
