import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/models/sales_station.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';

import '../../../core/controller/location_controller/location_controller.dart';
import '../../../core/controller/location_controller/sales_office_controller.dart';
import '../../../utils/text_style.dart';
import '../../widget/common_space_divider_widget.dart';
import '../menu_screen/user_management_screen/user_management_screen.dart';
import 'gase_station_screen.dart';
import 'location_screen.dart';
import 'package:geolocator/geolocator.dart';

class SalesOfficeScreen extends StatefulWidget {
  const SalesOfficeScreen({super.key});

  @override
  State<SalesOfficeScreen> createState() => _SalesOfficeScreenState();
}

class _SalesOfficeScreenState extends State<SalesOfficeScreen> {
  SalesOfficeController salesOfficeController =
      Get.put(SalesOfficeController());
  late GoogleMapController mapController;
  late CameraPosition cameraPosition;
  final double _defaultLatitude = 24.741305;
  final double _defaultLongitude = 46.805976;
  final LatLng _center = const LatLng(24.741305000000001, 46.805976000000001);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    cameraPosition = CameraPosition(target: _center, zoom: 5.0);
                       salesOfficeController .isDefaultMap.value=true;

    _fetchUserLocation();
  }

  Future<void> _fetchUserLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Please enable location services.")),
      );
      _useDefaultLocation();
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Location permissions are denied.")),
        );
        _useDefaultLocation();
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              "Location permissions are permanently denied. Please enable them in settings."),
          action: SnackBarAction(
            label: 'Settings',
            onPressed: () {
              Geolocator.openAppSettings();
            },
          ),
        ),
      );
      _useDefaultLocation();
      return;
    }

    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      salesOfficeController.userLatitude = position.latitude;
      salesOfficeController.userLongitude = position.longitude;

      salesOfficeController
          .getSalesOfficeStations200kmRadius(
            salesOfficeController.userLatitude!,
            salesOfficeController.userLongitude!,
            salesOfficeController.radiusInKm,
          )
          .then((_) => _add(salesOfficeController.salesStationList));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Failed to get location: $e")),
      );
      _useDefaultLocation();
    }
  }

  void _useDefaultLocation() {
    salesOfficeController.userLatitude = _defaultLatitude;
    salesOfficeController.userLongitude = _defaultLongitude;

    salesOfficeController
        .getSalesOfficeStations200kmRadius(
          salesOfficeController.userLatitude!,
          salesOfficeController.userLongitude!,
          salesOfficeController.radiusInKm,
        )
        .then((_) => _add(salesOfficeController.salesStationList));
  }

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;
    if (Platform.isIOS) {
      controller.setMapStyle('''
    [
      {
        "featureType": "water",
        "elementType": "labels",
        "stylers": [
          {
            "visibility": "off"
          }
        ]
      }
    ]
    ''');
    }
    //_add();
  }

  Map<MarkerId, Marker> markers = <MarkerId, Marker>{};

  // Future<void> _add(List<SalesStationModel> stations) async {
  //   if (stations.isEmpty) {
  //     return;
  //   }

  //   markers.clear();

  //   for (int i = 0; i < stations.length; i++) {
  //     var markerIdVal = "$i";
  //     final MarkerId markerId = MarkerId(markerIdVal);

  //     final Marker marker = Marker(
  //       markerId: markerId,
  //       position: LatLng(
  //         double.parse(stations[i].latitude),
  //         double.parse(stations[i].longitude),
  //       ),
  //       icon: await createMarkerImageFromAsset(
  //           context, DefaultImages.gasStationMarker),
  //       onTap: () {
  //         showModalBottomSheet(
  //           context: context,
  //           shape: const RoundedRectangleBorder(
  //               borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
  //           isScrollControlled: true,
  //           builder: (context) {
  //             return salesStationsBottomSheetWidget(
  //               title: salesOfficeController.salesStationList[i].placeDesc.toString(),
  //               subTitle: salesOfficeController.salesStationList[i].officeDesc.toString(),
  //               status:
  //                   salesOfficeController.salesStationList[i].offStatus == "Yes"
  //                       ? "Active"
  //                       : "Inactive",
  //               latitude: double.parse(
  //                   salesOfficeController.salesStationList[i].latitude),
  //               longitude: double.parse(
  //                   salesOfficeController.salesStationList[i].longitude),
  //               coordinates:
  //                   "https://www.google.com/maps/search/${double.parse(salesOfficeController.salesStationList[i].latitude)},${double.parse(salesOfficeController.salesStationList[i].longitude)}",
  //             );
  //           },
  //         );
  //       },
  //     );

  //     if (mounted) {
  //       setState(() {
  //         markers[markerId] = marker;
  //       });
  //     }
  //   }
  // }


  Future<void> _add(List<SalesStationModel> stations) async {
    if (stations.isEmpty) {
      return;
    }

   
    markers.clear();

    for (int i = 0; i < stations.length; i++) {
      var markerIdVal = "$i";
      final MarkerId markerId = MarkerId(markerIdVal);

      final Marker marker = Marker(
        markerId: markerId,
        position: LatLng(
          double.parse(stations[i].latitude),
          double.parse(stations[i].longitude),
        ),
        icon: await createMarkerImageFromAsset(
            context, DefaultImages.gasStationMarker),
        onTap: () {
        

          showModalBottomSheet(
            context: context,
            barrierColor: AppColor.cBlackOpacity,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
            ),
            isScrollControlled: true,
            builder: (context) {
              // return gasStationsBottomSheetWidget(
              //   title: stations[i].placeDesc.toString(),
              //   subTitle: stations[i].stationName.toString(),
              //   products: stations[i].products,
              //   latitude: double.parse(stations[i].latitude),
              //   longitude: double.parse(stations[i].longitude),
              //   coordinates: '${stations[i].latitude},${stations[i].longitude}',
              //   status: stations[i].stationStatus,
              // );

return salesStationsBottomSheetWidget(
                title: stations[i].placeDesc.toString(),
                subTitle: stations[i].officeDesc.toString(),
                status:
                    stations[i].offStatus == "Yes"
                        ? "Active"
                        : "Inactive",
                latitude: double.parse(
                    stations[i].latitude),
                longitude: double.parse(
                    stations[i].longitude),
                coordinates:
                    "https://www.google.com/maps/search/${double.parse(stations[i].latitude)},${double.parse(stations[i].longitude)}",
              );
            },
          );
        },
      );

      if (mounted) {
        setState(() {
          markers[markerId] = marker;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => salesOfficeController.salesStationList.isNotEmpty
          ? Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                        top: 0, right: 16, left: 16, bottom: 16),
                    child: Container(
                      width: Get.width,
                      height: 45,
                      decoration: BoxDecoration(
                          color: AppColor.cLightGrey,
                          borderRadius: BorderRadius.circular(4)),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 4),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            commonLocationTab(
                              onTap: () {
                                salesOfficeController.isMap.value = true;
                                salesOfficeController.isList.value = false;
                              },
                              text: "Map".trr,
                              icon: DefaultImages.mapIcn,
                              btnColor:
                                  salesOfficeController.isMap.value == true
                                      ? AppColor.cWhite
                                      : AppColor.cLightGrey,
                              textColor:
                                  salesOfficeController.isMap.value == true
                                      ? AppColor.cText
                                      : AppColor.cDarkGreyFont,
                            ),
                            commonLocationTab(
                                onTap: () {
                                  salesOfficeController.isMap.value = false;
                                  salesOfficeController.isList.value = true;
                                },
                                text: "List".trr,
                                icon: DefaultImages.listIcn,
                                btnColor:
                                    salesOfficeController.isList.value == true
                                        ? AppColor.cWhite
                                        : AppColor.cLightGrey,
                                textColor:
                                    salesOfficeController.isList.value == true
                                        ? AppColor.cText
                                        : AppColor.cDarkGreyFont),
                          ]),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            salesOfficeController.isDefaultMap.value = true;
                            await salesOfficeController
                                .getSalesOfficeStations200kmRadius(
                                    salesOfficeController.userLatitude!,
                                    salesOfficeController.userLongitude!,
                                    salesOfficeController.radiusInKm);
                            _add(salesOfficeController.salesStationList);
                          },
                          child: Container(
                            width: (Get.width - 48) / 2,
                            height: 45,
                            decoration: BoxDecoration(
                              color: salesOfficeController.isDefaultMap.value
                                  ? AppColor.cWhite
                                  : AppColor.cLightGrey,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black12,
                                  offset: Offset(0, 4),
                                  blurRadius: 6,
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.map_rounded, color: AppColor.cText),
                                SizedBox(width: 6),
                                Text(
                                  "Default".trr,
                                  style: TextStyle(
                                      color: salesOfficeController
                                              .isDefaultMap.value
                                          ? AppColor.cText
                                          : AppColor.cDarkGreyFont),
                                ),
                              ],
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            salesOfficeController.isDefaultMap.value = false;
                             _add(salesOfficeController.allStationsList);
                            // await salesOfficeController
                            //     .getAllSalesStations(
                            //         // gasStationController.userLatitude!,
                            //         // gasStationController.userLongitude!,
                            //         // gasStationController.radiusInKm

                            //         );
                            // _add(salesOfficeController.salesStationList);


                          },
                          child: Container(
                            width: (Get.width - 48) / 2,
                            height: 45,
                            decoration: BoxDecoration(
                              color: !salesOfficeController.isDefaultMap.value
                                  ? AppColor.cWhite
                                  : AppColor.cLightGrey,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black12,
                                  offset: Offset(0, 4),
                                  blurRadius: 6,
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(DefaultImages.mapIcn),
                                SizedBox(width: 6),
                                Text(
                                  "All".trr,
                                  style: TextStyle(
                                      color: !salesOfficeController
                                              .isDefaultMap.value
                                          ? AppColor.cText
                                          : AppColor.cDarkGreyFont),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  if (salesOfficeController.isMap.value == true) ...[
                    Expanded(
                      child: Container(
                        height: Get.height / 1.5,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6)),
                        child: GoogleMap(
                          onMapCreated: _onMapCreated,
                          markers: Set<Marker>.of(markers.values),
                          // YOUR MARKS IN MAP
                          initialCameraPosition: cameraPosition,
                          scrollGesturesEnabled: true,
                        ),
                      ),
                    )
                  ] else
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const BouncingScrollPhysics(),
                          itemCount: (salesOfficeController.isDefaultMap ==
                                  true)
                              ? salesOfficeController.salesStationList.length
                              : salesOfficeController.allStationsList.length,
                          itemBuilder: (context, index) {
                            var data = (salesOfficeController.isDefaultMap ==
                                    true)
                                ? salesOfficeController.salesStationList[index]
                                : salesOfficeController.allStationsList[index];

                            return Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(bottom: 8.0),
                                    child: GestureDetector(
                                      onTap: () {
                                        showModalBottomSheet(
                                          context: context,
                                          shape: const RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.vertical(
                                                      top:
                                                          Radius.circular(12))),
                                          isScrollControlled: true,
                                          builder: (context) {
                                            return salesStationsBottomSheetWidget(
                                              title: data.placeDesc,
                                              subTitle: data.officeDesc,
                                              status: data.offStatus == "Yes"
                                                  ? "Active"
                                                  : "Inactive",
                                              latitude:
                                                  double.parse(data.latitude),
                                              longitude:
                                                  double.parse(data.longitude),
                                              coordinates:
                                                  "https://www.google.com/maps/search/${double.parse(data.latitude)},${double.parse(data.longitude)}",
                                            );
                                          },
                                        );
                                      },
                                      child: listDataContainer(
                                        title: data.placeDesc,
                                        subTitle: data.officeDesc,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                ],
              ),
            )
          : const Center(
              child: CircularProgressIndicator(),
            ),
    );
  }

  @override
  void dispose() {
    mapController.dispose(); // Dispose of the controller
    super.dispose();
  }
}

Widget salesStationsBottomSheetWidget({
  required String title,
  required String subTitle,
  required String status,
  //required String products,
  //required String phoneNo,
  required double latitude,
  required double longitude,
  required String coordinates,
}) {
  return Container(
    decoration: const BoxDecoration(
      borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
    ),
    padding: const EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: pSemiBold16,
        ),
        verticalSpace(6),
        Text(
          subTitle,
          style: pRegular12,
        ),
        verticalSpace(32),
        userDataRowWidget(title: 'Status', value: status),
        //verticalSpace(20),
        //userDataRowWidget(title: 'Products', value: products),
        //verticalSpace(20),
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //   children: [
        //     userDataRowWidget(title: 'Phone', value: '+$phoneNo'),
        //     GestureDetector(
        //         onTap: () {
        //           Clipboard.setData(ClipboardData(text: phoneNo));
        //         },
        //         child: assetSvdImageWidget(image: DefaultImages.clipboardIcn)),
        //   ],
        // ),
        verticalSpace(28),
        gotoMapButton(onTap: () async {
          //MapsLauncher.launchCoordinates(latitude, longitude);

          final Uri url0 = Uri.parse(coordinates);
          if (!await launchUrl(url0)) {
            // You can show an error message if the URL couldn't be launched
            print('Could not launch $url0');
          }
        }),
        verticalSpace(16),
      ],
    ),
  );
}
