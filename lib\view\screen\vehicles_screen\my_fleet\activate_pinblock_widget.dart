import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/vehicle_controller/change_status_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/vehicle_controller/activate_pinblock_controller.dart';
import '../../../../utils/validator.dart';
import '../../../widget/common_text_field.dart';
import 'bulk_actions_widget.dart';

class ActivatePinblockWidget extends StatefulWidget {
  final bool isPin;
  final String serialid;
  const ActivatePinblockWidget({
    super.key,
    required this.serialid,
    required this.isPin,
  });

  @override
  State<ActivatePinblockWidget> createState() => _ActivatePinblockWidgetState();
}

class _ActivatePinblockWidgetState extends State<ActivatePinblockWidget> {
  ActivatePinblockController activatePinblockController =
      Get.put(ActivatePinblockController());
  VehicleController vehicleController = Get.put(VehicleController());

  final vehicle = GetStorage();
  final isPinblock = GetStorage();

  @override
  Widget build(BuildContext context) {
    //activatePinblockController.isActivate.value = widget.pinblock;
    return Container(
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              children: [
                GestureDetector(
                    onTap: () {
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.back();
                      // Get.offAll(
                      //   () => DashBoardManagerScreen(
                      //     currantIndex: 0,
                      //   ),
                      //   //preventDuplicates: false,
                      // );
                    },
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: AppColor.cLightBlueContainer,
                      child: Center(
                          child: assetSvdImageWidget(
                              image: DefaultImages.backIcn)),
                    )),
                Expanded(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(
                        "PinBlock".trr,
                        style: pBold20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          verticalSpace(24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text("OFF"),
              Obx(
                () => Switch(
                  // This bool value toggles the switch.
                  value: widget.isPin == true
                      ? activatePinblockController.isActivate.value = true
                      : activatePinblockController.isActivate.value,
                  activeColor: Colors.orange,
                  onChanged: (bool value) {
                    print(value);
                    print(activatePinblockController.isActivate.value);
                    // This is called when the user toggles the switch.
                    setState(() {
                      activatePinblockController.isActivate.value = value;
                      if (value == true) {
                        activatePinblockController.pinblockController.text =
                            "Y";
                      } else {
                        activatePinblockController.pinblockController.text =
                            "N";
                      }
                      print(activatePinblockController.pinblockController.text);
                    });
                  },
                ),
              ),
              const Text("ON"),
            ],
          ),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonButton(
                title: 'Back'.trr,
                onPressed: () {
                  vehicleController.selectedSerialList.clear();
                  vehicleController.selectedVehicleList.clear();
                  vehicleController.selectedFleetList.clear();
                  vehicleController.filterValueList.refresh();
                  vehicleController.selectedVehicleList.refresh();
                  vehicleController.selectedSerialList.refresh();
                  vehicleController.selectedFleetList.refresh();
                  Get.back();
                  // Get.offAll(
                  //   () => DashBoardManagerScreen(
                  //     currantIndex: 0,
                  //   ),
                  //   //preventDuplicates: false,
                  // );
                },
                btnColor: AppColor.cBackGround,
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Submit'.trr,
                  onPressed: () {
                    activatePinblockController
                        .activatePinblock(widget.serialid);
                  },
                  btnColor: AppColor.themeOrangeColor,
                  horizontalPadding: 16,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
