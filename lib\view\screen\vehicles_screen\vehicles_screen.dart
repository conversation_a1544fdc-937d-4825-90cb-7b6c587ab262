// ignore_for_file: prefer_const_constructors, must_be_immutable, unnecessary_brace_in_string_interps, avoid_print, unnecessary_string_interpolations, avoid_function_literals_in_foreach_calls, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/menu_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/my_fleet_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_installation/tag_installation_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/tag_transfer_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../widget/common_appbar_widget.dart';
import '../../widget/common_button.dart';
import 'my_fleet/bulk_actions_widget.dart';
import 'my_fleet/fleet_filter_screen.dart';
import 'my_fleet/row_adjustment_widget.dart';
import 'my_fleet/search_bottomsheet.dart';
import 'tag_installation/schedule_installation_widget.dart';

class VehiclesScreen extends StatefulWidget {
  const VehiclesScreen({super.key});

  @override
  State<VehiclesScreen> createState() => _VehiclesScreenState();
}

class _VehiclesScreenState extends State<VehiclesScreen> {
  final bool _hasVehicleLoaded = false;
  VehicleController vehicleController = Get.put(VehicleController());
  FleetController fleetController = Get.put(FleetController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          simpleMyAppBar(
              title: "Vehicles".trr,
              onTap: () {
                Get.back();
              },
              backString: "Back".trr),
          Container(
            padding: EdgeInsets.only(right: 16, top: 16, left: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                /*simpleAppBar(
                    title: '',
                    onTap: () {
                      Get.offAll(() => MenuScreen());
                    },
                    backString: "Back".trr),*/
               /* Text(
                  "Vehicles".trr,
                  style: pBold20,
                  textAlign: TextAlign.center,
                ),*/

                // GestureDetector(
                //     onTap: () {
                //       showModalBottomSheet(
                //         context: context,
                //         barrierColor: AppColor.cBlackOpacity,
                //         shape: RoundedRectangleBorder(
                //             borderRadius: BorderRadius.vertical(
                //                 top: Radius.circular(12))),
                //         isScrollControlled: true,
                //         builder: (context) {
                //           return SearchBottomSheetWidget();
                //         },
                //       );
                //     },
                //     child: assetSvdImageWidget(
                //         image: DefaultImages.searchCircleIcn))
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Obx(() {
                return Column(
                  children: [
                    /*simpleAppBar(
                    title: '',
                    onTap: () {
                      Get.back();
                    },
                    backString: "Back".trr),*/
                    SizedBox(
                      // height: Get.height * 0.06,
                      child: Row(
                        // shrinkWrap: true,
                        // scrollDirection: Axis.horizontal,
                        children: [
                          tabWidget(
                            title: 'My Fleet'.trr,
                            onTap: () {
                              vehicleController.isMyFleet.value = true;
                              vehicleController.isTagInstallation.value = false;
                              vehicleController.isTagTransfer.value = false;
                              vehicleController.isScheduleInstallation.value =
                                  false;
                            },
                            isSelected: vehicleController.isMyFleet.value,
                          ),
                          // tabWidget(
                          //   title: 'Tag Installation'.trr,
                          //   onTap: () {
                          //     vehicleController.isMyFleet.value = false;
                          //     vehicleController.isTagInstallation.value = true;
                          //     vehicleController.isTagTransfer.value = false;
                          //   },
                          //   isSelected:
                          //       vehicleController.isTagInstallation.value,
                          // ),
                          tabWidget(
                            title: 'Tag Transfer'.trr,
                            onTap: () {
                              vehicleController.isMyFleet.value = false;
                              vehicleController.isTagInstallation.value = false;
                              vehicleController.isTagTransfer.value = true;
                            },
                            isSelected: vehicleController.isTagTransfer.value,
                          ),
                        ],
                      ),
                    ),
                    verticalSpace(24),
                    vehicleController.isMyFleet.value
                        ? MyFleetScreen()
                        // : vehicleController.isTagInstallation.value
                        //     ? TagInstallationScreen()
                        : TagTransferScreen()
                  ],
                );
              }),
            ),
          )
        ],
      ),
      bottomNavigationBar: Obx(() {
        return vehicleController.isMyFleet.value &&
                fleetController.fleetList.isNotEmpty
            //vehicleController.myFleetList.isNotEmpty
            ? Container(
                padding: EdgeInsets.all(16),
                child: vehicleController.selectedFleetList.isNotEmpty
                    ? vehicleController.isScheduleInstallation.value == true
                        ? scheduleInstallationWidget(
                            context,
                            total:
                                '${vehicleController.selectedFleetList.length} ',
                            scheduleOnTap: () {
                              vehicleController.selectedFleetList.clear();
                              vehicleController.myFleetList.forEach((element) {
                                print("object ${element}");
                                element['value'].value = false;
                              });
                              vehicleController.filterValueList.refresh();
                              Get.to(ScheduleInstallationWidget());
                              // showModalBottomSheet(
                              //   context: context,
                              //   shape: RoundedRectangleBorder(
                              //       borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                              //   backgroundColor: AppColor.cBackGround,
                              //   barrierColor: AppColor.cBlackOpacity,
                              //   isScrollControlled: true,
                              //   builder: (context) {
                              //     return ScheduleInstallationWidget();
                              //   },
                              // );
                            },
                          )
                        : bulkActionRowWidget(
                            context,
                            total:
                                '${vehicleController.selectedFleetList.length}',
                            onTap: () {
                              vehicleController.selectedVehicleList.refresh();
                              vehicleController.selectedSerialList.refresh();
                              vehicleController.selectedFleetList.refresh();
                              vehicleController.filterValueList.refresh();
                              showModalBottomSheet(
                                isDismissible: false,
                                context: context,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16))),
                                backgroundColor: AppColor.cBackGround,
                                barrierColor: AppColor.cBlackOpacity,
                                isScrollControlled: true,
                                builder: (context) {
                                  return BulkActionsWidget();
                                },
                              );
                            },
                            clearTap: () {
                              vehicleController.selectedFleetList.clear();
                              // vehicleController.myFleetList.forEach((element) {
                              //   print("object ${element}");
                              //   element['value'].value = false;
                              // });
                              vehicleController.filterValueList.refresh();
                            },
                          )
                    : Row(
                        children: const [
                          // Expanded(
                          //   flex: 3,
                          //   child: GestureDetector(
                          //     onTap: () {
                          //       showModalBottomSheet(
                          //         context: context,
                          //         shape: RoundedRectangleBorder(
                          //             borderRadius: BorderRadius.vertical(
                          //                 top: Radius.circular(16))),
                          //         backgroundColor: AppColor.cBackGround,
                          //         barrierColor: AppColor.cBlackOpacity,
                          //         isScrollControlled: true,
                          //         builder: (context) {
                          //           return FleetFilterScreen();
                          //         },
                          //       );
                          //     },
                          //     child: filterButton(),
                          //   ),
                          // ),
                          // horizontalSpace(16),
                          // Expanded(
                          //   flex: 1,
                          //   child: GestureDetector(
                          //       onTap: () {
                          //         showModalBottomSheet(
                          //           context: context,
                          //           shape: RoundedRectangleBorder(
                          //               borderRadius: BorderRadius.vertical(
                          //                   top: Radius.circular(16))),
                          //           backgroundColor: AppColor.cBackGround,
                          //           barrierColor: AppColor.cBlackOpacity,
                          //           isScrollControlled: true,
                          //           builder: (context) {
                          //             return RowAdjustmentWidget();
                          //           },
                          //         );
                          //       },
                          //       child: listButton("9")),
                          // ),
                        ],
                      ),
              )
            : SizedBox();
      }),
    );
  }

  Row bulkActionRowWidget(BuildContext context,
      {required String total,
      required Function() onTap,
      required Function() clearTap}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text.rich(
          TextSpan(
            text: '$total  ' + "Vehicles".trr,
            style: pBold14,
            children: <TextSpan>[
              TextSpan(
                  text: ' ' + 'selected'.trr,
                  style: pRegular14.copyWith(color: AppColor.cDarkGreyFont)),
            ],
          ),
        ),
        Row(
          children: [
            CommonButton(
                height: 40,
                width: 130,
                title: "Bulk actions".trr,
                horizontalPadding: 16,
                btnColor: AppColor.themeDarkBlueColor,
                onPressed: onTap),
            horizontalSpace(24),
            GestureDetector(
                onTap: clearTap,
                child: assetSvdImageWidget(image: DefaultImages.cancelIcn))
          ],
        )
      ],
    );
  }

  Row scheduleInstallationWidget(BuildContext context,
      {required String total, required Function() scheduleOnTap}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        assetSvdImageWidget(image: DefaultImages.truckIcn),
        horizontalSpace(12),
        Text.rich(
          TextSpan(
            text: '$total',
            style: pBold14,
            children: <TextSpan>[
              TextSpan(
                  text: 'selected'.trr,
                  style: pRegular14.copyWith(color: AppColor.cDarkGreyFont)),
            ],
          ),
        ),
        horizontalSpace(24),
        Expanded(
          child: CommonIconButton(
              height: 40,
              title: "SCHEDULE INSTALLATION".trr,
              iconData: DefaultImages.scheduleInstallationIcn,
              btnColor: AppColor.themeOrangeColor,
              fontSize: 12,
              onPressed: scheduleOnTap),
        )
      ],
    );
  }
}
