import 'dart:convert';

List<TagTransferHistoryModel> tagTransferHistoryModelFromJson(String str) =>
    List<TagTransferHistoryModel>.from(
        json.decode(str).map((x) => TagTransferHistoryModel.fromJson(x)));

String tagTransferHistoryModelToJson(List<TagTransferHistoryModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TagTransferHistoryModel {
  int rowNumber;
  String reqId;
  String reqMainType;
  String reqSubType;
  String ttype;
  String visorEmpno;
  String reqDate;
  String stnId;
  String priority;
  String reqDesc;
  String refcompCode;
  String compCode;
  String acctCode;
  String status;
  String statusDisp;
  String state;
  String approval1;
  dynamic apprBy1;
  dynamic apprDate1;
  dynamic apprRemarks1;
  String approval2;
  dynamic apprBy2;
  dynamic apprDate2;
  dynamic apprRemarks2;
  String approval3;
  String apprBy3;
  String apprDate3;
  String apprRemarks3;
  String approval4;
  String apprBy4;
  String apprDate4;
  String apprRemarks4;
  String sysUserid;
  String sysDate;
  String pic1;
  String pic2;
  String pic3;
  String reqRemarks;
  dynamic refDocno;
  String customer;
  int qty;

  TagTransferHistoryModel({
    required this.rowNumber,
    required this.reqId,
    required this.reqMainType,
    required this.reqSubType,
    required this.ttype,
    required this.visorEmpno,
    required this.reqDate,
    required this.stnId,
    required this.priority,
    required this.reqDesc,
    required this.refcompCode,
    required this.compCode,
    required this.acctCode,
    required this.status,
    required this.statusDisp,
    required this.state,
    required this.approval1,
    required this.apprBy1,
    required this.apprDate1,
    required this.apprRemarks1,
    required this.approval2,
    required this.apprBy2,
    required this.apprDate2,
    required this.apprRemarks2,
    required this.approval3,
    required this.apprBy3,
    required this.apprDate3,
    required this.apprRemarks3,
    required this.approval4,
    required this.apprBy4,
    required this.apprDate4,
    required this.apprRemarks4,
    required this.sysUserid,
    required this.sysDate,
    required this.pic1,
    required this.pic2,
    required this.pic3,
    required this.reqRemarks,
    required this.refDocno,
    required this.customer,
    required this.qty,
  });

  factory TagTransferHistoryModel.fromMap(Map<String, dynamic> map) {
    return TagTransferHistoryModel(
      rowNumber: map["RowNumber"] ?? '',
      reqId: map["REQ_ID"] ?? '',
      reqMainType: map["REQ_MAIN_TYPE"] ?? '',
      reqSubType: map["REQ_SUB_TYPE"] ?? '',
      ttype: map["TTYPE"] ?? '',
      visorEmpno: map["VISOR_EMPNO"] ?? '',
      reqDate: map["REQ_DATE"] ?? '',
      stnId: map["STN_ID"] ?? '',
      priority: map["PRIORITY"] ?? '',
      reqDesc: map["REQ_DESC"] ?? '',
      refcompCode: map["REFCOMP_CODE"] ?? '',
      compCode: map["COMP_CODE"] ?? '',
      acctCode: map["ACCT_CODE"] ?? '',
      status: map["STATUS"] ?? '',
      statusDisp: map["STATUS_DISP"] ?? '',
      state: map["STATE"] ?? '',
      approval1: map["APPROVAL_1"] ?? '',
      apprBy1: map["APPR_BY_1"] ?? '',
      apprDate1: map["APPR_DATE_1"] ?? '',
      apprRemarks1: map["APPR_REMARKS_1"] ?? '',
      approval2: map["APPROVAL_2"] ?? '',
      apprBy2: map["APPR_BY_2"] ?? '',
      apprDate2: map["APPR_DATE_2"] ?? '',
      apprRemarks2: map["APPR_REMARKS_2"] ?? '',
      approval3: map["APPROVAL_3"] ?? '',
      apprBy3: map["APPR_BY_3"] ?? '',
      apprDate3: map["APPR_DATE_3"] ?? '',
      apprRemarks3: map["APPR_REMARKS_3"] ?? '',
      approval4: map["APPROVAL_4"] ?? '',
      apprBy4: map["APPR_BY_4"] ?? '',
      apprDate4: map["APPR_DATE_4"] ?? '',
      apprRemarks4: map["APPR_REMARKS_4"] ?? '',
      sysUserid: map["SYS_USERID"] ?? '',
      sysDate: map["SYS_DATE"] ?? '',
      pic1: map["PIC1"] ?? '',
      pic2: map["PIC2"] ?? '',
      pic3: map["PIC3"] ?? '',
      reqRemarks: map["REQ_REMARKS"] ?? '',
      refDocno: map["REF_DOCNO"] ?? '',
      customer: map["CUSTOMER"] ?? '',
      qty: map["QTY"] ?? '',
    );
  }

  factory TagTransferHistoryModel.fromJson(Map<String, dynamic> json) =>
      TagTransferHistoryModel(
        rowNumber: json["RowNumber"],
        reqId: json["REQ_ID"],
        reqMainType: json["REQ_MAIN_TYPE"],
        reqSubType: json["REQ_SUB_TYPE"],
        ttype: json["TTYPE"],
        visorEmpno: json["VISOR_EMPNO"],
        reqDate: json["REQ_DATE"],
        stnId: json["STN_ID"],
        priority: json["PRIORITY"],
        reqDesc: json["REQ_DESC"],
        refcompCode: json["REFCOMP_CODE"],
        compCode: json["COMP_CODE"],
        acctCode: json["ACCT_CODE"],
        status: json["STATUS"],
        statusDisp: json["STATUS_DISP"],
        state: json["STATE"],
        approval1: json["APPROVAL_1"],
        apprBy1: json["APPR_BY_1"],
        apprDate1: json["APPR_DATE_1"],
        apprRemarks1: json["APPR_REMARKS_1"],
        approval2: json["APPROVAL_2"],
        apprBy2: json["APPR_BY_2"],
        apprDate2: json["APPR_DATE_2"],
        apprRemarks2: json["APPR_REMARKS_2"],
        approval3: json["APPROVAL_3"],
        apprBy3: json["APPR_BY_3"],
        apprDate3: json["APPR_DATE_3"],
        apprRemarks3: json["APPR_REMARKS_3"],
        approval4: json["APPROVAL_4"],
        apprBy4: json["APPR_BY_4"],
        apprDate4: json["APPR_DATE_4"],
        apprRemarks4: json["APPR_REMARKS_4"],
        sysUserid: json["SYS_USERID"],
        sysDate: json["SYS_DATE"],
        pic1: json["PIC1"],
        pic2: json["PIC2"],
        pic3: json["PIC3"],
        reqRemarks: json["REQ_REMARKS"],
        refDocno: json["REF_DOCNO"],
        customer: json["CUSTOMER"],
        qty: json["QTY"],
      );

  Map<String, dynamic> toJson() => {
        "RowNumber": rowNumber,
        "REQ_ID": reqId,
        "REQ_MAIN_TYPE": reqMainType,
        "REQ_SUB_TYPE": reqSubType,
        "TTYPE": ttype,
        "VISOR_EMPNO": visorEmpno,
        "REQ_DATE": reqDate,
        "STN_ID": stnId,
        "PRIORITY": priority,
        "REQ_DESC": reqDesc,
        "REFCOMP_CODE": refcompCode,
        "COMP_CODE": compCode,
        "ACCT_CODE": acctCode,
        "STATUS": status,
        "STATUS_DISP": statusDisp,
        "STATE": state,
        "APPROVAL_1": approval1,
        "APPR_BY_1": apprBy1,
        "APPR_DATE_1": apprDate1,
        "APPR_REMARKS_1": apprRemarks1,
        "APPROVAL_2": approval2,
        "APPR_BY_2": apprBy2,
        "APPR_DATE_2": apprDate2,
        "APPR_REMARKS_2": apprRemarks2,
        "APPROVAL_3": approval3,
        "APPR_BY_3": apprBy3,
        "APPR_DATE_3": apprDate3,
        "APPR_REMARKS_3": apprRemarks3,
        "APPROVAL_4": approval4,
        "APPR_BY_4": apprBy4,
        "APPR_DATE_4": apprDate4,
        "APPR_REMARKS_4": apprRemarks4,
        "SYS_USERID": sysUserid,
        "SYS_DATE": sysDate,
        "PIC1": pic1,
        "PIC2": pic2,
        "PIC3": pic3,
        "REQ_REMARKS": reqRemarks,
        "REF_DOCNO": refDocno,
        "CUSTOMER": customer,
        "QTY": qty,
      };
}
