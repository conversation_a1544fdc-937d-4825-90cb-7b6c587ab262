// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/auth/create_dc_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/digital_coupon/digital_coupon_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class CreateDCScreen extends StatelessWidget {
  final bool isBack;

  CreateDCScreen({super.key, required this.isBack});

  CreateDCController createDCController = Get.put(CreateDCController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: Column(
            children: [
              simpleMyAppBar(
                  title: isBack
                      ? "Create a digital coupon".trr
                      : "Create digital coupon".trr,
                  onTap: () {
                    Get.back();
                    // Get.offAll(() => LoginScreen());
                  },
                 // backString: isBack ? "Menu".trr : "Verification".trr),
                  backString: isBack ? "Back".trr : "Verification".trr),
              Expanded(
                child: Obx(() {
                  return ListView(
                    scrollDirection: Axis.vertical,
                    shrinkWrap: true,
                    padding: const EdgeInsets.only(
                        top: 24, left: 16, right: 16, bottom: 26),
                    children: [
                      Row(
                        children: [
                          Expanded(
                            flex: 1,
                            child: selectRadioWidget(
                                onTap: () {
                                  createDCController.isFuel.value = true;
                                  createDCController.isNonFuel.value = false;
                                },
                                icon: createDCController.isFuel.value == true
                                    ? DefaultImages.checkCircleIcn
                                    : DefaultImages.circleIcn,
                                title: "Fuel".trr,
                                borderColor:
                                    createDCController.isFuel.value == true
                                        ? AppColor.themeDarkBlueColor
                                        : AppColor.cBorder),
                          ),
                          horizontalSpace(16),
                          /*Expanded(
                            flex: 1,
                            child: selectRadioWidget(
                                onTap: () {
                                  createDCController.isFuel.value = false;
                                  createDCController.isNonFuel.value = true;
                                },
                                icon: createDCController.isNonFuel.value == true
                                    ? DefaultImages.checkCircleIcn
                                    : DefaultImages.circleIcn,
                                title: "Non-fuel".trr,
                                borderColor: createDCController.isNonFuel.value == true
                                    ? AppColor.themeDarkBlueColor
                                    : AppColor.cBorder),
                          ),*/
                          Expanded(
                            flex: 1,
                            child: IgnorePointer(
                              ignoring: createDCController.disableClick.value,
                              // Set this value to true to disable clicks
                              child: selectRadioWidget(
                                onTap: () {
                                  createDCController.isFuel.value = false;
                                  createDCController.isNonFuel.value = true;
                                },
                                icon: createDCController.isNonFuel.value == true
                                    ? DefaultImages.checkCircleIcn
                                    : DefaultImages.circleIcn,
                                title: "Non-fuel".trr,
                                borderColor:
                                    createDCController.isNonFuel.value == true
                                        ? AppColor.themeDarkBlueColor
                                        : AppColor.cBorder,
                              ),
                            ),
                          ),
                        ],
                      ),
                      verticalSpace(16),
                      CommonHintDropdownWidget(
                        labelText: 'Plate number'.trr,
                        hint: 'Select plate number'.trr,
                        list: createDCController.platNoList,
                        value: createDCController.selectedPlatNo.value,
                        onChanged: (value) {
                          createDCController.selectedPlatNo.value = value;
                          createDCController.ReloadDriverService();
                        },
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 16, bottom: 24),
                        child: horizontalDivider(),
                      ),
                      /*buildDisplayRowWidget(title: "Company type".trr, value: "3948DJA"),
                      buildDisplayRowWidget(title: "User ID".trr, value: "1-222-A23523"),
                      buildDisplayRowWidget(title: "Fuel type".trr, value: "Petrol 91"),
                      buildDisplayRowWidget(title: "Quota value".trr, value: "50"),
                      buildDisplayRowWidget(title: "Quota remains".trr, value: "40"),*/
                      buildDisplayRowWidget(
                          title: "Plate No".trr,
                          value: createDCController.driver.plateno),
                      buildDisplayRowWidget(
                          title: "User ID".trr,
                          value: createDCController.driver.custid),
                      buildDisplayRowWidget(
                          title: "Fuel type".trr,
                          value: createDCController.driver.fueltype),
                      buildDisplayRowWidget(
                          title: "Quota value".trr,
                          value: createDCController.driver.quotavalue),
                      buildDisplayRowWidget(
                          title: "Quota remains".trr,
                          value: createDCController.driver.remquotavalue),
                      Padding(
                        padding: const EdgeInsets.only(top: 16, bottom: 24),
                        child: horizontalDivider(),
                      ),
                      CommonTextField(
                        controller: createDCController.fillController.value,
                        labelText: 'Filling liters'.trr,
                        hintText: '0',
                        keyboardType: TextInputType.numberWithOptions(
                            signed: true, decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        onChanged: (value) {
                          createDCController.fillController.refresh();
                          log('--------${createDCController.fillController.value.text}');
                        },
                      ),
                      verticalSpace(16),
                    ],
                  );
                }),
              )
            ],
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16, bottom: 36),
          child: Obx(() {
            return CommonButton(
              title: 'Generate QR code'.trr,
              btnColor: (createDCController.selectedPlatNo.value != '' &&
                      createDCController.fillController.value.text.isNotEmpty)
                  ? AppColor.themeOrangeColor
                  : AppColor.cLightOrange,
              onPressed: (createDCController.selectedPlatNo.value != '' &&
                      createDCController.fillController.value.text.isNotEmpty)
                  ? () {
                      /* isBack == true
                          ? Get.off(() => DigitalCouponScreen(
                                isBack: isBack,
                              ))
                          : Get.to(() => DigitalCouponScreen(
                                isBack: isBack,
                              ));*/
                      createDCController.GenerateQRCode();
                    }
                  : null,
            );
          }),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      ),
    );
  }
}

Widget selectRadioWidget(
    {required Function() onTap,
    required String icon,
    required String title,
    required Color borderColor}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: borderColor)),
      padding: EdgeInsets.all(6),
      child: Row(
        children: [
          assetSvdImageWidget(image: icon),
          horizontalSpace(8),
          Text(
            title,
            style: pRegular13,
          ),
        ],
      ),
    ),
  );
}

buildDisplayRowWidget({required String title, required String value}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 12.5),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: pRegular17.copyWith(color: AppColor.cDarkGreyFont),
        ),
        Text(
          value,
          style: pSemiBold16.copyWith(fontSize: 17),
        ),
      ],
    ),
  );
}
