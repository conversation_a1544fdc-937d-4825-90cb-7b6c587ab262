class Custbalance {
  Custbalance({
      String? unallocatbalance, 
      String? currentbalance,}){
    _unallocatbalance = unallocatbalance;
    _currentbalance = currentbalance;
}

  Custbalance.fromJson(dynamic json) {
    _unallocatbalance = json['UNALLOCATBALANCE'];
    _currentbalance = json['CURRENTBALANCE'];
  }
  String? _unallocatbalance;
  String? _currentbalance;

  String? get unallocatbalance => _unallocatbalance;
  String? get currentbalance => _currentbalance;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['UNALLOCATBALANCE'] = _unallocatbalance;
    map['CURRENTBALANCE'] = _currentbalance;
    return map;
  }
}