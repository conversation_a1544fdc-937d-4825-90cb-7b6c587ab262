import 'dart:convert';

class Report_Dept {
  final String DEPTCODE;
  final String DEPTNAME;
  final String PARENTID;

  Report_Dept({
    required this.DEPTCODE,
    required this.DEPTNAME,
    required this.PARENTID,
  });

  Map<String, dynamic> toMap() {
    return {
      'DEPTCODE': DEPTCODE,
      'DEPTNAME': DEPTNAME,
      'PARENTID': PARENTID,
    };
  }

  factory Report_Dept.fromMap(Map<String, dynamic> map) {
    return Report_Dept(
      DEPTCODE: map['DEPTCODE'] ?? '',
      DEPTNAME: map['DEPTNAME'] ?? '',
      PARENTID: map['PARENTID'] ?? '',
    );
  }
  String toJson() => json.encode(toMap());

  factory Report_Dept.fromJson(String source) =>
      Report_Dept.fromMap(json.decode(source));
}