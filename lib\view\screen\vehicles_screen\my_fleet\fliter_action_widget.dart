// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/change_plate_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/change_plate_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/file_complaint_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/activate_pinblock_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/plate_change_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/vehicle_controller/vehicle_controller.dart';
import '../tag_transfer_screen/transfer_tags_widget.dart';
import 'bulk_actions_widget.dart';
import 'change_status_widget.dart';
import 'choose_gas_stations_widget.dart';
import 'file_complaint_screen.dart';
import 'set_quota_limits_widget.dart';

class FilterActionWidget extends StatelessWidget {
  FilterActionWidget({super.key});
  FileComplaintController fileComplaintController =
      Get.put(FileComplaintController());
  ChangePlateController changePlateController =
      Get.put(ChangePlateController());
  final vehicle = GetStorage();
  final isPinblock = GetStorage();
  final isDCBlock = GetStorage();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              children: [
                GestureDetector(
                    onTap: () {
                      Get.back();
                      // Get.offAll(
                      //   () => DashBoardManagerScreen(
                      //     currantIndex: 0,
                      //   ),
                      //   //preventDuplicates: false,
                      // );
                    },
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: AppColor.cLightBlueContainer,
                      child: Center(
                          child: assetSvdImageWidget(
                              image: DefaultImages.backIcn)),
                    )),
                Expanded(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(
                        "Filters".trr,
                        style: pBold20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          verticalSpace(22),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonTextField(
                controller: changePlateController.oldPlateController,
                labelText: 'Old Plate'.trr,
                hintText: '',
                fillColor: AppColor.lightBlueColor,
                readOnly: true,
              ),
              verticalSpace(15),
              CommonTextField(
                controller: changePlateController.newPlateController,
                labelText: 'New Plate'.trr,
              ),
            ],
          ),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonButton(
                title: 'Back'.trr,
                onPressed: () {
                  Get.back();
                  // Get.offAll(
                  //   () => DashBoardManagerScreen(
                  //     currantIndex: 0,
                  //   ),
                  //   //preventDuplicates: false,
                  // );
                },
                btnColor: AppColor.cBackGround,
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Submit'.trr,
                  onPressed: () {},
                  btnColor: AppColor.themeOrangeColor,
                  horizontalPadding: 16,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
