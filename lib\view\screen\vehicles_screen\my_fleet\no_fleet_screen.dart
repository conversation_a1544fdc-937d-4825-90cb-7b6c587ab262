import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/add_pure_dc_vehicle_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class NoFleetScreen extends StatelessWidget {
  const NoFleetScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              assetSvdImageWidget(image: DefaultImages.noFleetImg),
              verticalSpace(32),
              Text(
                "You need to order smart tags or cards first".trr,
                style: pBold20,
                textAlign: TextAlign.center,
              ),
              verticalSpace(16),
              Text(
                "Before you can add vehicles to your fleet, you need to order tags or cards for them first.".trr,
                style: pRegular13,
                textAlign: TextAlign.center,
              ),
              verticalSpace(32),
              CommonIconButton(
                iconData: DefaultImages.circleAddIcn,
                title: 'Order tags cards'.trr,
                onPressed: () {
                  Get.to(() => AddPureDCVehicleScreen(
                        isAdd: true,
                        title: "Add Vehicle".trr,
                      ));
                },
                btnColor: AppColor.themeOrangeColor,
              )
            ],
          ),
        ),
      ),
    );
  }
}
