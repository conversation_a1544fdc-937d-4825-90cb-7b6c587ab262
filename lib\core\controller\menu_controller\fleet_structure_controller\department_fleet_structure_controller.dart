import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/fleet_structure.dart';

import '../../../../utils/api_endpoints.dart';

class DepartmentFleetStructureController extends GetxController {
  TextEditingController departmentNameController = TextEditingController();
  final departmentList = <FleetStructureModel>[].obs;

  Future<dynamic> loadDepartment(parentID) async {
    print("loadDepartment");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadDepartment),
          body: {
            "custid": userid,
            "parentid": parentID,
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      List result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        FleetStructureModel structures =
            FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
        departmentList.add(structures);
      }
      print("===============================================================");

      return departmentList;
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
