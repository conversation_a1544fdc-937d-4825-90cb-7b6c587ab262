//
//  webViewCont.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 15/01/2024.
//

import Foundation
import WebKit

class webViewCont
{
//    fileprivate var gatewayScheme: String = "ios"
//    fileprivate var gatewayHost: String = "paymentresult"
//    var transactionId: String? = randomID()
//    func checkTransaction() {
//        var resultOfCall :String = ""
//        let  todoEndpoint =  SystemParameter.MADA_URL + "/api/rest/version/" + SystemParameter.MADA_VERSION + "/merchant/" + SystemParameter.MADA_MID + "/order/" + transactionId!
//                      
//        let url = URL(string: todoEndpoint)!
//                
//        var request = URLRequest(url: url)
//        request.httpMethod = "GET"
//        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
//        request.setValue("application/json", forHTTPHeaderField: "Accept")
//        let username = "merchant." + SystemParameter.MADA_MID
//        
//        let password = SystemParameter.MADA_APIKEY
//        let loginString = String(format: "%@:%@", username, password)
//        //  print(loginString)
//        let loginData = loginString.data(using: String.Encoding.windowsCP1251)!
//        let base64LoginString = loginData.base64EncodedString(options: [])
//        request.addValue("Basic \(base64LoginString)", forHTTPHeaderField: "Authorization")
//        
//        let sessionConfig = URLSessionConfiguration.default
//        sessionConfig.timeoutIntervalForRequest = 60.0
//        sessionConfig.timeoutIntervalForResource = 60.0
//        let session = URLSession(configuration: sessionConfig)
//        
//        
//        let semaphore = DispatchSemaphore(value: 0)
//        let task = session.dataTask(with: request ) {
//            (data, response, error) in
//            guard error == nil else{
//                //Locale.current.languageCode
//                print("Error: " + error.debugDescription)
//                semaphore.signal()
//                return
//            }
//            guard let result = data else {
//                print("Error: did not receive data")
//                semaphore.signal()
//                return
//            }
//            
//            
//            resultOfCall =   String(data: result , encoding: .utf8) ?? ""
//            
//            //  return "Time out"
//            // print("finish")
//            semaphore.signal()
//        }
//        
//        
//        task.resume()
//        semaphore.wait()
//        
//        let dict = convertToDictionary(text: resultOfCall)
//        
//        
//        let results = dict!["transaction"] as! [[String:Any]]
//        
//        let response = results[0]["response"]  as! [String:Any]
//        //   print(response["gatewayCode"])
//        
//        
//        
//        let gatewayCode = response["gatewayCode"] as! String
//        //  print(gatewayCode)
//        var addMadaResult = ""
//        if (gatewayCode == "APPROVED"){
//          
//        }else{
//           
//        }
//       
//    }
//    
//    func convertToDictionary(text: String) -> [String: Any]? {
//        if let data = text.data(using: .utf8) {
//            do {
//                return try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
//            } catch {
//                print(error.localizedDescription)
//            }
//        }
//        return nil
//    }
//    static func randomID() -> String {
////        return String(UUID().uuidString.split(separator: "-").first!)
//        return "I"+String(UUID().uuidString.prefix(12).replacingOccurrences(of: "-", with: ""))
//
//    }
    
    fileprivate var gatewayScheme: String = "ios"
    fileprivate var gatewayHost: String = "paymentresult"
    //fileprivate var gatewayResultParam: String = "acsResult"
    //fileprivate var threeDSecureIdParam: String = "3DSecureId"
    var amountP : String?
    var qty : String?
    var type : Int?
    var sv = UIView()
    var transactionId: String? = randomID()
    public var activityIndicator: UIActivityIndicatorView!


    var webView: WKWebView!
    var sessionId : String = ""
    //webView = WKWebView()
    //webView.navigationDelegate = self
           // view = webView
//    public required init?(coder aDecoder: NSCoder) {
//        super.init(coder: aDecoder)
//        activityIndicator = UIActivityIndicatorView(style: .gray)
//        activityIndicator.hidesWhenStopped = true
//        navigationItem.rightBarButtonItems = [UIBarButtonItem(customView: activityIndicator)]
//
//    }
    
//    override func loadView() {
//        webView = WKWebView()
//        webView.navigationDelegate = self
//        view = webView
//    }
   
        //checkTransaction()
        // Do any additional setup after loading the view.
     //   checkTransaction()
       

//
//                getSessionId(parameters: jsonData!)
//
          // loadContent(session_id: "")
//            }
    
    
    

    /*
    // MARK: - Navigation

    // In a storyboard-based application, you will often want to do a little preparation before navigation
    override func prepare(for segue: UIStoryboardSegue, sender: Any?) {
        // Get the new view controller using segue.destination.
        // Pass the selected object to the new view controller.
    }
    */
    
    /*Session Start*/
    func getSessionId(parameters :  Any) {
        var resultOfCall :String = ""
        let  todoEndpoint = SystemParameter.MADA_URL + "/api/rest/version/" + SystemParameter.MADA_VERSION + "/merchant/" + SystemParameter.MADA_MID + "/session"
        
      
        
        let url = URL(string: todoEndpoint)!
        
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        let username = "merchant." + SystemParameter.MADA_MID
                 
        let password = SystemParameter.MADA_APIKEY
                 let loginString = String(format: "%@:%@", username, password)
               //  print(loginString)
                 let loginData = loginString.data(using: String.Encoding.windowsCP1251)!
                 let base64LoginString = loginData.base64EncodedString(options: [])
                 request.addValue("Basic \(base64LoginString)", forHTTPHeaderField: "Authorization")
        
        let sessionConfig = URLSessionConfiguration.default
        sessionConfig.timeoutIntervalForRequest = 60.0
        sessionConfig.timeoutIntervalForResource = 60.0
        let session = URLSession(configuration: sessionConfig)
        
    
        
        request.httpBody = parameters as? Data
       // print(request.httpBody)
        
        let semaphore = DispatchSemaphore(value: 0)
        let task = session.dataTask(with: request ) {
            (data, response, error) in
            guard error == nil else{
                //Locale.current.languageCode
                print("Error: " + error.debugDescription)
                semaphore.signal()
                return
            }
            guard let result = data else {
                print("Error: did not receive data")
                semaphore.signal()
                return
            }
            
            
            resultOfCall =   String(data: result , encoding: .utf8) ?? ""
            
            //  return "Time out"
            // print("finish")
            semaphore.signal()
        }
        
        
        task.resume()
        semaphore.wait()
        
    let dict = convertToDictionary(text: resultOfCall)

        //print (dict)
        let results = dict!["session"] as? [String: Any]
        sessionId = results!["id"]  as! String
        
        
    }
    
   // Checked
        func checkTransaction() {
            var resultOfCall :String = ""
            let  todoEndpoint =  SystemParameter.MADA_URL + "/api/rest/version/" + SystemParameter.MADA_VERSION + "/merchant/" + SystemParameter.MADA_MID + "/order/" + transactionId!
    
            let url = URL(string: todoEndpoint)!
    
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.setValue("application/json", forHTTPHeaderField: "Accept")
            let username = "merchant." + SystemParameter.MADA_MID
    
            let password = SystemParameter.MADA_APIKEY
            let loginString = String(format: "%@:%@", username, password)
            //  print(loginString)
            let loginData = loginString.data(using: String.Encoding.windowsCP1251)!
            let base64LoginString = loginData.base64EncodedString(options: [])
            request.addValue("Basic \(base64LoginString)", forHTTPHeaderField: "Authorization")
    
            let sessionConfig = URLSessionConfiguration.default
            sessionConfig.timeoutIntervalForRequest = 60.0
            sessionConfig.timeoutIntervalForResource = 60.0
            let session = URLSession(configuration: sessionConfig)
    
    
            let semaphore = DispatchSemaphore(value: 0)
            let task = session.dataTask(with: request ) {
                (data, response, error) in
                guard error == nil else{
                    //Locale.current.languageCode
                    print("Error: " + error.debugDescription)
                    semaphore.signal()
                    return
                }
                guard let result = data else {
                    print("Error: did not receive data")
                    semaphore.signal()
                    return
                }
    
    
                resultOfCall =   String(data: result , encoding: .utf8) ?? ""
    
                //  return "Time out"
                // print("finish")
                semaphore.signal()
            }
    
    
            task.resume()
            semaphore.wait()
    
            let dict = convertToDictionary(text: resultOfCall)
    
    
            let results = dict!["transaction"] as! [[String:Any]]
    
            let response = results[0]["response"]  as! [String:Any]
            //   print(response["gatewayCode"])
    
    
    
            let gatewayCode = response["gatewayCode"] as! String
            //  print(gatewayCode)
            var addMadaResult = ""
            if (gatewayCode == "APPROVED"){
    
            }else{
    
            }
    
        }
    
    func convertToDictionary(text: String) -> [String: Any]? {
        if let data = text.data(using: .utf8) {
            do {
                return try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
            } catch {
                print(error.localizedDescription)
            }
        }
        return nil
    }
    
   
    public func loadContent(session_id : String) {
        
        print("loadContent called");
        
        let json: [String: Any] = ["apiOperation": "CREATE_CHECKOUT_SESSION",
                                   "interaction": ["operation":"PURCHASE", "returnUrl":"ios://paymentresult"],
                                   "order": [ "amount": amountP ,"currency":"SAR" ,  "id": transactionId , "reference" : transactionId] ,
                                   "transaction" : ["reference": transactionId ]
        ]

        let jsonData = try? JSONSerialization.data(withJSONObject: json)
        
       // let jsonDataForString = try? JSONSerialization.data(withJSONObject: json, options: [])
        let jsonString = String(data: jsonData!, encoding: .utf8)
        
        var bodyContent : String?
        /*Payment HTML*/
        bodyContent = "<html><head><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"><script type=\"application/javascript\" src=\"" + SystemParameter.MADA_URL + "/checkout/version/" + SystemParameter.MADA_VERSION + "/checkout.js\" data-error=\"errorCallback\" data-cancel=\"cancelCallback\"></script><script type=\"text/javascript\">function errorCallback(error) {console.log(JSON.stringify(error));} function cancelCallback() {console.log('Payment Cancel');} function completeCallback() {console.log('Payment Complete');}Checkout.configure({merchant: '" + SystemParameter.MADA_MID + "', order: { description: 'Amount', }, session: { id: '" + self.sessionId + "'  }, interaction: {merchant: {name: 'Aldrees', email: '<EMAIL>', phone: '+966118352514', logo: 'https://waie.aldrees.com/Images/aldreeslogonew.png', url: 'https://waie.aldrees.com', address: { line1: 'Riyadh', line2: 'KSA' } },displayControl: {billingAddress: 'HIDE', customerEmail: 'HIDE', orderSummary: 'SHOW', paymentConfirmation: 'HIDE', paymentTerms: 'HIDE', }, }, }); Checkout.showLightbox();</script> </head><body><form ><input type=\"hidden\" run-in=\"server\" value=\"Pay with Lightbox \"onclick=\"Checkout.showLightbox();\" /><input type=\"hidden\" value=\"Pay with Payment Page\" onclick=\"Checkout.showPaymentPage();\" /></form></body></html>"
        webView = WKWebView()
              // webView.navigationDelegate = self
           //   view = webView
        
        webView.loadHTMLString(bodyContent ?? "", baseURL: nil)
      //  webView.load(URLRequest(URL(string: "gatewaysdk://3dsecure")))
        
    }
    
    
    
    @objc func cancelAction() {
        //completion?(self, .cancelled)
        //self.showToast(message: "Hello"))
    }
    
    // MARK: - WKNavigationDelegate methods
    public func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        activityIndicator.startAnimating()
        //print("start")
    }
    
    public func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
     //   activityIndicator.stopAnimating()
        //print("finish")
    }
    
    public func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
       // activityIndicator.startAnimating()
        //print(navigationAction.request.url)
        if ((navigationAction.request.url?.description.contains("lightbox/SESSION")) != false){
            activityIndicator.stopAnimating()
        }
       if let url = navigationAction.request.url, let comp = URLComponents(url: url, resolvingAgainstBaseURL: false), comp.scheme == gatewayScheme, comp.host == gatewayHost {
            decisionHandler(.cancel)
        
        
        checkTransaction()
        
         
        } else {
            
            decisionHandler(.allow)
        }
    }
    /*func webView(_ webView: WKWebView, runJavaScriptAlertPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping () -> Void) {
        let alertController = UIAlertController(title: "alert", message: message, preferredStyle: UIAlertController.Style.alert)
        alertController.addAction(UIAlertAction(title: "OK", style: UIAlertAction.Style.cancel, handler: nil))
        self.present(alertController, animated: true, completion: nil)

        print("An error from web view: \(message)")
    }*/
    
    /*Mada WebServis*/
   
    
    
    
    static func randomID() -> String {
//        return String(UUID().uuidString.split(separator: "-").first!)
        return "I"+String(UUID().uuidString.prefix(12).replacingOccurrences(of: "-", with: ""))

    }
}
