import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class NoRefundFoundScreen extends StatelessWidget {
  const NoRefundFoundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 80.0),
          child: Text(
            "You can refund paid services that are not yet installed/printed)".trr,
            style: pRegular13,
          ),
        ),
        verticalSpace(Get.height * 0.15),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: [
              Center(
                  child: assetSvdImageWidget(image: DefaultImages.noRefundImg)),
              verticalSpace(32),
              Center(
                  child: Text("You don't have any orders to refund right now".trr,
                      style: pBold20, textAlign: TextAlign.center)),
              verticalSpace(16),
              Text(
                "You can only refund tags or cards that haven't yet been installed or printed.",
                style: pRegular13,
                textAlign: TextAlign.center,
              )
            ],
          ),
        )
      ],
    );
  }
}
