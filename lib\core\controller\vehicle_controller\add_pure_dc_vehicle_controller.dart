import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import '../../../utils/api_endpoints.dart';

class AddPureDcVehicleController extends GetxController {
  RxBool isVehicleDetail = true.obs;
  List plateList = ["Private", "Public"];
  RxString selectedPlatValue = 'Private'.obs;
  TextEditingController plateController = TextEditingController();
  TextEditingController driverNameController = TextEditingController();
  TextEditingController limitController = TextEditingController();
  TextEditingController driverPasswordController = TextEditingController();
  TextEditingController vehicleReferenceController = TextEditingController();
  TextEditingController quotaValueController = TextEditingController();
  TextEditingController WAIEquotaValueController = TextEditingController();
  List vehicleTypeList = ["Vehicle1", "Vehicle2"];
  RxString selectedVehicleValue = ''.obs;
  //RxString quotaValue = ''.obs;
  List makeList = ["Make1", "Make2"];
  RxString selectedMakeValue = ''.obs;
  List modelList = ["Model1", "Model2"];
  RxString selectedModelValue = ''.obs;
  List fuelList = ["Fuel1", "Fuel2"];
  RxString selectedFuelValue = ''.obs;
  RxBool isOne = true.obs;
  RxBool isTwo = false.obs;
  List limitList = ["Liters", "Amount"];
  RxString selectedLimitValue = 'Liters'.obs;

  RxBool isVehicleLevel = true.obs;
  List divisionList = ["Division1", "Division2"];
  RxString selectDivisionValue = ''.obs;
  List branchList = ["Branch1", "Branch2"];
  RxString selectedBranchValue = ''.obs;
  List departmentList = ["Department1", "Department2"];
  RxString selectedDepartmentValue = ''.obs;
  List operationList = ["Operation1", "Operation2"];
  RxString selectedOperationValue = ''.obs;

  RxBool isRefuelLimits = true.obs;
  List quotaTypeList = ["Unlimited", "Limited"];
  RxString selectedQuotaTypeValue = 'Unlimited'.obs;
  List quotaLimitList = ["Liters", "Amount"];
  RxString selectedQuotaLimitValue = 'Liters'.obs;
  List quotaValueList = ["00.00", "10.00"];
  RxString selectedQuotaValue = '00.00'.obs;
  List quotaBalanceList = ["00.00", "10.00"];
  RxString selectedQuotaBalanceValue = '00.00'.obs;
  RxBool isDigitalCoupon = false.obs;
  RxBool isSticker = false.obs;
  RxString coupType = "".obs;
  TextEditingController driverMobileNoController = TextEditingController();
  TextEditingController fuelQuotaController = TextEditingController();
  TextEditingController fuelBalanceController = TextEditingController();
  TextEditingController moneyBalanceController = TextEditingController();
  TextEditingController moneyQuotaController = TextEditingController();
  TextEditingController dcMoneyBalanceController = TextEditingController();

  RxBool isFillingDays = true.obs;
  RxBool isAllDays = true.obs;
  RxList<int> tag = <int>[].obs;
  String tags = "";
  RxString tagList = "".obs;
  List<String> tagLists = [];
  List<String> daysList = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  RxBool isAvailableStations = true.obs;
  RxList availableStationList = [
    {"value": true.obs, "title": "WAIE stations"}.obs,
    {"value": false.obs, "title": "Non-WAIE stations"}.obs,
    {"value": false.obs, "title": "Sub-contractors"}.obs,
  ].obs;
  RxList multipleSelected = [].obs;
  RxList stationList = [
    {
      "id": 0,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Khobar",
      "data": [
        {'value': false.obs, "title": "Al Khobar 204850"},
        {'value': false.obs, "title": "Al Khobar 201940"},
      ]
    },
    {
      "id": 1,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Abha",
      "data": [
        {'value': false.obs, "title": "Abha 204850"},
        {'value': false.obs, "title": "Abha 201940"},
      ]
    },
    {
      "id": 2,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Addam",
      "data": [
        {'value': false.obs, "title": "Addam 204850"},
        {'value': false.obs, "title": "Addam 201940"},
      ]
    },
    {
      "id": 3,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Addam",
      "data": [
        {'value': false.obs, "title": "Addam 204850"},
        {'value': false.obs, "title": "Addam 201940"},
      ]
    },
    {
      "id": 4,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Afif",
      "data": [
        {'value': false.obs, "title": "Afif 204850"},
        {'value': false.obs, "title": "Afif 201940"},
      ]
    },
    {
      "id": 5,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Ahsa",
      "data": [
        {'value': false.obs, "title": "Al Ahsa 204850"},
        {'value': false.obs, "title": "Al Ahsa 201940"},
      ]
    },
    {
      "id": 6,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Aqiq",
      "data": [
        {'value': false.obs, "title": "Al Aqiq 204850"},
        {'value': false.obs, "title": "Al Aqiq 201940"},
      ]
    },
    {
      "id": 7,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Artawia",
      "data": [
        {'value': false.obs, "title": "Al Artawia 204850"},
        {'value': false.obs, "title": "Al Artawia 201940"},
      ]
    },
    {
      "id": 8,
      "value": false.obs,
      "isSelected": false.obs,
      "title": "Al Artawia",
      "data": [
        {'value': false.obs, "title": "Al Artawia 204850"},
        {'value': false.obs, "title": "Al Artawia 201940"},
      ]
    },
  ].obs;

  clear() {
    plateController.clear();
    driverNameController.clear();
    limitController.clear();
    driverPasswordController.clear();
    vehicleReferenceController.clear();
    driverMobileNoController.clear();
    fuelQuotaController.clear();
    moneyBalanceController.clear();
    moneyQuotaController.clear();
    dcMoneyBalanceController.clear();
  }

  void addFleets(tank) async {
    var client = http.Client();
    var body = {
      "CUSTID ": "",
      "EMAILID ": "",
      "IsDC ": isDigitalCoupon.value.toString(),
      "SERVICESTATUS ": "",
      "PLATENO ": plateController.text,
      "VEHICLETYPE ": "",
      "VEHLIC_TYPE ": "",
      "DRIVER ": driverNameController.text,
      "FUELTYPE ": "",
      "QUOTATYPE ": "",
      "QUOTAVALUE": "100",
      "QUOTABAL ": "0",
      "DC_QUOTAVALUE ": "0",
      "DC_QUOTAVALUE2 ": "0",
      "SERVICETYPE ": "",
      "PWFLAG ": "",
      "SERVICEPW ": "",
      "QUOTACLASS ": "",
      "ISOFFLINE ": "",
      "OFFLINELIMIT ": limitController.text,
      "DIVISION ": "",
      "DIVISIONNAME ": "",
      "BRANCH ": "",
      "BRANCHNAME ": "",
      "DEPTNO ": "",
      "DEPTNAME ": "",
      "OPERATION ": "",
      "OPERATIONNAME ": "",
      "TANK_NO ": "1", //tank = true ? "1" : "2",
      "TRANSFERDATE ": "",
      "UNIQUEFIELD ": "",
      "INSTDATE ": "",
      "FILLINGDAYS ": "",
      "SERIALID ": "",
      "SERIALCODE ": "",
      "STATION_PUSH ": "",
      "UNLI_VALUE ": vehicleReferenceController.text,
      "STATIONS ": "",
      "NEWSTATIONS": "",
      "DC_STATIONS": "",
      "SUB_STATIONS ": "",
      "DELETEDSTATIONS ": "",
      "PINBLOCK ": driverPasswordController.text,
      "DC_MOBILENO ": driverMobileNoController.text,
      "COUPTYPE ": "",
      "TERMINATE ": "",
      "QUOTACLASS": "",
    };

    print("Request Body ======== $body");

    try {
      String username = ApiEndPoints.username;
      String password = ApiEndPoints.password;
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.addFleet),
          body: jsonEncode(body),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          });

      List result = jsonDecode(response.body);

      print("addFleets statusCode===> ${response.statusCode}");
      print(" addFleetsresponse body===> ${response.body}");

      print(" addFleets response ===> $result");
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
