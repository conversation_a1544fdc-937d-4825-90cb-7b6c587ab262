// ignore_for_file: prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/promotion_controller/promotion_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';

import '../../../../utils/constants.dart';

class NewPromotionScreen extends StatefulWidget {
  const NewPromotionScreen({super.key});

  @override
  State<StatefulWidget> createState() => _NewPromotionalScreen();
}

class _NewPromotionalScreen extends State<NewPromotionScreen> {
  /*const NewPromotionScreen({Key? key}) : super(key: key);*/
  PromotionController promotionalController = Get.put(PromotionController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        verticalSpace(24),
        Container(
          decoration: BoxDecoration(
            color: AppColor.cLightGrey,
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Image.asset(DefaultImages.tawuniyaIMG),
                  Text("Tawuniya\nPromotions",
                      style: pSemiBold18,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.justify)
                ],
              ),
              verticalSpace(24),
              Text(
                'Promo Smart Card'.trr,
                style: pRegular12,
              ),
              verticalSpace(4),
              CommonTextField(
                controller: promotionalController.PromoFieldValue,
                /* onChanged: (value) {
                  // Listen to text changes and update enteredText variable
                  setState(() {
                   promotionalController.PromoFieldValue.value;
                  });
                },*/
                labelText: '',
                hintText: 'Please enter Card ID here'.trr + '...',
                fillColor: AppColor.cBackGround,
                filled: true,
              ),
              verticalSpace(8),
              CommonButton(
                title: 'Activate'.trr,
                btnColor: AppColor.themeOrangeColor,
                onPressed: () {
                  print("Smart Tawuniya\nPromotions");
                  print(promotionalController.PromoFieldValue.value);
                  promotionalController.activePromoCode("CARD", "TW");
                },
              ),
              verticalSpace(24),
              Text(
                'Promo Topup Code'.trr,
                style: pRegular12,
              ),
              verticalSpace(4),
              CommonTextField(
                controller: promotionalController.tawuniyaTopupController,
                labelText: '',
                hintText: 'Please enter code here'.trr + '...',
                fillColor: AppColor.cBackGround,
                filled: true,
              ),
              verticalSpace(8),
              CommonButton(
                title: 'Activate'.trr,
                btnColor: AppColor.themeOrangeColor,
                onPressed: () {
                  print("Topup Tawuniya\nPromotions");
                  print(promotionalController.tawuniyaTopupController.value);
                  promotionalController.activePromoCode("CODE", "TW");
                },
              ),
            ],
          ),
        ),
        verticalSpace(24),
        Container(
          decoration: BoxDecoration(
            color: AppColor.cLightGrey,
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Image.asset(DefaultImages.walaplusImage),
                  Expanded(
                    child: Text(
                      "WALA Plus\n Promotions",
                      style: pSemiBold18,
                      maxLines: 2,
                      textAlign: TextAlign.end,
                    ),
                  )
                ],
              ),
              verticalSpace(24),
              Text(
                'Promo Topup Code'.trr,
                style: pRegular12,
              ),
              verticalSpace(4),
              CommonTextField(
                controller: promotionalController.walaTopupController,
                labelText: '',
                hintText: 'Please enter code here'.trr + '...',
                fillColor: AppColor.cBackGround,
                filled: true,
              ),
              verticalSpace(8),
              CommonButton(
                title: 'Activate'.trr,
                btnColor: AppColor.themeOrangeColor,
                onPressed: () {
                  print("WALA Plus\n Promotions");
                  print(promotionalController.walaTopupController.value);
                  promotionalController.activePromoCode("CODE", "WP");
                },
              ),
            ],
          ),
        ),
        if (Constants.YGGEnable == "Y") verticalSpace(24),
        if (Constants.YGGEnable == "Y")
          Container(
            decoration: BoxDecoration(
              color: AppColor.cLightGrey,
              borderRadius: BorderRadius.circular(6),
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Image.asset(DefaultImages.yggImage),
                    Expanded(
                      child: Text(
                        "You Got a Gift\n Promotions",
                        style: pSemiBold18,
                        maxLines: 2,
                        textAlign: TextAlign.end,
                      ),
                    )
                  ],
                ),
                verticalSpace(24),
                Text(
                  'Promo Topup Code'.trr,
                  style: pRegular12,
                ),
                verticalSpace(4),
                CommonTextField(
                  controller: promotionalController.yggCODETopupController,
                  labelText: '',
                  hintText: 'Please enter code here'.trr + '...',
                  fillColor: AppColor.cBackGround,
                  filled: true,
                ),
                verticalSpace(4),
                Text(
                  'Pin'.trr,
                  style: pRegular12,
                ),
                verticalSpace(4),
                CommonTextField(
                  controller: promotionalController.yggPINTopupController,
                  labelText: '',
                  hintText: 'Please enter pin here'.trr + '...',
                  fillColor: AppColor.cBackGround,
                  filled: true,
                ),
                verticalSpace(8),
                CommonButton(
                  title: 'Activate'.trr,
                  btnColor: AppColor.themeOrangeColor,
                  onPressed: () {
                    print("You Got a Gift\n Promotions");
                    print(promotionalController.yggCODETopupController.value);
                    print(promotionalController.yggPINTopupController.value);
                    promotionalController.activePromoCode("CODE", "YGG");
                  },
                ),
              ],
            ),
          ),
        // verticalSpace(24),
        // Container(
        //   decoration: BoxDecoration(
        //     color: AppColor.cLightGrey,
        //     borderRadius: BorderRadius.circular(6),
        //   ),
        //   padding: const EdgeInsets.all(16),
        //   child: Column(
        //     crossAxisAlignment: CrossAxisAlignment.start,
        //     children: [
        //       Row(
        //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //         children: [
        //           Image.asset(DefaultImages.stcImage),
        //           Expanded(
        //             child: Text(
        //               "STC\n Promotions",
        //               style: pSemiBold18,
        //               maxLines: 2,
        //               textAlign: TextAlign.end,
        //             ),
        //           )
        //         ],
        //       ),
        //       verticalSpace(24),
        //       Text(
        //         'Promo Topup Code'.trr,
        //         style: pRegular12,
        //       ),
        //       verticalSpace(4),
        //       CommonTextField(
        //         controller: promotionalController.stcTopupController,
        //         labelText: '',
        //         hintText: 'Please enter code here'.trr + '...',
        //         fillColor: AppColor.cBackGround,
        //         filled: true,
        //       ),
        //       verticalSpace(8),
        //       CommonButton(
        //         title: 'Activate'.trr,
        //         btnColor: AppColor.themeOrangeColor,
        //         onPressed: () {
        //           print("STC\n Promotions");
        //           print(promotionalController.stcTopupController.value);
        //           promotionalController.activePromoCode("CODE", "STCP");
        //         },
        //       ),
        //     ],
        //   ),
        // ),
      ],
    );
  }
}
