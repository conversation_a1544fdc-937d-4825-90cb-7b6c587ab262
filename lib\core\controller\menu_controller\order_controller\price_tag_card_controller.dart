import 'dart:convert';

import 'package:get/get.dart';
import 'package:waie_app/models/pricetagcard.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/models/profile.dart';

class PriceTagCardController extends GetxController {
  var priceTagCards = <PriceTagCardModel>[].obs;
  Future<List<PriceTagCardModel>> fetchPriceTagCards() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var custid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);

    List<PriceTagCardModel> prices = [];

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getPriceTagCard),
        body: {"custid": custid, "accttype": userData.auCust?.accttype});

    List result = jsonDecode(response.body);

    for (int i = 0; i < result.length; i++) {
      PriceTagCardModel price =
          PriceTagCardModel.fromMap(result[i] as Map<String, dynamic>);
      prices.add(price);
    }
    priceTagCards.value = prices;

    return priceTagCards;
  }

  @override
  void onInit() {
    super.onInit();
    fetchPriceTagCards();
  }
}
