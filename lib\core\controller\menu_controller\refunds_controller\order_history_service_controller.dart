// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';

class OrderHistoryServiceController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  GetStorage usersData = GetStorage('usersData');
  var loadOrderHistoryServices = <RefundableService>[].obs;

  getloadOrderHistoryServices() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = jsonEncode(custsData.read('custData'));
    var userData = jsonDecode(jsonEncode(usersData.read('usrData')));
    print("OrderHistoryCardController custid>>>>>>> $custid");
    print("OrderHistoryCardController emailid>>>>>>> $emailid");
    print("OrderHistoryCardController custData>>>>>>> $custData");
    print("OrderHistoryCardController userData>>>>>>> ${userData['USERNAME']}");
    var client = http.Client();
    try {
      //if (loadOrderHistoryServices.isEmpty) {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.loadOrderHistoryService),
          body: {
            "custdata": custData,
            "stype": "T",
            "IsAR": "true",
            "pageNo": "1",
            "username": userData['USERNAME'],
          });
      //List result = jsonDecode(response.body);
      print("OrderHistoryServiceController response >>>>> $response");
      print(
          "OrderHistoryServiceController STATUS >>>>> ${response.statusCode}");

      //print("OrderHistoryServiceController result >>>>> $result");
      //print("OrderHistoryServiceController COUNT >>>>> ${result.length}");
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");

      // for (int i = 0; i < result.length; i++) {
      //   RefundableService order =
      //       RefundableService.fromJson(result[i] as Map<String, dynamic>);
      //   loadOrderHistoryServices.add(order);
      // }
      print("===============================================================");
      //print(
      //"OrderHistoryServiceController >>>>> ${jsonDecode(jsonEncode(loadOrderHistoryTopups))}")
      print("===============================================================");

      //return loadOrderHistoryServices;
      //}
      //print("ERROR: NO DATA");
      return [];
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('OrderHistoryTopupController');
    // print(jsonDecode(jsonEncode(loadOrderHistoryServices)));
    // if (loadOrderHistoryServices.isEmpty) {
    //   print("sulod");
    //   await getloadOrderHistoryServices();
    // }
    await getloadOrderHistoryServices();
    //Navigator.of(Get.context!).pop();
  }
}
