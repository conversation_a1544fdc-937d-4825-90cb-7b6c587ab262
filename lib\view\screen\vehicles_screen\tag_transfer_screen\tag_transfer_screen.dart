// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/incoming_tags_transfer_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/tag_transfer_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/user_management_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/no_tag_transfer_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/tag_action_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../my_fleet/my_fleet_screen.dart';
import 'incoming_tags_transfer_widget.dart';
import 'transfer_tags_widget.dart';
import 'package:gap/gap.dart';

class TagTransferScreen extends StatefulWidget {
  const TagTransferScreen({super.key});

  @override
  State<TagTransferScreen> createState() => _TagTransferScreenState();
}

class _TagTransferScreenState extends State<TagTransferScreen> {
  TagTransferController tagTransferController =
      Get.put(TagTransferController());

  IncomingTagsTransferController incomingTagsTransferController =
      Get.put(IncomingTagsTransferController());

  // @override
  // void initState() {
  //   // TODO: implement initState
  //   super.initState();
  //   tagTransferController.fetchTagTransferHistory();
  // }

  @override
  Widget build(BuildContext context) {
    print("TagTransferScreen");
    return Expanded(
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () => incomingTagsTransferController
                      .tagTransferCompanyInfos.isNotEmpty
                  ? Row(
                      children: [
                        Expanded(
                            child: Text(
                                "You have an incoming service transfer request for confirmation.",
                                style: pRegular12,
                                maxLines: 2)),
                        Gap(13),
                        Expanded(
                          child: CommonButton(
                            title: "VIEW TRANSFER DETAILS".trr,
                            onPressed: () async {
                              // showModalBottomSheet(
                              //   context: context,
                              //   shape: RoundedRectangleBorder(
                              //       borderRadius:
                              //           BorderRadius.vertical(top: Radius.circular(16))),
                              //   backgroundColor: AppColor.cBackGround,
                              //   barrierColor: AppColor.cBlackOpacity,
                              //   isScrollControlled: true,
                              //   builder: (context) {
                              //     return TransferTagsWidget();
                              //   },
                              // );
                              await incomingTagsTransferController.getDatas();
                            },
                            width: 100,
                            horizontalPadding: 14,
                            btnColor: AppColor.themeOrangeColor,
                          ),
                        )
                      ],
                    )
                  : SizedBox(),
            ),
            // verticalSpace(24),
            // Row(
            //   children: [
            //     // Expanded(
            //     //   child: CommonTextField(
            //     //     controller: tagTransferController.searchController,
            //     //     labelText: '',
            //     //     hintText: 'Search',
            //     //     prefixIcon: Padding(
            //     //       padding: const EdgeInsets.all(12),
            //     //       child: assetSvdImageWidget(image: DefaultImages.searchIcn),
            //     //     ),
            //     //   ),
            //     // ),
            //     // horizontalSpace(16),
            //     Expanded(
            //         child: CommonDropdownButtonWidget(
            //       labelText: '',
            //       value: tagTransferController.searchValue.value,
            //       list: tagTransferController.searchList,
            //       onChanged: (value) {
            //         tagTransferController.searchValue.value = value;
            //       },
            //     ))
            //   ],
            // ),
            verticalSpace(32),
            Obx(
              () => tagTransferController.tagTransferHistoryLists.isNotEmpty
                  ? ListView.builder(
                      itemCount:
                          tagTransferController.tagTransferHistoryLists.length,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = tagTransferController
                            .tagTransferHistoryLists[index];
                        var count = tagTransferController
                            .tagTransferHistoryLists.length;
                        //print('data12312312 ${jsonDecode(jsonEncode(data))}');
                        return tagTransferDetailWidget(
                          // value: data['value'].value,
                          // onChanged: (value) {
                          //   data['value'].value = value;
                          // },
                          title: data.reqId,
                          type: data.ttype,
                          qty: data.qty.toString(),
                          status: data.statusDisp,
                          textColor: data.statusDisp == "APPROVED"
                              ? AppColor.cGreen
                              : data.statusDisp == "CANCELLED"
                                  ? AppColor.cRedText
                                  : AppColor.cDarkBlueFont,
                          bgColor: data.statusDisp == "APPROVED"
                              ? AppColor.cLightGreen
                              : data.statusDisp == "CANCELLED"
                                  ? AppColor.cLightRedContainer
                                  : AppColor.cLightBlueContainer,
                          totalTag: count.toString(),
                          companyName: data.customer,
                          date: data.reqDate,
                          moreTap: () {
                            showModalBottomSheet(
                              context: context,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(16))),
                              backgroundColor: AppColor.cBackGround,
                              barrierColor: AppColor.cBlackOpacity,
                              isScrollControlled: true,
                              builder: (context) {
                                return TagActionWidget(
                                  code: data.reqId,
                                  status: data.statusDisp,
                                );
                              },
                            );
                          },
                        );
                      })
                  : NoTagTransferWidgetScreen(),
            ),
          ],
        ),
      ),
    );
  }

  Widget tagTransferDetailWidget(
      {bool? value,
      void Function(bool?)? onChanged,
      String? title,
      String? type,
      String? qty,
      String? status,
      Color? textColor,
      Color? bgColor,
      String? totalTag,
      String? companyName,
      String? date,
      Function()? moreTap}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        padding: EdgeInsets.fromLTRB(16, 16, 16, 16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: AppColor.cLightGrey),
            color: AppColor.lightBlueColor),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: Get.width - 80,
                // color: Colors.green,
                child: Row(
                  children: [
                    // SizedBox(
                    //     height: 24,
                    //     width: 24,
                    //     child: Checkbox(
                    //       value: value,
                    //       onChanged: onChanged,
                    //       activeColor: AppColor.themeDarkBlueColor,
                    //       shape: RoundedRectangleBorder(
                    //           borderRadius: BorderRadius.circular(4)),
                    //       side: BorderSide(
                    //         color: AppColor.cBlack,
                    //       ),
                    //     )),
                    // horizontalSpace(10),
                    // horizontalSpace(Get.width*0.03),

                    Text(
                      title!,
                      style: pBold16.copyWith(fontSize: 14),
                    ),
                    horizontalSpace(14),
                    // horizontalSpace(Get.width*0.04),
                    statusWidget(
                        text: status,
                        textColor: textColor,
                        color: bgColor,
                        horizontalSpace: 4)
                  ],
                ),
              ),
              Expanded(
                  flex: 1,
                  child: GestureDetector(
                      onTap: moreTap,
                      child: assetSvdImageWidget(
                          image: DefaultImages.verticleMoreIcn)))
            ],
          ),
          Gap(12),
          userDataRowWidget(title: "Company Name:".trr, value: companyName!),
          Gap(12),
          userDataRowWidget(title: "Date:".trr, value: date!),
          Gap(12),
          userDataRowWidget(title: "Type:".trr, value: type!),
          Gap(12),
          userDataRowWidget(title: "Qty:".trr, value: qty!),
        ]),
      ),
    );
  }
}
