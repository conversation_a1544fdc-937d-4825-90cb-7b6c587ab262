// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:get/get_connect/http/src/request/request.dart';
import 'package:http_parser/http_parser.dart';

import 'package:file_picker/src/file_picker_result.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:mime/mime.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_service_menu_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_topup_menu_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../view/screen/dashboard_manager/dashboard_manager.dart';
import '../../../../view/widget/loading_widget.dart';

class SubmitOrderRefundTopupController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  final topupOrderRefund = GetStorage();
  var refundablesServices = <RefundableService>[].obs;
  TextEditingController accountHolderController = TextEditingController();
  TextEditingController bankController = TextEditingController();
  TextEditingController iBanNoController = TextEditingController();
  TextEditingController reasonRefundController = TextEditingController();

  RxDouble tAmt = 0.00.obs;
  RxDouble tVAT = 0.00.obs;

  RxString companyReg = ''.obs;
  RxString bankLetter = ''.obs;
  RxString rblRefundOPT = ''.obs;
  List ibanList = ["SA"];
  RxString selectedIBAN = 'SA'.obs;

  submitOrderRefundFormB() async {
    Loader.showLoader();
    var custData = jsonEncode(custsData.read('custData'));
    var orderRefundCurrentOrderIDList =
        topupOrderRefund.read('orderRefundCurrentOrderIDList');
    var tAmt = topupOrderRefund.read('topupTotaltAmt');
    var tVAT = topupOrderRefund.read('topupTotaltVAT');
    var chkres = topupOrderRefund.read('chkRES');

    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.submitRefundFormTopup),
          body: {
            "custdata": custData,
            "rblRefundOptions": rblRefundOPT.toString(),
            "txtReason": reasonRefundController.text,
            "orderids": orderRefundCurrentOrderIDList,
            "tAmt": tAmt,
            "tVAT": tVAT,
            "chkRes": chkres,
            "stype": "",
            "otype": 'S', //SERVICE
          });
      var parsedJson = jsonDecode(response.body);
      if (parsedJson['response']['Action'] == "EXCEPTION") {
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    parsedJson['response']['Message'].toString(),
                    style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      clear();
                      topupOrderRefund.remove('orderRefundTagSerialIDList');
                      topupOrderRefund.remove('orderRefundTagOrderIDList');
                      topupOrderRefund.remove('tAmt');
                      topupOrderRefund.remove('tVAT');
                      topupOrderRefund.remove('chkRES');
                      topupOrderRefund.remove('orderType');
                      Get.to(() => OrderRefundTopupMenuScreen());
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
      if (parsedJson['response']['Action'] == "POPUP") {
        Loader.hideLoader();
        var msg = "";
        if (parsedJson['message'] != null) {
          msg = parsedJson['message'].toString();
        } else {
          msg = parsedJson['response']['Message'].toString();
        }
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    msg,
                    style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      clear();
                      topupOrderRefund.remove('orderRefundTagSerialIDList');
                      topupOrderRefund.remove('orderRefundTagOrderIDList');
                      topupOrderRefund.remove('tAmt');
                      topupOrderRefund.remove('tVAT');
                      topupOrderRefund.remove('chkRES');
                      topupOrderRefund.remove('orderType');
                      Get.to(() => OrderRefundTopupMenuScreen());
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
      print(parsedJson);
      print(parsedJson['message']);
      print(parsedJson['response']['Action']);
      print(parsedJson['response']['Message']);

      return response;
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  submitOrderRefundFormQ(bankLetter, companyRegistration) async {
    Loader.showLoader();
    var custData = jsonEncode(custsData.read('custData'));
    var orderRefundCurrentOrderIDList =
        topupOrderRefund.read('orderRefundCurrentOrderIDList');
    var tAmt = topupOrderRefund.read('topupTotaltAmt');
    var tVAT = topupOrderRefund.read('topupTotaltVAT');
    var chkres = topupOrderRefund.read('chkRES');
    print("submitFile tAmt $tAmt");
    print("submitFile tVAT $tVAT");
    print(
        "submitFile orderRefundCurrentOrderIDList $orderRefundCurrentOrderIDList");
    print("submitFile chkres $chkres");

    print("submitFile bankLetter $bankLetter");
    print("submitFile bankLetter ${bankLetter.name}");
    print("submitFile bankLetter ${bankLetter.readStream}");
    print("submitFile companyRegistration $companyRegistration");
    print("submitFile companyRegistration ${companyRegistration.name}");
    print("submitFile companyRegistration ${companyRegistration.readStream}");
    //submitFile bankLetter PlatformFile(, name: bank_letter.pdf, bytes: null, readStream: Instance of '_ControllerStream<List<int>>', size: 13264)
    // for (var element in bankLetter!.files) {
    //   print(element.name);
    //   print(element.identifier);
    //   print("submitFile bankLetter ${bankLetter.files.first.size}");
    // }
    final file = bankLetter;
    final mimeType = lookupMimeType(file.name) ?? '';
    // for (var element in companyRegistration!.files) {
    //   print(element.name);
    //   print(
    //       "submitFile companyRegistration ${companyRegistration.files.first.size}");
    // }

    var client = http.Client();
    try {
      // var response = await client.post(
      //     Uri.parse(
      //         ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.submitFile),
      //     body: {
      //       "custid": "000000054A",
      //       "IsAR": Constants.IsAr_App,
      //     });

      var bankletter = bankLetter.readStream;
      var companyregistration = companyRegistration.readStream;

      print("bankletter $bankletter");
      print("companyrvegistration $companyregistration");

      var request = http.MultipartRequest(
          'POST',
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.submitRefundFormTopup));
      var headers = {'Content-Type': 'application/pdf; charset=UTF-8'};

      request.files.add(http.MultipartFile(
          'bankletter', bankletter!, bankLetter.size,
          filename: bankLetter.name,
          contentType: MediaType('application', 'pdf')));

      bankletter.cast();

      request.files.add(http.MultipartFile(
          'companyregistration', companyregistration!, companyRegistration.size,
          filename: companyRegistration.name,
          contentType: MediaType('application', 'pdf')));
      companyregistration.cast();
      request.headers.addAll(headers);
      request.fields['custdata'] = custData;
      request.fields['chkRes'] = chkres;
      request.fields['rblRefundOptions'] = rblRefundOPT.toString();
      request.fields['txtReason'] = reasonRefundController.text;
      request.fields['orderids'] = orderRefundCurrentOrderIDList;
      request.fields['tAmt'] = tAmt;
      request.fields['tVAT'] = tVAT;
      //request.fields['nationIdNo'] = '**********';
      request.fields['accountHolder'] = accountHolderController.text;
      request.fields['ibanNo'] = iBanNoController.text;
      request.fields['selectedBankType'] = selectedIBAN.toString();
      request.fields['otype'] = 'S'; //SERVICE
      var res = await request.send();
      // print("===============================================================");
      // print("res >>>>> ${jsonDecode(jsonEncode(res))}");
      // print("===============================================================");
      // List result = jsonDecode(response.body);

      // print("OrderRefundsController result >>>>> $result");
      // print("===============================================================");
      // print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      // print("===============================================================");

      // for (int i = 0; i < result.length; i++) {
      //   RefundableService order =
      //       RefundableService.fromJson(result[i] as Map<String, dynamic>);
      //   refundablesServices.add(order);
      // }
      // print("===============================================================");
      // print(
      //     "loadPlacesloadPlacesloadPlacesloadPlaces >>>>> ${jsonDecode(jsonEncode(refundablesServices))}");
      // print("===============================================================");
      // var response = await http.Response.fromStream(res);
      // print("===============================================================");
      // print("response >>>>> ${jsonDecode(response.body)}");
      // print("===============================================================");

      return res.stream.bytesToString().asStream().listen((event) {
        var parsedJson = json.decode(event);
        print(
            "===============================================================");
        print(parsedJson['response']['Action']);
        print(parsedJson['response']['Message']);
        print(
            "===============================================================");
        if (parsedJson['response']['Action'] == "EXCEPTION") {
          Loader.hideLoader();
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      parsedJson['response']['Message'].toString(),
                      style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        clear();
                        topupOrderRefund.remove('orderRefundTagSerialIDList');
                        topupOrderRefund.remove('orderRefundTagOrderIDList');
                        topupOrderRefund.remove('tAmt');
                        topupOrderRefund.remove('tVAT');
                        topupOrderRefund.remove('chkRES');
                        topupOrderRefund.remove('orderType');
                        //Get.to(() => OrderRefundTopupMenuScreen());
                        await Get.to(
                            () => DashBoardManagerScreen(
                                  currantIndex: 0,
                                ),
                            preventDuplicates: false);
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
        if (parsedJson['response']['Action'] == "POPUP") {
          Loader.hideLoader();
          var msg = "";
          if (parsedJson['message'] != null) {
            msg = parsedJson['message'].toString();
          } else {
            msg = parsedJson['response']['Message'].toString();
          }
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      msg,
                      style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        clear();
                        topupOrderRefund.remove('orderRefundTagSerialIDList');
                        topupOrderRefund.remove('orderRefundTagOrderIDList');
                        topupOrderRefund.remove('tAmt');
                        topupOrderRefund.remove('tVAT');
                        topupOrderRefund.remove('chkRES');
                        topupOrderRefund.remove('orderType');
                        //Get.to(() => OrderRefundTopupMenuScreen());
                        await Get.to(
                            () => DashBoardManagerScreen(
                                  currantIndex: 0,
                                ),
                            preventDuplicates: false);
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
      });
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  clear() {
    accountHolderController.clear();
    bankController.clear();
    iBanNoController.clear();
    reasonRefundController.clear();
  }
}
