// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/order_history_service.dart';
import 'package:waie_app/models/order_history_topup.dart';
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../../utils/constants.dart';

class OrderHistoryTopupController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  var loadOrderHistoryTopups = <OrderHistoryTopupModel>[].obs;

  getLoadOrderHistoryTopup() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = jsonEncode(custsData.read('custData'));
    print("OrderHistoryTopupController custid>>>>>>> $custid");
    print("OrderHistoryTopupController emailid>>>>>>> $emailid");
    var client = http.Client();
    try {
      if (loadOrderHistoryTopups.isEmpty) {
        var response = await client.post(
            Uri.parse(ApiEndPoints.baseUrl +
                ApiEndPoints.authEndpoints.loadOrderHistoryTopup),
            body: {
              "custdata": custData,
              "IsAR": Constants.IsAr_App,
              "pageNo": "1",
            });
        List result = jsonDecode(response.body);
        print(
            "OrderHistoryTagController response >>>>> ${jsonDecode(response.body)}");
        print(
            "OrderHistoryTopupController STATUS >>>>> ${response.statusCode}");

        //print("OrderHistoryTopupController result >>>>> $result");
        //print("OrderHistoryTopupController COUNT >>>>> ${result.length}");
        print(
            "===============================================================");
        print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
        print(
            "===============================================================");

        for (int i = 0; i < result.length; i++) {
          OrderHistoryTopupModel order = OrderHistoryTopupModel.fromJson(
              result[i] as Map<String, dynamic>);
          loadOrderHistoryTopups.add(order);
        }
        print(
            "===============================================================");
        //print(
        //"OrderHistoryTopupController >>>>> ${jsonDecode(jsonEncode(loadOrderHistoryTopups))}")
        print(
            "===============================================================");

        return loadOrderHistoryTopups;
      }
      print("ERROR: NO DATA");
      return [];
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('OrderHistoryTopupController');
    print(jsonDecode(jsonEncode(loadOrderHistoryTopups)));
    if (loadOrderHistoryTopups.isEmpty) {
      print("sulod");
      await getLoadOrderHistoryTopup();
    }
    //Navigator.of(Get.context!).pop();
  }


}


