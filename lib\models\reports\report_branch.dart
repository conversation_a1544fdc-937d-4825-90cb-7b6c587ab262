import 'dart:convert';

class Report_Branch {
  final String BRANCHCODE;
  final String BRANCHNAME;
  final String PARENTID;

  Report_Branch({
    required this.BRANCH<PERSON><PERSON>,
    required this.BRANCHNAME,
    required this.PARENTID,
  });

  Map<String, dynamic> toMap() {
    return {
      'BRANCHCODE': <PERSON><PERSON><PERSON><PERSON><PERSON>,
      'BRANCHNAME': BRANCHNA<PERSON>,
      'PARENTID': PARENTID,
    };
  }

  factory Report_Branch.fromMap(Map<String, dynamic> map) {
    return Report_Branch(
      BRANCHCODE: map['BRANCHCODE'] ?? '',
      BRANCHNAME: map['BRANCHNAME'] ?? '',
      PARENTID: map['PARENTID'] ?? '',
    );
  }
  String toJson() => json.encode(toMap());

  factory Report_Branch.fromJson(String source) =>
      Report_Branch.fromMap(json.decode(source));
}