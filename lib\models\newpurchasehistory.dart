import 'dart:convert';

NewPurchaseHistory newPurchaseHistoryFromJson(String str) =>
    NewPurchaseHistory.fromJson(json.decode(str));

String newPurchaseHistoryToJson(NewPurchaseHistory data) =>
    json.encode(data.toJson());

class NewPurchaseHistory {
  List<OrderHistoryDt> orderHistoryDt;
  int totalCount;

  NewPurchaseHistory({
    required this.orderHistoryDt,
    required this.totalCount,
  });

  factory NewPurchaseHistory.fromJson(Map<String, dynamic> json) =>
      NewPurchaseHistory(
        orderHistoryDt: json["OrderHistoryDT"] != null
            ? List<OrderHistoryDt>.from(
                json["OrderHistoryDT"].map((x) => OrderHistoryDt.fromJson(x)))
            : [],
        totalCount: json["totalCount"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "OrderHistoryDT":
            List<dynamic>.from(orderHistoryDt.map((x) => x.toJson())),
        "totalCount": totalCount,
      };
}

class OrderHistoryDt {
  int line;
  String custid;
  String orderid;
  String ordertype;
  String ordertypes;
  DateTime? dateordered;
  String orderdate;
  String paytype;
  String paymode;
  String hasinvoice;
  dynamic invno;
  int totAmt;
  dynamic servicetype;
  dynamic qty;
  String accttype;
  String paid;
  String state;
  String status;
  String isorderclear;

  OrderHistoryDt({
    required this.line,
    required this.custid,
    required this.orderid,
    required this.ordertype,
    required this.ordertypes,
    required this.dateordered,
    required this.orderdate,
    required this.paytype,
    required this.paymode,
    required this.hasinvoice,
    required this.invno,
    required this.totAmt,
    required this.servicetype,
    required this.qty,
    required this.accttype,
    required this.paid,
    required this.state,
    required this.status,
    required this.isorderclear,
  });

  factory OrderHistoryDt.fromJson(Map<String, dynamic> json) => OrderHistoryDt(
        line: json["LINE"] ?? "",
        custid: json["CUSTID"] ?? "",
        orderid: json["ORDERID"] ?? "",
        ordertype: json["ORDERTYPE"] ?? "",
        ordertypes: json["ORDERTYPES"] ?? "",
        dateordered: json["DATEORDERED"] == null
            ? null
            : DateTime.parse(json["DATEORDERED"]),
        orderdate: json["ORDERDATE"] ?? "",
        paytype: json["PAYTYPE"] ?? "",
        paymode: json["PAYMODE"] ?? "",
        hasinvoice: json["HASINVOICE"] ?? "",
        invno: json["INVNO"] ?? "",
        totAmt: json["TOT_AMT"] ?? "",
        servicetype: json["SERVICETYPE"] ?? "",
        qty: json["QTY"] ?? "",
        accttype: json["ACCTTYPE"] ?? "",
        paid: json["PAID"] ?? "",
        state: json["STATE"] ?? "",
        status: json["STATUS"] ?? "",
        isorderclear: json["ISORDERCLEAR"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "LINE": line,
        "CUSTID": custid,
        "ORDERID": orderid,
        "ORDERTYPE": ordertype,
        "ORDERTYPES": ordertypes,
        "DATEORDERED": dateordered?.toIso8601String(),
        "ORDERDATE": orderdate,
        "PAYTYPE": paytype,
        "PAYMODE": paymode,
        "HASINVOICE": hasinvoice,
        "INVNO": invno,
        "TOT_AMT": totAmt,
        "SERVICETYPE": servicetype,
        "QTY": qty,
        "ACCTTYPE": accttype,
        "PAID": paid,
        "STATE": state,
        "STATUS": status,
        "ISORDERCLEAR": isorderclear,
      };
}
