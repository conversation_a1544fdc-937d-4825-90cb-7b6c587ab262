// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/bulk_actions_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/vehicle_controller/tag_installation_controller.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_installation/no_upcoming_installation_widget.dart';

import 'edit_tag_installation.dart';

class UpcomingScreen extends StatelessWidget {
  UpcomingScreen({Key? key}) : super(key: key);
  TagInstallationController tagInstallationController = Get.find();
  VehicleController vehicleController = Get.put(VehicleController());

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ListView(
        shrinkWrap: true,
        children: [
          tagInstallationController.upcomimgDataList.isEmpty
              ? NoUpcomingWidgetScreen()
              : Column(
                  children: [
                    verticalSpace(24),
                    buildTitleRowWidget(
                        totalTag: '24',
                        onTap: () {
                          vehicleController.isMyFleet.value = true;
                          vehicleController.isTagInstallation.value = false;
                          vehicleController.isScheduleInstallation.value = true;
                        }),
                    verticalSpace(24),
                    ListView.builder(
                      itemCount: tagInstallationController.upcomimgDataList.length,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = tagInstallationController.upcomimgDataList[index];
                        return upcomingDataWidget(
                          title: data['title'],
                          totalVehicle: data['totalVehicle'],
                          type: data['type'].toString().trr,
                          scheduledOn: data['scheduledOn'],
                          location: data['location'],
                          referenceId: data['referenceId'],
                          onTap: () {
                            MapsLauncher.launchCoordinates(data["latlng"].latitude, data["latlng"].longitude);
                          },
                          moreOnTap: () {
                            showModalBottomSheet(
                              context: context,
                              shape:
                                  RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                              builder: (context) {
                                return tagInstallationActionWidget(
                                  title: data['title'],
                                  date: data['scheduledOn'],
                                  editOnTap: () {
                                    Get.back();
                                    Get.to(EditTagInstallationWidget());
                                  },
                                  deleteOnTap: () {
                                    Get.back();
                                    showDialog(
                                      context: context,
                                      builder: (context) {
                                        return AlertDialog(
                                          insetPadding: EdgeInsets.all(16),
                                          contentPadding: EdgeInsets.all(16),
                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                          content: cancelDialogWidget(
                                            cancelTap: () {
                                              Get.back();
                                            },
                                            deleteTap: () {
                                              Get.back();
                                            },
                                          ),
                                        );
                                      },
                                    );
                                  },
                                );
                              },
                            );
                          },
                        );
                      },
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  buildTitleRowWidget({String? totalTag, Function()? onTap}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Tags available".trr,
              style: pSemiBold18,
            ),
            verticalSpace(8),
            Text(
             "${"Total".trr} $totalTag ${"Tags available".trr}", // "You have $totalTag more tags available.",
              style: pRegular12,
            ),
          ],
        ),
        CommonButton(
          title: 'SHOW'.trr,
          onPressed: onTap,
          height: 40,
          width: 97,
          horizontalPadding: 16,
          btnColor: AppColor.themeOrangeColor,
        )
      ],
    );
  }
}

Widget upcomingDataWidget({
  String? title,
  String? totalVehicle,
  String? type,
  String? scheduledOn,
  String? location,
  String? referenceId,
  Function()? onTap,
  Function()? moreOnTap,
}) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 12),
    child: Container(
      decoration: BoxDecoration(
        color: AppColor.cLightOrangeContainer,
        borderRadius: BorderRadius.circular(4),
      ),
      padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: Column(children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                assetSvdImageWidget(image: DefaultImages.arrowRightIcn),
                horizontalSpace(10),
                Text(
                  title!,
                  style: pSemiBold14,
                ),
              ],
            ),
            GestureDetector(onTap: moreOnTap, child: assetSvdImageWidget(image: DefaultImages.verticleMoreIcn))
          ],
        ),
        verticalSpace(6),
        Row(
          children: [
            Text(
              "$totalVehicle Vehicles",
              style: pRegular13,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: assetSvdImageWidget(image: DefaultImages.dotIcn),
            ),
            Text(
              type!,
              style: pRegular13,
            ),
          ],
        ),
        verticalSpace(18),
        dataWidget(title: 'Scheduled on'.trr, subtitle: scheduledOn!),
        verticalSpace(12),
        dataWidget(title: 'Location'.trr, subtitle: location!),
        verticalSpace(12),
        dataWidget(title: 'Reference ID'.trr, subtitle: referenceId!, titleColor: AppColor.cText, titleSize: 14),
        verticalSpace(30),
        GestureDetector(
          onTap: onTap,
          child: Container(
            // width: Get.width,
            // height: 44,
            decoration: BoxDecoration(
                color: AppColor.cWhite,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: AppColor.cDarkBlueBorder, width: 1)),
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(DefaultImages.googleMapIcn, height: 16, width: 15),
                horizontalSpace(11),
                Text(
                  'Show on Google Maps'.trr,
                  style: pRegular14.copyWith(color: AppColor.cDarkBlueFont),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        )
      ]),
    ),
  );
}

dataWidget({required String title, required String subtitle, Color? titleColor, double? titleSize}) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      SizedBox(
          width: 140,
          child: Text(
            title,
            style: pRegular13.copyWith(color: titleColor ?? AppColor.cDarkGreyFont, fontSize: titleSize ?? 13),
          )),
      Expanded(
        child: Text(
          subtitle,
          style: pRegular13,
        ),
      ),
    ],
  );
}

Widget cancelDialogWidget({Function()? cancelTap, Function()? deleteTap}) {
  return Container(
    decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            Get.back();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              assetSvdImageWidget(image: DefaultImages.cancelIcn),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(24),
          child: Text("Are you sure you want to cancel this installation?".trr,
              style: pSemiBold14, textAlign: TextAlign.center),
        ),
        Text(
          "This can't be undone, but you can book a new appointment whenever you like.".trr,
          style: pRegular14,
          textAlign: TextAlign.center,
        ),
        verticalSpace(24),
        Row(
          children: [
            Expanded(
              child: CommonBorderButton(
                title: 'No, keep it'.trr,
                onPressed: cancelTap,
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.themeDarkBlueColor,
              ),
            ),
            horizontalSpace(16),
            Expanded(
              child: CommonButton(
                title: 'Yes, cancel'.trr,
                onPressed: deleteTap,
                btnColor: AppColor.cRedText,
                bColor: AppColor.cRedText,
              ),
            ),
          ],
        )
      ],
    ),
  );
}

Container tagInstallationActionWidget({String? title, String? date, Function()? editOnTap, Function()? deleteOnTap}) {
  return Container(
    decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
    padding: EdgeInsets.all(16),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title!,
          style: pSemiBold17,
        ),
        verticalSpace(8),
        Text(
          date!,
          style: pRegular13.copyWith(color: AppColor.cDarkFont),
        ),
        verticalSpace(23),
        bulkActionWidget(
          title: "Edit Appointment".trr,
          onTap: editOnTap!,
        ),
        bulkActionWidget(title: "Cancel appointment".trr, onTap: deleteOnTap!, textColor: AppColor.cDarkOrangeText)
      ],
    ),
  );
}
