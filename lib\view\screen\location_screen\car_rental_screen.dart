// ignore_for_file: prefer_const_constructors

import 'dart:io';

import 'package:flutter_svg/svg.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/core/controller/location_controller/car_rental_controller.dart';
import 'package:waie_app/core/controller/location_controller/car_service_controller.dart';
import 'package:waie_app/core/controller/location_controller/food_resturant_controller.dart';
import 'package:waie_app/core/controller/location_controller/mosque_controller.dart';
import 'package:waie_app/core/controller/location_controller/stations_controller.dart';
import 'package:waie_app/models/car_rental_station.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/screen/location_screen/location_widgets.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../../../core/controller/location_controller/gas_station_controller.dart';
import '../../../core/controller/location_controller/location_controller.dart';
import '../menu_screen/user_management_screen/user_management_screen.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import '../../../utils/colors.dart';
import '../../../utils/images.dart';
import 'package:get/get.dart';
import 'location_screen.dart';
import 'dart:ui' as ui;
import 'package:flutter_svg/svg.dart';
import 'package:geolocator/geolocator.dart';

class CarRentalScreen extends StatefulWidget {
  const CarRentalScreen({
    super.key,
  });

  @override
  State<CarRentalScreen> createState() => _CarRentalScreenState();
}

class _CarRentalScreenState extends State<CarRentalScreen> {
  CarRentalController carRentalController = Get.put(CarRentalController());
  GoogleMapController? mapController;
  late CameraPosition cameraPosition;

  final double _defaultLatitude = 24.741305;
  final double _defaultLongitude = 46.805976;
  final LatLng _center = const LatLng(24.741305000000001, 46.805976000000001);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    cameraPosition = CameraPosition(target: _center, zoom: 7.0);
    carRentalController.isDefaultMap.value = true;
    _fetchUserLocation();
  }

  Future<void> _fetchUserLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Please enable location services.")),
      );
      _useDefaultLocation();
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Location permissions are denied.")),
        );
        _useDefaultLocation();
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              "Location permissions are permanently denied. Please enable them in settings."),
          action: SnackBarAction(
            label: 'Settings',
            onPressed: () {
              Geolocator.openAppSettings();
            },
          ),
        ),
      );
      _useDefaultLocation();
      return;
    }

    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      carRentalController.userLatitude = position.latitude;
      carRentalController.userLongitude = position.longitude;

      carRentalController
          .getCarRentalStations200kmRadius(
            carRentalController.userLatitude!,
            carRentalController.userLongitude!,
            carRentalController.radiusInKm,
          )
          .then((_) => _add(carRentalController.carRentalstationsList));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Failed to get location: $e")),
      );
      _useDefaultLocation();
    }
  }

  void _useDefaultLocation() {
    carRentalController.userLatitude = _defaultLatitude;
    carRentalController.userLongitude = _defaultLongitude;
    carRentalController
        .getCarRentalStations200kmRadius(
          carRentalController.userLatitude!,
          carRentalController.userLongitude!,
          carRentalController.radiusInKm,
        )
        .then((_) => _add(carRentalController.carRentalstationsList));
  }

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;
    if (Platform.isIOS) {
      controller.setMapStyle('''
    [
      {
        "featureType": "water",
        "elementType": "labels",
        "stylers": [
          {
            "visibility": "off"
          }
        ]
      }
    ]
    ''');
    }
    //_add();
  }

  Map<MarkerId, Marker> markers = <MarkerId, Marker>{};

  Future<void> _add(List<CarRentalStation> stations) async {
    if (stations.isEmpty) {
      return;
    }

    markers.clear();

    for (int i = 0; i < stations.length; i++) {
      var markerIdVal = "$i";
      final MarkerId markerId = MarkerId(markerIdVal);

      final Marker marker = Marker(
        markerId: markerId,
        position: LatLng(
          double.parse(stations[i].latitude),
          double.parse(stations[i].longitude),
        ),
        icon: await createMarkerImageFromAsset(
            context, DefaultImages.gasStationMarker),
        onTap: () {
          List<String> availableProducts = [];

          if (stations[i].stnPetrol91 != null && stations[i].stnPetrol91 > 0) {
            availableProducts.add("PETROL91");
          }
          if (stations[i].stnPetrol95 != null && stations[i].stnPetrol95 > 0) {
            availableProducts.add("PETROL95");
          }
          if (stations[i].stnDiesel != null && stations[i].stnDiesel > 0) {
            availableProducts.add("DIESEL");
          }

          String productsDisplay = availableProducts.isNotEmpty
              ? availableProducts.join(", ")
              : "No products";

          showModalBottomSheet(
            context: context,
            barrierColor: AppColor.cBlackOpacity,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
            ),
            isScrollControlled: true,
            builder: (context) {
              return gasStationsBottomSheetWidget(
                title: stations[i].stnNameE.toString(),
                subTitle: stations[i].stnNameA.toString(),
                products: productsDisplay,
                latitude: double.parse(stations[i].latitude),
                longitude: double.parse(stations[i].longitude),
                coordinates: '${stations[i].latitude},${stations[i].longitude}',
              );
            },
          );
        },
      );

      if (mounted) {
        setState(() {
          markers[markerId] = marker;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => carRentalController.carRentalstationsList.isNotEmpty
        ? Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () async {
                          carRentalController.isDefaultMap.value = true;
                          await carRentalController
                              .getCarRentalStations200kmRadius(
                                  carRentalController.userLatitude!,
                                  carRentalController.userLongitude!,
                                  carRentalController.radiusInKm);
                          _add(carRentalController.carRentalstationsList);
                        },
                        child: Container(
                          width: (Get.width - 48) / 2,
                          height: 45,
                          decoration: BoxDecoration(
                            color: carRentalController.isDefaultMap.value
                                ? AppColor.cWhite
                                : AppColor.cLightGrey,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black12,
                                offset: Offset(0, 4),
                                blurRadius: 6,
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.map_rounded, color: AppColor.cText),
                              SizedBox(width: 6),
                              Text(
                                "Default".trr,
                                style: TextStyle(
                                    color:
                                        carRentalController.isDefaultMap.value
                                            ? AppColor.cText
                                            : AppColor.cDarkGreyFont),
                              ),
                            ],
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () async {
                          carRentalController.isDefaultMap.value = false;
                          _add(carRentalController.allStationsList);
                        },
                        child: Container(
                          width: (Get.width - 48) / 2,
                          height: 45,
                          decoration: BoxDecoration(
                            color: !carRentalController.isDefaultMap.value
                                ? AppColor.cWhite
                                : AppColor.cLightGrey,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black12,
                                offset: Offset(0, 4),
                                blurRadius: 6,
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(DefaultImages.mapIcn),
                              SizedBox(width: 6),
                              Text(
                                "All".trr,
                                style: TextStyle(
                                    color:
                                        !carRentalController.isDefaultMap.value
                                            ? AppColor.cText
                                            : AppColor.cDarkGreyFont),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 15,
                ),
                if (carRentalController.isMap.value == true) ...[
                  Expanded(
                    child: Container(
                      height: Get.height / 1.5,
                      decoration:
                          BoxDecoration(borderRadius: BorderRadius.circular(6)),
                      child: GoogleMap(
                        onMapCreated: _onMapCreated,
                        markers: Set<Marker>.of(markers.values),
                        // YOUR MARKS IN MAP
                        initialCameraPosition: cameraPosition,
                        scrollGesturesEnabled: true,
                      ),
                    ),
                  )
                ] else

                  //THIS WIDGET IS FOR LIST SHOW NOT MAP LIST ONLYN LIKE IN FIRST THREE MODULES
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: ListView.builder(
                        scrollDirection: Axis.vertical,
                        shrinkWrap: true,
                        physics: BouncingScrollPhysics(),
                        itemCount: (carRentalController.isDefaultMap == true)
                            ? carRentalController.carRentalstationsList.length
                            : carRentalController.allStationsList.length,
                        itemBuilder: (context, index) {
                          var data = (carRentalController.isDefaultMap == true)
                              ? carRentalController.carRentalstationsList[index]
                              : carRentalController.allStationsList[index];

                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: GestureDetector(
                                    onTap: () {
                                      showModalBottomSheet(
                                        context: context,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(12)),
                                        ),
                                        isScrollControlled: true,
                                        builder: (context) {
                                          List<String> availableProducts = [];

                                          if (data.stnPetrol91 != null &&
                                              data.stnPetrol91 > 0) {
                                            availableProducts
                                                .add("STN_PETROL91");
                                          }
                                          if (data.stnPetrol95 != null &&
                                              data.stnPetrol95 > 0) {
                                            availableProducts
                                                .add("STN_PETROL95");
                                          }
                                          if (data.stnDiesel != null &&
                                              data.stnDiesel > 0) {
                                            availableProducts.add("STN_DIESEL");
                                          }

                                          String productsDisplay =
                                              availableProducts.isNotEmpty
                                                  ? availableProducts.join(", ")
                                                  : "No products";

                                          return gasStationsBottomSheetWidget(
                                            title: data.stnNameE,
                                            subTitle: data.stnNameA,
                                            products: productsDisplay,
                                            latitude:
                                                double.parse(data.latitude),
                                            longitude:
                                                double.parse(data.longitude),
                                            coordinates: data.stnNameE,
                                          );
                                        },
                                      );
                                    },
                                    child: listDataContainer(
                                      title: data.stnNameE,
                                      subTitle: data.stnNameA,
                                    ),
                                  ),
                                )
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
          )
        : Center(
            child: CircularProgressIndicator(),
          ));
  }

  @override
  void dispose() {
    mapController?.dispose(); // Dispose of the controller
    super.dispose();
  }
}
