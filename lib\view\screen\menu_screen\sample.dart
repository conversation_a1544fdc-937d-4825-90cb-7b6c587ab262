// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/reports_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/fleet_wise_screen.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/fuel_consumption_screen.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/monthly_quota_screen.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/station_list_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../core/controller/menu_controller/reports_controller/samplecontroller.dart';

class SampleScreen extends StatefulWidget {
  const SampleScreen({super.key});

  @override
  State<SampleScreen> createState() => _SampleScreenState();
}

class _SampleScreenState extends State<SampleScreen> {
  late final List myList;
  final ScrollController _scrollController = ScrollController();
  final int _initialItemsLength = 1;
  bool isScrollEnable = false, isLoading = false;

  @override
  void initState() {
    super.initState();

    print("\ninitState work!");
    print("_initialItemsLength: $_initialItemsLength");
    myList = List.generate(_initialItemsLength, (i) => 'Item : ${i + 1}');
    _scrollController.addListener(() {
      print("\nListener work!");
      print("position: ${_scrollController.position.pixels}");
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) _getData();
    });
    _helper();
  }

  Future _helper() async {
    print("\nhelper work!");
    while (!isScrollEnable) {
      print("\nwhile loop work!");
      await Future.delayed(
          Duration.zero); //Prevent errors from looping quickly.
      try {
        print("maxScroll: ${_scrollController.position.maxScrollExtent}");
        isScrollEnable = 0 != _scrollController.position.maxScrollExtent;
        print("isScrollEnable: $isScrollEnable");

        if (!isScrollEnable) _getData();
      } catch (e) {
        print(e);
      }
    }
    print("\nwhile loop break!");
  }

  void _getData() {
    print("\n_getData work!");
    if (isLoading) return;
    isLoading = true;

    int i = myList.length;
    int j = myList.length + 1;
    for (i; i < j; i++) {
      myList.add("Item : ${i + 1}");
    }
    print("myList.length: ${myList.length}");
    isLoading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView.builder(
        controller: _scrollController,
        itemBuilder: (context, i) {
          if (i == myList.length) {
            return const CupertinoActivityIndicator();
          }
          return ListTile(title: Text(myList[i]));
        },
        itemCount: myList.length + 1,
      ),
    );
  }
}
