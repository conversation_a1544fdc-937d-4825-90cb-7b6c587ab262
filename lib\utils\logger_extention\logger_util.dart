import 'logger_extension.dart'; // Import the logger extension.

/// Global utility functions for logging without needing `this`.

void logInfo(String message, Object caller) {
  caller.logInfo(message); // Calls the `logInfo` method from the extension.
}

void logError(String message, Object caller) {
  caller.logError(message); // Calls the `logError` method from the extension.
}

void logWarning(String message, Object caller) {
  caller.logWarning(message); // Calls the `logWarning` method from the extension.
}

void logSuccess(String message, Object caller) {
  caller.logSuccess(message); // Calls the `logSuccess` method from the extension.
}
