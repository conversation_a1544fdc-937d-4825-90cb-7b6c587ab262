// ignore_for_file: avoid_print

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/donation_controller/donation_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/login_manager/aldrees_donation.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:webview_flutter_plus/webview_flutter_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../widget/loading_widget.dart';

class SocialResponsibilityScreen extends StatefulWidget {
  const SocialResponsibilityScreen({super.key});

  @override
  State<SocialResponsibilityScreen> createState() =>
      _SocialResponsibilityScreenState();
}

class _SocialResponsibilityScreenState
    extends State<SocialResponsibilityScreen> {
  final DonationController donationController = Get.put(DonationController());
  late Future<dynamic> socialResponsibilityFuture;

  @override
  void initState() {
    super.initState();
    socialResponsibilityFuture = donationController.socialResponsibilityList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cWhite,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      padding: EdgeInsets.only(left: 20, right: 0),
                      child: Container(
                        
                        child: assetSvdImageWidget(
                            image: DefaultImages.backIcn,
                            height: 20,
                            colorFilter: ColorFilter.mode(
                                AppColor.cBlack, BlendMode.srcIn)),
                      ),
                    ),
                  ),
                  Text(
                    "SOCIAL RESPONSIBILITY".trr,
                    style: pBold18.copyWith(color: AppColor.cBlack),
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
                      child: Image.asset(
                        DefaultImages.socialResLogoImg,
                        height: 40,
                      ),
                    ),
                  ),
                ],
              ),
              FutureBuilder<dynamic>(
                future: socialResponsibilityFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Center(
                      child: Text(
                        "Error: ${snapshot.error}",
                        style: pBold14.copyWith(color: Colors.red),
                      ),
                    );
                  } else if (!snapshot.hasData ||
                      donationController.donationList.isEmpty) {
                    return Center(
                      child: Text(
                        "No data available",
                        style:
                            pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                      ),
                    );
                  } else {
                    return GridView.builder(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        mainAxisSpacing: 2,
                        crossAxisSpacing: 2,
                        mainAxisExtent: 180,
                      ),
                      itemCount: donationController.donationList.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = donationController.donationList[index];
                        return Container(
                          padding: EdgeInsets.only(left: 5, right: 5),
                          child: GestureDetector(
                            onTap: () async {
                              await Get.to(
                                () => AldreesDonationScreen(url: data.LINK),
                              );
                            },
                            child: donationWidget(
                              logo: data.IMG_LOC,
                              descEN: data.TYPEDESCEN,
                              descAR: data.TYPEDESCAR,
                            ),
                          ),
                        );
                      },
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget donationWidget({
    required String logo,
    required String descEN,
    required String descAR,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      padding: const EdgeInsets.only(top: 12, left: 12, right: 12, bottom: 8),
      decoration: BoxDecoration(
        color: AppColor.cWhite,
        borderRadius: BorderRadius.circular(28),
        border: Border.all(
          color: Color(0xFF76B947).withOpacity(0.5),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0xFFD1D1D1).withOpacity(0.5),
            //Colors.grey.withOpacity(0.3),
            offset: const Offset(
              5.0,
              1.0,
            ),
            blurRadius: 10.0,
            spreadRadius: 2.0,
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              logo,
              width: 60,
              height: 50,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.image_not_supported,
                  size: 50,
                  color: AppColor.cDarkGreyFont,
                );
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return SizedBox(
                  width: 50,
                  height: 50,
                  child: Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              (loadingProgress.expectedTotalBytes ?? 1)
                          : null,
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 10),
          Text(
            "$descEN - $descAR",
            style: pBold14.copyWith(color: AppColor.cEhsan),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            "SOCIAL RESPONSIBILITY".trr,
            style: pRegular8.copyWith(color: AppColor.cDarkGreyFont),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// class SocialResponsibilityScreen extends StatefulWidget {
//   const SocialResponsibilityScreen({super.key});

//   @override
//   State<SocialResponsibilityScreen> createState() =>
//       _SocialResponsibilityScreenState();
// }

// class _SocialResponsibilityScreenState
//     extends State<SocialResponsibilityScreen> {
//   DonationController donationController = Get.put(DonationController());
//   @override
//   Widget build(BuildContext context) {
//     // Getting screen size to dynamically adjust the image size
//     final screenWidth = MediaQuery.of(context).size.width;
//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: AppColor.themeBlueColor,
//         title: Text(
//           "SOCIAL RESPONSIBILITY".trr,
//           style: pBold20.copyWith(color: AppColor.cWhiteFont),
//           textAlign: TextAlign.center,
//         ),
//       ),
//       body: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           FutureBuilder<dynamic>(
//               future: donationController.socialResponsibilityList(),
//               builder: (context, AsyncSnapshot snapshot) {
//                 if (snapshot.connectionState != ConnectionState.done) {
//                   return const Center(child: CircularProgressIndicator());
//                 } else {
//                   return ListView.builder(
//                     itemCount: donationController.donationList.length,
//                     shrinkWrap: true,
//                     physics: NeverScrollableScrollPhysics(),
//                     itemBuilder: (context, index) {
//                       var data = donationController.donationList[index];
//                       return Container(
//                         decoration: BoxDecoration(
//                           border: Border(bottom: BorderSide()),
//                         ),
//                         child: GestureDetector(
//                           onTap: () async {
//                             // Handle image click event
//                             print("Image clicked!");
//                             await Get.to(
//                               () => AldreesDonationScreen(url: data.LINK),
//                             );
//                           },
//                           child: donationWidget(
//                             logo: data.IMG_LOC,
//                             descEN: data.TYPEDESCEN,
//                             descAR: data.TYPEDESCAR,
//                           ),
//                         ),
//                       );
//                     },
//                   );
//                 }
//               }),
//         ],
//       ),
//     );
//   }

//   Widget donationWidget({
//     required String logo,
//     required String descEN,
//     required String descAR,
//   }) {
//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Row(
//         children: [
//           // The image that resizes based on screen width
//           Image.network(
//             logo,
//             width: 50, //screenWidth * 0.6, // 60% of screen width
//             fit: BoxFit.cover,
//           ),
//           const Gap(8),
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Text(
//                 "$descEN - $descAR",
//                 style: pBold14.copyWith(color: AppColor.cEhsan),
//                 textAlign: TextAlign.left,
//               ),
//               Text(
//                 "SOCIAL RESPONSIBILITY".trr,
//                 style: pRegular8.copyWith(color: AppColor.cDarkGreyFont),
//                 textAlign: TextAlign.left,
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }
