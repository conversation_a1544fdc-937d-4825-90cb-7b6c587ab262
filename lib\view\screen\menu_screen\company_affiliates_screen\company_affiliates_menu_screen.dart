// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/company_affiliate_controller/company_affiliate_controller.dart';
import 'package:waie_app/core/controller/menu_controller/promotion_controller/promotion_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/company_affiliates_current_screen.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/company_affiliates_screen.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/current_affiliate_screen.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/view/screen/menu_screen/promotions_screen/new_promotion_screen.dart';
import 'package:waie_app/view/screen/menu_screen/promotions_screen/promotion_history_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class CompanyAffiliateMenuScreen extends StatelessWidget {
  CompanyAffiliateMenuScreen({Key? key}) : super(key: key);

  final CompanyAffiliateController companyAffiliateController =
      Get.put(CompanyAffiliateController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            simpleAppBar(
                title: "Affiliates".trr,
                onTap: () {
                  Get.offAll(DashBoardManagerScreen(
                    currantIndex: 0,
                  ));
                },
               // backString: "Menu".trr),
                backString: "Back".trr),
            Expanded(
              child: Obx(() {
                return Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    // shrinkWrap: true,
                    // padding: EdgeInsets.all(16),
                    children: [
                      SizedBox(
                        //height: Get.height * 0.06,
                        child: Row(
                          children: [
                            tabWidget(
                              title: 'Current Affiliates'.trr,
                              onTap: () {
                                companyAffiliateController.isAffiliated.value =
                                    true;
                                companyAffiliateController.isHistory.value =
                                    false;
                              },
                              isSelected:
                                  companyAffiliateController.isAffiliated.value,
                            ),
                            tabWidget(
                              title: 'Request history'.trr,
                              onTap: () {
                                companyAffiliateController.isAffiliated.value =
                                    false;
                                companyAffiliateController.isHistory.value =
                                    true;
                              },
                              isSelected:
                                  companyAffiliateController.isHistory.value,
                            ),
                          ],
                        ),
                      ),
                      companyAffiliateController.isAffiliated.value == true
                          ? CurrentAffiliatesScreen(
                              companyAffiliateController:
                                  companyAffiliateController,
                            )
                          : RequestHistoryScreen()
                    ],
                  ),
                );
              }),
            )
          ],
        ),
      ),
    );
  }
}

Widget tabWidget({
  String? title,
  // Color? indicatorColor,
  // Color? fontColor,
  // double? indicatorSize,
  Function()? onTap,
  bool? isSelected,
}) {
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          FittedBox(
            child: Text(
              title!,
              style: pSemiBold17.copyWith(
                  color: isSelected == true
                      ? AppColor.cText
                      : AppColor.cDarkGreyFont),
            ),
          ),
          verticalSpace(8),
          Container(
            // width: Get.width/2,
            height: isSelected == true ? 3 : 1,
            color: isSelected == true
                ? AppColor.themeOrangeColor
                : AppColor.cIndicator,
          )
        ],
      ),
    ),
  );
}
