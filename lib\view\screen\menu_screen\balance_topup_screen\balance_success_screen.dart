// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/order_controller/balance_topup_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import 'cash_balance_screen.dart';

class BalanceSuccessScreen extends StatelessWidget {
  BalanceSuccessScreen({super.key});

  BalanceTopUpController balanceTopUpController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            simpleAppBar(
                title: "Balance top up".trr,
                onTap: () {
                  Get.back();
                },
                backString: "Back".trr),
            Expanded(
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                  child: Obx(() {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: Get.height - 150,
                          decoration: BoxDecoration(
                              border: Border.all(color: AppColor.cBorder), borderRadius: BorderRadius.circular(4)),
                          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Center(
                                  child: Text(
                                "Thanks for your order".trr,
                                style: pBold20,
                              )),
                              verticalSpace(40),
                              Text(
                                "${"Your order".trr} #00000785234 ${"created.We will notify you about status changes".trr}   ",
                                style: pRegular14,
                                textAlign: TextAlign.center,
                              ),
                              verticalSpace(40),
                              CommonIconButton(
                                title: "Download  receipt".trr.toUpperCase(),
                                iconData: DefaultImages.whiteDownloadIcn,
                                btnColor: AppColor.themeOrangeColor,
                                onPressed: () {},
                              ),
                              verticalSpace(20),
                              CommonBorderButton(
                                title: 'share receipt by email'.trr.toUpperCase(),
                                onPressed: () {
                                  balanceTopUpController.isShowEmail.value = !balanceTopUpController.isShowEmail.value;
                                },
                                bColor: AppColor.themeDarkBlueColor,
                                textColor: AppColor.themeDarkBlueColor,
                              ),
                              verticalSpace(40),
                              balanceTopUpController.isShowEmail.value
                                  ? Text('Recepient email'.trr, style: pRegular8.copyWith(fontSize: 11))
                                  : SizedBox(),
                              verticalSpace(6),
                              balanceTopUpController.isShowEmail.value
                                  ? Container(
                                      height: 44,
                                      decoration: BoxDecoration(
                                          border: Border.all(color: AppColor.cBorder),
                                          borderRadius: BorderRadius.circular(6)),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 3,
                                            child: TextFormField(
                                              style: pRegular14,
                                              keyboardType: TextInputType.emailAddress,
                                              cursorColor: AppColor.cBorder,
                                              decoration: InputDecoration(
                                                  border: InputBorder.none,
                                                  hintText: 'Enter email'.trr,
                                                  hintStyle: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                                                  contentPadding: EdgeInsets.only(left: 16, right: 16, bottom: 8)),
                                            ),
                                          ),
                                          sendEmailBtn(() {}),
                                        ],
                                      ),
                                    )
                                  : SizedBox()
                            ],
                          ),
                        ),
                      ],
                    );
                  }),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
