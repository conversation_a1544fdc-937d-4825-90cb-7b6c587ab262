// ignore_for_file: prefer_const_constructors

import 'package:get/get.dart';
import 'package:waie_app/core/controller/notification_controller/notification_controller.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/screen/notification_screen/complaints_screen.dart';
import 'package:waie_app/view/screen/notification_screen/activity_screen.dart';
import '../../../utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/text_style.dart';
import '../../widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  NotificationController notificationController =
      Get.put(NotificationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            simpleAppBar(
                title: "",
                onTap: () {
                  Get.back();
                },
                backString: "Back".trr),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Obx(() {
                  return ListView(
                    shrinkWrap: true,
                    children: [
                      Row(
                        children: [
                          tabWidget(
                            title: "Activity".trr,
                           // total: '3',
                            total: notificationController.genNotyCount.value,
                            fontColor: notificationController.isActivity.value
                                ? AppColor.cText
                                : AppColor.cDarkGreyFont,
                            textColor: notificationController.isActivity.value
                                ? AppColor.cDarkOrangeText
                                : AppColor.cText,
                            containerColor:
                                notificationController.isActivity.value
                                    ? AppColor.lightOrangeColor
                                    : AppColor.cLightGrey,
                            indicatorColor:
                                notificationController.isActivity.value
                                    ? AppColor.themeOrangeColor
                                    : AppColor.cIndicator,
                            indicatorSize:
                                notificationController.isActivity.value ? 3 : 1,
                            onTap: () {
                              notificationController.isActivity.value = true;
                              notificationController.isComplaints.value = false;
                            },
                          ),
                          tabWidget(
                            title: "Complaints".trr,
                            total: notificationController.genCompNotyCount.value,
                            fontColor: notificationController.isComplaints.value
                                ? AppColor.cText
                                : AppColor.cDarkGreyFont,
                            textColor: notificationController.isComplaints.value
                                ? AppColor.cDarkOrangeText
                                : AppColor.cText,
                            containerColor:
                                notificationController.isComplaints.value
                                    ? AppColor.lightOrangeColor
                                    : AppColor.cLightGrey,
                            indicatorColor:
                                notificationController.isComplaints.value
                                    ? AppColor.themeOrangeColor
                                    : AppColor.cIndicator,
                            indicatorSize:
                                notificationController.isComplaints.value
                                    ? 3
                                    : 1,
                            onTap: () {
                              notificationController.isActivity.value = false;
                              notificationController.isComplaints.value = true;
                            },
                          ),
                        ],
                      ),
                      verticalSpace(22),
                      notificationController.isActivity.value
                          ? ActivityScreen(
                              notificationController: notificationController,
                            )
                          : ComplaintsScreen(
                              notificationController: notificationController,
                            ),
                    ],
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget tabWidget({
    String? title,
    String? total,
    Color? indicatorColor,
    Color? fontColor,
    Color? textColor,
    Color? containerColor,
    double? indicatorSize,
    Function()? onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Text(
                title!,
                style: pSemiBold17.copyWith(color: fontColor),
              ),
              horizontalSpace(10),
              Container(
                decoration: BoxDecoration(
                    color: containerColor ?? AppColor.lightOrangeColor,
                    borderRadius: BorderRadius.circular(32)),
                padding: EdgeInsets.symmetric(vertical: 1, horizontal: 8),
                child: Text(
                  total!,
                  style: pMedium12.copyWith(
                      color: textColor ?? AppColor.cDarkOrangeText),
                ),
              )
            ]),
            verticalSpace(8),
            Container(
              // width: Get.width/2,
              height: indicatorSize ?? 3,
              color: indicatorColor ?? AppColor.themeOrangeColor,
            )
          ],
        ),
      ),
    );
  }
}
