// ignore_for_file: prefer_const_constructors, must_be_immutable, prefer_interpolation_to_compose_strings

import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/vehicle_controller/tag_transfer_controller.dart';

class TransferTagsWidget extends StatefulWidget {
  String serialid;
  TransferTagsWidget({
    super.key,
    required this.serialid,
  });

  @override
  State<TransferTagsWidget> createState() => _TransferTagsWidgetState();
}

class _TransferTagsWidgetState extends State<TransferTagsWidget> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  VehicleController vehicleController = Get.put(VehicleController());
  TagTransferController tagTransferController =
      Get.put(TagTransferController());

  @override
  Widget build(BuildContext context) {
    print("TransferTagsWidget");
    // print(
    //     "singleVehicleSerialID >>>>> ${vehicle.read('singleVehicleSerialID')}");
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                    flex: 3,
                    child: Text(
                      "Transfer tag".trr,
                      style: pSemiBold17,
                      textAlign: TextAlign.center,
                    )),
                // Spacer(),
                GestureDetector(
                    onTap: () {
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      // Get.offAll(
                      //   () => DashBoardManagerScreen(
                      //     currantIndex: 0,
                      //   ),
                      //   //preventDuplicates: false,
                      // );
                      Get.back();
                    },
                    child: assetSvdImageWidget(image: DefaultImages.cancelIcn))
              ],
            ),
            verticalSpace(25),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CommonTextField(
                  controller: tagTransferController.companyIdController,
                  keyboardType: TextInputType.numberWithOptions(
                      signed: true, decimal: true),
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  labelText: 'Transfer to Company ID'.trr,
                  hintText: 'Enter company ID'.trr,
                  onChanged: (value) {
                    Validator.validateCompanyID(value);
                  },
                  validator: (value) {
                    return Validator.validateCompanyID(value);
                  },
                ),
                verticalSpace(24),
                CommonTextField(
                    controller: tagTransferController.commentController,
                    labelText:
                        'Let us know why you re transferring your tag'.trr,
                    hintText: 'Please enter here'.trr + '...',
                    maxLines: 3),
              ],
            ),
            verticalSpace(24),
            Row(
              children: [
                Expanded(
                    child: CommonButton(
                  title: 'Cancel'.trr,
                  onPressed: () {
                    vehicleController.selectedSerialList.clear();
                    vehicleController.selectedVehicleList.clear();
                    vehicleController.selectedFleetList.clear();
                    vehicleController.filterValueList.refresh();
                    vehicleController.selectedVehicleList.refresh();
                    vehicleController.selectedSerialList.refresh();
                    vehicleController.selectedFleetList.refresh();
                    // Get.offAll(
                    //   () => DashBoardManagerScreen(
                    //     currantIndex: 0,
                    //   ),
                    //   //preventDuplicates: false,
                    // );
                    Get.back();
                  },
                  btnColor: AppColor.cBackGround,
                  bColor: AppColor.themeDarkBlueColor,
                  textColor: AppColor.cDarkBlueFont,
                )),
                horizontalSpace(16),
                Expanded(
                  child: CommonButton(
                    title: 'Confirm'.trr,
                    onPressed: () {
                      // print("*************************************");
                      // final vehicleSerialID = vehicle.read('vehicleSerialID');
                      // print("vehicleSerialID ---- $vehicleSerialID");
                      // List<String> vehicleserialids = [];
                      // vehicleserialids.add(vehicleSerialID.toString());
                      // String vehicleserialIDList = vehicleserialids.join(",");
                      // print(
                      //     "vehicleserialIDList -------- $vehicleserialIDList");
                      // print("*************************************");

                      tagTransferController.tagTransferRequest(widget.serialid);
                    },
                    btnColor: AppColor.themeOrangeColor,
                    horizontalPadding: 16,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
