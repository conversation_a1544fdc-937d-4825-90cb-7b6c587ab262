// ignore_for_file: avoid_print

import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/new_order_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:webview_flutter_plus/webview_flutter_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../widget/loading_widget.dart';

class MADAtopupScreen extends StatefulWidget {
  final String url;
  final String replacement;
  final String totalAmount;
  const MADAtopupScreen({
    super.key,
    required this.url,
    required this.replacement,
    required this.totalAmount,
  });

  @override
  State<MADAtopupScreen> createState() => _MADAtopupScreenState();
}

class _MADAtopupScreenState extends State<MADAtopupScreen> {
  late WebViewController _controler;
  final double _height = 1;
  bool isLoading = true;
  String pageText = '';
  String pgaeHandlerURL = "";

  @override
  void initState() {
    _controler = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.url))
      ..runJavaScript(
          "navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_15 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.77 Mobile Safari/537.36';")
      ..setNavigationDelegate(
        NavigationDelegate(onProgress: (progress) {
          print("widget.url---> ${widget.url}");
          print("progress---> $progress");
          if (progress == 100) {
            setState(() {
              isLoading = false;
            });
          }
        }, onPageFinished: (url) {
          _getPageText();
        }),
      );
    super.initState();
  }

  // Method to retrieve text from the webpage
  void _getPageText() async {
    //if (progress == 100) {
    try {
      // await Future.delayed(Duration(seconds: 5));

      String text = "";
      if (Platform.isAndroid || Platform.isWindows) {
        text = await _controler
            .runJavaScriptReturningResult('document.body.innerText') as String;
      } else if (Platform.isIOS) {
        text = await _controler.runJavaScriptReturningResult(
            'document.body ? document.body.innerText : ""') as String;
      }
      // String text = await _controler.runJavaScriptReturningResult(
      //     'document.body ? document.body.innerText : ""') as String;

      log(text.toString());
      // if (text.isEmpty) {
      //   log("hi fuzail");
      //   Get.back();
      // }
      if (text.isNotEmpty) {
        setState(() {
          pageText = text;
        });
        print("Page text: $pageText");

        print("Page text contains '200': ${pageText.contains("200")}");

        if (pageText.contains("200")) {
          if (widget.replacement == "true") {
            Get.back();
            Get.back();
            Get.back();
            Future.delayed(Duration(milliseconds: 100), () {
              showDialog(
                context: Get.context!,
                builder: (context) {
                  return AlertDialog(
                    contentPadding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    insetPadding: const EdgeInsets.all(25),
                    content: madaTopupSuccessWidget(
                      totalAmount: widget.totalAmount.toString(),
                    ),
                  );
                },
              );
            });
          } else {
            Get.offAll(() => DashBoardManagerScreen(currantIndex: 0));
            Future.delayed(Duration(milliseconds: 100), () {
              showDialog(
                context: Get.context!,
                builder: (context) {
                  return AlertDialog(
                    contentPadding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    insetPadding: const EdgeInsets.all(25),
                    content: madaTopupSuccessWidget(
                      totalAmount: widget.totalAmount.toString(),
                    ),
                  );
                },
              );
            });
          }
        }
      } else {
        print(
            "Page text is empty. Possibly the page hasn't rendered properly.");
      }
    } catch (e) {
      print("Error evaluating JavaScript: $e");
    }
    //}
  }

  Future<bool> _onWillPop() async {
    if (await _controler.canGoBack()) {
      // If there's history, go back to the previous page
      _controler.goBack();
      return false; // Prevent the app from exiting
    } else {
      return true; // Allow the app to exit
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (await _controler.canGoBack()) {
          _controler.goBack(); // Navigate back within WebView
          return false; // Prevent closing the screen
        }
        return true; // Close the screen
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColor.themeBlueColor,
          title: Text(
            "MADA ALRAJHI".trr,
            style: pBold20.copyWith(color: AppColor.cWhiteFont),
            textAlign: TextAlign.center,
          ),
        ),
        body: isLoading
            ? const Center(
                child: LoadingWidget(),
              )
            : WebViewWidget(
                controller: _controler,
              ),
      ),
    );
  }

  Widget madaTopupSuccessWidget({
    required String totalAmount,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Center(
            child: Text(
          "Success!".trr,
          style: pBold20,
        )),
        verticalSpace(14),
        Center(
            child: Text(
          "Your order has been successful!.".trr,
          style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          textAlign: TextAlign.center,
        )),
        verticalSpace(8),
        Text.rich(
          TextSpan(
            text:
                "Your order has been created, you can print the order details/invoice from your order history."
                    .trr,
            style: pRegular13,
            children: <TextSpan>[
              TextSpan(
                  text: 'We keep the details of your order'.trr +
                      " in Order History".trr,
                  style: pBold12.copyWith(fontSize: 13)),
            ],
          ),
          textAlign: TextAlign.center,
        ),
        verticalSpace(16),
        successmadaTopupWidget(
            totalAmount:
                double.parse(totalAmount).toStringAsFixed(2)), //+ " SAR".trr
        verticalSpace(16),
        verticalSpace(16),
        buttonRow(
          pdfFun: () {
            Get.back();
          },
        ),
      ],
    );
  }

  Widget successmadaTopupWidget({String? totalAmount}) {
    return Container(
      decoration: BoxDecoration(
          color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          totalsDataWidget(title: "Purchase total".trr, value: totalAmount!),
        ],
      ),
    );
  }

  Widget buttonRow({required Function() pdfFun}) {
    return Row(
      children: [
        Expanded(
            child: CommonButton(
          title: "Close".trr,
          onPressed: pdfFun,
          btnColor: AppColor.themeOrangeColor,
          horizontalPadding: 16,
        )),
      ],
    );
  }
}
