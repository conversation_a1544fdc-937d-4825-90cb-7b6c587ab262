import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class EditTagInstallationController extends GetxController {
  List<LatLng> latLongList = [
    const <PERSON>t<PERSON><PERSON>(21.44484793949768, 53.295109691390245),
    const <PERSON>t<PERSON><PERSON>(20.582120409829233, 44.96018559757371),
    const <PERSON><PERSON><PERSON><PERSON>(23.899725409740984, 54.046233681296975),
    const <PERSON><PERSON><PERSON><PERSON>(22.302502578679334, 47.422575301577865),
    const <PERSON><PERSON><PERSON><PERSON>(25.169169066706516, 38.833075072755804),
    const <PERSON><PERSON><PERSON><PERSON>(23.359337577784572, 45.01153009917833),
    const <PERSON><PERSON><PERSON><PERSON>(20.950188013577115, 49.39292756943918),
  ];
  List<String> cityList = [
    'Al-<PERSON><PERSON>',
    '<PERSON><PERSON>',
    'AL REEF',
    'Bur Dubai',
    'Jumeirah',
    'Dubai Marina',
    'Downtown Dubai',
    'Palm Jumeirah',
    'Al Barsha',
    'Business Bay',
    'Jumeirah Lakes Towers (JLT)',
    'Dubai Silicon Oasis',
  ];
  RxString selectedCity = 'AL REEF'.obs;
  RxList getTagList = [
    {
      "title": "Al Reef",
      "address": "Old Industrial 87, Riyadh, Saudi Arabia",
      'time': [
        "08:15 AM",
        "08:30 AM",
        "09:15 AM",
        "09:30 AM",
        "10:15 AM",
        "11:15 AM",
        "12:00 AM",
        "12:30 AM",
        "01:30 AM",
        "10:15 PM",
        '15:15 PM',
        '19:15 PM',
      ],
      'plat': [
        "9768TTB",
        "1013ANA",
        "8768XTA",
        "Plat#",
        "Plat#1",
        "Plat#2",
        "Plat#3",
        "Plat#4",
      ],
      'vehicle': [
        {
          "dateController": '08/29/23',
          "selectedPlat": '8768XTA'.obs,
          "selectedTime": '10:15 AM'.obs,
        },
        {
          "dateController": '08/29/23',
          "selectedPlat": '9768TTB'.obs,
          "selectedTime": '15:15 PM'.obs,
        },
        {
          "dateController": '08/29/23',
          "selectedPlat": '1013ANA'.obs,
          "selectedTime": '19:15 PM'.obs,
        },
      ]
    }
  ].obs;
}
