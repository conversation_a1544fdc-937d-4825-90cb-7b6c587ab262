// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/new_user_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/fleet_structure_controller/overview_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/view_vehicle_details_controller.dart';
import '../../dashboard_manager/dashboard_manager.dart';
import 'action_widget.dart';
import 'add_pure_dc_vehicle_screen.dart';
import 'assign_digital_coupon_widget.dart';
import 'my_fleet_screen.dart';

class FinalViewVehicleDetailsScreen extends StatefulWidget {
  const FinalViewVehicleDetailsScreen({super.key});

  @override
  State<FinalViewVehicleDetailsScreen> createState() =>
      _FinalViewVehicleDetailsScreenState();
}

class _FinalViewVehicleDetailsScreenState
    extends State<FinalViewVehicleDetailsScreen> {
  VehicleController vehicleController = Get.put(VehicleController());
  //ViewVehicleDetailsController viewVehicleDetailsController =
  //Get.put(ViewVehicleDetailsController());
  ViewVehicleDetailsController viewVehicleDetailsController =
      Get.put(ViewVehicleDetailsController());

  String hidePartOfNumber(String rfid) {
    // Convert the number to a string
    String numberString = rfid.toString();

    // Determine how many digits to hide (in this example, hiding all but the last four digits)
    int digitsToShow = 6;
    int visibleDigits = numberString.length - digitsToShow;

    // Replace the visible digits with asterisks
    String hiddenDigits = 'X' * visibleDigits;

    // Concatenate the visible part of the number with the hidden part
    String maskedNumber = hiddenDigits + numberString.substring(visibleDigits);

    return maskedNumber;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: SingleChildScrollView(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.only(right: 16, left: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        // Get.offAll(() => DashBoardManagerScreen(
                        //       currantIndex: 0,
                        //     ));
                        viewVehicleDetailsController.vehicleDetails.clear();
                        Get.back();
                      },
                      child: Container(
                        padding: const EdgeInsets.only(
                          top: 15,
                          bottom: 16,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.backIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cDarkBlueFont, BlendMode.srcIn)),
                            horizontalSpace(10),
                            Text(
                              "Back".trr,
                              style: pRegular18.copyWith(
                                  color: AppColor.cDarkBlueFont, fontSize: 17),
                              textAlign: TextAlign.start,
                            )
                          ],
                        ),
                      ),
                    ),
                    // horizontalSpace(35),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "Vehicle Details".trr,
                          style: pBold20,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: ListView.builder(
                  itemCount: viewVehicleDetailsController.vehicleDetails.length,
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    var data =
                        viewVehicleDetailsController.vehicleDetails[index];
                    return GestureDetector(
                      child: fleetWidget(
                        value: data.isDc, //datas['isDC'] ?? '',
                        code: data.plateno, //datas['plateno'] ?? '',
                        status: data
                            .servicestatusDisp, //datas['servicestatusDisp'].toString().trr,
                        title: data.vehicletypeDisp, //datas['vehicletypeDisp'],
                        type: data.fueltypeDisp, //datas['fuelTypeDisp'] ?? '',
                        driver: data.driver, //datas['driver'] ?? '',
                        vehicleType:
                            data.vehicletypeDisp, //datas['vehicletypeDisp'],
                        tanks:
                            data.tankNo, //datas['tankNo1'] ?? datas['tankNo1'],
                        offlineLimit: data.offlinelimit
                            .toString(), //data['offlineLimit'],
                        quotaTotal: data.quotaDisp, //datas['quotaTotal'] ?? '',
                        quotaString: data.quotatypeDisp, //datas['quotaString'],
                        division: data.branchDisp, //datas['branchDisp'],
                        password: data.passwordDisp, //datas['password'] ?? '',
                        //dayList: ,
                        serviceStatus:
                            data.servicestatus, //datas['serviceStatus'] ?? '',
                        serviceType:
                            data.servicetype, //datas['serviceType'] ?? '',
                        serialXID: data.servicetype == "C"
                            ? data.serialid
                            : hidePartOfNumber(
                                data.serialcode), //datas['serialXID'] ?? '',
                        insTermDate: data.servicestatus == "T"
                            ? data.terminatedate
                            : data.instdate, //datas['insTermDate'] ?? '',
                        disp: data.vehlicTypeDisp,

                        textColor: data.servicestatusDisp == "IN-ACTIVE" ||
                                data.servicestatusDisp == 'In progress'
                            ? AppColor.cRedText
                            : data.servicestatusDisp == "NEW"
                                ? AppColor.cBlackFont
                                : data.servicestatusDisp == "TERMINATED"
                                    ? AppColor.cDarkGreyFont
                                    : AppColor.cDarkBlueFont,
                        color: data.servicestatusDisp == "IN-ACTIVE" ||
                                data.servicestatusDisp == 'In progress'
                            ? AppColor.cLightRedContainer
                            : data.servicestatusDisp == "NEW"
                                ? AppColor.cLiteYellow
                                : data.servicestatusDisp == "TERMINATED"
                                    ? AppColor.cMediumGreyContainer
                                    : AppColor.cLightBlueContainer,
                        tag: data.servicetype == "D" || data.isDc == true
                            ? DefaultImages.scannerIcn
                            : data.servicetype == "T"
                                ? DefaultImages.tagIcn
                                : DefaultImages.cardIcn,
                        viewMore: () {
                          data.servicestatusDisp == 'TERMINATED' ||
                                  data.servicestatusDisp == "NEW"
                              ? SizedBox()
                              : showModalBottomSheet(
                                  isDismissible: false,
                                  context: context,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.vertical(
                                          top: Radius.circular(16))),
                                  backgroundColor: AppColor.cBackGround,
                                  barrierColor: AppColor.cBlackOpacity,
                                  isScrollControlled: true,
                                  builder: (context) {
                                    return ActionWidget(
                                      code: data.plateno,
                                      serviceStatus: data.servicestatus,
                                      isComplaint:
                                          data.servicestatusDisp == "ACTIVE"
                                              ? true
                                              : false,
                                    );
                                  },
                                );
                        },
                        scanTap: () {
                          showModalBottomSheet(
                            context: context,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(16))),
                            backgroundColor: AppColor.cBackGround,
                            barrierColor: AppColor.cBlackOpacity,
                            isScrollControlled: true,
                            builder: (context) {
                              return AssignDigitalCouponWidget(
                                code: data.servicestatusDisp,
                              );
                            },
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
              // Padding(
              //   padding: const EdgeInsets.all(16),
              //   child: availableStationTitleRowWidget(
              //     title: "Available stations".trr,
              //     isSelected: vehicleController.isAvailableStations.value,
              //     onTap: () {
              //       vehicleController.isAvailableStations.value =
              //           !vehicleController.isAvailableStations.value;
              //     },
              //   ),
              // ),
              // verticalSpace(16),
              // Padding(
              //   padding: const EdgeInsets.all(16),
              //   child: Column(
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       ListView.builder(
              //         shrinkWrap: true,
              //         physics: NeverScrollableScrollPhysics(),
              //         itemCount: vehicleController.availableStationList.length,
              //         itemBuilder: (context, index) {
              //           var data = vehicleController.availableStationList[index];
              //           return Padding(
              //             padding: const EdgeInsets.only(bottom: 8.0),
              //             child: checkBoxWidget(
              //               value: data['value'].value,
              //               onChanged: (value) {
              //                 data['value'].value = value!;
              //               },
              //               title: data['title'].toString().trr,
              //             ),
              //           );
              //         },
              //       ),
              //       verticalSpace(24),
              //       Container(
              //         padding: EdgeInsets.all(16),
              //         decoration: BoxDecoration(
              //             borderRadius: BorderRadius.circular(12),
              //             border: Border.all(
              //               color: AppColor.cBorder,
              //             )),
              //         child: Column(
              //           children: [
              //             Row(children: [
              //               Text(
              //                 "All stations".trr,
              //                 style: pRegular14,
              //               )
              //             ]),
              //             verticalSpace(16),
              //             ListView.builder(
              //               shrinkWrap: true,
              //               physics: NeverScrollableScrollPhysics(),
              //               itemCount:
              //                   viewVehicleDetailsController.loadPlaces.length,
              //               itemBuilder: (context, index) {
              //                 var data =
              //                     viewVehicleDetailsController.loadPlaces[index];
              //                 return Padding(
              //                   padding: const EdgeInsets.only(bottom: 8.0),
              //                   child: Container(
              //                     padding: EdgeInsets.symmetric(
              //                         vertical: 10, horizontal: 8),
              //                     decoration: BoxDecoration(
              //                       color: AppColor.cLightBlueContainer,
              //                       borderRadius: BorderRadius.circular(6),
              //                     ),
              //                     child: Column(
              //                       children: [
              //                         Row(
              //                           mainAxisAlignment:
              //                               MainAxisAlignment.spaceBetween,
              //                           children: [
              //                             Row(
              //                               children: [
              //                                 Text.rich(
              //                                   TextSpan(
              //                                     children: [
              //                                       WidgetSpan(
              //                                           child: Icon(
              //                                         Icons.check_box_rounded,
              //                                         size: 18,
              //                                         color: AppColor
              //                                             .themeDarkBlueColor,
              //                                       )),
              //                                       TextSpan(
              //                                           text: data.placeName,
              //                                           style: pRegular13.copyWith(
              //                                               color: AppColor
              //                                                   .cDarkBlueFont)),
              //                                     ],
              //                                   ),
              //                                 ),
              //                               ],
              //                             ),
              //                           ],
              //                         ),
              //                         Padding(
              //                           padding: const EdgeInsets.only(
              //                               left: 10, top: 6, bottom: 16),
              //                           child: ListView.builder(
              //                             itemCount: data.stations.length,
              //                             physics: NeverScrollableScrollPhysics(),
              //                             shrinkWrap: true,
              //                             itemBuilder: (context, i) {
              //                               var myData = data.stations[i];
              //                               return Padding(
              //                                 padding:
              //                                     const EdgeInsets.only(top: 10),
              //                                 child: Row(
              //                                   children: [
              //                                     Text.rich(
              //                                       TextSpan(
              //                                         children: [
              //                                           WidgetSpan(
              //                                               child: Icon(
              //                                             Icons.check_box_rounded,
              //                                             size: 15,
              //                                             color: AppColor
              //                                                 .themeDarkBlueColor,
              //                                           )),
              //                                           TextSpan(
              //                                               text: myData
              //                                                   .stationName,
              //                                               style: pRegular13.copyWith(
              //                                                   color: AppColor
              //                                                       .cDarkBlueFont)),
              //                                         ],
              //                                       ),
              //                                     ),
              //                                   ],
              //                                 ),
              //                               );
              //                             },
              //                           ),
              //                         )
              //                       ],
              //                     ),
              //                   ),
              //                 );
              //               },
              //             ),
              //           ],
              //         ),
              //       ),
              //       verticalSpace(32),
              //     ],
              //   ),
              // )
            ],
          )),
        ),
      ),
    );
  }

  Widget fleetWidget({
    required String code,
    String? status,
    Color? textColor,
    Color? color,
    bool? value,
    String? title,
    String? tag,
    String? type,
    String? driver,
    String? vehicleType,
    String? tanks,
    String? offlineLimit,
    String? quotaTotal,
    String? quotaString,
    String? division,
    String? password,
    String? disp,
    //List? dayList,
    Function()? viewMore,
    Function()? scanTap,
    String? serviceStatus,
    String? serviceType,
    String? serialXID,
    String? insTermDate,
  }) {
    //print(dayList);
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: AppColor.cLightGrey),
          color: AppColor.lightBlueColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                        height: 24,
                        decoration: BoxDecoration(
                            color: status == "NEW"
                                ? AppColor.cLightGrey
                                : AppColor.cLightBlueContainer,
                            border: Border.all(
                                color: status == "NEW"
                                    ? AppColor.cLightGrey
                                    : AppColor.cLightBlueContainer),
                            borderRadius: BorderRadius.circular(4)),
                        padding:
                            EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                        child: Center(
                          child: Text(
                            code,
                            style: pBold12.copyWith(
                                fontSize: 12,
                                color: status == 'NEW'
                                    ? AppColor.cDarkGreyFont
                                    : AppColor.cDarkBlueText),
                          ),
                        )),
                    horizontalSpace(8),
                    statusWidget(
                        text: status,
                        textColor: textColor,
                        color: color,
                        tag: tag,
                        horizontalSpace: 4),
                    if (value == "Y") horizontalSpace(8),
                    if (value == "Y")
                      GestureDetector(
                        onTap: status == "ACTIVE" ? scanTap : null,
                        child: assetSvdImageWidget(
                            image: DefaultImages.scannerIcn,
                            colorFilter: ColorFilter.mode(
                                status == "ACTIVE"
                                    ? AppColor.cText
                                    : AppColor.cLightBlueFont,
                                BlendMode.srcIn)),
                      )
                  ],
                ),
                // status == "TERMINATED"
                //     ? SizedBox()
                //     : GestureDetector(
                //         onTap: viewMore,
                //         child: assetSvdImageWidget(
                //             image: DefaultImages.verticleMoreIcn))
              ],
            ),
            verticalSpace(8),
            Text(
              disp!,
              style: pRegular13,
            ),
            verticalSpace(18),
            // title == ''
            //     ? SizedBox()
            //     : Row(
            //         children: [
            //           Text(
            //             title!,
            //             style: pRegular13,
            //           ),
            //           Padding(
            //             padding: const EdgeInsets.symmetric(horizontal: 8.0),
            //             child: assetSvdImageWidget(image: DefaultImages.dotIcn),
            //           ),
            //           Text(
            //             type!,
            //             style: pRegular13,
            //           ),
            //         ],
            //       ),
            // verticalSpace(18),
            //dataWidget(title: "Driver".trr, subtitle: driver!),
            Row(
              children: [
                SizedBox(
                    width: 140,
                    child: Text(
                      "Driver".trr,
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    )),
                Expanded(
                  child: Text.rich(
                    TextSpan(
                      text: '$driver ',
                      style: pRegular13,
                    ),
                  ),
                )
              ],
            ),
            verticalSpace(12),
            dataWidget(title: "Vehicle Type".trr, subtitle: vehicleType!),
            verticalSpace(12),
            dataWidget(title: "Tanks".trr, subtitle: tanks!),
            verticalSpace(12),
            Row(
              children: [
                SizedBox(
                    width: 140,
                    child: Text(
                      "Quota".trr,
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    )),
                Expanded(
                  child: Text.rich(
                    TextSpan(
                      text: '$quotaTotal ',
                      style: pBold14.copyWith(fontSize: 13),
                      // children: <TextSpan>[
                      //   TextSpan(text: quotaString, style: pRegular13),
                      // ],
                    ),
                  ),
                )
              ],
            ),
            verticalSpace(12),
            dataWidget(title: "Offline limit".trr, subtitle: offlineLimit!),
            verticalSpace(12),
            dataWidget(title: "Division".trr, subtitle: division!),
            verticalSpace(12),
            dataWidget(title: "Password".trr, subtitle: password!),
            verticalSpace(12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                    width: 140,
                    child: Text(
                      "Filling days".trr,
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    )),
                Expanded(
                  child: Wrap(
                      spacing: 4,
                      direction: Axis.horizontal,
                      runSpacing: 7,
                      alignment: WrapAlignment.start,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: AppColor.themeDarkBlueColor),
                          padding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          child: Text("Mon",
                              style: pRegular10.copyWith(
                                  fontSize: 11, color: AppColor.cWhiteFont)),
                        ),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: AppColor.themeDarkBlueColor),
                          padding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          child: Text("Tue",
                              style: pRegular10.copyWith(
                                  fontSize: 11, color: AppColor.cWhiteFont)),
                        ),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: AppColor.themeDarkBlueColor),
                          padding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          child: Text("Wed",
                              style: pRegular10.copyWith(
                                  fontSize: 11, color: AppColor.cWhiteFont)),
                        ),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: AppColor.themeDarkBlueColor),
                          padding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          child: Text("Thu",
                              style: pRegular10.copyWith(
                                  fontSize: 11, color: AppColor.cWhiteFont)),
                        ),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: AppColor.themeDarkBlueColor),
                          padding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          child: Text("Fri",
                              style: pRegular10.copyWith(
                                  fontSize: 11, color: AppColor.cWhiteFont)),
                        ),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: AppColor.themeDarkBlueColor),
                          padding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          child: Text("Sat",
                              style: pRegular10.copyWith(
                                  fontSize: 11, color: AppColor.cWhiteFont)),
                        ),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: AppColor.themeDarkBlueColor),
                          padding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          child: Text("Sun",
                              style: pRegular10.copyWith(
                                  fontSize: 11, color: AppColor.cWhiteFont)),
                        ),
                      ]
                      //dayList!.map<Widget>((e) => dayDataWidget(e)).toList(),
                      ),
                )
              ],
            ),
            verticalSpace(12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                    width: 140,
                    child: Text(
                      serviceStatus != "T" //"Division".trr,
                          ? serviceType == "T"
                              ? "Installation Date".trr
                              : "Activation Date".trr
                          : "Terminated Date".trr,
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    )),
                Text(
                  insTermDate!,
                  style: pRegular13,
                ),
              ],
            ),
            verticalSpace(12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                    width: 140,
                    child: Text(
                      serviceType == "C" //"Division".trr,
                          ? "Card No"
                          : "RFID",
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    )),
                Text(
                  serialXID!,
                  style: pRegular13,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  dayDataWidget(String day) {
    print(day);
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: AppColor.themeDarkBlueColor),
      padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Text(day,
          style: pRegular10.copyWith(fontSize: 11, color: AppColor.cWhiteFont)),
    );
  }

  dataWidget({required String title, required String subtitle}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
            width: 140,
            child: Text(
              title,
              style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
            )),
        Text(
          subtitle,
          style: pRegular13,
        ),
      ],
    );
  }
}

Widget availableStationTitleRowWidget(
    {required String title, required bool isSelected, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      color: AppColor.cBackGround,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pBold20,
          ),
          // assetSvdImageWidget(
          //     image: isSelected == true
          //         ? DefaultImages.arrowUpIcn
          //         : DefaultImages.dropDownIcn,
          //     width: 24,
          //     height: 24)
        ],
      ),
    ),
  );
}
