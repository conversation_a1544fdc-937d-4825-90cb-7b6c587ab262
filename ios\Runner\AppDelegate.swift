import UIKit
import Flutter
import GoogleMaps
import WebKit
import Firebase
import UserNotifications

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        FirebaseApp.configure()
        
        UNUserNotificationCenter.current().delegate = self
        
        application.registerForRemoteNotifications()

        let METHOD_CHANNEL_NAME = "flutter.native.helper"
        let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
        let methodChannel = FlutterMethodChannel(name: METHOD_CHANNEL_NAME, binaryMessenger: controller.binaryMessenger)
        
        methodChannel.setMethodCallHandler({ (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
            let argumentsDictionary = call.arguments as? [String: Any]
            SystemParameter.totalPurchased = argumentsDictionary?["totalPurchased"] as? String ?? ""
            SystemParameter.sessionID = argumentsDictionary?["sessionID"] as? String ?? ""
            SystemParameter.orderID = argumentsDictionary?["orderID"] as? String ?? ""
            SystemParameter.serviceType = argumentsDictionary?["serviceType"] as? String ?? ""
            SystemParameter.orderType = argumentsDictionary?["orderType"] as? String ?? ""
            SystemParameter.custid = argumentsDictionary?["custid"] as? String ?? ""
            SystemParameter.qty = argumentsDictionary?["qty"] as? String ?? ""
            
            switch call.method {
            case "MADAPayment":
                self.showNativeUIViewController()
            default:
                result(FlutterMethodNotImplemented)
                return
            }
        })

  // added by fuzail for location Load Google Maps API Key from Info.plist
        if let apiKey = Bundle.main.object(forInfoDictionaryKey: "GMSApiKey") as? String, apiKey != "DUMMY_API_KEY" {
            GMSServices.provideAPIKey(apiKey)
            print("✅ Google Maps API Key Loaded: \(apiKey)")
        } else {
            print("⚠️ Google Maps API Key is missing or not set!")
        }

        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    //     GMSServices.provideAPIKey("AIzaSyDVjB-dVk0Fy325gPWUaJGAbatgTSLD-PU")
    //     GeneratedPluginRegistrant.register(with: self)
    //     return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    // }

//02-03-2025

    func showNativeUIViewController() {
        print("Called Native Function")
        let viewController = webViewController()
        window?.rootViewController?.present(viewController, animated: true, completion: nil)
    }

    override func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        let userInfo = notification.request.content.userInfo
        print("Foreground notification received: \(userInfo)")

        // Clear badge when notification is received
        UIApplication.shared.applicationIconBadgeNumber = 0
        
        // Clear badge when notification is received
        UIApplication.shared.applicationIconBadgeNumber = 0
        if #available(iOS 14.0, *) {
            completionHandler([.banner, .list, .sound]) // Removed .badge
        } else {
            completionHandler([.alert, .sound]) // Removed .badge
        }
    }

    override func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        print("User tapped on notification: \(userInfo)")
        completionHandler()
    }
}
