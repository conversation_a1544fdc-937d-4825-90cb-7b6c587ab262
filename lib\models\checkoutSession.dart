import 'dart:convert';

List<CheckoutSessionModel> checkoutSessionModelFromJson(String str) =>
    List<CheckoutSessionModel>.from(
        json.decode(str).map((x) => CheckoutSessionModel.fromJson(x)));

String checkoutSessionModelToJson(List<CheckoutSessionModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CheckoutSessionModel {
  String merchant;
  String result;
  Session session;
  String successIndicator;

  CheckoutSessionModel({
    required this.merchant,
    required this.result,
    required this.session,
    required this.successIndicator,
  });

  factory CheckoutSessionModel.fromJson(Map<String, dynamic> json) =>
      CheckoutSessionModel(
        merchant: json["merchant"],
        result: json["result"],
        session: Session.fromJson(json["session"]),
        successIndicator: json["successIndicator"],
      );

  Map<String, dynamic> toJson() => {
        "merchant": merchant,
        "result": result,
        "session": session.toJson(),
        "successIndicator": successIndicator,
      };
}

class Session {
  String id;
  String updateStatus;
  String version;

  Session({
    required this.id,
    required this.updateStatus,
    required this.version,
  });

  factory Session.fromJson(Map<String, dynamic> json) => Session(
        id: json["id"],
        updateStatus: json["updateStatus"],
        version: json["version"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "updateStatus": updateStatus,
        "version": version,
      };
}
