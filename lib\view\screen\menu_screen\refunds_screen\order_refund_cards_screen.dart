// ignore_for_file: prefer_const_constructors

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_card_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/submit_refund_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import '../user_management_screen/user_management_screen.dart';
import 'no_refund_found_screen.dart';

class OrderRefundCardsScreen extends StatefulWidget {
  const OrderRefundCardsScreen({super.key});

  @override
  State<OrderRefundCardsScreen> createState() => _OrderRefundCardsScreenState();
}

class _OrderRefundCardsScreenState extends State<OrderRefundCardsScreen> {
  @override
  void initState() {
    super.initState();
    for (var item in orderRefundCardController.refundableCardServices) {
      item.isvalue = false;
    }
  }

  final tagOrderRefund = GetStorage();

  RefundsController refundsController = Get.find();

  OrderRefundCardController orderRefundCardController =
      Get.put(OrderRefundCardController());

  @override
  Widget build(BuildContext context) {
    print("OLD STYPE ${tagOrderRefund.read('orderType')}");
    tagOrderRefund.remove('orderType');
    tagOrderRefund.write('orderType', "C");
    print("NEW STYPE ${tagOrderRefund.read('orderType')}");
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Obx(
              () => orderRefundCardController.refundableCardServices.isEmpty
                  ? NoRefundFoundScreen()
                  : ListView.builder(
                      itemCount: orderRefundCardController
                          .refundableCardServices.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = orderRefundCardController
                            .refundableCardServices[index];

                        //Original code but new logic added in uncommented code read if any problem

                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              bool newValue = !(data.isvalue ?? false);

                              setState(() {
                                data.isvalue = newValue;
                              });

                              if (newValue == true) {
                                refundsController.selectedOrderRefundCardList
                                    .add(data); // Added by fuzail
                                refundsController.selectedOrderRefundTagList
                                    .clear(); // clear tags list from smart card selection

                                log("smart card screen tab selectedOrderRefundList ${refundsController.selectedOrderRefundList}");

                                refundsController.selectedOrderRefundList
                                    .add(data);
                                log("smart card screen tab selectedOrderRefundList assigned data ${refundsController.selectedOrderRefundList}");

                                refundsController
                                    .selectedOrderRefundCardSerialIDList
                                    .add(data.serialid);
                                refundsController
                                    .selectedOrderRefundCardOrderIDList
                                    .add(data.reforderid);

                                log("check serial selectedOrderRefundCardSerialIDList ${refundsController.selectedOrderRefundCardSerialIDList}");
                                log("check serial selectedOrderRefundCardOrderIDList ${refundsController.selectedOrderRefundCardOrderIDList}");

                                orderRefundCardController.totaltAmt +=
                                    data.rateDisc;
                                orderRefundCardController.totaltVAT += data.vat;
                              } else {
                                refundsController.selectedOrderRefundCardList
                                    .remove(data); // added by fuzail
                                refundsController.selectedOrderRefundTagList
                                    .clear(); // clear tags list from smart card selection

                                refundsController.selectedOrderRefundList
                                    .remove(data);
                                refundsController
                                    .selectedOrderRefundCardSerialIDList
                                    .remove(data.serialid);
                                refundsController
                                    .selectedOrderRefundCardOrderIDList
                                    .remove(data.reforderid);

                                orderRefundCardController.totaltAmt -=
                                    data.rateDisc;
                                orderRefundCardController.totaltVAT -= data.vat;
                              }

                              refundsController.selectedOrderRefundList
                                  .refresh();
                              refundsController
                                  .selectedOrderRefundCardSerialIDList
                                  .refresh();
                              refundsController
                                  .selectedOrderRefundCardOrderIDList
                                  .refresh();

                              print("*************************************");
                              print(
                                  "refundsController.selectedOrderRefundList.length >>>> ${refundsController.selectedOrderRefundList.length}");
                              print("*************************************");

                              String orderRefundCardSerialIDList =
                                  refundsController
                                      .selectedOrderRefundCardSerialIDList
                                      .join(",");
                              String orderRefundCardOrderIDList =
                                  refundsController
                                      .selectedOrderRefundCardOrderIDList
                                      .join(",");

                              print("*************************************");
                              print(
                                  "orderRefundCardSerialIDList >>>> $orderRefundCardSerialIDList");
                              print(
                                  "orderRefundCardOrderIDList >>>> $orderRefundCardOrderIDList");

                              tagOrderRefund.write(
                                  'selectedOrderRefundCardSerialIDList',
                                  orderRefundCardSerialIDList);
                              tagOrderRefund.write(
                                  'selectedOrderRefundCardOrderIDList',
                                  orderRefundCardOrderIDList);

                              // Writing again to other keys for backward compatibility? Confirm if needed.
                              tagOrderRefund.write('orderRefundTagSerialIDList',
                                  orderRefundCardSerialIDList);
                              tagOrderRefund.write('orderRefundTagOrderIDList',
                                  orderRefundCardOrderIDList);

                              tagOrderRefund.write(
                                  'tAmt',
                                  orderRefundCardController.totaltAmt
                                      .toStringAsFixed(2));
                              tagOrderRefund.write(
                                  'tVAT',
                                  orderRefundCardController.totaltVAT
                                      .toStringAsFixed(2));

                              print("*************************************");
                              log("orderRefundCardSerialIDList.write >>>> ${tagOrderRefund.read('orderRefundTagSerialIDList')}");
                              log("orderRefundCardOrderIDList.write >>>> ${tagOrderRefund.read('orderRefundTagOrderIDList')}");
                              log("tAmt write >>>> ${tagOrderRefund.read('tAmt')}");
                              log("tVAT write >>>> ${tagOrderRefund.read('tVAT')}");
                            },
                            child: refundDataWidget(
                              isShowCheckBox: true,
                              onChanged:
                                  null, // disable checkbox tap, handled by onTap
                              value: data.isvalue,
                              code: data.reforderid,
                              status: data.servicestatusDisp,
                              orderType: data.servicetype,
                              plate: data.plateno ?? "-",
                              vehicleType: data.vehicletypeDisp,
                              orderDate: data.orderdate,
                              amount: data.rateDisc.toStringAsFixed(2),
                              vat: data.vat.toStringAsFixed(2),
                              textColor: data.servicestatusDisp == "ACTIVE"
                                  ? AppColor.cGreen
                                  : data.servicestatusDisp == "IN-ACTIVE"
                                      ? AppColor.cYellow
                                      : data.servicestatusDisp == "TERMINATED"
                                          ? AppColor.cRedText
                                          : AppColor.cDarkBlueFont,
                              color: data.servicestatusDisp == "ACTIVE"
                                  ? AppColor.cLightGreen
                                  : data.servicestatusDisp == "IN-ACTIVE"
                                      ? AppColor.cLightYellow
                                      : data.servicestatusDisp == "TERMINATED"
                                          ? AppColor.cLightRedContainer
                                          : AppColor.cLightBlueContainer,
                            ),
                          ),
                        );
                        //Original code but new logic added in uncommented code read if any problem

                        // return Padding(
                        //   padding: const EdgeInsets.only(bottom: 8.0),
                        //   child: refundDataWidget(
                        //     isShowCheckBox: true,
                        //     //smart card screennnnnn
                        //     onChanged: (value) {
                        //       setState(() {
                        //         data.isvalue = value ?? false;
                        //         print("data.isvalue ${data.isvalue}");
                        //         print("value $value");
                        //       });
                        //       if (value == true) {
                        //         refundsController.selectedOrderRefundCardList
                        //             .add(data); // Added by fuzail
                        //         refundsController.selectedOrderRefundTagList
                        //             .clear(); // clear the tags list from smart card selection

                        //         log("smart card screen tab selectedOrderRefundList ${refundsController.selectedOrderRefundList} ");

                        //         refundsController.selectedOrderRefundList
                        //             .add(data);
                        //         log("smart card screen tab selectedOrderRefundList assigned data ${refundsController.selectedOrderRefundList} ");

                        //         refundsController
                        //             .selectedOrderRefundCardSerialIDList
                        //             .add(data.serialid);
                        //         refundsController
                        //             .selectedOrderRefundCardOrderIDList
                        //             .add(data.reforderid);

                        //         log("check serial selectedOrderRefundCardSerialIDList ${refundsController.selectedOrderRefundCardSerialIDList}");
                        //         log("check serial selectedOrderRefundCardOrderIDList ${refundsController.selectedOrderRefundCardOrderIDList}");

                        //    orderRefundCardController.     totaltAmt += data.rateDisc;
                        //         orderRefundCardController.totaltVAT += data.vat;

                        //       } else {
                        //         refundsController.selectedOrderRefundCardList
                        //             .remove(data); // added by fuzail
                        //         refundsController.selectedOrderRefundTagList
                        //             .clear(); // clear the tags list from smart card selection

                        //         refundsController.selectedOrderRefundList
                        //             .removeAt(index);
                        //         refundsController
                        //             .selectedOrderRefundCardSerialIDList
                        //             .removeAt(index);

                        //         refundsController
                        //             .selectedOrderRefundCardOrderIDList
                        //             .removeAt(index);

                        //         orderRefundCardController.totaltAmt -= data.rateDisc;
                        //        orderRefundCardController. totaltVAT -= data.vat;

                        //       }
                        //       refundsController.selectedOrderRefundList
                        //           .refresh();
                        //       refundsController
                        //           .selectedOrderRefundCardSerialIDList
                        //           .refresh();
                        //       refundsController
                        //           .selectedOrderRefundCardOrderIDList
                        //           .refresh();

                        //       print("*************************************");
                        //       print(
                        //           "refundsController.selectedOrderRefundList.lenght >>>> ${refundsController.selectedOrderRefundList.length}");
                        //       print("*************************************");

                        //       String orderRefundCardSerialIDList =
                        //           refundsController
                        //               .selectedOrderRefundCardSerialIDList
                        //               .join(",");
                        //       String orderRefundCardOrderIDList =
                        //           refundsController
                        //               .selectedOrderRefundCardOrderIDList
                        //               .join(",");

                        //       print("*************************************");
                        //       print(
                        //           "orderRefundCardSerialIDList >>>> $orderRefundCardSerialIDList");
                        //       print(
                        //           "orderRefundCardOrderIDList >>>> $orderRefundCardOrderIDList");

                        //       tagOrderRefund.write(
                        //           'selectedOrderRefundCardSerialIDList',
                        //           orderRefundCardSerialIDList);
                        //       tagOrderRefund.write('selectedOrderRefundCardOrderIDList',
                        //           orderRefundCardOrderIDList);

                        //              tagOrderRefund.write(
                        //         'orderRefundTagSerialIDList',
                        //         orderRefundCardSerialIDList);
                        //     tagOrderRefund.write(
                        //         'orderRefundTagOrderIDList',
                        //         orderRefundCardOrderIDList);

                        //       tagOrderRefund.write(
                        //           'tAmt', orderRefundCardController.totaltAmt.toStringAsFixed(2));
                        //       tagOrderRefund.write(
                        //           'tVAT', orderRefundCardController.totaltVAT.toStringAsFixed(2));
                        //       print("*************************************");
                        //       log(
                        //           "fuuuu orderRefundCardSerialIDList.write('orderRefundCardSerialIDList' >>>> ${tagOrderRefund.read('orderRefundTagSerialIDList')}");
                        //       log(
                        //           "siuuuuu orderRefundCardOrderIDList.write('orderRefundCardOrderIDList' >>>> ${tagOrderRefund.read('orderRefundTagOrderIDList')}");

                        //       log(
                        //           "siuuu tagOrderRefund.write('tAmt' >>>> ${tagOrderRefund.read('tAmt')}");
                        //       log(
                        //           "siuuu tagOrderRefund.write('tVAT' >>>> ${tagOrderRefund.read('tVAT')}");
                        //     },
                        //     value: data.isvalue,
                        //     code: data.reforderid,
                        //     status: data.servicestatusDisp,
                        //     orderType: data.servicetype,
                        //     plate: data.plateno ?? "-",
                        //     vehicleType: data.vehicletypeDisp,
                        //     orderDate: data.orderdate,
                        //     amount: data.rateDisc.toStringAsFixed(2),
                        //     vat: data.vat.toStringAsFixed(2),
                        //     textColor: data.servicestatusDisp == "ACTIVE"
                        //         ? AppColor.cGreen
                        //         : data.servicestatusDisp == "IN-ACTIVE"
                        //             ? AppColor.cYellow
                        //             : data.servicestatusDisp == "TERMINATED"
                        //                 ? AppColor.cRedText
                        //                 : AppColor.cDarkBlueFont,
                        //     color: data.servicestatusDisp == "ACTIVE"
                        //         ? AppColor.cLightGreen
                        //         : data.servicestatusDisp == "IN-ACTIVE"
                        //             ? AppColor.cLightYellow
                        //             : data.servicestatusDisp == "TERMINATED"
                        //                 ? AppColor.cLightRedContainer
                        //                 : AppColor.cLightBlueContainer,
                        //   ),
                        // );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget refundDataWidget({
  required String code,
  required String status,
  Color? color,
  Color? textColor,
  bool? value,
  ValueChanged<bool?>? onChanged,
  required String orderType,
  required String plate,
  required String vehicleType,
  required String orderDate,
  required String amount,
  required String vat,
  bool? isShowButton = false,
  bool? isShowCheckBox = false,
  Function()? cancelReqOnTap,
}) {
  return Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(4),
      border: Border.all(
          color: value == true ? AppColor.cDarkBlueFont : AppColor.cLightGrey),
    ),
    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                isShowCheckBox == false
                    ? SizedBox()
                    : SizedBox(
                        height: 24,
                        width: 24,
                        child: Checkbox(
                          value: value,
                          onChanged: (on){},
                          activeColor: AppColor.themeDarkBlueColor,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                              side: BorderSide(color: AppColor.cBorder)),
                        ),
                      ),
                horizontalSpace(isShowCheckBox == false ? 0 : 8),
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.cLightBlueBorder),
                        borderRadius: BorderRadius.circular(4)),
                    padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 13, color: AppColor.cDarkBlueText),
                      ),
                    )),
                horizontalSpace(8),
                newWidget(text: status, color: color, textColor: textColor),
              ],
            ),
            // assetSvdImageWidget(image: DefaultImages.verticleMoreIcn)
          ],
        ),
        verticalSpace(18),
        userDataRowWidget(
          title: "Order type".trr,
          value: orderType == "C" ? "Smart Card" : "Tag",
        ),
        verticalSpace(12),
        userDataRowWidget(
            title: "${"Plate".trr} #",
            value: plate,
            textColor: AppColor.cDarkBlueText),
        verticalSpace(12),
        userDataRowWidget(title: "Vehicle type".trr, value: vehicleType),
        verticalSpace(12),
        userDataRowWidget(title: "Order date".trr, value: orderDate),
        verticalSpace(12),
        userTotalValueWidget(title: "Amount".trr, value: amount),
        verticalSpace(12),
        userTotalValueWidget(title: "VAT".trr, value: vat),
        verticalSpace(isShowButton == true ? 14 : 0),
        isShowButton == true
            ? CommonIconBorderButton(
                iconData: DefaultImages.cancelRequestIcn,
                title: "Cancel request".trr,
                onPressed: cancelReqOnTap,
              )
            : SizedBox()
      ],
    ),
  );
}
