// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_history_tag_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_tag_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../user_management_screen/user_management_screen.dart';
import 'no_refund_found_screen.dart';

class OrderHistoryCurrentScreen extends StatefulWidget {
  const OrderHistoryCurrentScreen({super.key});

  @override
  State<OrderHistoryCurrentScreen> createState() =>
      _OrderHistoryCurrentScreenState();
}

class _OrderHistoryCurrentScreenState extends State<OrderHistoryCurrentScreen> {
  double totaltAmt = 0.0;
  double totaltVAT = 0.0;
  RefundsController refundsController = Get.find();

  OrderRefundTagController orderRefundTagController =
      Get.put(OrderRefundTagController());

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () => orderRefundTagController.refundableTagServices.isEmpty
                  ? NoRefundFoundScreen()
                  : ListView.builder(
                      itemCount:
                          orderRefundTagController.refundableTagServices.length,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = orderRefundTagController
                            .refundableTagServices[index];
                        // print(
                        //     "orderRefundTagController.refundableTagServices.length ${orderRefundTagController.refundableTagServices.length}");
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: refundDataWidget(
                            isShowCheckBox: true,
                            onChanged: (value) {
                              setState(() {
                                data.isvalue = value ?? false;
                                print("data.isvalue ${data.isvalue}");
                                print("value $value");
                              });
                              if (value == true) {
                                refundsController.selectedOrderRefundList
                                    .add(data);
                                refundsController
                                    .selectedOrderRefundTagSerialIDList
                                    .add(data.serialid);
                                refundsController
                                    .selectedOrderRefundTagOrderIDList
                                    .add(data.reforderid);

                                totaltAmt += data.rateDisc;
                                totaltVAT += data.vat;
                                print(
                                    "totaltAmt ${totaltAmt.toStringAsFixed(2)}");
                                print(
                                    "totaltAmt ADD ${totaltAmt.toStringAsFixed(2)}");
                                print(
                                    "totaltVAT ${totaltVAT.toStringAsFixed(2)}");
                                print(
                                    "totaltVAT ADD ${totaltVAT.toStringAsFixed(2)}");
                              } else {
                                refundsController.selectedOrderRefundList
                                    .removeAt(index);
                                refundsController
                                    .selectedOrderRefundTagSerialIDList
                                    .removeAt(index);
                                refundsController
                                    .selectedOrderRefundTagOrderIDList
                                    .removeAt(index);

                                totaltAmt -= data.rateDisc;
                                totaltVAT -= data.vat;
                                print(
                                    "totaltAmt ${totaltAmt.toStringAsFixed(2)}");
                                print(
                                    "totaltAmt MINUS ${totaltAmt.toStringAsFixed(2)}");
                                print(
                                    "totaltVAT ${totaltVAT.toStringAsFixed(2)}");
                                print(
                                    "totaltVAT MINUS ${totaltVAT.toStringAsFixed(2)}");
                              }
                              refundsController.selectedOrderRefundList
                                  .refresh();
                              refundsController
                                  .selectedOrderRefundTagSerialIDList
                                  .refresh();
                              refundsController
                                  .selectedOrderRefundTagOrderIDList
                                  .refresh();
                              print("*************************************");
                              print(
                                  "refundsController.selectedOrderRefundList.lenght >>>> ${refundsController.selectedOrderRefundList.length}");
                              print("*************************************");

                              String orderRefundTagSerialIDList =
                                  refundsController
                                      .selectedOrderRefundTagSerialIDList
                                      .join(",");
                              String orderRefundTagOrderIDList =
                                  refundsController
                                      .selectedOrderRefundTagOrderIDList
                                      .join(",");

                              print("*************************************");
                              print(
                                  "orderRefundTagSerialIDList >>>> $orderRefundTagSerialIDList");
                              print(
                                  "orderRefundTagOrderIDList >>>> $orderRefundTagOrderIDList");
                            },
                            value: data.isvalue,
                            code: data.reforderid,
                            status: data.servicestatusDisp,
                            orderType: data.servicetype,
                            plate: data.plateno ?? "-",
                            vehicleType: data.vehicletypeDisp,
                            orderDate: data.orderdate,
                            amount: data.rateDisc.toStringAsFixed(2),
                            vat: data.vat.toStringAsFixed(2),
                            textColor: data.servicestatusDisp == "ACTIVE"
                                ? AppColor.cGreen
                                : data.servicestatusDisp == "IN-ACTIVE"
                                    ? AppColor.cYellow
                                    : data.servicestatusDisp == "TERMINATED"
                                        ? AppColor.cRedText
                                        : AppColor.cDarkBlueFont,
                            color: data.servicestatusDisp == "ACTIVE"
                                ? AppColor.cLightGreen
                                : data.servicestatusDisp == "IN-ACTIVE"
                                    ? AppColor.cLightYellow
                                    : data.servicestatusDisp == "TERMINATED"
                                        ? AppColor.cLightRedContainer
                                        : AppColor.cLightBlueContainer,
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget refundDataWidget({
  required String code,
  required String status,
  Color? color,
  Color? textColor,
  bool? value,
  ValueChanged<bool?>? onChanged,
  required String orderType,
  required String plate,
  required String vehicleType,
  required String orderDate,
  required String amount,
  required String vat,
  bool? isShowButton = false,
  bool? isShowCheckBox = false,
  Function()? cancelReqOnTap,
}) {
  return Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(4),
      border: Border.all(
          color: value == true ? AppColor.cDarkBlueFont : AppColor.cLightGrey),
    ),
    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                isShowCheckBox == false
                    ? SizedBox()
                    : SizedBox(
                        height: 24,
                        width: 24,
                        child: Checkbox(
                          value: value,
                          onChanged: onChanged,
                          activeColor: AppColor.themeDarkBlueColor,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                              side: BorderSide(color: AppColor.cBorder)),
                        ),
                      ),
                horizontalSpace(isShowCheckBox == false ? 0 : 8),
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.cLightBlueBorder),
                        borderRadius: BorderRadius.circular(4)),
                    padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 13, color: AppColor.cDarkBlueText),
                      ),
                    )),
                horizontalSpace(8),
                newWidget(text: status, color: color, textColor: textColor),
              ],
            ),
            // assetSvdImageWidget(image: DefaultImages.verticleMoreIcn)
          ],
        ),
        verticalSpace(18),
        userDataRowWidget(
          title: "Order type".trr,
          value: orderType == "T" ? "Tag" : "Smart Card",
        ),
        verticalSpace(12),
        userDataRowWidget(
            title: "${"Plate".trr} #",
            value: plate,
            textColor: AppColor.cDarkBlueText),
        verticalSpace(12),
        userDataRowWidget(title: "Vehicle type".trr, value: vehicleType),
        verticalSpace(12),
        userDataRowWidget(title: "Order date".trr, value: orderDate),
        verticalSpace(12),
        userDataRowWidget(title: "Amount".trr, value: amount),
        verticalSpace(12),
        userDataRowWidget(title: "VAT".trr, value: vat),
        verticalSpace(isShowButton == true ? 14 : 0),
        isShowButton == true
            ? CommonIconBorderButton(
                iconData: DefaultImages.cancelRequestIcn,
                title: "Cancel request".trr,
                onPressed: cancelReqOnTap,
              )
            : SizedBox()
      ],
    ),
  );
}
