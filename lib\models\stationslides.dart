import 'dart:convert';

List<StationSlides> stationSlidesFromJson(String str) =>
    List<StationSlides>.from(
        json.decode(str).map((x) => StationSlides.fromJson(x)));

String stationSlidesToJson(List<StationSlides> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class StationSlides {
  String stnDesc;
  String placeDesc;
  String stnCoor;
  String stnStatus;

  StationSlides({
    required this.stnDesc,
    required this.placeDesc,
    required this.stnCoor,
    required this.stnStatus,
  });

  factory StationSlides.fromJson(Map<String, dynamic> json) => StationSlides(
        stnDesc: json["STN_DESC"],
        placeDesc: json["PLACE_DESC"],
        stnCoor: json["STN_COOR"],
        stnStatus: json["STN_STATUS"],
      );

  factory StationSlides.fromMap(Map<String, dynamic> map) {
    return StationSlides(
      stnDesc: map["STN_DESC"] ?? '',
      placeDesc: map["PLACE_DESC"] ?? '',
      stnCoor: map["STN_COOR"] ?? '',
      stnStatus: map["STN_STATUS"] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        "STN_DESC": stnDesc,
        "PLACE_DESC": placeDesc,
        "STN_COOR": stnCoor,
        "STN_STATUS": stnStatus,
      };
}
