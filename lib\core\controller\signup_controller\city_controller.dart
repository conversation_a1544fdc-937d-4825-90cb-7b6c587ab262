import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/city.dart';
import 'package:waie_app/models/load_data.dart';
import 'package:waie_app/models/serviceknown.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../utils/constants.dart';

class CityController extends GetxController {
  var client = http.Client();
  final cities = <Load_Data_Model>[].obs;
  final serviceknowns = <Load_Data_Model>[].obs;
  // RxList<String> cities = <String>[].obs;
  // RxList<String> serviceknowns = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    print('CityController');
    fetchCities();
  }

  Future<List<Load_Data_Model>> fetchCities() async {
    List<Load_Data_Model> citys = [];
    List<Load_Data_Model> services = [];
    try {
      var cityResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "CITY",
            "searchCode": "",
            "lookupCode": ""
          });
      var serviceResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadQueryData),
          body: {
            "IsAR": Constants.IsAr_App,
            "mode": "LOOKUPS",
            "searchCode": "ORIGIN",
            "lookupCode": ""
          });

      print("responsegetCity===> ${jsonDecode(cityResponse.body)}");

      List cityResult = jsonDecode(cityResponse.body);

      for (int i = 0; i < cityResult.length; i++) {
        Load_Data_Model loadData2 =
            Load_Data_Model.fromMap(cityResult[i] as Map<String, dynamic>);
        citys.add(loadData2);
        print("cityResult ===============${loadData2.TYPEDESC}");
      }

      print("serviceResponse===> ${jsonDecode(serviceResponse.body)}");

      List waieResult = jsonDecode(serviceResponse.body);

      for (int i = 0; i < waieResult.length; i++) {
        Load_Data_Model loadData5 =
            Load_Data_Model.fromMap(waieResult[i] as Map<String, dynamic>);
        services.add(loadData5);
        print("loadData ===============${loadData5.TYPEDESC}");
      }
      // print("responsegetCities===> ${jsonDecode(cityResponse.body)}");
      // print("responsegetServiceKnows===> ${jsonDecode(serviceResponse.body)}");
      // List cityResult = jsonDecode(cityResponse.body);
      // List serviceResult = jsonDecode(serviceResponse.body);

      // for (int i = 0; i < cityResult.length; i++) {
      //   CityModel city =
      //       CityModel.fromMap(cityResult[i] as Map<String, dynamic>);
      //   citys.add(city);
      // }

      // for (int i = 0; i < serviceResult.length; i++) {
      //   ServiceknownModel service =
      //       ServiceknownModel.fromMap(serviceResult[i] as Map<String, dynamic>);
      //   services.add(service);
      // }

      // cities.value = citys.map((item) => item.text).toList();
      // serviceknowns.value = services.map((item) => item.text).toList();
      cities.value = citys;
      serviceknowns.value = services;
      print("servicserviceseknowns.value===> $services");
      print("serviceknowns.value===> $serviceknowns");
      print("cities.value===> $cities");
      print("citys===> ${jsonDecode(jsonEncode(cities))}");
      return citys;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
