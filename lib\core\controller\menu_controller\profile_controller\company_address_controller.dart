import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';

class CompanyAddressController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  TextEditingController cpPhoneNoController = TextEditingController();
  TextEditingController buildingNoController = TextEditingController();
  TextEditingController postalCodeController = TextEditingController();
  TextEditingController poBoxController = TextEditingController();
  TextEditingController regionController = TextEditingController();
  TextEditingController districtController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  List countryList = ['Saudi Arabia'];
  List regionList = ['Jazan', 'Asir', 'Najran'];
  List<String> districtsList = [
    'Abu Yahya Dist',
    'Deira',
    'Bur Dubai',
    'Jumeirah',
    'Dubai Marina',
    'Downtown Dubai',
    'Palm Jumeirah',
    'Al Barsha',
    'Business Bay',
    'Jumeirah Lakes Towers (JLT)',
    'Dubai Silicon Oasis',
  ];
  List<String> cityList = [
    'Al-Darb',
    'Deira',
    'Bur Dubai',
    'Jumeirah',
    'Dubai Marina',
    'Downtown Dubai',
    'Palm Jumeirah',
    'Al Barsha',
    'Business Bay',
    'Jumeirah Lakes Towers (JLT)',
    'Dubai Silicon Oasis',
  ];

  getstoredCompanyAddress() async {
    var custData = custsData.read('custData');
    cpPhoneNoController.text = custData['COMPANYTEL'];
    buildingNoController.text = custData['BUILDING_NO'];
    postalCodeController.text = custData['POSTALCODE'];
    poBoxController.text = custData['POBOX'];
  }

  RxString selectedDistricts = 'Abu Yahya Dist'.obs;
  RxString selectedCountry = 'Saudi Arabia'.obs;
  RxString selectedCity = 'Al-Darb'.obs;
  RxString selectedRegion = 'Jazan'.obs;

  Future<void> updateProfileDetail() async {
    print("updateProfileDetail");
    // showDialog(
    //   barrierDismissible: false,
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custData = custsData.read('custData');
    var client = http.Client();
    print("selectedRegion.value >>>>>>>>> ${selectedRegion.value}");
    print("selectedDistricts.value >>>>>>>>> ${selectedDistricts.value}");
    print("selectedCity.value >>>>>>>>> ${selectedCity.value}");
    print("cpPhoneNoController.text >>>>>>>>> ${cpPhoneNoController.text}");
    print("buildingNoController.text >>>>>>>>> ${buildingNoController.text}");
    print("postalCodeController.text >>>>>>>>> ${postalCodeController.text}");
    print("poBoxController.text >>>>>>>>> ${poBoxController.text}");
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.updateProfileDetails),
          body: {
            "ACTION": "caddress",
            "CUSTID": custData['CUSTID'] ?? "",
            "EMAILID": custData['EMAILID'] ?? "",
            "COMPANYNAME": custData['COMPANYNAME'] ?? "",
            "COMPANYNAMEAR": custData['COMPANYNAMEAR'] ?? "",
            "CONTACTPERSON": custData['CONTACTPERSON'] ?? "",
            "DESIGNATION": custData['DESIGNATION'] ?? "",
            "CRNO": custData['CRNO'] ?? "",
            "VAT_NO": custData['VAT_NO'] ?? "",
            "SALESMAN__CODE": custData['SELECTEDSALESMAN'] ?? "",
            "COUNTRY_CODE": "SA",
            "REGION_CODE": regionController.text,
            "CITY_CODE": cityController.text,
            "DISTRICT_CODE": districtController.text,
            "STREET": custData['STREET'] ?? "",
            "BUILDING_NO": buildingNoController.text,
            "MOBILENO": custData['MOBILENO'] ?? "",
            "LANG": custData['SELECTEDLANG'] ?? "",
            "SERVICEKNOWN_CODE": custData['SERVICEKNOWN_CODE'] ?? "",
            "POSTALCODE": postalCodeController.text,
            "POBOX": poBoxController.text,
            "FIRSTNAME": custData['FIRSTNAME'] ?? "",
            "MIDNAME": custData['MIDNAME'] ?? "",
            "LASTNAME": custData['LASTNAME'] ?? "",
            "COMPANYTEL": cpPhoneNoController.text,
            "COMPANYFAX": custData['COMPANYFAX'] ?? "",
            "HOUSE_NO": custData['HOUSE_NO'] ?? "",
            "ID_TYPE": custData['SELECTEDIDTYPE'] ?? "",
            "ID_NUMBER": custData['ID_NUMBER'] ?? "",
            "GPS": custData['GPS'] ?? "",
            "FUEL91": custData['FUEL91'] ?? "",
            "FUEL95": custData['FUEL95'] ?? "",
            "DIESEL": custData['DIESEL'] ?? "",
            "OTHERS": custData['OTHERS'] ?? "",
          });
      print("++++++++++++++++++++");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");
      print("+++++++++++++++++++");
      if (response.statusCode == 200) {
        // Navigator.of(Get.context!).pop();
        buildingNoController.clear();
        postalCodeController.clear();
        cpPhoneNoController.clear();
        poBoxController.clear();
        custsData.remove('custData');
        await Get.to(
            () => DashBoardManagerScreen(
                  currantIndex: 0,
                ),
            preventDuplicates: false);
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
