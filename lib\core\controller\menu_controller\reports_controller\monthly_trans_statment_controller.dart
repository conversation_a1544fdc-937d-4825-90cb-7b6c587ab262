import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/cupertino.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../utils/api_endpoints.dart';
import '../../../../view/widget/common_snak_bar_widget.dart';

class MonthlyTransStatmentController extends GetxController {
  List<String> connectionStatusList = [
    "All",
    "Online",
    "Offline",
  ];
  RxString selectedconnectionStatusList = 'All'.obs;
  final TextEditingController datePickerController = TextEditingController();


  //Fleet Wise Fuel Usage
  TextEditingController datePickerFleetFromController =
  TextEditingController();
  TextEditingController datePickerFleetToController =
  TextEditingController();

  RxString dateFrom=''.obs;
  RxString dateTo=''.obs;
  bool isCheckedSummary=false;
  bool isCheckedGroupBy=false;

  final TextEditingController datePickerMonthyController =  TextEditingController();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  reportRequestSubmit() async{
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');

    String username = 'aldrees';
    String password = 'testpass';
    String grp="N";
    String summry="N";
    String con="A";
    if(isCheckedSummary)
      {
        grp="Y";
      }
    if(isCheckedGroupBy)
      {
        summry="Y";
      }
    if(selectedconnectionStatusList.value=="Online")
      {
        con="0";
      }
    else if(selectedconnectionStatusList.value=="Offline")
    {
      con="1";
    }else    {
      con="2";
    }

    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map dataBody = {};
    dataBody = {
      "periodfrom":"01/"+datePickerFleetFromController.text,
      "PeriodTo":"01/"+datePickerFleetFromController.text,
      "IsSummary":summry,
      "CUSTID":userid,
      "GroupByPlate":grp,
      "ConnectionStatus":con,
      "REPORTTYPE":"MonthlyTransactionStatement"};

    print("Json Value=================="+dataBody.toString());
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportReqSubmit),
          //  body: jsonEncode(dataBody));
          //body: dataBody,
          body: jsonEncode(dataBody),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          }
      );
      print("REPORT RESPONSE FleetWiseFuelUsage============"+response.body);
      //List result = jsonDecode(response.body);
      commonToast(response.body);
      return "";
    } catch (e) {
      log(e.toString());
      commonToast(e.toString());
      Get.back();
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}