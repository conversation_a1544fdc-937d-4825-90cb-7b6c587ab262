// ignore_for_file: prefer_const_constructors, avoid_print, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/apple_pay_screen.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/confirm_balance_screen.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/confirm_order_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/review_controller/review_controller.dart';
import '../../../../utils/images.dart';
import '../../../widget/common_otp_textfield.dart';
import '../../../widget/icon_and_image.dart';

import '../balance_topup_screen/balance_topup_tab_screen.dart';

Widget myBalanceSuccessWidget({
  required String replacement,
  required String code,
  required String unitPrice,
  required String qty,
  required String subTotal,
  required String vat,
  required String purchaseTotal,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisSize: MainAxisSize.min,
    children: [
      Center(
          child: Text(
        "Success".trr,
        style: pBold20,
      )),
      verticalSpace(14),
      Center(
          child: Text(
        "Your order has been successful!".trr,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      verticalSpace(8),
      Center(
          child: Text(
        'We keep your order'.trr + "#" + code,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      // Text.rich(
      //   TextSpan(
      //     text: 'We keep your order'.trr,
      //     style: pRegular13,
      //     children: <TextSpan>[
      //       TextSpan(text: ' #$code  ', style: pBold12.copyWith(fontSize: 13)),
      //       TextSpan(
      //         text: 'details in'.trr + " ",
      //       ),
      //       TextSpan(
      //           text: 'Order History'.trr,
      //           style:
      //               pBold12.copyWith(fontSize: 13, color: AppColor.cLinkText)),
      //     ],
      //   ),
      //   textAlign: TextAlign.center,
      // ),
      verticalSpace(16),
      successTotalWidget(
          unitPrice: unitPrice, // + " SAR".trr
          qty: "$qty pcs",
          subTotal: subTotal, // + " SAR".trr
          vat: vat, // + " SAR".trr
          purchaseTotal: purchaseTotal), // + " SAR".trr
      verticalSpace(16),
      buttonRow(
        pdfFun: () {
          Get.back();
        },
        dashboardFun: () {
          if (replacement == "true") {
            Get.back();
            Get.back();
            Get.back();
          } else {
            Get.offAll(DashBoardManagerScreen(
              currantIndex: 0,
            ));
          }
        },
      ),
    ],
  );
}

Widget madaSuccessWidget({
  required String code,
  required String unitPrice,
  required String qty,
  required String subTotal,
  required String vat,
  required String purchaseTotal,
  required String serviceType,
  required String sessionID,
  required String orderID,
  required String depositto,
  required String orderType,
  required String custid,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisSize: MainAxisSize.min,
    children: [
      Center(
          child: Text(
        "Confirmation!".trr,
        style: pBold20,
      )),
      verticalSpace(14),
      Center(
          child: Text(
        "Please Confirm This Order".trr,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      verticalSpace(8),
      Center(
          child: Text(
        'We keep your order'.trr + "#" + orderID,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      // Text.rich(
      //   TextSpan(
      //     text: 'We keep your order'.trr,
      //     style: pRegular13,
      //     children: <TextSpan>[
      //       TextSpan(text: ' #$code  ', style: pBold12.copyWith(fontSize: 13)),
      //       TextSpan(
      //         text: 'details in'.trr + " ",
      //       ),
      //       TextSpan(
      //           text: 'Order History'.trr,
      //           style:
      //               pBold12.copyWith(fontSize: 13, color: AppColor.cLinkText)),
      //     ],
      //   ),
      //   textAlign: TextAlign.center,
      // ),
      verticalSpace(16),
      madaSuccessTotalWidget(
          unitPrice: unitPrice, // SAR"
          qty: "$qty pcs",
          subTotal: subTotal, // SAR"
          vat: vat, // SAR"
          purchaseTotal: purchaseTotal), // SAR"
      verticalSpace(16),
      Row(
        children: [
          Expanded(
              flex: 2,
              child: CommonButton(
                title: 'Confirm'.trr,
                onPressed: () {
                  Get.off(ConfirmOrderScreen(
                    totalPurchased: purchaseTotal,
                    sessionID: sessionID,
                    orderID: orderID,
                    depositto: depositto,
                    serviceType: serviceType,
                    qty: qty,
                    orderType: orderType,
                    custid: custid,
                  ));
                },
                btnColor: AppColor.themeOrangeColor,
                horizontalPadding: 16,
              )),
          horizontalSpace(12),
          Expanded(
              flex: 1,
              child: CommonButton(
                title: "Cancel".trr,
                onPressed: () {
                  Get.back();
                },
                btnColor: AppColor.themeOrangeColor,
                horizontalPadding: 16,
              ))
        ],
      ),
    ],
  );
}

Widget applePayOrderSuccessWidget({
  required String code,
  required String unitPrice,
  required String qty,
  required String subTotal,
  required String vat,
  required String purchaseTotal,
  required String serviceType,
  required String sessionID,
  required String orderID,
  required String depositto,
  required String orderType,
  required String custid,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisSize: MainAxisSize.min,
    children: [
      Center(
          child: Text(
        "Confirmation!".trr,
        style: pBold20,
      )),
      verticalSpace(14),
      Center(
          child: Text(
        "Please Confirm This Order".trr,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      verticalSpace(8),
      Center(
          child: Text(
        'We keep your order'.trr + "#" + orderID,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      // Text.rich(
      //   TextSpan(
      //     text: 'We keep your order'.trr,
      //     style: pRegular13,
      //     children: <TextSpan>[
      //       TextSpan(text: ' #$code  ', style: pBold12.copyWith(fontSize: 13)),
      //       TextSpan(
      //         text: 'details in'.trr + " ",
      //       ),
      //       TextSpan(
      //           text: 'Order History'.trr,
      //           style:
      //               pBold12.copyWith(fontSize: 13, color: AppColor.cLinkText)),
      //     ],
      //   ),
      //   textAlign: TextAlign.center,
      // ),
      verticalSpace(16),
      madaSuccessTotalWidget(
          unitPrice: "$unitPrice SAR",
          qty: "$qty pcs",
          subTotal: "$subTotal SAR",
          vat: "$vat SAR",
          purchaseTotal: "$purchaseTotal SAR"),
      verticalSpace(16),
      Row(
        children: [
          Expanded(
              flex: 2,
              child: CommonButton(
                title: 'Confirm'.trr,
                onPressed: () {
                  Get.off(ApplePayScreen(
                    unitPrice: unitPrice,
                    totalPurchased: purchaseTotal,
                    sessionID: sessionID,
                    orderID: orderID,
                    depositto: depositto,
                    serviceType: serviceType,
                    qty: qty,
                    orderType: orderType,
                    custid: custid,
                    subTotal: subTotal,
                    vat: vat,
                  ));
                },
                btnColor: AppColor.themeOrangeColor,
                horizontalPadding: 16,
              )),
          horizontalSpace(12),
          Expanded(
              flex: 1,
              child: CommonButton(
                title: "Cancel".trr,
                onPressed: () {
                  Get.back();
                },
                btnColor: AppColor.themeOrangeColor,
                horizontalPadding: 16,
              ))
        ],
      ),
    ],
  );
}

Widget madaBalanceSuccessWidget({
  required String? code,
  required String unitPrice,
  required String vat,
  required String purchaseTotal,
  required String sessionID,
  required String orderID,
  required String depositto,
  required String orderType,
  String? custid,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisSize: MainAxisSize.min,
    children: [
      Center(
          child: Text(
        "Confirmation!".trr,
        style: pBold20,
      )),
      verticalSpace(14),
      Center(
          child: Text(
        "Please Confirm This Order".trr,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      verticalSpace(8),
      Center(
          child: Text(
        'We keep your order'.trr + "#" + orderID,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      // Text.rich(
      //   TextSpan(
      //     text: 'We keep your order'.trr,
      //     style: pRegular13,
      //     children: <TextSpan>[
      //       TextSpan(text: ' #$code  ', style: pBold12.copyWith(fontSize: 13)),
      //       TextSpan(
      //         text: 'details in'.trr + " ",
      //       ),
      //       TextSpan(
      //           text: 'Order History'.trr,
      //           style:
      //               pBold12.copyWith(fontSize: 13, color: AppColor.cLinkText)),
      //     ],
      //   ),
      //   textAlign: TextAlign.center,
      // ),
      verticalSpace(16),
      madaBalanceSuccessTotalWidget(
          // unitPrice: "$unitPrice SAR",
          unitPrice: "$unitPrice SAR",
          vat: "$vat SAR",
          purchaseTotal: "$purchaseTotal SAR"),
      verticalSpace(16),
      Row(
        children: [
          Expanded(
              flex: 2,
              child: CommonButton(
                title: 'Confirm'.trr,
                onPressed: () async {
                  Get.off(ConfirmBalanceScreen(
                    totalPurchased: purchaseTotal,
                    sessionID: sessionID,
                    orderID: orderID,
                    depositto: depositto,
                    serviceType: "D",
                    qty: "1",
                    orderType: orderType,
                    custid: custid.toString(),
                  ));
                  final reviewController = Get.find<ReviewController>();
                  await reviewController.triggerReview();
                },
                btnColor: AppColor.themeOrangeColor,
                horizontalPadding: 16,
              )),
          horizontalSpace(12),
          Expanded(
              flex: 1,
              child: CommonButton(
                title: "Cancel".trr,
                onPressed: () {
                  Get.back();
                },
                btnColor: AppColor.themeOrangeColor,
                horizontalPadding: 16,
              ))
        ],
      ),
    ],
  );
}

// Widget applePayBalanceSuccessWidget({
//   required String? code,
//   required String unitPrice,
//   required String vat,
//   required String purchaseTotal,
//   required String sessionID,
//   required String orderID,
//   required String depositto,
//   required String orderType,
//   String? custid,
// }) {
//   return Column(
//     crossAxisAlignment: CrossAxisAlignment.start,
//     mainAxisSize: MainAxisSize.min,
//     children: [
//       Center(
//           child: Text(
//         "Confirmation!".trr,
//         style: pBold20,
//       )),
//       verticalSpace(14),
//       Center(
//           child: Text(
//         "Please Confirm This Order".trr,
//         style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
//       )),
//       verticalSpace(8),
//       Center(
//           child: Text(
//         'We keep your order'.trr + "#" + orderID,
//         style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
//       )),
//       // Text.rich(
//       //   TextSpan(
//       //     text: 'We keep your order'.trr,
//       //     style: pRegular13,
//       //     children: <TextSpan>[
//       //       TextSpan(text: ' #$code  ', style: pBold12.copyWith(fontSize: 13)),
//       //       TextSpan(
//       //         text: 'details in'.trr + " ",
//       //       ),
//       //       TextSpan(
//       //           text: 'Order History'.trr,
//       //           style:
//       //               pBold12.copyWith(fontSize: 13, color: AppColor.cLinkText)),
//       //     ],
//       //   ),
//       //   textAlign: TextAlign.center,
//       // ),
//       verticalSpace(16),
//       madaBalanceSuccessTotalWidget(
//           unitPrice: "$unitPrice SAR",
//           vat: "$vat SAR",
//           purchaseTotal: "$purchaseTotal SAR"),
//       verticalSpace(16),
//       Row(
//         children: [
//           Expanded(
//               flex: 2,
//               child: CommonButton(
//                 title: 'Confirm'.trr,
//                 onPressed: () {
//                   Get.off(ApplePayScreen(
//                     totalPurchased: purchaseTotal,
//                     sessionID: sessionID,
//                     orderID: orderID,
//                     depositto: depositto,
//                     serviceType: "D",
//                     qty: "1",
//                     orderType: orderType,
//                     custid: custid.toString(),
//                     subTotal: subTotal,
//                     vat: vat,
//                   ));
//                 },
//                 btnColor: AppColor.themeOrangeColor,
//                 horizontalPadding: 16,
//               )),
//           horizontalSpace(12),
//           Expanded(
//               flex: 1,
//               child: CommonButton(
//                 title: "Cancel".trr,
//                 onPressed: () {
//                   Get.back();
//                 },
//                 btnColor: AppColor.themeOrangeColor,
//                 horizontalPadding: 16,
//               ))
//         ],
//       ),
//     ],
//   );
// }

Widget cashSuccessWidget({
  required String replacement,
  required String code,
  required String unitPrice,
  required String qty,
  required String subTotal,
  required String vat,
  required String purchaseTotal,
}) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      Center(
          child: Text(
        "Success!".trr,
        style: pBold20,
      )),
      verticalSpace(14),
      Center(
          child: Text(
        "Your order has been successful!.".trr,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
        textAlign: TextAlign.center,
      )),
      Center(
          child: Text(
        "Your order has been created, you can print the order details/invoice from your order history."
            .trr,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
        textAlign: TextAlign.center,
      )),
      verticalSpace(8),
      Center(
          child: Text(
        'We keep the details of your order'.trr +
            "#" +
            code +
            " in Order History",
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      // Text.rich(
      //   TextSpan(
      //     text: 'We keep your order'.trr,
      //     style: pRegular13,
      //     children: <TextSpan>[
      //       TextSpan(text: ' #$code  ', style: pBold12.copyWith(fontSize: 13)),
      //       TextSpan(
      //         text: 'details in'.trr + " ",
      //       ),
      //       TextSpan(
      //           text: 'Order History'.trr,
      //           style:
      //               pBold12.copyWith(fontSize: 13, color: AppColor.cLinkText)),
      //     ],
      //   ),
      //   textAlign: TextAlign.center,
      // ),
      verticalSpace(16),
      successTotalWidget(
          unitPrice: unitPrice, //SAR
          qty: "$qty pcs",
          subTotal: subTotal, //SAR
          vat: vat, //SAR
          purchaseTotal: purchaseTotal), //SAR
      verticalSpace(16),
      /*Container(
        decoration: BoxDecoration(
            color: AppColor.lightOrangeColor,
            borderRadius: BorderRadius.circular(6)),
        padding: EdgeInsets.symmetric(vertical: 13, horizontal: 20),
        child: Text(
            "Order will be automatically confirmed after successful payment".trr,
            style: pRegular13,
            textAlign: TextAlign.center),
      ),*/
      verticalSpace(16),
      buttonRow(
        pdfFun: () {
          Get.back();
        },
        dashboardFun: () async {
          if (replacement == "true") {
            Get.back();
            Get.back();
            Get.back();
          } else {
            Get.offAll(DashBoardManagerScreen(
              currantIndex: 0,
            ));
            final reviewController = Get.find<ReviewController>();
            await reviewController.triggerReview();
          }
        },
      ),
    ],
  );
}

Widget eTransferSuccessWidget({
  required String replacement,
  required String code,
  required String unitPrice,
  required String qty,
  required String subTotal,
  required String vat,
  required String purchaseTotal,
}) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      Center(
          child: Text(
        "Order Processing".trr,
        style: pBold20,
      )),
      verticalSpace(14),
      Center(
          child: Text(
        "Your order has been created and the payment is processing. In some banks, the transaction may take 1-3 days. Your order will be in status pending until the transaction is successful."
            .trr,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
        textAlign: TextAlign.center,
      )),
      verticalSpace(8),
      Center(
          child: Text(
        'We keep your order'.trr + "#" + code,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
      )),
      // Text.rich(
      //   TextSpan(
      //     text: 'We keep your order'.trr,
      //     style: pRegular13,
      //     children: <TextSpan>[
      //       TextSpan(text: ' #$code  ', style: pBold12.copyWith(fontSize: 13)),
      //       TextSpan(
      //         text: 'details in'.trr + " ",
      //       ),
      //       TextSpan(
      //           text: 'Order History'.trr,
      //           style:
      //               pBold12.copyWith(fontSize: 13, color: AppColor.cLinkText)),
      //     ],
      //   ),
      //   textAlign: TextAlign.center,
      // ),
      verticalSpace(16),
      successTotalWidget(
          unitPrice: unitPrice, //SAR
          qty: "$qty pcs",
          subTotal: subTotal, //SAR
          vat: vat, //SAR
          purchaseTotal: purchaseTotal), //SAR
      verticalSpace(16),
      Container(
        decoration: BoxDecoration(
            color: AppColor.lightOrangeColor,
            borderRadius: BorderRadius.circular(6)),
        padding: EdgeInsets.symmetric(vertical: 13, horizontal: 20),
        child: Text(
            "Order will be automatically confirmed after successful payment".trr,
            style: pRegular13,
            textAlign: TextAlign.center),
      ),
      verticalSpace(16),
      CommonButton(
        title: "OK".trr,
        onPressed: () async {
          if (replacement == "true") {
            Get.back();
            Get.back();
            Get.back();
          } else {
            Get.offAll(DashBoardManagerScreen(
              currantIndex: 0,
            ));
            final reviewController = Get.find<ReviewController>();
            await reviewController.triggerReview();
          }
        },
        btnColor: AppColor.themeOrangeColor,
        horizontalPadding: 16,
      )
    ],
  );
}

Widget orderErrorWidget() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisSize: MainAxisSize.min,
    children: [
      Center(
          child: Text(
        "Order Error!".trr,
        style: pBold20,
      )),
      verticalSpace(14),
      Center(
          child: Text(
        "There are some issues with MADA payment. Go back to your order and try again or choose another payment option"
            .trr,
        style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
        textAlign: TextAlign.center,
      )),
      verticalSpace(8),
      Center(
        child: Text.rich(
          TextSpan(
            text: 'If you have any questions'.trr + ' ',
            style: pRegular13,
            children: <TextSpan>[
              TextSpan(
                  text: 'contact us',
                  style: pBold12.copyWith(
                      fontSize: 13, color: AppColor.cLinkText)),
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ),
      verticalSpace(16),
      CommonButton(
        title: "OK".trr,
        onPressed: () {
          Get.offAll(DashBoardManagerScreen(
            currantIndex: 0,
          ));
        },
        btnColor: AppColor.themeOrangeColor,
        horizontalPadding: 16,
      )
    ],
  );
}

Widget accountVerificationWidget({
  Function(String)? onSubmit,
  required BuildContext context,
  required String code,
  required String unitPrice,
  required String qty,
  required String subTotal,
  required String vat,
  required String purchaseTotal,
}) {
  return Container(
    decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
    padding: EdgeInsets.all(16),
    child: Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 10),
            child: Row(
              children: [
                GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: AppColor.cLightBlueContainer,
                      child: Center(
                          child: assetSvdImageWidget(
                              image: DefaultImages.backIcn)),
                    )),
                Expanded(
                  child: Align(
                      alignment: Alignment.center,
                      child: Center(
                          child: Text(
                        "Account Verification".trr,
                        style: pBold20,
                      ))),
                ),
              ],
            ),
          ),
          verticalSpace(21),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text(
                'We have just sent you 6-digits authorisation code to your phone number'
                    .trr,
                style: pRegular13,
                textAlign: TextAlign.center),
          ),
          verticalSpace(10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text.rich(
              TextSpan(
                children: <TextSpan>[
                  TextSpan(
                      text: ' +1 XXX XXX XXX 75. ',
                      style: pBold12.copyWith(fontSize: 13)),
                  TextSpan(
                    text: 'Please provide the digits below.'.trr,
                    style: pRegular13,
                  )
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 16, top: 16),
            child: FittedBox(
              fit: BoxFit.fill,
              child: CommonOtpTextField(
                numberOfFields: 6,
                borderColor: AppColor.cBorder,
                enabledBorderColor: AppColor.cBorder,
                disabledBorderColor: AppColor.cBorder,
                focusedBorderColor: AppColor.cBorder,
                fieldWidth: 54,
                filled: false,
                borderRadius: BorderRadius.circular(6),
                textStyle: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                keyboardType: TextInputType.numberWithOptions(
                    signed: true, decimal: true),
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                decoration: InputDecoration(),
                showFieldAsBox: true,
                hintText: 'X',
                onCodeChanged: (String code) {
                  print("code==> $code");
                },
                onSubmit: onSubmit, // end onSubmit
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text.rich(
              TextSpan(
                children: <TextSpan>[
                  TextSpan(
                      text: "Didn’t get the code?".trr + ' ', style: pRegular17),
                  TextSpan(
                    text: 'Resend code again'.trr,
                    style: pSemiBold17.copyWith(color: AppColor.cLinkText),
                  )
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: CommonButton(
              title: 'Confirm'.trr,
              btnColor: AppColor.themeOrangeColor,
              onPressed: () {
                Get.back();
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                        contentPadding: EdgeInsets.all(16),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        insetPadding: EdgeInsets.all(25),
                        content: myBalanceSuccessWidget(
                          replacement: "false",
                          code: code,
                          unitPrice: unitPrice,
                          qty: qty,
                          subTotal: subTotal,
                          vat: vat,
                          purchaseTotal: purchaseTotal,
                        ));
                  },
                );
              },
            ),
          )
        ],
      ),
    ),
  );
}

Widget buttonRow(
    {required Function() pdfFun, required Function() dashboardFun}) {
  return Row(
    children: [
      Expanded(
          flex: 1,
          child: CommonBorderButton(
            title: 'Close'.trr,
            onPressed: pdfFun,
            textColor: AppColor.cDarkBlueFont,
            bColor: AppColor.themeDarkBlueColor,
          )),
      horizontalSpace(12),
      Expanded(
          flex: 2,
          child: CommonButton(
            title: "Continue to Dashboard".trr,
            onPressed: dashboardFun,
            btnColor: AppColor.themeOrangeColor,
            horizontalPadding: 16,
          ))
    ],
  );
}

Widget madaSuccessTotalWidget(
    {String? unitPrice,
    String? qty,
    String? subTotal,
    String? vat,
    String? purchaseTotal}) {
  return Container(
    decoration: BoxDecoration(
        color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
    padding: EdgeInsets.all(16),
    child: Column(
      children: [
        // totalsDataWidget(title: "Unit Price".trr, value: unitPrice!),
        // verticalSpace(8),
        // totalsDataWidget(title: "Quantity".trr, value: qty!),
        // verticalSpace(8),
        // totalsDataWidget(title: "SubTotal".trr, value: subTotal!),
        totalsDataWidget(title: "Total w/o VAT".trr, value: subTotal!),
        verticalSpace(8),
        totalsDataWidget(title: "VAT".trr, value: vat!),
        verticalSpace(8),
        totalsDataWidget(title: "Purchase total".trr, value: purchaseTotal!),
      ],
    ),
  );
}

Widget madaBalanceSuccessTotalWidget(
    {String? unitPrice, String? vat, String? purchaseTotal}) {
  return Container(
    decoration: BoxDecoration(
        color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
    padding: EdgeInsets.all(16),
    child: Column(
      children: [
        totalsDataWidget(title: "Unit Price".trr, value: unitPrice!),
        verticalSpace(8),
        totalsDataWidget(title: "VAT".trr, value: vat!),
        verticalSpace(8),
        totalsDataWidget(title: "Purchase total".trr, value: purchaseTotal!),
      ],
    ),
  );
}

Widget successTotalWidget(
    {String? unitPrice,
    String? qty,
    String? subTotal,
    String? vat,
    String? purchaseTotal}) {
  return Container(
    decoration: BoxDecoration(
        color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
    padding: EdgeInsets.all(16),
    child: Column(
      children: [
        // totalsDataWidget(title: "Unit Price".trr, value: unitPrice!),
        // verticalSpace(8),
        // totalsDataWidget(title: "Quantity".trr, value: qty!),
        // verticalSpace(8),
        totalsDataWidget(title: "Total w/o VAT".trr, value: subTotal!),
        verticalSpace(8),
        totalsDataWidget(title: "VAT".trr, value: vat!),
        verticalSpace(8),
        totalsDataWidget(title: "Purchase total".trr, value: purchaseTotal!),
      ],
    ),
  );
}

Widget totalsDataWidget(
    {required String title,
    required String value,
    double? fontSize,
    Color? fontColor}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(
        title,
        style: pRegular13.copyWith(
            fontSize: fontSize ?? 13, color: fontColor ?? AppColor.cText),
      ),
      Directionality(
        textDirection: TextDirection.ltr,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            assetSvdImageWidget(
              image: DefaultImages.saudiRiyal,
              width: fontSize == 14
                  ? 13
                  : fontSize == 17
                      ? 16
                      : 13,
              height: fontSize == 14
                  ? 13
                  : fontSize == 17
                      ? 16
                      : 13,
            ),
            Gap(4),
            Text(
              value,
              // double.parse(value).toStringAsFixed(2),
              style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
            ),
          ],
        ),
      ),
      // Text(
      //   value,
      //   // double.parse(value).toStringAsFixed(2),
      //   style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
      // ),
    ],
  );
}
