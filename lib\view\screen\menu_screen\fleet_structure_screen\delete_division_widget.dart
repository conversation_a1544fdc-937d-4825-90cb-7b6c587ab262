import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class DeleteDivisionWidget extends StatelessWidget {
  final Function() confirmOnTap;
  final String title;
  final String subTitle;

  const DeleteDivisionWidget({super.key, required this.confirmOnTap, required this.title, required this.subTitle});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Center(child: Text(title, style: pSemiBold17)),
          Padding(
            padding: const EdgeInsets.only(top: 24,left: 24,right: 24,bottom: 10),
            child: Center(
                child: Text(
              "${"Are you sure you want to delete this".trr} $subTitle?",
              style: pSemiBold14,
              textAlign: TextAlign.center,
            )),
          ),
          Text.rich(TextSpan(
              text: '${'Warning'.trr}:  ',
              style: pRegular13.copyWith(color: AppColor.cRedText),
              children: [TextSpan(text: 'you cant undo this.'.trr, style: pRegular13)])),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonBorderButton(
                title: 'Cancel'.trr,
                onPressed: () {
                  Get.back();
                },
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(8),
              Expanded(
                  child: CommonButton(
                title: 'Confirm'.trr,
                onPressed: confirmOnTap,
                btnColor: AppColor.cRedText,
              ))
            ],
          ),
          verticalSpace(16),
        ],
      ),
    );
  }
}
