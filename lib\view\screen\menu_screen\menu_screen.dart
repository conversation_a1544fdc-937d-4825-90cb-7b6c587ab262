// ignore_for_file: prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:waie_app/core/controller/splash_controller/splash_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/regDB.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/login_manager/login_manager_with_email_screen.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/company_affiliates_menu_screen.dart';
import 'package:waie_app/view/screen/menu_screen/fleet_structure_screen/fleet_structure_screen.dart';
import 'package:waie_app/view/screen/menu_screen/help_center_screen/terms_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/profile_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/restrict_profile_screen.dart';
import 'package:waie_app/view/screen/menu_screen/promotions_screen/promotions_screen.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/new_purchase_history_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/refund_menu_screen.dart';
import 'package:waie_app/view/screen/menu_screen/setting_screen/subscription_plan_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/new_tag_transfer_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../core/controller/dashboard_manager_controller/dashboard_manager_controller.dart';
import '../../../core/controller/menu_controller/usermenucontroller.dart';
import '../../../utils/constant.dart';
import '../../../utils/prefer.dart';
import '../../widget/common_button.dart';
import '../dashboard_manager/dashboard_manager.dart';
import '../notification_screen/notification_screen.dart';
import '../vehicles_screen/my_fleet/final_fleet_screen.dart';
import 'help_center_screen/help_center_screen.dart';
import 'menu_screen_widgets/menu_item_widget.dart';
import 'order_screen/new_order_screen.dart';
import 'reports_screen/reports_screen.dart';
import 'setting_screen/setting_screen.dart';
import 'user_management_screen/user_management_screen.dart';

class MenuScreen extends StatefulWidget {
  const MenuScreen({super.key});

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> {
  DashboardManagerController dashboardManagerController =
      Get.put(DashboardManagerController());
  GetStorage userStorage = GetStorage('User');
  final vehicle = GetStorage();
  GetStorage custsData = GetStorage('custsData');
  GetStorage usersData = GetStorage('usersData');
  RegisterDatabase db = RegisterDatabase();
  final _isReg = Hive.box('isReg_DB');
  final _isActivate = Hive.box('isActivate_DB');
  UserMenuController userMenuController = Get.put(UserMenuController());

  @override
  void initState() {
    super.initState();
    _loadHomeImage();
  }

  SplashController splashController = Get.put(SplashController());
  String homeLogo = '';

  Future<void> _loadHomeImage() async {
    String path = await splashController.loadImage('MOBLOGOIMGS');
    setState(() {
      homeLogo = path;
    });
  }

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async => false,
      child: Padding(
        padding: EdgeInsets.only(left: 16, right: 16, top: 7),
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: homeLogo.isNotEmpty
                    ? Image.network(
                        homeLogo,
                        height: 35,
                        fit: BoxFit.cover,
                      )
                    : Image.asset(
                        'asset/image/image/logotransparent.png',
                        height: 35,
                        fit: BoxFit.cover,
                      ),
              ),
              profileWidget(
                name: dashboardManagerController.userName.toString(),
                userId: dashboardManagerController.userID.toString(),
                emailID: dashboardManagerController.emailID.toString(),
                onTap: () {
                  userMenuController.navigateToProfileOrSettings(
                      context, ()=>ProfileScreen(), ()=>SettingScreen());
                },
              ),
              verticalSpace(22),
              if (Constants.menu_Finance)
                Text(
                  "Finance".trr,
                  style: pBold20,
                ),
              verticalSpace(8),
              if (Constants.NewOrderBtn == "Y")
                menuItemWidget(
                  title: "Service order".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>NewOrderScreen());
                  },
                ),
              if (Constants.TopUpBtn == "Y")
                menuItemWidget(
                    title: "Balance Topup".trr,
                    onTap: () {

                      if (Constants.hasCloseAccountRequest == 'Y') {
                        showSnackBarIfAccountPending(context);
                      } else if (Constants.custAcctType == "C" &&
                          Constants.custAcctStatus != "A") {
                        Get.to(() => ProfileScreen());
                      } else {
                        Get.offAll(() => DashBoardManagerScreen(
                              currantIndex: 1,
                            ));
                      }
                    }),
              if (Constants.subMenu_PurchaseHistory)
                menuItemWidget(
                  title: "Purchase History".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>NewPurchaseHistoryScreen());
                  },
                ),
              if (Constants.subMenu_Refund)
                menuItemWidget(
                  title: "Refund Request".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>RefundMenuScreen());
                  },
                ),
              if (Constants.subMenu_CompanyAffiliates)
                menuItemWidget(
                  title: "Company affiliates".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>CompanyAffiliateMenuScreen());
                  },
                ),
              verticalSpace(8),
              if (Constants.menu_Fleet)
                Text(
                  "Fleet".trr,
                  style: pBold20,
                ),
              verticalSpace(4),
              if (Constants.fleetStruc == "Y")
                menuItemWidget(
                  title: "Fleet structure".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>FleetStructureScreen());
                  },
                ),
              verticalSpace(4),
              if (Constants.subMenu_FleetStruc)
                menuItemWidget(
                  title: "Vehicle".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>FinalFleetScreen());
                  },
                ),
              verticalSpace(4),
              menuItemWidget(
                title: "Tag Transfer History".trr,
                onTap: () {
                  userMenuController.navigateToProfileOrSettings(
                      context, ()=>ProfileScreen(), ()=>NewTagTransferScreen());
                },
              ),
              /*verticalSpace(8),
              menuItemWidget(
                  title: "Notification".trr,
                  onTap: () {
                    Get.to(() => NotificationScreen());
                  },
                  textStyle: pSemiBold17),*/
              verticalSpace(8),
              if (Constants.menu_profile)
                Text(
                  "Account".trr,
                  style: pBold20,
                ),
              verticalSpace(8),
              if (Constants.subMenu_MyCompany)
                menuItemWidget(
                  title: "My Profile".trr,
                  onTap: () {
                    if (Constants.hasCloseAccountRequest == 'Y') {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          backgroundColor: AppColor.cLiteYellow,
                          content: Text(
                              "Your account has pending closing request, it cannot use any facility at this moment."
                                  .trr,
                              style:
                              pBold14.copyWith(color: AppColor.cBlackFont)),
                        ),
                      );
                    } else {
                      print("Constants.profileEnable");
                      print(Constants.profileEnable);
                      if (Constants.profileEnable == "Y") {
                        Get.to(() => ProfileScreen());
                      } else {
                        Get.to(() => RestrictProfileScreen());
                      }
                      //Get.to(() => ProfileScreen());
                    }
                  },
                ),
              verticalSpace(8),
              if (Constants.subMenu_MyCompany)
                menuItemWidget(
                  title: "Account Settings".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>SettingScreen());
                  },
                ),
              if (Constants.subMenu_Users) verticalSpace(8),
              if (Constants.subMenu_Users)
                menuItemWidget(
                  title: "Users".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>UserManagementScreen());
                  },
                ),
              if (Constants.custRegType != "C") verticalSpace(8),
              //  if (Constants.subMenu_Subscription || Constants.custRegType != "C")
              if (Constants.custRegType != "C")
                menuItemWidget(
                  title: "Subscriptions".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>SubscriptionPlanScreen());
                  },
                ),
              verticalSpace(8),
              if (Constants.menu_Reports)
                menuItemWidget(
                    title: "Reports".trr,
                    onTap: () {
                      Get.to(() => ReportsScreen());
                    },
                    textStyle: pSemiBold17),
              verticalSpace(4),
              menuItemWidget(
                  title: "Notification".trr,
                  onTap: () {
                    userMenuController.navigateToProfileOrSettings(
                        context, ()=>ProfileScreen(), ()=>NotificationScreen());
                  },
                  textStyle: pSemiBold17),
              verticalSpace(4),
              if (Constants.promoMenu == 'Y')
                menuItemWidget(
                    title: "Aldrees partners".trr,
                    onTap: () {
                      userMenuController.navigateToProfileOrSettings(context,
                        ()=>ProfileScreen(),
                            ()=>PromotionsScreen(),
                      );

                    },
                    textStyle: pSemiBold17),
              verticalSpace(4),
              if (Constants.menu_Help)
                menuItemWidget(
                    title: "Help center".trr,
                    onTap: () {
                      Get.to(() => HelpCenterScreen());
                    },
                    textStyle: pSemiBold17,
                    icon: DefaultImages.questionMarkCircleIcn),
              verticalSpace(4),
              if (Constants.menu_Help)
                menuItemWidget(
                  title: "Terms And Conditions".trr,
                  onTap: () {
                    Get.to(() => TermsScreen());
                  },
                  textStyle: pSemiBold17,
                ),
              if (Constants.menu_Help)
                menuItemWidget(
                  title: "Download Manual".trr,
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            insetPadding: const EdgeInsets.all(16),
                            contentPadding: const EdgeInsets.all(24),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  "Download Manual Users".trr,
                                  style: pBold20,
                                  textAlign: TextAlign.center,
                                ),
                                verticalSpace(24),
                                CommonButton(
                                  title: "Yes".trr,
                                  onPressed: () {
                                    userMenuController.downloadPdf(
                                        AppConstant.manualsLink, "Manual".trr);
                                    // downloadPdf(ApiEndPoints.manualsLink, "Manual".trr);
                                  },
                                  btnColor: AppColor.themeOrangeColor,
                                ),
                                CommonButton(
                                  title: "Cancel".trr,
                                  onPressed: () async {
                                    Get.back();
                                  },
                                  btnColor: AppColor.themeDarkBlueColor,
                                )
                              ],
                            ),
                          );
                        });
                  },
                  textStyle: pSemiBold17,
                ),
              verticalSpace(4),
              if (Constants.menu_Help)
                menuItemWidget(
                    title: "Log out".trr,
                    onTap: () {
                      Prefs.clear();
                      //Get.offAll(() => LoginManagerScreen());
                      vehicle.erase();
                      userStorage.erase();
                      custsData.erase();
                      usersData.erase();
                      if (_isReg.get("regUser") == null) {
                        if (_isActivate.get("regActivate") != null) {
                          db.deleteActivate();
                        }
                      }
                      Get.offAll(() => LoginManagerWithEmailScreen());
                    },
                    textStyle: pRegular17.copyWith(color: AppColor.cRedText),
                    icon: DefaultImages.logoutIcn),
            ],
          ),
        ),
      ),
    );
  }

  Widget profileWidget({
    required String name,
    required String userId,
    required String emailID,
    required Function() onTap,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(
        //"Hi".trr + ", $name",
        name,
        style: pBold18.copyWith(
          color: AppColor.themeOrangeColor,
          fontSize: 18,
        ),
      ),
      subtitle: Text.rich(
        TextSpan(
          text: '',
          style: pRegular16.copyWith(
            color: AppColor.themeOrangeColor,
            fontSize: 16,
          ),
          children: <TextSpan>[
            TextSpan(
              text: "$userId\n$emailID",
              style: pRegular16.copyWith(
                color: AppColor.themeOrangeColor,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
      trailing: GestureDetector(
        onTap: onTap,
        child: assetSvdImageWidget(
          image: DefaultImages.settingIcn,
        ),
      ),
    );
  }

  void showSnackBarIfAccountPending(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: AppColor.cLiteYellow,
        content: Text(
          "Your account has pending closing request, it cannot use any facility at this moment."
              .trr,
          style: pBold14.copyWith(color: AppColor.cBlackFont),
        ),
      ),
    );
  }
}
