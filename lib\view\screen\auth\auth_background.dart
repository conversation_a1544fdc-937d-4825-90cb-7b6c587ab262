// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/splash_controller/splash_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../core/controller/auth/auth_controller.dart';
import '../../../main.dart';
import '../../../utils/constant.dart';

class AuthBackGroundWidget extends StatefulWidget {
  final Widget widget;

  AuthBackGroundWidget({super.key, required this.widget});

  @override
  State<AuthBackGroundWidget> createState() => _AuthBackGroundWidgetState();
}

class _AuthBackGroundWidgetState extends State<AuthBackGroundWidget> {
  @override
  void initState() {
    super.initState();
    _loadLoginImage();
  }

  AuthController authController = Get.put(AuthController());

  SplashController splashController = Get.put(SplashController());

  String loginImagePath = '';

  String languageCode = myStorage!.getString(AppConstant.LANGUAGE_CODE) ?? 'ar';

  Future<void> _loadLoginImage() async {
    String path = await splashController.loadImage('MOBLOGINIMGS');
    setState(() {
      loginImagePath = path;
      log(loginImagePath);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColor.themeBlueColor,
      child: Stack(
        // fit: StackFit.expand,
        alignment: Alignment.topCenter,
        children: [
          Stack(
            children: [
              assetSvdImageWidget(
                  image: DefaultImages.loginBg, width: Get.width),
              loginImagePath.isNotEmpty
                  ? Image(
                      image: splashController.splashImage,
                      fit: BoxFit.cover) //Image.network(loginImagePath)
                  : assetSvdImageWidget(
                      image: DefaultImages.loginBg, width: Get.width),
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Obx(() {
                          WidgetsBinding.instance
                              .addPostFrameCallback((timeStamp) {
                            if (languageCode == 'en') {
                              authController.selectedLanguage.value = "English";
                              authController.selectedLanguageImage.value =
                                  DefaultImages.englishImg;
                            } else {
                              authController.selectedLanguage.value = "Arabic";
                              authController.selectedLanguageImage.value =
                                  DefaultImages.arabicImg;
                            }
                            authController.selectedLanguage.refresh();
                            authController.selectedLanguageImage.refresh();
                          });
                          return PopupMenuButton(
                            onSelected: (item) {},
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6)),
                            itemBuilder: (BuildContext context) =>
                                authController.languageList.map((data) {
                              return PopupMenuItem(
                                value: data['title'],
                                child: GestureDetector(
                                  onTap: () {
                                    // authController
                                    //     .selectedLanguageIndex
                                    //     .value = i;
                                    languageCode = data['languageCode'];
                                    authController.selectedLanguage.value =
                                        data['title'];
                                    authController.selectedLanguageImage.value =
                                        data['image'];
                                    authController.selectedLanguageIndex
                                        .refresh();
                                    authController
                                        .updateLanguage(data['local']);
                                    Get.back();
                                  },
                                  child: languageWidget(
                                    image: data['image'],
                                    title: data['title'],
                                    color: data['languageCode'] == languageCode
                                        ? AppColor.themeBlueColor
                                        : AppColor.cWhite,
                                    bColor: data['languageCode'] == languageCode
                                        ? AppColor.themeBlueColor
                                        : AppColor.cBorder,
                                  ),
                                ),
                              );
                            }).toList(),
                            child: Row(
                              children: [
                                ClipRRect(
                                    borderRadius: BorderRadius.circular(6),
                                    child: Image.asset(
                                      authController
                                          .selectedLanguageImage.value,
                                      width: 28,
                                      height: 21,
                                    )),
                                horizontalSpace(6),
                                Text(
                                  authController.selectedLanguage.value,
                                  style: pSemiBold14.copyWith(
                                      color: loginImagePath.isEmpty
                                          ? AppColor.cWhiteFont
                                          : AppColor.cBlackFont),
                                )
                              ],
                            ),
                          );
                        }),
                        //totalNotificationWidget(totalNotification: '4')
                      ],
                    ),
                    verticalSpace(20),
                    if (loginImagePath.isEmpty) ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          assetSvdImageWidget(
                              image: DefaultImages.loginLogoImage),
                          horizontalSpace(24),
                          assetSvdImageWidget(
                              image: DefaultImages.loginLogoImage2),
                        ],
                      )
                    ]
                  ],
                ),
              )
            ],
          ),
          Padding(
            padding: EdgeInsets.only(
                top: loginImagePath.isEmpty
                    ? MediaQuery.of(context).size.height * 0.23
                    : MediaQuery.of(context).size.height * 0.28),
            child: Container(
                height: Get.height,
                width: Get.width,
                decoration: BoxDecoration(
                  color: AppColor.cBackGround,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                padding: EdgeInsets.only(right: 16.0, left: 16.0),
                child: widget.widget),
          ),
        ],
      ),
    );
  }
}

Widget totalNotificationWidget({required String totalNotification}) {
  return Stack(
    alignment: Alignment.topRight,
    children: [
      assetSvdImageWidget(image: DefaultImages.notificationIcn),
      CircleAvatar(
        radius: 7,
        backgroundColor: AppColor.themeOrangeColor,
        child: Center(
          child: Text(
            totalNotification,
            style: pSemiBold10.copyWith(
              color: AppColor.cWhiteFont,
              fontSize: 11,
            ),
          ),
        ),
      )
    ],
  );
}

Widget languageWidget(
    {String? image, String? title, Color? color, Color? bColor}) {
  return Padding(
    padding: const EdgeInsets.only(top: 10),
    child: Container(
      height: 55,
      width: Get.width,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: bColor ?? AppColor.cBorder)),
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              image == null
                  ? SizedBox(
                      width: 0,
                      height: 0,
                    )
                  : ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Image.asset(
                        image,
                        width: 28,
                        height: 21,
                      )),
              horizontalSpace(15),
              Text(
                title!,
                style: pMedium12,
              ),
            ],
          ),
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
                border: Border.all(color: bColor ?? AppColor.cBorder, width: 1),
                shape: BoxShape.circle),
            padding: EdgeInsets.all(01.5),
            child: CircleAvatar(
              backgroundColor: color ?? AppColor.cWhite,
            ),
          ),
        ],
      ),
    ),
  );
}
