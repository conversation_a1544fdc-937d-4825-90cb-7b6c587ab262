import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SearchVehicleController extends GetxController {
  var searchController = TextEditingController().obs;
  RxList myFleetList = [
    {
      "value": false.obs,
      "code": "Plate #",
      "status": "New",
      "title": "",
      "type": "",
      "driver": "",
      "quotaTotal": "",
      "quotaString": '',
      "division": "",
    }.obs,
    {
      "value": false.obs,
      "code": "0411XTA",
      "status": "Active",
      "title": "Nissan NV300",
      "type": "<PERSON>",
      "driver": "<PERSON>",
      "quotaTotal": "50 / 300",
      "quotaString": 'Liters Monthly',
      "division": "Tabuk /\nAuto Parts /\nSales /\nCouriers",
    }.obs,
    {
      "value": false.obs,
      "code": "9886KTR",
      "status": "Inactive",
      "title": "Nissan NV300",
      "type": "<PERSON>",
      "driver": "<PERSON>",
      "quotaTotal": "50 / 300",
      "quotaString": 'Liters Monthly',
      "division": "Tabuk /\nAuto Parts /\nSales /\nCouriers",
    }.obs
  ].obs;
  RxList itemList=[].obs;
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    searchController.value=TextEditingController();
  }
}
