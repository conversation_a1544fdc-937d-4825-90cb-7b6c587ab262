// ignore_for_file: must_be_immutable, prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:waie_app/core/controller/notification_controller/show_tag_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../utils/helper.dart';
import '../menu_screen/order_screen/new_order_screen.dart';

/*class ShowTagScreen extends StatelessWidget {
  ShowTagScreen({Key? key}) : super(key: key);
  ShowTagController showTagController = Get.put(ShowTagController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            simpleMyAppBar(
                title: "Tag replacement".tr,
                onTap: () {
                  Get.back();
                },
                backString: "Back".tr,
                horizontalSize: 45),
            Obx(() {
              return Expanded(
                child: ListView(
                  shrinkWrap: true,
                  padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
                  children: [
                    verticalSpace(8),
                    Row(
                      children: [
                        plusWidget(
                          image: DefaultImages.plusIcn,
                          onTap: () {
                            showTagController.quantity.value += 1;
                          },
                        ),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            child: Container(
                              height: 44,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4), border: Border.all(color: AppColor.cBorder)),
                              child: Center(
                                child: Text(
                                  showTagController.quantity.value.toString(),
                                  style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                                ),
                              ),
                            ),
                          ),
                        ),
                        plusWidget(
                          image: DefaultImages.minusIcn,
                          onTap: () {
                            if (showTagController.quantity.value > 0) {
                              showTagController.quantity.value -= 1;
                            }
                          },
                        ),
                      ],
                    ),
                    verticalSpace(32),
                    horizontalDivider(),
                    verticalSpace(32),
                    Text(
                      "Payment options".tr,
                      style: pSemiBold17,
                    ),
                    verticalSpace(16),
                    ListView.builder(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: showTagController.paymentOptionList.length,
                        itemBuilder: (context, index) {
                          var data = showTagController.paymentOptionList[index];
                          return Obx(() {
                            return paymentOptionDataWidget(
                              onTap: () {
                                for (int i = 0; i < showTagController.paymentOptionList.length; i++) {
                                  if (index == i) {
                                    data['value'].value = true;
                                    showTagController.currantIndex.value = index;
                                    print("---${showTagController.currantIndex.value}");
                                  } else {
                                    showTagController.paymentOptionList[i]['value'].value = false;
                                  }
                                }
                                showTagController.paymentOptionList.refresh();
                              },
                              isSelected: data['value'].value,
                              title: data['title'].toString().tr,
                              balance: data['balance'],
                              widget: showTagController.currantIndex.value == 1
                                  ? cashDetailWidget("${data['label'].toString().tr} :")
                                  : showTagController.currantIndex.value == 3
                                      ? Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            verticalSpace(20),
                                            CommonTextField(
                                              labelText: 'Card number'.tr,
                                              hintText: '_ _ _ _ - _ _ _ _ - _ _ _ _ - _ _ _ _',
                                              inputFormatters: [
                                                FilteringTextInputFormatter.digitsOnly,
                                                CardNumberFormatter(),
                                              ],
                                              maxLength: 19,
                                              keyboardType: TextInputType.number,
                                            ),
                                            verticalSpace(16),
                                            CommonTextField(
                                              labelText: 'Cardholder name'.tr,
                                              hintText: 'Enter name'.tr,
                                            ),
                                            verticalSpace(16),
                                            Row(
                                              children: [
                                                Expanded(
                                                    child: expiryMonthWidget(
                                                        onTap: () {
                                                          showDialog(
                                                              context: context,
                                                              builder: (context) => AlertDialog(
                                                                    content: SizedBox(
                                                                      height: Get.height / 2,
                                                                      width: Get.width,
                                                                      child: SfDateRangePicker(
                                                                        onSelectionChanged:
                                                                            (dateRangePickerSelectionChangedArgs) {
                                                                          Get.back();
                                                                          showTagController
                                                                              .expireMonth.value = returnMonth(
                                                                                  dateRangePickerSelectionChangedArgs
                                                                                      .value)
                                                                              .toString();
                                                                          print(
                                                                              "value============= ${showTagController.expireMonth.value}");
                                                                        },
                                                                        view: DateRangePickerView.year,
                                                                        selectionMode:
                                                                            DateRangePickerSelectionMode.single,
                                                                        enableMultiView: false,
                                                                        monthFormat: "MM",
                                                                        monthViewSettings:
                                                                            DateRangePickerMonthViewSettings(
                                                                                enableSwipeSelection: false,
                                                                                firstDayOfWeek: 1,
                                                                                dayFormat: 'MM'),
                                                                        showActionButtons: false,
                                                                        allowViewNavigation: false,
                                                                        monthCellStyle: DateRangePickerMonthCellStyle(),
                                                                      ),
                                                                    ),
                                                                  ));
                                                        },
                                                        label: "Expire month".tr,
                                                        month: showTagController.expireMonth.value)),
                                                horizontalSpace(16),
                                                Expanded(
                                                    child: expiryMonthWidget(
                                                        onTap: () {
                                                          showDialog(
                                                              context: context,
                                                              builder: (context) => AlertDialog(
                                                                    content: SizedBox(
                                                                      height: Get.height / 2,
                                                                      width: Get.width,
                                                                      child: SfDateRangePicker(
                                                                        onSelectionChanged:
                                                                            (dateRangePickerSelectionChangedArgs) {
                                                                          Get.back();
                                                                          showTagController
                                                                              .expireYear.value = dateFormatted(
                                                                                  formatType: formatForDateTime(
                                                                                      FormatType.year),
                                                                                  date:
                                                                                      dateRangePickerSelectionChangedArgs
                                                                                          .value
                                                                                          .toString())
                                                                              .toString();
                                                                          print(
                                                                              "value============= ${showTagController.expireYear.value}");
                                                                        },
                                                                        view: DateRangePickerView.decade,
                                                                        selectionMode:
                                                                            DateRangePickerSelectionMode.single,
                                                                        enableMultiView: false,
                                                                        monthFormat: "MM",
                                                                        monthViewSettings:
                                                                            DateRangePickerMonthViewSettings(
                                                                                enableSwipeSelection: false,
                                                                                firstDayOfWeek: 1,
                                                                                dayFormat: 'MM'),
                                                                        showActionButtons: false,
                                                                        allowViewNavigation: false,
                                                                        monthCellStyle: DateRangePickerMonthCellStyle(),
                                                                      ),
                                                                    ),
                                                                  ));
                                                        },
                                                        label: "Expire year".tr,
                                                        month: showTagController.expireYear.value))
                                              ],
                                            ),
                                            verticalSpace(16),
                                            Text(
                                              "Security code".tr,
                                              style: pRegular12,
                                            ),
                                            verticalSpace(6),
                                            Row(
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                Expanded(
                                                    flex: 2,
                                                    child: CommonTextField(
                                                      labelText: '',
                                                      hintText: '- - -',
                                                      inputFormatters: [
                                                        FilteringTextInputFormatter.digitsOnly,
                                                      ],
                                                      maxLength: 3,
                                                      keyboardType: TextInputType.number,
                                                    )),
                                                horizontalSpace(12),
                                                Expanded(
                                                    flex: 3,
                                                    child: Row(
                                                      crossAxisAlignment: CrossAxisAlignment.center,
                                                      children: [
                                                        assetSvdImageWidget(image: DefaultImages.cardIcn),
                                                        horizontalSpace(4),
                                                        Expanded(
                                                          child: Text(
                                                            "3 digits on back of your card".tr,
                                                            style: pRegular12,
                                                            overflow: TextOverflow.ellipsis,
                                                          ),
                                                        ),
                                                      ],
                                                    )),
                                              ],
                                            ),
                                            verticalSpace(16),
                                          ],
                                        ) //MADA PAYMENT
                                      : showTagController.currantIndex.value == 4
                                          ? Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                verticalSpace(20),
                                                Text(
                                                  "Promotion".tr,
                                                  style: pRegular14,
                                                ),
                                                verticalSpace(8),
                                                Row(
                                                  children: [
                                                    promotionWidget(
                                                      title: "Al-Rajhi Mokafaa".tr,
                                                      isSelected: showTagController.isUser1.value,
                                                      onTap: () {
                                                        showTagController.isUser1.value = true;
                                                        showTagController.isUser2.value = false;
                                                      },
                                                    ),
                                                    horizontalSpace(12),
                                                    promotionWidget(
                                                      title: "STC Qitaf".tr,
                                                      isSelected: showTagController.isUser2.value,
                                                      onTap: () {
                                                        showTagController.isUser1.value = false;
                                                        showTagController.isUser2.value = true;
                                                      },
                                                    ),
                                                  ],
                                                ),
                                                verticalSpace(16),
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Text(
                                                      "Phone Number".tr,
                                                      style: pRegular14,
                                                    ),
                                                    showTagController.isSubmit.value ||
                                                            showTagController.isValidate.value
                                                        ? SizedBox()
                                                        : Text(
                                                            "Not Confirmed".tr,
                                                            style: pSemiBold12.copyWith(color: AppColor.cRedText),
                                                          ),
                                                  ],
                                                ),
                                                verticalSpace(6),
                                                Container(
                                                  height: 44,
                                                  decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(6),
                                                      border: Border.all(
                                                          color: showTagController.isSubmit.value == true ||
                                                                  showTagController.isValidate.value == true
                                                              ? AppColor.cBorder
                                                              : AppColor.cRedText)),
                                                  child: InternationalPhoneNumberInput(
                                                    onInputChanged: (PhoneNumber number) {
                                                      print("====>${number.phoneNumber}");
                                                      print("---->${number.isoCode}");
                                                      showTagController.isoCode.value = number.isoCode!;
                                                    },
                                                    onInputValidated: (bool value) {
                                                      print(value);
                                                    },
                                                    cursorColor: AppColor.cHintFont,
                                                    selectorConfig: SelectorConfig(
                                                      selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                                                      leadingPadding: 16,
                                                      setSelectorButtonAsPrefixIcon: true,
                                                    ),
                                                    ignoreBlank: false,
                                                    autoValidateMode: AutovalidateMode.disabled,
                                                    textStyle: pRegular14.copyWith(color: AppColor.cLabel),
                                                    initialValue: PhoneNumber(
                                                        isoCode: showTagController.isoCode.value,
                                                        dialCode: showTagController.isoCode.value),
                                                    inputBorder: OutlineInputBorder(),
                                                    keyboardAction: TextInputAction.done,
                                                    scrollPadding: EdgeInsets.zero,
                                                    selectorTextStyle:
                                                        pRegular14.copyWith(color: AppColor.cLabel, fontSize: 14),
                                                    textAlign: TextAlign.start,
                                                    textAlignVertical: TextAlignVertical.center,
                                                    inputDecoration: InputDecoration(
                                                        contentPadding: EdgeInsets.only(left: 16, bottom: 8),
                                                        isDense: true,
                                                        prefixText: "|  ",
                                                        prefixStyle: TextStyle(fontSize: 30, color: AppColor.cBorder),
                                                        counterText: '',
                                                        hintText: ' ' + 'Please enter here'.tr,
                                                        counterStyle: TextStyle(fontSize: 0, height: 0),
                                                        errorStyle: TextStyle(fontSize: 0, height: 0),
                                                        hintStyle: pRegular14.copyWith(
                                                          color: AppColor.cHintFont,
                                                        ),
                                                        border: InputBorder.none),
                                                    onSaved: (PhoneNumber number) {
                                                      print('On Saved: $number');
                                                      print('On Saved:111:: ${number.dialCode}');
                                                      print('On Saved:2222: ${number.phoneNumber}');
                                                    },
                                                  ),
                                                ),
                                                verticalSpace(15),
                                                showTagController.isValidate.value
                                                    ? Row(
                                                        children: [
                                                          assetSvdImageWidget(
                                                              image: DefaultImages.checkCircleIcn,
                                                              colorFilter:
                                                                  ColorFilter.mode(AppColor.cGreen, BlendMode.srcIn)),
                                                          horizontalSpace(12),
                                                          Text(
                                                            "CONFIRMED".tr,
                                                            style: pSemiBold12.copyWith(color: AppColor.cGreen),
                                                          )
                                                        ],
                                                      )
                                                    : CommonButton(
                                                        title: showTagController.isSubmit.value == true
                                                            ? "Submit".tr
                                                            : "Try again".tr,
                                                        onPressed: () {
                                                          if (showTagController.isSubmit.value == false) {
                                                            showTagController.isValidate.value = true;
                                                          } else {
                                                            showTagController.isSubmit.value = false;
                                                          }
                                                        },
                                                        btnColor: AppColor.themeOrangeColor,
                                                      ),
                                              ],
                                            )
                                          : eTransferWidget(),
                            );
                          });
                        }),
                    verticalSpace(32),
                    Container(
                      decoration: BoxDecoration(color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Totals".tr,
                            style: pBold24.copyWith(fontSize: 22),
                          ),
                          verticalSpace(16),
                          totalValueWidget("Unit price".tr, "50 SAR"),
                          totalValueWidget("Quantity".tr, "1"),
                          totalValueWidget("VAT".tr, "13.04 SAR"),
                          horizontalDivider(),
                          Padding(
                            padding: const EdgeInsets.only(top: 16, bottom: 16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Total".tr,
                                  style: pRegular17,
                                ),
                                Text(
                                  "63.04 SAR",
                                  style: pSemiBold17,
                                ),
                              ],
                            ),
                          ),
                          CommonButton(
                            title: 'Place order'.tr,
                            onPressed: () {},
                            btnColor: AppColor.themeOrangeColor,
                          ),
                        ],
                      ),
                    ),
                    verticalSpace(10),
                    Text(
                      "* " + "Diesel cars are exempt from monthly volume discounts.".tr,
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    ),
                    verticalSpace(3),
                    Text(
                      "* " + "We'll confirm stock availability after you've made payment.".tr,
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    ),
                    verticalSpace(3),
                    Text(
                      "* " + "Enable popups for this page to view/print Order Reports.".tr,
                      style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    ),
                    verticalSpace(24),
                  ],
                ),
              );
            })
          ],
        ),
      ),
    );
  }

  Widget eTransferWidget() {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: Column(
        children: [
          verticalSpace(20),
          CommonTextField(
            controller: showTagController.emailController,
            labelText: 'Email'.tr,
            hintText: "Please enter here".tr + '...',
            keyboardType: TextInputType.emailAddress,
          ),
          verticalSpace(16),
          CommonDropdownButtonWidget(
            labelText: "Select Bank".tr,
            list: showTagController.itemList,
            value: showTagController.selectedItem.value,
            onChanged: (value) {
              showTagController.selectedItem.value = value;
            },
          ),
          verticalSpace(8)
        ],
      ),
    );
  }

  Column cashDetailWidget(data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSpace(15),
        Text(
          data,
          style: pSemiBold14,
        ),
        verticalSpace(8),
        Text(
          "• " +
              "Fill up with fuel or pick up the items you need.".tr +
              "\n• " +
              "Go to the cashier.".tr +
              "\n• " +
              'Tell the cashier your user account ID.'.tr,
          style: pRegular14,
        ),
      ],
    );
  }

  totalValueWidget(String title, String subTitle) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pRegular13,
          ),
          Text(
            subTitle,
            style: pSemiBold14,
          ),
        ],
      ),
    );
  }
}

Widget plusWidget({String? image, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      height: 44,
      width: 44,
      decoration:
          BoxDecoration(borderRadius: BorderRadius.circular(6), border: Border.all(color: AppColor.cLightBlueBorder)),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Center(
        child: assetSvdImageWidget(image: image),
      ),
    ),
  );
}*/
