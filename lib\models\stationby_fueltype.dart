import 'dart:convert';

List<StationsByFuelType> stationsByFuelTypeFromJson(String str) =>
    List<StationsByFuelType>.from(
        json.decode(str).map((x) => StationsByFuelType.fromJson(x)));

String stationsByFuelTypeToJson(List<StationsByFuelType> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class StationsByFuelType {
  double stationCode;
  String stnNameE;
  String stnNameA;
  String stnAddress;
  String cityCode;
  String cityE;
  String cityA;
  String areaCode;
  String areaE;
  String areaA;
  String districtE;
  String districtA;
  String latitude;
  String longitude;
  String stnWaie;
  String stnCctv;
  String stnAtg;
  String stnMosque;
  String stnCarService;
  String stnFoodRest;
  String stnCarRent;
  String stnAtm;
  double? stnPetrol91;
  double? stnPetrol95;
  double? stnDiesel;
  dynamic stnOwners;

  StationsByFuelType({
    required this.stationCode,
    required this.stnNameE,
    required this.stnNameA,
    required this.stnAddress,
    required this.cityCode,
    required this.cityE,
    required this.cityA,
    required this.areaCode,
    required this.areaE,
    required this.areaA,
    required this.districtE,
    required this.districtA,
    required this.latitude,
    required this.longitude,
    required this.stnWaie,
    required this.stnCctv,
    required this.stnAtg,
    required this.stnMosque,
    required this.stnCarService,
    required this.stnFoodRest,
    required this.stnCarRent,
    required this.stnAtm,
    this.stnPetrol91,
    this.stnPetrol95,
    this.stnDiesel,
    this.stnOwners,
  });

  factory StationsByFuelType.fromJson(Map<String, dynamic> json) =>
      StationsByFuelType(
        stationCode: json["STATION_CODE"]?.toDouble() ?? 0.0,
        stnNameE: json["STN_NAME_E"] ?? "",
        stnNameA: json["STN_NAME_A"] ?? "",
        stnAddress: json["STN_ADDRESS"] ?? "",
        cityCode: json["CITY_CODE"] ?? "",
        cityE: json["CITY_E"] ?? "",
        cityA: json["CITY_A"] ?? "",
        areaCode: json["AREA_CODE"] ?? "",
        areaE: json["AREA_E"] ?? "",
        areaA: json["AREA_A"] ?? "",
        districtE: json["DISTRICT_E"] ?? "",
        districtA: json["DISTRICT_A"] ?? "",
        latitude: json["LATITUDE"]?.trim() ?? "",
        longitude: json["LONGITUDE"]?.trim() ?? "",
        stnWaie: json["STN_WAIE"] ?? "",
        stnCctv: json["STN_CCTV"] ?? "",
        stnAtg: json["STN_ATG"] ?? "",
        stnMosque: json["STN_MOSQUE"] ?? "",
        stnCarService: json["STN_CAR_SERVICE"] ?? "",
        stnFoodRest: json["STN_FOOD_REST"] ?? "",
        stnCarRent: json["STN_CAR_RENT"] ?? "",
        stnAtm: json["STN_ATM"] ?? "",
        stnPetrol91: json["STN_PETROL91"]?.toDouble(),
        stnPetrol95: json["STN_PETROL95"]?.toDouble(),
        stnDiesel: json["STN_DIESEL"]?.toDouble(),
        stnOwners: json["STN_OWNERS"],
      );

  Map<String, dynamic> toJson() => {
        "STATION_CODE": stationCode,
        "STN_NAME_E": stnNameE,
        "STN_NAME_A": stnNameA,
        "STN_ADDRESS": stnAddress,
        "CITY_CODE": cityCode,
        "CITY_E": cityE,
        "CITY_A": cityA,
        "AREA_CODE": areaCode,
        "AREA_E": areaE,
        "AREA_A": areaA,
        "DISTRICT_E": districtE,
        "DISTRICT_A": districtA,
        "LATITUDE": latitude,
        "LONGITUDE": longitude,
        "STN_WAIE": stnWaie,
        "STN_CCTV": stnCctv,
        "STN_ATG": stnAtg,
        "STN_MOSQUE": stnMosque,
        "STN_CAR_SERVICE": stnCarService,
        "STN_FOOD_REST": stnFoodRest,
        "STN_CAR_RENT": stnCarRent,
        "STN_ATM": stnAtm,
        "STN_PETROL91": stnPetrol91,
        "STN_PETROL95": stnPetrol95,
        "STN_DIESEL": stnDiesel,
        "STN_OWNERS": stnOwners,
      };
}
