import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/cupertino.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../utils/api_endpoints.dart';
import '../../../../view/widget/common_snak_bar_widget.dart';

class FleetWiseController extends GetxController {

  final TextEditingController datePickerController = TextEditingController();


  //Fleet Wise Fuel Usage
   TextEditingController datePickerFleetFromController =
  TextEditingController();
   TextEditingController datePickerFleetToController =
  TextEditingController();

RxString dateFrom=''.obs;
RxString dateTo=''.obs;



  final TextEditingController datePickerMonthyController =  TextEditingController();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();

  }

  reportRequestSubmit() async{
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map dataBody = {};
    dataBody = {"periodfrom":datePickerFleetFromController.text,
      "PeriodTo":datePickerFleetToController.text,
      "CUSTID":userid,
      "REPORTTYPE":"FleetWiseFuelUsage"};

    print("Json Value=================="+dataBody.toString());
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportReqSubmit),
          //  body: jsonEncode(dataBody));
          //body: dataBody,
          body: jsonEncode(dataBody),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          }
      );
      print("REPORT RESPONSE FleetWiseFuelUsage============"+response.body);
      //List result = jsonDecode(response.body);
      commonToast(response.body);
      Get.back();
      return "";
    } catch (e) {
      log(e.toString());
      commonToast(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}