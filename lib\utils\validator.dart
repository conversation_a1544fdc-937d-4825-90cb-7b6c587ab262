// ignore_for_file: avoid_print

import 'package:get/get.dart';

class Validator {
  static String? validateRequired(String value) {
    if (value.isEmpty) {
      return 'Field is required'.tr;
    } else {
      return "";
    }
  }

  static String validateEmail(String value) {
    String pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regex = RegExp(pattern);
    if (value.isEmpty) {
      return 'Email is required'.tr;
    } else if (!(regex.hasMatch(value))) {
      return "Invalid Email".tr;
    } else {
      return "";
    }
  }

  static String? validatePassword(String value) {
    String pattern =
        r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&-_])[A-Za-z\d@$!%*?&-_]{8,}$'; //capital ,small letter,digit,special character ,total length 8
    // String pattern = r'(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])'; //capital ,small letter,digit

    RegExp regex = RegExp(pattern);
    if (value.isEmpty) {
      return 'Password is required'.tr;
    }
    // else
    // if (!regex.hasMatch(value)) {
    //  return 'At least 6 characters long but 14 or more is better. (Ex:Dr@12345)';
    // } else if (value.length <= 12) {
    //   return 'At least 12 characters long but 14 or more is better.'.tr;
    //}
    else {
      return "";
    }
  }

  static String? validateConfirmPassword(String value, String password) {
    String pattern =
        r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&-_])[A-Za-z\d@$!%*?&-_]{8,}$'; //capital ,small letter,digit,special character ,total length 8
    // String pattern = r'(?=.*?[0-9])';//only digit
    // String pattern = r'(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])'; //capital ,small letter,digit
    RegExp regex = RegExp(pattern);
    if (value.isEmpty) {
      return "Please Re-Enter New Password".tr;
    } else if (value != password) {
      return "Password must be same as above".tr;
      // } else if (value.length <= 12) {
      //   return 'At least 12 characters long but 14 or more is better.'.tr;
    } else {
      return "";
    }
    //  else if (!regex.hasMatch(value)) {
    //   return 'Enter valid password'.tr;
    // }
  }

  static String? validateName(String value, String string) {
    String pattern = '[a-zA-Z]';

    RegExp regex = RegExp(pattern);
    if (value.isEmpty) {
      return '$string ${'is required'.tr}';
    } else if (!regex.hasMatch(value)) {
      return 'Enter valid $string'.tr;
    } else {
      return "";
    }
  }

  static String? validateMobile(String value) {
    String pattern = r'(^(?:[+0]9)?[0-9]{12}$)';
    RegExp regExp = RegExp(pattern);
    if (value.isEmpty) {
      return 'Mobile number is required'.tr;
    } else if (!regExp.hasMatch(value)) {
      return 'Please enter valid mobile number'.tr;
    } else {
      return "";
    }
  }

  static String? validateCompanyID(String value) {
    String pattern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
    RegExp regExp = RegExp(pattern);
    if (value.isEmpty) {
      return 'Company ID is required'.tr;
    } else if (!regExp.hasMatch(value)) {
      return 'Please enter valid company id'.tr;
    } else {
      return "";
    }
  }

  static String? validateCommercialRegisterNo(String value) {
    String pattern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
    RegExp regExp = RegExp(pattern);
    if (value.isEmpty) {
      return 'Commercial Register No is required'.tr;
    } else if (!regExp.hasMatch(value)) {
      return 'Please enter valid Commercial Register No'.tr;
    } else {
      return "";
    }
  }
}
