// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/listview_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/filter_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class ListViewScreen extends StatelessWidget {
  ListViewScreen({Key? key}) : super(key: key);
  ListViewController listViewController = Get.put(ListViewController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: 3,
              child: GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(6))),
                    backgroundColor: AppColor.cBackGround,
                    barrierColor: AppColor.cLightGrey,
                    isScrollControlled: true,
                    builder: (context) {
                      return FilterScreen();
                    },
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                      color: AppColor.themeDarkBlueColor,
                      borderRadius: BorderRadius.circular(6)),
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      assetSvdImageWidget(image: DefaultImages.filterIcn),
                      horizontalSpace(8),
                      Text(
                        "Filter".trr,
                        style: pRegular13.copyWith(
                          color: AppColor.cWhiteFont,
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
            // horizontalSpace(16),
            // Expanded(
            //   flex: 1,
            //   child: Container(
            //     decoration: BoxDecoration(
            //         color: AppColor.cBackGround,
            //         border: Border.all(color: AppColor.cBorder),
            //         borderRadius: BorderRadius.circular(6)),
            //     padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            //     child: Row(
            //       mainAxisAlignment: MainAxisAlignment.center,
            //       children: [
            //         assetSvdImageWidget(image: DefaultImages.listGridIcn),
            //         horizontalSpace(8),
            //         Container(
            //           padding: EdgeInsets.symmetric(vertical: 1, horizontal: 8),
            //           decoration: BoxDecoration(
            //               color: AppColor.cLightBlueContainer,
            //               borderRadius: BorderRadius.circular(6)),
            //           child: Text(
            //             "9",
            //             style: pMedium12.copyWith(
            //               color: AppColor.cDarkBlueFont,
            //             ),
            //           ),
            //         )
            //       ],
            //     ),
            //   ),
            // ),
          ],
        ),
        verticalSpace(24),
        ListView.builder(
          itemCount: listViewController.listViewData.length,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            var data = listViewController.listViewData[index];
            return Obx(() {
              return listViewDataWidget(
                title: data['title'],
                fuelType: data['fuelType'],
                quotaUnit: data['quotaUnit'],
                quota: data['quota'],
                consumerLiter: data['consumerLiter'],
                consumerSar: data['consumerSar'],
                variance: data['variance'],
                serviceType: data['serviceType'].toString().trr,
                driver: data['driver'],
                department: data['department'],
                branch: data['branch'],
                division: data['division'],
                isShowMore: data['isShowMore'].value,
                onTap: () {
                  data['isShowMore'].value = !data['isShowMore'].value;
                },
              );
            });
          },
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: CommonIconButton(
            onPressed: () {},
            iconData: DefaultImages.downloadReportIcn,
            title: 'Download report'.trr,
            btnColor: AppColor.themeOrangeColor,
          ),
        ),
      ],
    );
  }

  Widget listViewDataWidget({
    String? title,
    String? fuelType,
    String? quotaUnit,
    String? quota,
    String? consumerLiter,
    String? consumerSar,
    String? variance,
    String? serviceType,
    String? driver,
    String? department,
    String? branch,
    String? division,
    bool? isShowMore,
    Function()? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
        decoration: BoxDecoration(
          color: AppColor.cLightBlueContainer,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: AppColor.cLightGrey,
          ),
        ),
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title!,
              style: pBold17.copyWith(
                color: AppColor.cDarkBlueFont,
              ),
            ),
            verticalSpace(20),
            buildRowWidget("Fuel type".trr, fuelType!),
            buildRowWidget("Quota unit".trr, quotaUnit!),
            buildRowWidget("Quota".trr, quota!),
            buildRowWidget("Consumer liter".trr, consumerLiter!),
            buildRowWidget("Consumed SAR".trr, consumerSar!),
            isShowMore == true
                ? Column(children: [
                    buildRowWidget("Variance".trr, variance!),
                    buildRowWidget("Service type".trr, serviceType!),
                    buildRowWidget("Driver".trr, driver!),
                    buildRowWidget("Department".trr, department!),
                    buildRowWidget("Branch".trr, branch!),
                    buildRowWidget("Division".trr, division!),
                  ])
                : SizedBox(
                    width: 0,
                    height: 0,
                  ),
            GestureDetector(
              onTap: onTap,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    isShowMore == true ? "Details".trr : "Hide".trr,
                    style: pSemiBold12.copyWith(color: AppColor.cDarkBlueFont),
                  ),
                  horizontalSpace(4),
                  assetSvdImageWidget(image: DefaultImages.blueArrowDownIcn)
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  buildRowWidget(String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              title,
              style: pRegular10.copyWith(
                  fontSize: 13, color: AppColor.cDarkGreyFont),
            ),
          ),
          Text(
            value,
            style: pRegular10.copyWith(fontSize: 13, color: AppColor.cText),
          ),
        ],
      ),
    );
  }
}
