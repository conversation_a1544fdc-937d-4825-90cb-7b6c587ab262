// ignore_for_file: avoid_print, prefer_const_constructors

import 'dart:convert';
import 'dart:developer';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/dashboard_manager_controller/dashboard_manager_controller.dart';
import 'package:waie_app/models/Userlogin.dart';
import 'package:waie_app/models/useraccess.dart';
import 'package:waie_app/utils/prefer.dart';
import 'package:waie_app/view/screen/auth/new_otp_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/profile_screen.dart';

import '../../../main.dart';

import '../../../models/Users.dart';

import '../../../models/profile.dart';
import '../../../utils/api_endpoints.dart';
import '../../../utils/colors.dart';
import '../../../utils/constant.dart';
import '../../../utils/constants.dart';
import '../../../utils/images.dart';
import '../../../utils/locale_string.dart';
import '../../../utils/text_style.dart';
import '../../../view/screen/auth/login_with_email_screen.dart';
import '../../../view/screen/auth/otp_screen.dart';
import '../../../view/screen/dashboard_manager/dashboard_manager.dart';
import '../../../view/widget/common_button.dart';
import '../../../view/widget/common_snak_bar_widget.dart';
import '../../../view/widget/common_space_divider_widget.dart';
import '../../../view/widget/loading_widget.dart';

class AuthController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController phoneNoController = TextEditingController();
  RxBool isRemember = false.obs;
  RxBool isisLoginObscure = true.obs;

  ///individual
  TextEditingController newFNameController = TextEditingController();
  TextEditingController newLNameController = TextEditingController();
  TextEditingController newEmailController = TextEditingController();
  TextEditingController newMobileNoController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();

  ///company
  TextEditingController cpFNameController = TextEditingController();
  TextEditingController cpLNameController = TextEditingController();
  TextEditingController cpEmailController = TextEditingController();
  TextEditingController cpMobileNoController = TextEditingController();
  TextEditingController cpPasswordController = TextEditingController();
  TextEditingController cpConfirmPasswordController = TextEditingController();
  RxBool isPrivate = true.obs;
  RxBool isGovernment = false.obs;
  RxBool isObscure = true.obs;
  RxBool isPassword = true.obs;
  RxBool isConfirmPass = true.obs;
  RxBool isIndividual = true.obs;
  RxBool isCompany = false.obs;
  RxBool isSelectedPolicy = false.obs;
  List cityList = ['Surat', "Ahmedabad", 'Vadodara'];
  RxString selectedCity = ''.obs;
  List waieList = ['Aldrees Website'];
  RxString selectedWaie = ''.obs;
  RxString verificationCode = ''.obs;
  RxString phonenumber = ''.obs;
  RxString isoCode = 'SA'.obs;

  late List<Users> UserList;

  final ValueNotifier<Users?> _selectedOptionNotifier =
      ValueNotifier<Users?>(null);

  clear() {
    emailController.clear();
    newFNameController.clear();
    newLNameController.clear();
    newEmailController.clear();
    newMobileNoController.clear();
    newPasswordController.clear();
    cpFNameController.clear();
    cpLNameController.clear();
    cpEmailController.clear();
    cpMobileNoController.clear();
    cpPasswordController.clear();
    cpConfirmPasswordController.clear();
    emailController.clear();
    passwordController.clear();
    phoneNoController.clear();
    emailController.text = "";
    passwordController.text = "";
    /*emailController.text = "<EMAIL>";
    passwordController.text = "ADDD@123";*/
    /* emailController.text = "<EMAIL>";
    passwordController.text = "123456";*/
  }

  clearStorage() {
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');
    final tagOrderRefund = GetStorage();
    final topupOrderRefund = GetStorage();
    final vehicle = GetStorage();
    final getOTPDataVerify = GetStorage();
    final isPinblock = GetStorage();
    final isDCBlock = GetStorage();
    Prefs.clear();
    //Get.offAll(() => LoginManagerScreen());
    vehicle.erase();
    tagOrderRefund.erase();
    topupOrderRefund.erase();
    getOTPDataVerify.erase();
    isPinblock.erase();
    isDCBlock.erase();
    userStorage.erase();
    custsData.erase();
    usersData.erase();
  }

  RxInt selectedLanguageIndex = 0.obs;
  static const String mobileNo = "";
  RxString selectedLanguage = "Arabic".obs;
  RxString selectedLanguageImage = DefaultImages.arabicImg.obs;
  RxList languageList = [
    {
      'image': DefaultImages.englishImg,
      'title': "English",
      "local": Locale("en", "US"),
      "languageCode": "en",
      "countryCode": "US"
    }.obs, //en_US
    {
      'image': DefaultImages.arabicImg,
      'title': "Arabic".tr,
      "local": Locale("ar", "AR"),
      "languageCode": "ar",
      "countryCode": "AR"
    }.obs, //ae_AR
  ].obs;

  RxString selectedEmail = "".obs;

  updateLanguage(Locale locale) {
    print("================$locale");
    Get.updateLocale(locale);
    if (locale.languageCode == 'ar') {
      DefaultImages.backIcn = "asset/image/svg_image/next.svg";
      DefaultImages.nextIcn = 'asset/image/svg_image/back.svg';
    } else {
      DefaultImages.backIcn = 'asset/image/svg_image/back.svg';
      DefaultImages.nextIcn = 'asset/image/svg_image/next.svg';
    }
    myStorage!.setString(AppConstant.LANGUAGE_CODE, locale.languageCode);
    myStorage!
        .setString(AppConstant.COUNTRY_CODE, locale.countryCode.toString());
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    clear();
  }

  //late SharedPreferences sharedUser;

  mobileLogin() async {
    Loader.showLoader();
    clearStorage();
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    print("Mobile number===${phoneNoController.text}");
    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      String url =
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.custMobileValidate;

      var response = await post(Uri.parse(url), headers: <String, String>{
        'authorization': basicAuth,
        "Access-Control-Allow-Credentials":
            'true', // Required for cookies, authorization headers with HTTPS
        "Access-Control-Allow-Headers":
            "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      }, body: {
        'mobileno': phoneNoController.text.toString().replaceAll("+", "")
      });
      print(phoneNoController.text.toString());
      print(response.statusCode);
      print(response.body);
      // print(basicAuth);
      Loader.hideLoader();
      print('Loader loads');
      sharedUser.setString('userMobile', phoneNoController.text.toString());

      print("Phone number===========${phoneNoController.text}");
      if (response.statusCode == 200) {
        print('Suucessfully Fetch');
        print("response Relult============ ${response.body}");
        if (response.body == '"success"') {
          Get.to(() => NewOTPScreen());
          //Get.to(() => OtpScreen());
        } else {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      response.body,
                      style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () {
                        Get.offAll(() => LoginWithEmailScreen());
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
      } else {
        print('Login Fail');
        loginFailWidget('Mobile number not registred');
        print(response.statusCode.toString());
      }
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  void OTPValidate() async {
    print("verificationCode.value =============== ${verificationCode.value}");
    Loader.showLoader();
    clearStorage();
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    var userMobile = sharedUser.getString('userMobile');
    //mobileNo=phoneNoController.text;
    print("Phone number===========$userMobile");
    print("verificationCode.value===========${verificationCode.value}");
    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      String url =
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loginWithMobile;

      var response = await post(Uri.parse(url), headers: <String, String>{
        'authorization': basicAuth,
        "Access-Control-Allow-Credentials":
            'true', // Required for cookies, authorization headers with HTTPS
        "Access-Control-Allow-Headers":
            "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      }, body: {
        'mobileno': userMobile.toString().replaceAll("+", ""),
        'otp': verificationCode.value
      });
      print("OTP Response =============== ${response.body}");
      Loader.hideLoader();
      print('Loader loads');
      if (response.statusCode == 200) {
        // List<Users>UserList2 = jsonDecode(response.body);
        Iterable list = jsonDecode(response.body);
        UserList = List<Users>.from(list.map((model) => Users.fromJson(model)));
        print(list.length);
        print(UserList.length);
        print(UserList[0].emailid.toString());
        if (list.length > 1) {
          _showPopup();
        } else {
          selectedEmail.value = UserList[0].emailid.toString();
          userLogin();
        }
      } else {
        print('Login Fail');
        String msg = 'Invalid OTP';
        loginFailWidget(msg);
        commonToast('Invalid OTP');
        print(response.statusCode.toString());
      }
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  userLogin() async {
    Loader.showLoader();
    clearStorage();
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    var userMobile = sharedUser.getString('userMobile');
    print("selectedEmail.value==============${selectedEmail.value}");
    print("mobileno==============$userMobile");
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.loginMobileUsers),
          body: {
            "email": selectedEmail.value,
            "mobileno": userMobile.toString()
          });
      print(response.statusCode);
      print(response.body);
      // print(basicAuth);
      //Loader.hideLoader();
      print('Loader loads');
      if (response.statusCode == 200) {
        print('Suucessfully Fetch');
        Loader.hideLoader();
        Map cardInfo = json.decode(response.body);
        print("json.decode(response.body) ${json.decode(response.body)}");
        print('1');
        print(cardInfo);
        Profile userData = Profile.fromJson(cardInfo);
        print('2');

        if (userData.returnMessage!.isValidTransaction!) {
          if (userData.returnMessage!.action == 'POPUP') {
            Loader.hideLoader();
            //loginFailWidget(userData.returnMessage!.message);
            commonToast(userData.returnMessage!.message);
          } else {
            print(userData.auUsers?.custid);
            var custData = jsonDecode(jsonEncode(userData.auCust));
            var usrData = jsonDecode(jsonEncode(userData.auUsers));
            print(custData);
            if (custData['IS_VERIFIED'] == 'Y') {
              Constants.custIsVerified == "Y";
            }
            Constants.custAcctStatus = userData.auCust!.acctstatus!;

            print(custData['MOBILENO']);
            print(usrData['CUSTID']);
            print(usrData['EMAILID']);
            print(usrData['USERNAME']);
            print("usrData $usrData");
            print(userData.auUsers!.password);
            SharedPreferences sharedUser =
                await SharedPreferences.getInstance();
            // Map decode_options = jsonDecode(jsonString);
            String user = jsonEncode(cardInfo);
            sharedUser.setString('user', user);
            sharedUser.setString('userid', userData.auCust!.custid!);
            userStorage.writeIfNull('custid', userData.auCust!.custid!);
            userStorage.writeIfNull('emailid', userData.auCust!.emailid!);
            userStorage.writeIfNull('accttype', userData.auCust?.accttype);
            usersData.writeIfNull('usrData', usrData);
            custsData.writeIfNull('custData', custData);
            //ADDED BY FUZAIL26-2-2025
            sharedUser.setString('username', usrData['USERNAME']);
            ////////////////////////////////////

            /* Get.to(() => DashBoardManagerScreen(
                currantIndex: 0,
              ));*/
            authController.getUserAccess();
          }
        } else {
          print("Invalid ID Password");
          //loginFailWidget("Invalid ID or password");
          //commonToast("Invalid ID or password");
          if (userData.returnMessage!.message!
              .toString()
              .contains("ACCTLOCKEDMSG")) {
            Loader.hideLoader();
            commonToast("Your Account is lock, try after 5 minutes");
          } else if (userData.returnMessage!.message!
              .toString()
              .contains("INVALIDPASSWORD")) {
            Loader.hideLoader();
            commonToast("Invalid ID Password");
          } else {
            Loader.hideLoader();
            commonToast(userData.returnMessage!.message!.toString());
          }
        }
        return userData;
      } else {
        print('Login Fail');
        commonToast('Login Fail');
        print(response.statusCode.toString());
        loginFailWidget(response.statusCode.toString());
      }
    } catch (e) {
      print('Error');
      print(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void _showPopup() {
    showDialog(
      context: Get.context!,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('User Account'),
          content: SingleChildScrollView(
            child: Column(
              children: UserList.map(
                (option) => ValueListenableBuilder<Users?>(
                  valueListenable: _selectedOptionNotifier,
                  builder: (context, selectedOption, child) {
                    return RadioListTile<Users>(
                      title: Text(option.emailid.toString()),
                      value: option,
                      groupValue: selectedOption,
                      onChanged: (Users? value) {
                        _selectedOptionNotifier.value = value;
                      },
                    );
                  },
                ),
              ).toList(),
            ),
          ),
          actions: <Widget>[
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                print("Done");
                Users? selectedUser = _selectedOptionNotifier.value;
                print(
                    "_selectedOptionNotifier=========== ${selectedUser!.emailid}");
                selectedEmail.value = selectedUser.emailid.toString();
                userLogin();
              },
              child: Text('Select'),
            ),
          ],
        );
      },
    );
  }

  Future<dynamic> getUserAccess() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    //var custData = custsData.read('custData');
    print("access >>>>> $userid");
    //print("access >>>>> ${custData['USERNAME']}");
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getUserAccess),
          body: {
            "custId": userid,
            //"userid": custData['USERNAME'],
          });
      print("User Access List============${response.body}");
      if (response.statusCode == 200) {
        UserAccess access =
            UserAccess.fromJson(jsonDecode(jsonEncode(response.body)));

        print("access >>>>> $access");
        print("ETrans >>>>> ${access.ETrans}");
        print("newETrans >>>>> ${access.newETrans}");
        print("virtualAccountBTN >>>>> ${access.virtualAccountBTN}");
        print("YGGEnable >>>>> ${access.YGGEnable}");
        print("STCEnable >>>>> ${access.STCEnable}");
        print("AlrajhiMADAEnable >>>>> ${access.AlrajhiMADAEnable}");
        print("custRegType >>>>> ${access.STCEnable}");

        Constants.hasCloseAccountRequest = access.hasCloseAccountRequest;
        Constants.Usebalan = access.Usebalan;
        Constants.promoMenu = access.promoMenu;
        Constants.promoPayment = access.promoPayment;
        Constants.ETrans = access.ETrans;
        Constants.newETrans = access.newETrans;
        Constants.virtualAccountBTN = access.virtualAccountBTN;

        Constants.YGGEnable = access.YGGEnable;
        Constants.STCEnable = access.STCEnable;
        Constants.AlrajhiMADAEnable = access.AlrajhiMADAEnable;
        Constants.IsApplePayEnable = access.isApplePayEnable;
        Constants.IsAlrajhiApplePaySwitchEnable =
            access.isAlrajhiApplePaySwitchEnable;
        Constants.isDCShow = access.isDCShow;
        Constants.IsAlinmaEnable = access.IsAlinmaEnable;

        Constants.isSTNWAIE = access.isSTNWAIE;
        Constants.isSTNMOSQ = access.isSTNMOSQ;
        Constants.isSTNCARSERVICE = access.isSTNCARSERVICE;
        Constants.isSTNFOODRES = access.isSTNFOODRES;
        Constants.isSTNCARRENT = access.isSTNCARRENT;
        Constants.isSTNATM = access.isSTNATM;

        Constants.SCrbBtn = access.SCrbBtn;
        Constants.PlaceOrdrBtn = access.PlaceOrdrBtn;
        Constants.TagrbBtn = access.TagrbBtn;
        Constants.OrderHist = access.OrderHist;
        Constants.BalHist = access.BalHist;
        Constants.TopUpBtn = access.TopUpBtn;
        Constants.NewOrderBtn = access.NewOrderBtn;
        Constants.BookingBtn = access.BookingBtn;
        Constants.UnalocAmtView = access.UnalocAmtView;
        Constants.ResAmtView = access.ResAmtView;
        Constants.isActiveDC = access.isActiveDC;
        Constants.isRSRVDView = access.isRSRVDView;
        Constants.MadaPayOpt = access.MadaPayOpt;
        Constants.profileEnable = access.profileEnable;

        Constants.ReportSTNList = access.ReportSTNList;
        Constants.ReportMnthlyTrStmnt = access.ReportMnthlyTrStmnt;
        Constants.ReportFleetStmnt = access.ReportFleetStmnt;
        Constants.ReportFuelCmsp = access.ReportFuelCmsp;
        Constants.ReportMnthlyQtaSum = access.ReportMnthlyQtaSum;
        Constants.fleetStruc = access.FleetStruc;

        Constants.bankRYD = access.BankRYD;
        Constants.bankNCB = access.BankNCB;
        Constants.bankARB = access.BankARB;

        // await Future.delayed(
        //   const Duration(seconds: 5),
        //   () => Get.off(() => DashBoardManagerScreen(
        //         currantIndex: 0,
        //       )),
        // );

        // if (Constants.custAcctStatus == "A") {
        //   await Future.delayed(
        //     const Duration(seconds: 5),
        //     () => Get.off(() => DashBoardManagerScreen(
        //           currantIndex: 0,
        //         )),
        //   );

        //   Loader.hideLoader();
        // } else {
        //   await Future.delayed(
        //     const Duration(seconds: 5),
        //     () => Get.off(() => DashBoardManagerScreen(
        //           currantIndex: Constants.TopUpBtn == 'Y' ? 4 : 3,
        //         )),
        //   );

        //   Loader.hideLoader();
        //   ScaffoldMessenger.of(Get.context!).showSnackBar(
        //     SnackBar(
        //       backgroundColor: AppColor.themeOrangeColor,
        //       content: Text("Please update your profile.",
        //           style: pBold14.copyWith(color: AppColor.cWhiteFont)),
        //     ),
        //   );
        // }

        // if (Constants.custIsVerified == "Y") {
        //   Get.off(() => DashBoardManagerScreen(
        //         currantIndex: 0,
        //       ));
        // } else {
        //   Get.off(() => DashBoardManagerScreen(
        //         currantIndex: 4,
        //       ));
        // }
        passwordController.clear();
        // if (isRemember == false) {
        //   emailController.clear();
        // }
        Get.off(() => DashBoardManagerScreen(
              currantIndex: 0,
            ));

        return "";
      } else {}
    } catch (e) {
      log("User Menu List Error $e");
      Get.off(() => LoginWithEmailScreen());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<dynamic> userAccess() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    //var custData = custsData.read('custData');
    print("access >>>>> $userid");
    //print("access >>>>> ${custData['USERNAME']}");
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getUserAccess),
          body: {
            "custId": userid,
            //"userid": custData['USERNAME'],
          });
      print("User Access List============${response.body}");
      if (response.statusCode == 200) {
        UserAccess access =
            UserAccess.fromJson(jsonDecode(jsonEncode(response.body)));

        print("access >>>>> $access");
        print("ETrans >>>>> ${access.ETrans}");
        print("newETrans >>>>> ${access.newETrans}");
        print("virtualAccountBTN >>>>> ${access.virtualAccountBTN}");

        Constants.hasCloseAccountRequest = access.hasCloseAccountRequest;
        Constants.Usebalan = access.Usebalan;
        Constants.promoMenu = access.promoMenu;
        Constants.promoPayment = access.promoPayment;
        Constants.ETrans = access.ETrans;
        Constants.newETrans = access.newETrans;
        Constants.virtualAccountBTN = access.virtualAccountBTN;

        Constants.SCrbBtn = access.SCrbBtn;
        Constants.PlaceOrdrBtn = access.PlaceOrdrBtn;
        Constants.TagrbBtn = access.TagrbBtn;
        Constants.OrderHist = access.OrderHist;
        Constants.BalHist = access.BalHist;
        Constants.TopUpBtn = access.TopUpBtn;
        Constants.NewOrderBtn = access.NewOrderBtn;
        Constants.BookingBtn = access.BookingBtn;
        Constants.UnalocAmtView = access.UnalocAmtView;
        Constants.ResAmtView = access.ResAmtView;
        Constants.isActiveDC = access.isActiveDC;
        Constants.isRSRVDView = access.isRSRVDView;
        Constants.MadaPayOpt = access.MadaPayOpt;

        Constants.ReportSTNList = access.ReportSTNList;
        Constants.ReportMnthlyTrStmnt = access.ReportMnthlyTrStmnt;
        Constants.ReportFleetStmnt = access.ReportFleetStmnt;
        Constants.ReportFuelCmsp = access.ReportFuelCmsp;
        Constants.ReportMnthlyQtaSum = access.ReportMnthlyQtaSum;
        Constants.fleetStruc = access.FleetStruc;

        Constants.bankRYD = access.BankRYD;
        Constants.bankNCB = access.BankNCB;
        Constants.bankARB = access.BankARB;
        Get.offAll(() => DashBoardManagerScreen(
              currantIndex: 0,
            ));

        return "";
      } else {}
    } catch (e) {
      log("User Menu List Error $e");
      Get.off(() => LoginWithEmailScreen());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  List<Widget> _buildRadioButtons() {
    List<Widget> radioButtons = [];

    for (var item in UserList) {
      radioButtons.add(
        RadioListTile<int>(
          //title: Text(item['item1'] ?? ''),
          title: Text(item.emailid ?? ''),
          // value: item.item3 as int,
          value: 1,
          groupValue: 0,
          onChanged: (int? value) {},
        ),
      );
    }

    return radioButtons;
  }

  Widget loginFailWidget(String msg) {
    return Container(
      width: Get.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: AppColor.cLightGrey,
      ),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: FutureBuilder<String>(
        future: null,
        builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return CircularProgressIndicator(); // Display a loading indicator while fetching data
          } else if (snapshot.hasError) {
            return Text('Error: ${snapshot.error}');
          } else {
            return Text(
              msg.tr,
              style: pBold14,
            );
          }
        },
      ), /*Text.rich(
        TextSpan(
          text: "Current balance".tr + ": ",
          style: pBold14,
          children: [
            TextSpan(text: balance, style: pSemiBold14),
          ],
        ),
      ),*/
    );
  }

  resetMobileOTP() async {
    Loader.showLoader();
    print(phonenumber);
    print(phonenumber.value.toString());
    print("resetMobileOTP");
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.resetMobileOTP),
          body: {
            "phonenumber": phonenumber.value.toString().replaceAll("+", ""),
          });

      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      Map<String, dynamic> result = jsonDecode(response.body);
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      // print(
      //     "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print("result .>>>>> $result");
      print("===============================================================");

      // for (int i = 0; i < result.length; i++) {
      //   FleetStructureModel structures =
      //       FleetStructureModel.fromJson(result[i] as Map<String, dynamic>);
      //   divisionList.add(structures);
      // }
      // print("===============================================================");
      if (jsonDecode(response.body)["MessageType"] == "error") {
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    jsonDecode(response.body)["message"],
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        Loader.hideLoader();
      }

      // return divisionList;
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
