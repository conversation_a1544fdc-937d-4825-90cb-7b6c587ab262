// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/utils/api_endpoints.dart';

class CurrentBalanceController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  RxDouble currentBalance = 0.0.obs;
  RxDouble reservedBalance = 0.0.obs;

  getTopupBalances() async {
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = jsonEncode(custsData.read('custData'));
    print("OrderRefundsController custid>>>>>>> $custid");
    print("OrderRefundsController emailid>>>>>>> $emailid");
    var client = http.Client();
    try {
      var currentBalanceResponse = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getCurrentTopup),
          body: {
            "custdata": custData,
          });
      var reservedBalanceResponse = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getReservedTopup),
          body: {
            "custdata": custData,
          });
      double curBal = jsonDecode(currentBalanceResponse.body);
      double resBal = jsonDecode(reservedBalanceResponse.body);
      currentBalance.value = curBal;
      reservedBalance.value = resBal;

      print("===============================================================");
      print("currentBalanceResponse result >>>>> $currentBalance");
      print("reservedBalanceResponse result >>>>> $reservedBalance");
      // print("currentBalance result >>>>> $currentBalance");
      // print("reservedBalance result >>>>> $reservedBalance");
      print("===============================================================");

      return null;
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('CurrentBalanceController');
    await getTopupBalances();
    //Navigator.of(Get.context!).pop();
  }
}
