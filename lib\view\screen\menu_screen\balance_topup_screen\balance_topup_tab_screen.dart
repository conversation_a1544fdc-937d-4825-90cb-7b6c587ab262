// ignore_for_file: prefer_const_constructors, must_be_immutable, avoid_print, prefer_interpolation_to_compose_strings

import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/menu_controller/order_controller/balance_topup_controller.dart';
import 'package:waie_app/core/controller/menu_controller/order_controller/confirm_order_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../models/profile.dart';
import '../../../widget/common_appbar_widget.dart';
import '../../dashboard_manager/dashboard_manager.dart';
import '../order_screen/new_order_screen.dart';

class BalanceTopUpTabScreen extends StatefulWidget {
  const BalanceTopUpTabScreen({super.key});

  @override
  _BalanceTopUpTabScreen createState() => _BalanceTopUpTabScreen();
}

double roundNumber(double value, int places) {
  num val = pow(10.0, places);
  return ((value * val).round().toDouble() / val);
}

class _BalanceTopUpTabScreen extends State<BalanceTopUpTabScreen>
    with SingleTickerProviderStateMixin {
  final ConfirmOrderController confirmOrderController =
      Get.put(ConfirmOrderController());

  BalanceTopUpController balanceTopUpController =
      Get.put(BalanceTopUpController());
  //late Future<String> _CustBal;
  /// ///////////////////////////////////
  bool _showOtherOptions = false;
  bool _showHint = true;

  late AnimationController _hintController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _bounceAnimation;

  /// ///////////////////////////////////

  @override
  void initState() {
    super.initState();
    getCustBalance();

    balanceTopUpController.setUpdateUiCallback(() {
      setState(() {
        balanceTopUpController.AmountValue = "0";
        balanceTopUpController.VatExclusiveAmount = "0";
        balanceTopUpController.VatAmount = 0;
      });
    });
    print(
        "asdqwer ${balanceTopUpController.paymentOptionList[0]['value'].value}");
    print(
        "asdqwer ${balanceTopUpController.paymentOptionList[1]['value'].value}");
    print(
        "asdqwer ${balanceTopUpController.paymentOptionList[2]['value'].value}");
    print(
        "asdqwer ${balanceTopUpController.paymentOptionList[3]['value'].value}");
    print(
        "asdqwer ${balanceTopUpController.paymentOptionList[4]['value'].value}");
  }

  /// /////////////////////////////////////////////////
  /* String AmountValue = "0";
  num VatAmount = 0;
  String VatExclusiveAmount = "0";*/

  // num VatAmount = 0;
  // String VatExclusiveAmount = "0";
  Future<void> getCustBalance() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var user = prefs.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    String bal = userData.auCust?.balamt.toString() ?? '';
    print("CSUT BALANCE ============= " + bal);
    setState(() {
      //  _CustBal = Future.value(bal); // Replace 'stored_string_key' with your key
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Expanded(
        child: Stack(
          children: [
            SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              child: Obx(
                () => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    simpleAppBar(
                        title: "Top up balance".tr,
                        onTap: () {
                          Get.offAll(() => DashBoardManagerScreen(
                                currantIndex: 0,
                              ));
                        },
                        backString: "Back".tr),
                    //currentBalanceWidget(balance: "17,500 SAR"),
                    //currentBalanceWidget(balance: _CustBal.toString() ?? ""),
                    //currentBalanceWidget(balance: Constants.custBalance ?? ""),
                    currentBalanceWidget(
                        balance: Constants.custBalance == "-9999999"
                            ? "Not Available"
                            : (Constants.custBalance ?? "0.00")),
                    verticalSpace(24),
                    CommonTextField(
                      /* controller: balanceTopUpController.toptupamount,
                        keyboardType: TextInputType.numberWithOptions(
                            signed: false, decimal: false),*/
                      controller: balanceTopUpController.toptupamount,
                      keyboardType: TextInputType.numberWithOptions(
                          signed: true, decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      onChanged: (value) {
                        if (value != '') {
                          setState(() {
                            balanceTopUpController.AmountValue = value;
                            balanceTopUpController.Amount =
                                balanceTopUpController.AmountValue;
                            double vatExclusivAmt = roundNumber(
                                (int.parse(balanceTopUpController.AmountValue) /
                                    1.15),
                                2);
                            balanceTopUpController.VatExclusiveAmount =
                                vatExclusivAmt.toString();
                            balanceTopUpController.UnitPrice =
                                balanceTopUpController.VatExclusiveAmount
                                    .toString();
                            balanceTopUpController.VatAmount = roundNumber(
                                double.parse(
                                        balanceTopUpController.AmountValue) -
                                    vatExclusivAmt,
                                2);
                            balanceTopUpController.VatAmt =
                                balanceTopUpController.VatAmount.toString();
                          });
                        } else {
                          value = "0";
                          balanceTopUpController.toptupamount.text = "";
                          setState(() {
                            balanceTopUpController.AmountValue = value;
                            balanceTopUpController.Amount =
                                balanceTopUpController.AmountValue;
                            double vatExclusivAmt = roundNumber(
                                (int.parse(balanceTopUpController.AmountValue) /
                                    1.15),
                                2);
                            balanceTopUpController.VatExclusiveAmount =
                                vatExclusivAmt.toString();
                            balanceTopUpController.UnitPrice =
                                balanceTopUpController.VatExclusiveAmount
                                    .toString();
                            balanceTopUpController.VatAmount = roundNumber(
                                double.parse(
                                        balanceTopUpController.AmountValue) -
                                    vatExclusivAmt,
                                2);
                            balanceTopUpController.VatAmt =
                                balanceTopUpController.VatAmount.toString();
                          });
                        }
                      },
                      labelText: 'Topup amount'.tr + "*",
                      hintText: 'Please enter here'.tr,
                    ),
                    // verticalSpace(15),
                    // Text(
                    //   "* " + "Minimum top-up amount".tr + ": 1" + " SAR".tr,
                    //   style: pRegular13,
                    // ),
                    verticalSpace(18),
                    horizontalDivider(),
                    // verticalSpace(18),
                    /*ListView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: balanceTopUpController.paymentOptionList.length,
                          itemBuilder: (context, index) {
                            var data = balanceTopUpController.paymentOptionList[index];*/
                    /// //////////////////////////////////////////////////////////////////////////////////////////
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          alignment: Alignment.centerRight,
                          children: [
                            // Row with Title and Arrow Button
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text("Payment options".tr, style: pSemiBold17),

                                // Stack around the button and the hint
                                Stack(
                                  clipBehavior: Clip.none,
                                  alignment: Alignment.topCenter,
                                  children: [
                                    // Arrow button
                                    IconButton(
                                      onPressed: () {
                                        setState(() {
                                          _showOtherOptions =
                                              !_showOtherOptions;
                                          _showHint =
                                              false; // Hide the hint on click
                                        });
                                      },
                                      icon: Icon(
                                        _showOtherOptions
                                            ? Icons.keyboard_arrow_up_rounded
                                            : Icons.keyboard_arrow_down_rounded,
                                      ),
                                    ),

                                    // Hint bubble
                                    if (_showHint)
                                      Positioned(
                                        top: -25,
                                        left: Directionality.of(context) ==
                                                TextDirection.rtl
                                            ? 0
                                            : null,
                                        right: Directionality.of(context) ==
                                                TextDirection.ltr
                                            ? 0
                                            : null,
                                        child: Material(
                                          color: Colors.transparent,
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 12, vertical: 8),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                  color: AppColor.cBlueFont),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black12,
                                                  blurRadius: 6,
                                                  offset: Offset(2, 2),
                                                ),
                                              ],
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                // Text("👇",
                                                //     style: TextStyle(
                                                //         fontSize: 14)),

                                                Text(
                                                  "Tap to view more payment methods"
                                                      .trr,
                                                  style: TextStyle(
                                                    color: Colors.black87,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                SizedBox(width: 6),
                                                Text("👇".trr,
                                                    style: TextStyle(
                                                        fontSize: 14)),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                        // verticalSpace(16),
                        if (Platform.isIOS)
                          //applePayPaymentOptionDataWidget(
                          applePayPaymentOptionDataWidget(
                              onTap: () {
                                {
                                  balanceTopUpController
                                      .paymentOptionList[0]['value']
                                      .value = false;
                                  balanceTopUpController
                                      .paymentOptionList[1]['value']
                                      .value = false;
                                  balanceTopUpController
                                      .paymentOptionList[2]['value']
                                      .value = false;
                                  balanceTopUpController
                                      .paymentOptionList[3]['value']
                                      .value = false;
                                  if (Constants.STCEnable == "Y") {
                                    balanceTopUpController
                                        .paymentOptionList[4]['value']
                                        .value = false;
                                  }
                                  balanceTopUpController
                                      .paymentOptionList[5]['value']
                                      .value = true;
                                }
                                balanceTopUpController.paymentOptionList
                                    .refresh();
                                balanceTopUpController.paytype.value = "D";
                                balanceTopUpController.currantIndex.value = 5;
                                print(
                                    "MOBILE ${balanceTopUpController.currantIndex.value}");
                              },
                              isWidget: true,
                              isSelected: balanceTopUpController
                                  .paymentOptionList[5]['value'].value,
                              title: "Apple Pay".tr,
                              widget: ApplePayPaymentWidget()),
                        if (Platform.isAndroid && Constants.MadaPayOpt == "Y")
                          newPaymentOptionDataWidget(
                              onTap: () {
                                {
                                  balanceTopUpController
                                      .paymentOptionList[0]['value']
                                      .value = false;
                                  balanceTopUpController
                                      .paymentOptionList[1]['value']
                                      .value = false;
                                  balanceTopUpController
                                      .paymentOptionList[2]['value']
                                      .value = true;
                                  balanceTopUpController
                                      .paymentOptionList[3]['value']
                                      .value = false;
                                  if (Platform.isIOS) {
                                    balanceTopUpController
                                        .paymentOptionList[5]['value']
                                        .value = false;
                                  }
                                  if (Constants.STCEnable == "Y") {
                                    balanceTopUpController
                                        .paymentOptionList[4]['value']
                                        .value = false;
                                  }
                                }
                                balanceTopUpController.paymentOptionList
                                    .refresh();
                                balanceTopUpController.paytype.value = "D";
                                balanceTopUpController.currantIndex.value = 2;
                                print(
                                    "MOBILE ${balanceTopUpController.currantIndex.value}");
                              },
                              isWidget: true,
                              isSelected: balanceTopUpController
                                  .paymentOptionList[2]['value'].value,
                              title: "MADA".tr,
                              image: DefaultImages.madaIMG,
                              widget: MadaPaymentWidget()),
                        if (_showOtherOptions)
                          Column(
                            children: [
                              if (Platform.isIOS && Constants.MadaPayOpt == "Y")
                                newPaymentOptionDataWidget(
                                    onTap: () {
                                      {
                                        balanceTopUpController
                                            .paymentOptionList[0]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[1]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[2]['value']
                                            .value = true;
                                        balanceTopUpController
                                            .paymentOptionList[3]['value']
                                            .value = false;
                                        if (Platform.isIOS) {
                                          balanceTopUpController
                                              .paymentOptionList[5]['value']
                                              .value = false;
                                        }
                                        if (Constants.STCEnable == "Y") {
                                          balanceTopUpController
                                              .paymentOptionList[4]['value']
                                              .value = false;
                                        }
                                      }
                                      balanceTopUpController.paymentOptionList
                                          .refresh();
                                      balanceTopUpController.paytype.value =
                                          "D";
                                      balanceTopUpController
                                          .currantIndex.value = 2;
                                      print(
                                          "MOBILE ${balanceTopUpController.currantIndex.value}");
                                    },
                                    isWidget: true,
                                    isSelected: balanceTopUpController
                                        .paymentOptionList[2]['value'].value,
                                    title: "MADA".tr,
                                    image: DefaultImages.madaIMG,
                                    widget: MadaPaymentWidget()),
                              if (Constants.promoPayment == "Y")
                                partnersPaymentOptionDataWidget(
                                    onTap: () {
                                      {
                                        balanceTopUpController
                                            .paymentOptionList[0]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[1]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[2]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[3]['value']
                                            .value = true;
                                        if (Platform.isIOS) {
                                          balanceTopUpController
                                              .paymentOptionList[5]['value']
                                              .value = false;
                                        }
                                        if (Constants.STCEnable == "Y") {
                                          balanceTopUpController
                                              .paymentOptionList[4]['value']
                                              .value = false;
                                        }
                                        balanceTopUpController.paytype.value =
                                            "PRM";
                                        balanceTopUpController
                                            .currantIndex.value = 3;
                                      }
                                      balanceTopUpController.paymentOptionList
                                          .refresh();
                                      print(
                                          "MOBILE ${balanceTopUpController.currantIndex.value}");
                                    },
                                    isWidget: true,
                                    isSelected: balanceTopUpController
                                        .paymentOptionList[3]['value'].value,
                                    title: "Aldrees partners".tr,
                                    image: Constants.IsAr_App == "false"
                                        ? DefaultImages.aldreesPartnersEN
                                        : DefaultImages.aldreesPartnersAR,
                                    widget: AldreesPromotionWidget()),
                              cashPaymentOptionDataWidget(
                                  onTap: () {
                                    {
                                      balanceTopUpController
                                          .paymentOptionList[0]['value']
                                          .value = true;
                                      balanceTopUpController
                                          .paymentOptionList[1]['value']
                                          .value = false;
                                      balanceTopUpController
                                          .paymentOptionList[2]['value']
                                          .value = false;
                                      balanceTopUpController
                                          .paymentOptionList[3]['value']
                                          .value = false;
                                      if (Platform.isIOS) {
                                        balanceTopUpController
                                            .paymentOptionList[5]['value']
                                            .value = false;
                                      }
                                      if (Constants.STCEnable == "Y") {
                                        balanceTopUpController
                                            .paymentOptionList[4]['value']
                                            .value = false;
                                      }
                                    }
                                    balanceTopUpController.paytype.value = "C";
                                    balanceTopUpController.currantIndex.value =
                                        0;
                                    balanceTopUpController.paymentOptionList
                                        .refresh();
                                    print(
                                        "MOBILE ${balanceTopUpController.currantIndex.value}");
                                  },
                                  isWidget: true,
                                  isSelected: balanceTopUpController
                                      .paymentOptionList[0]['value'].value,
                                  title: "Cash".tr,
                                  image: Constants.IsAr_App == "false"
                                      ? DefaultImages.cashEN
                                      : DefaultImages.cashAR,
                                  widget: cashPaymentNoteWidget()),
                              if (Constants.STCEnable == "Y")
                                stcPayPaymentOptionDataWidget(
                                    onTap: () {
                                      {
                                        balanceTopUpController
                                            .paymentOptionList[0]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[1]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[2]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[3]['value']
                                            .value = false;
                                        if (Platform.isIOS) {
                                          balanceTopUpController
                                              .paymentOptionList[5]['value']
                                              .value = false;
                                        }
                                        balanceTopUpController
                                            .paymentOptionList[4]['value']
                                            .value = true;
                                      }
                                      balanceTopUpController.paymentOptionList
                                          .refresh();
                                      balanceTopUpController.paytype.value =
                                          "Y";
                                      balanceTopUpController
                                          .currantIndex.value = 4;
                                      print(
                                          "MOBILE ${balanceTopUpController.currantIndex.value}");
                                    },
                                    isWidget: true,
                                    isSelected: balanceTopUpController
                                        .paymentOptionList[4]['value'].value,
                                    title: "STC Pay".tr,
                                    image: DefaultImages.stcBank,
                                    widget: stcPayPaymentWidget()),
                              if (Constants.ETrans == "Y")
                                eTransferPaymentOptionDataWidget(
                                    onTap: () {
                                      {
                                        balanceTopUpController
                                            .paymentOptionList[0]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[1]['value']
                                            .value = true;
                                        balanceTopUpController
                                            .paymentOptionList[2]['value']
                                            .value = false;
                                        balanceTopUpController
                                            .paymentOptionList[3]['value']
                                            .value = false;
                                        if (Platform.isIOS) {
                                          balanceTopUpController
                                              .paymentOptionList[5]['value']
                                              .value = false;
                                        }
                                        if (Constants.STCEnable == "Y") {
                                          balanceTopUpController
                                              .paymentOptionList[4]['value']
                                              .value = false;
                                        }
                                      }
                                      balanceTopUpController.paytype.value =
                                          "E";
                                      balanceTopUpController
                                          .currantIndex.value = 1;
                                      balanceTopUpController.paymentOptionList
                                          .refresh();
                                      print(
                                          "MOBILE ${balanceTopUpController.currantIndex.value}");
                                    },
                                    isWidget: true,
                                    isSelected: balanceTopUpController
                                        .paymentOptionList[1]['value'].value,
                                    title: "E. Transfer".tr,
                                    image: Constants.IsAr_App == "false"
                                        ? DefaultImages.eTransferEN
                                        : DefaultImages.eTransferAR,
                                    widget: eTransferWidget()),
                              if (Constants.newETrans == "Y")
                                newETransferPaymentOptionDataWidget(
                                    title: "E. Transfer".tr,
                                    image: Constants.IsAr_App == "false"
                                        ? DefaultImages.eTransferEN
                                        : DefaultImages.eTransferAR,
                                    widget: newETransferWidget()),
                            ],
                          )
                      ],
                    ),

                    /// //////////////////////////////////////////////////////////////////////////////////////////
                    // ExpansionTile(
                    //   collapsedShape: LinearBorder.none,
                    //   shape: LinearBorder.none,
                    //   tilePadding: EdgeInsets.zero,
                    //   title: Text(
                    //     "Payment options".tr,
                    //     style: pSemiBold17,
                    //   ),
                    //   children: [
                    //     Column(
                    //       crossAxisAlignment: CrossAxisAlignment.start,
                    //       children: [
                    //         // iOS - Default to Apple Pay (show only Apple Pay for iOS)
                    //         if (Platform.isIOS)
                    //           applePayPaymentOptionDataWidget(
                    //             onTap: () {
                    //               balanceTopUpController
                    //                   .paymentOptionList[0]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[1]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[2]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[3]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[4]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[5]['value'].value = true;
                    //
                    //               balanceTopUpController.paymentOptionList
                    //                   .refresh();
                    //               balanceTopUpController.paytype.value = "D";
                    //               balanceTopUpController.currantIndex.value = 5;
                    //             },
                    //             isWidget: true,
                    //             isSelected: balanceTopUpController
                    //                 .paymentOptionList[5]['value'].value,
                    //             title: "Apple Pay".tr,
                    //             widget: ApplePayPaymentWidget(),
                    //           ),
                    //         // Android - Default to MADA (show only MADA for Android)
                    //         if (Platform.isAndroid)
                    //           newPaymentOptionDataWidget(
                    //             onTap: () {
                    //               balanceTopUpController
                    //                   .paymentOptionList[0]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[1]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[2]['value'].value = true;
                    //               balanceTopUpController
                    //                   .paymentOptionList[3]['value'].value = false;
                    //
                    //               if (Platform.isIOS) {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[5]['value']
                    //                     .value = false;
                    //               }
                    //               if (Constants.STCEnable == "Y") {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[4]['value']
                    //                     .value = false;
                    //               }
                    //
                    //               balanceTopUpController.paymentOptionList
                    //                   .refresh();
                    //               balanceTopUpController.paytype.value = "D";
                    //               balanceTopUpController.currantIndex.value = 2;
                    //             },
                    //             isWidget: true,
                    //             isSelected: balanceTopUpController
                    //                 .paymentOptionList[2]['value'].value,
                    //             title: "MADA".tr,
                    //             image: DefaultImages.madaIMG,
                    //             widget: MadaPaymentWidget(),
                    //           ),
                    //         // Hide other options by default
                    //         if (Constants.promoPayment == "Y")
                    //           partnersPaymentOptionDataWidget(
                    //             onTap: () {
                    //               balanceTopUpController
                    //                   .paymentOptionList[0]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[1]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[2]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[3]['value'].value = true;
                    //               if (Platform.isIOS) {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[5]['value']
                    //                     .value = false;
                    //               }
                    //               if (Constants.STCEnable == "Y") {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[4]['value']
                    //                     .value = false;
                    //               }
                    //               balanceTopUpController.paytype.value = "PRM";
                    //               balanceTopUpController.currantIndex.value = 3;
                    //             },
                    //             isWidget: true,
                    //             isSelected: balanceTopUpController
                    //                 .paymentOptionList[3]['value'].value,
                    //             title: "Aldrees partners".tr,
                    //             image: Constants.IsAr_App == "false"
                    //                 ? DefaultImages.aldreesPartnersEN
                    //                 : DefaultImages.aldreesPartnersAR,
                    //             widget: AldreesPromotionWidget(),
                    //           ),
                    //         // Cash payment option
                    //         cashPaymentOptionDataWidget(
                    //           onTap: () {
                    //             balanceTopUpController
                    //                 .paymentOptionList[0]['value'].value = true;
                    //             balanceTopUpController
                    //                 .paymentOptionList[1]['value'].value = false;
                    //             balanceTopUpController
                    //                 .paymentOptionList[2]['value'].value = false;
                    //             balanceTopUpController
                    //                 .paymentOptionList[3]['value'].value = false;
                    //             if (Platform.isIOS) {
                    //               balanceTopUpController
                    //                   .paymentOptionList[5]['value'].value = false;
                    //             }
                    //             if (Constants.STCEnable == "Y") {
                    //               balanceTopUpController
                    //                   .paymentOptionList[4]['value'].value = false;
                    //             }
                    //             balanceTopUpController.paytype.value = "C";
                    //             balanceTopUpController.currantIndex.value = 0;
                    //             balanceTopUpController.paymentOptionList.refresh();
                    //           },
                    //           isWidget: true,
                    //           isSelected: balanceTopUpController
                    //               .paymentOptionList[0]['value'].value,
                    //           title: "Cash".tr,
                    //           image: Constants.IsAr_App == "false"
                    //               ? DefaultImages.cashEN
                    //               : DefaultImages.cashAR,
                    //           widget: cashPaymentNoteWidget(),
                    //         ),
                    //         // STC Pay option (if enabled)
                    //         if (Constants.STCEnable == "Y")
                    //           stcPayPaymentOptionDataWidget(
                    //             onTap: () {
                    //               balanceTopUpController
                    //                   .paymentOptionList[0]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[1]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[2]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[3]['value'].value = false;
                    //               if (Platform.isIOS) {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[5]['value']
                    //                     .value = false;
                    //               }
                    //               balanceTopUpController
                    //                   .paymentOptionList[4]['value'].value = true;
                    //               balanceTopUpController.paymentOptionList
                    //                   .refresh();
                    //               balanceTopUpController.paytype.value = "Y";
                    //               balanceTopUpController.currantIndex.value = 4;
                    //             },
                    //             isWidget: true,
                    //             isSelected: balanceTopUpController
                    //                 .paymentOptionList[4]['value'].value,
                    //             title: "STC Pay".tr,
                    //             image: DefaultImages.stcPay,
                    //             widget: stcPayPaymentWidget(),
                    //           ),
                    //         // E. Transfer option
                    //         if (Constants.ETrans == "Y")
                    //           eTransferPaymentOptionDataWidget(
                    //             onTap: () {
                    //               balanceTopUpController
                    //                   .paymentOptionList[0]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[1]['value'].value = true;
                    //               balanceTopUpController
                    //                   .paymentOptionList[2]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[3]['value'].value = false;
                    //               if (Platform.isIOS) {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[5]['value']
                    //                     .value = false;
                    //               }
                    //               if (Constants.STCEnable == "Y") {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[4]['value']
                    //                     .value = false;
                    //               }
                    //               balanceTopUpController.paytype.value = "E";
                    //               balanceTopUpController.currantIndex.value = 1;
                    //               balanceTopUpController.paymentOptionList
                    //                   .refresh();
                    //             },
                    //             isWidget: true,
                    //             isSelected: balanceTopUpController
                    //                 .paymentOptionList[1]['value'].value,
                    //             title: "E. Transfer".tr,
                    //             image: Constants.IsAr_App == "false"
                    //                 ? DefaultImages.eTransferEN
                    //                 : DefaultImages.eTransferAR,
                    //             widget: eTransferWidget(),
                    //           ),
                    //         // New ETransfer option
                    //         if (Constants.newETrans == "Y")
                    //           newETransferPaymentOptionDataWidget(
                    //             title: "E. Transfer".tr,
                    //             image: Constants.IsAr_App == "false"
                    //                 ? DefaultImages.eTransferEN
                    //                 : DefaultImages.eTransferAR,
                    //             widget: newETransferWidget(),
                    //           ),
                    //       ],
                    //     ),
                    //   ],
                    // ),
                    /// /////////////////////////////////////////////////////////////////////////////////////////
                    // Column(
                    //   crossAxisAlignment: CrossAxisAlignment.start,
                    //   children: [
                    //     Row(
                    //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //       children: [
                    //         Text(
                    //           "Payment options".tr,
                    //           style: pSemiBold17,
                    //         ),
                    //         IconButton(
                    //           onPressed: () {
                    //             setState(() {
                    //               _showOtherOptions = !_showOtherOptions;
                    //             });
                    //           },
                    //           icon: Icon(
                    //             _showOtherOptions
                    //                 ? Icons
                    //                     .keyboard_arrow_up_rounded // Arrow pointing up when expanded
                    //                 : Icons
                    //                     .keyboard_arrow_down_rounded, // Arrow pointing down when collapsed
                    //           ),
                    //         ),
                    //         // SizedBox(
                    //         //   width: 150,
                    //         //   child: ElevatedButton(
                    //         //     style: ElevatedButton.styleFrom(
                    //         //       backgroundColor: AppColor.cBlueFont,
                    //         //       shape: RoundedRectangleBorder(
                    //         //         borderRadius: BorderRadius.circular(8),
                    //         //       ),
                    //         //       foregroundColor: Colors.white,
                    //         //       shadowColor: Colors.transparent,
                    //         //       padding: EdgeInsets.symmetric(horizontal: 4),
                    //         //     ),
                    //         //     onPressed: () {
                    //         //       setState(() {
                    //         //         _showOtherOptions = !_showOtherOptions;
                    //         //       });
                    //         //     },
                    //         //     child: Text(
                    //         //       'Change Payment Option'.tr,
                    //         //       style: TextStyle(
                    //         //         fontSize: 12,
                    //         //         fontWeight: FontWeight.w700,
                    //         //         color: Colors.white,
                    //         //       ),
                    //         //     ),
                    //         //   ),
                    //         // ),
                    //       ],
                    //     ),
                    //     // Default payment option based on platform
                    //     if (Platform.isIOS)
                    //       applePayPaymentOptionDataWidget(
                    //         onTap: () {
                    //           balanceTopUpController
                    //               .paymentOptionList[0]['value'].value = false;
                    //           balanceTopUpController
                    //               .paymentOptionList[1]['value'].value = false;
                    //           balanceTopUpController
                    //               .paymentOptionList[2]['value'].value = false;
                    //           balanceTopUpController
                    //               .paymentOptionList[3]['value'].value = false;
                    //           balanceTopUpController
                    //               .paymentOptionList[4]['value'].value = false;
                    //           balanceTopUpController
                    //               .paymentOptionList[5]['value'].value = true;
                    //           balanceTopUpController.paymentOptionList.refresh();
                    //           balanceTopUpController.paytype.value = "D";
                    //           balanceTopUpController.currantIndex.value = 5;
                    //         },
                    //         isWidget: true,
                    //         isSelected: balanceTopUpController
                    //             .paymentOptionList[5]['value'].value,
                    //         title: "Apple Pay".tr,
                    //         widget: ApplePayPaymentWidget(),
                    //       ),
                    //     if (Platform.isAndroid && Constants.MadaPayOpt == "Y")
                    //       newPaymentOptionDataWidget(
                    //         onTap: () {
                    //           balanceTopUpController
                    //               .paymentOptionList[0]['value'].value = false;
                    //           balanceTopUpController
                    //               .paymentOptionList[1]['value'].value = false;
                    //           balanceTopUpController
                    //               .paymentOptionList[2]['value'].value = true;
                    //           balanceTopUpController
                    //               .paymentOptionList[3]['value'].value = false;
                    //           if (Constants.STCEnable == "Y") {
                    //             balanceTopUpController
                    //                 .paymentOptionList[4]['value'].value = false;
                    //           }
                    //           balanceTopUpController.paymentOptionList.refresh();
                    //           balanceTopUpController.paytype.value = "D";
                    //           balanceTopUpController.currantIndex.value = 2;
                    //         },
                    //         isWidget: true,
                    //         isSelected: balanceTopUpController
                    //             .paymentOptionList[2]['value'].value,
                    //         title: "MADA".tr,
                    //         image: DefaultImages.madaIMG,
                    //         widget: MadaPaymentWidget(),
                    //       ),
                    //     // Change Payment Option button
                    //     // InkWell(
                    //     //   onTap: () {
                    //     //     setState(() {
                    //     //       _showOtherOptions = !_showOtherOptions;
                    //     //     });
                    //     //   },
                    //     //   child: Padding(
                    //     //     padding: const EdgeInsets.symmetric(vertical: 8.0),
                    //     //     child: Text(
                    //     //       "Change Payment Option".tr,
                    //     //       style: TextStyle(
                    //     //         color: Colors.blue,
                    //     //         decoration: TextDecoration.underline,
                    //     //       ),
                    //     //     ),
                    //     //   ),
                    //     // ),
                    //     // Other payment options
                    //     Visibility(
                    //       visible: _showOtherOptions,
                    //       child: Column(
                    //         crossAxisAlignment: CrossAxisAlignment.start,
                    //         children: [
                    //           if (Platform.isIOS) ...[
                    //             if (Constants.MadaPayOpt == "Y")
                    //               newPaymentOptionDataWidget(
                    //                 onTap: () {
                    //                   balanceTopUpController
                    //                       .paymentOptionList[0]['value']
                    //                       .value = false;
                    //                   balanceTopUpController
                    //                       .paymentOptionList[1]['value']
                    //                       .value = false;
                    //                   balanceTopUpController
                    //                       .paymentOptionList[2]['value']
                    //                       .value = true;
                    //                   balanceTopUpController
                    //                       .paymentOptionList[3]['value']
                    //                       .value = false;
                    //                   balanceTopUpController
                    //                       .paymentOptionList[5]['value']
                    //                       .value = false;
                    //                   if (Constants.STCEnable == "Y") {
                    //                     balanceTopUpController
                    //                         .paymentOptionList[4]['value']
                    //                         .value = false;
                    //                   }
                    //                   balanceTopUpController.paymentOptionList
                    //                       .refresh();
                    //                   balanceTopUpController.paytype.value = "D";
                    //                   balanceTopUpController.currantIndex.value = 2;
                    //                 },
                    //                 isWidget: true,
                    //                 isSelected: balanceTopUpController
                    //                     .paymentOptionList[2]['value'].value,
                    //                 title: "MADA".tr,
                    //                 image: DefaultImages.madaIMG,
                    //                 widget: MadaPaymentWidget(),
                    //               ),
                    //           ],
                    //           if (Platform.isAndroid) ...[
                    //             if (Constants.promoPayment == "Y")
                    //               partnersPaymentOptionDataWidget(
                    //                 onTap: () {
                    //                   balanceTopUpController
                    //                       .paymentOptionList[0]['value']
                    //                       .value = false;
                    //                   balanceTopUpController
                    //                       .paymentOptionList[1]['value']
                    //                       .value = false;
                    //                   balanceTopUpController
                    //                       .paymentOptionList[2]['value']
                    //                       .value = false;
                    //                   balanceTopUpController
                    //                       .paymentOptionList[3]['value']
                    //                       .value = true;
                    //                   if (Constants.STCEnable == "Y") {
                    //                     balanceTopUpController
                    //                         .paymentOptionList[4]['value']
                    //                         .value = false;
                    //                   }
                    //                   balanceTopUpController.paytype.value = "PRM";
                    //                   balanceTopUpController.currantIndex.value = 3;
                    //                   balanceTopUpController.paymentOptionList
                    //                       .refresh();
                    //                 },
                    //                 isWidget: true,
                    //                 isSelected: balanceTopUpController
                    //                     .paymentOptionList[3]['value'].value,
                    //                 title: "Aldrees partners".tr,
                    //                 image: Constants.IsAr_App == "false"
                    //                     ? DefaultImages.aldreesPartnersEN
                    //                     : DefaultImages.aldreesPartnersAR,
                    //                 widget: AldreesPromotionWidget(),
                    //               ),
                    //           ],
                    //           cashPaymentOptionDataWidget(
                    //             onTap: () {
                    //               balanceTopUpController
                    //                   .paymentOptionList[0]['value'].value = true;
                    //               balanceTopUpController
                    //                   .paymentOptionList[1]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[2]['value'].value = false;
                    //               balanceTopUpController
                    //                   .paymentOptionList[3]['value'].value = false;
                    //               if (Platform.isIOS) {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[5]['value']
                    //                     .value = false;
                    //               }
                    //               if (Constants.STCEnable == "Y") {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[4]['value']
                    //                     .value = false;
                    //               }
                    //               balanceTopUpController.paytype.value = "C";
                    //               balanceTopUpController.currantIndex.value = 0;
                    //               balanceTopUpController.paymentOptionList
                    //                   .refresh();
                    //             },
                    //             isWidget: true,
                    //             isSelected: balanceTopUpController
                    //                 .paymentOptionList[0]['value'].value,
                    //             title: "Cash".tr,
                    //             image: Constants.IsAr_App == "false"
                    //                 ? DefaultImages.cashEN
                    //                 : DefaultImages.cashAR,
                    //             widget: cashPaymentNoteWidget(),
                    //           ),
                    //           if (Constants.STCEnable == "Y")
                    //             stcPayPaymentOptionDataWidget(
                    //               onTap: () {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[0]['value']
                    //                     .value = false;
                    //                 balanceTopUpController
                    //                     .paymentOptionList[1]['value']
                    //                     .value = false;
                    //                 balanceTopUpController
                    //                     .paymentOptionList[2]['value']
                    //                     .value = false;
                    //                 balanceTopUpController
                    //                     .paymentOptionList[3]['value']
                    //                     .value = false;
                    //                 if (Platform.isIOS) {
                    //                   balanceTopUpController
                    //                       .paymentOptionList[5]['value']
                    //                       .value = false;
                    //                 }
                    //                 balanceTopUpController
                    //                     .paymentOptionList[4]['value'].value = true;
                    //                 balanceTopUpController.paymentOptionList
                    //                     .refresh();
                    //                 balanceTopUpController.paytype.value = "Y";
                    //                 balanceTopUpController.currantIndex.value = 4;
                    //               },
                    //               isWidget: true,
                    //               isSelected: balanceTopUpController
                    //                   .paymentOptionList[4]['value'].value,
                    //               title: "STC Pay".tr,
                    //               image: DefaultImages.stcPay,
                    //               widget: stcPayPaymentWidget(),
                    //             ),
                    //           if (Constants.ETrans == "Y")
                    //             eTransferPaymentOptionDataWidget(
                    //               onTap: () {
                    //                 balanceTopUpController
                    //                     .paymentOptionList[0]['value']
                    //                     .value = false;
                    //                 balanceTopUpController
                    //                     .paymentOptionList[1]['value'].value = true;
                    //                 balanceTopUpController
                    //                     .paymentOptionList[2]['value']
                    //                     .value = false;
                    //                 balanceTopUpController
                    //                     .paymentOptionList[3]['value']
                    //                     .value = false;
                    //                 if (Platform.isIOS) {
                    //                   balanceTopUpController
                    //                       .paymentOptionList[5]['value']
                    //                       .value = false;
                    //                 }
                    //                 if (Constants.STCEnable == "Y") {
                    //                   balanceTopUpController
                    //                       .paymentOptionList[4]['value']
                    //                       .value = false;
                    //                 }
                    //                 balanceTopUpController.paytype.value = "E";
                    //                 balanceTopUpController.currantIndex.value = 1;
                    //                 balanceTopUpController.paymentOptionList
                    //                     .refresh();
                    //               },
                    //               isWidget: true,
                    //               isSelected: balanceTopUpController
                    //                   .paymentOptionList[1]['value'].value,
                    //               title: "E. Transfer".tr,
                    //               image: Constants.IsAr_App == "false"
                    //                   ? DefaultImages.eTransferEN
                    //                   : DefaultImages.eTransferAR,
                    //               widget: eTransferWidget(),
                    //             ),
                    //           if (Constants.newETrans == "Y")
                    //             newETransferPaymentOptionDataWidget(
                    //               title: "E. Transfer".tr,
                    //               image: Constants.IsAr_App == "false"
                    //                   ? DefaultImages.eTransferEN
                    //                   : DefaultImages.eTransferAR,
                    //               widget: newETransferWidget(),
                    //             ),
                    //         ],
                    //       ),
                    //     ),
                    //   ],
                    // ),
                    /// /////////////////////////////////////////////////////////////////////////////////////////////////////
                    // Column(
                    //   crossAxisAlignment: CrossAxisAlignment.start,
                    //   children: [
                    //     Row(
                    //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //       children: [
                    //         Text(
                    //           "Payment options".tr,
                    //           style: pSemiBold17,
                    //         ),
                    //         SizedBox(
                    //           width: 150,
                    //           child: ElevatedButton(
                    //             style: ElevatedButton.styleFrom(
                    //               backgroundColor: AppColor.cBlueFont,
                    //               shape: RoundedRectangleBorder(
                    //                 borderRadius: BorderRadius.circular(8),
                    //               ),
                    //               foregroundColor: Colors.white,
                    //               shadowColor: Colors.transparent,
                    //               padding: EdgeInsets.symmetric(horizontal: 4),
                    //             ),
                    //             onPressed: () {
                    //               setState(() {
                    //                 _showOtherOptions = !_showOtherOptions;
                    //               });
                    //             },
                    //             child: Text(
                    //               'Change Payment Option'.tr,
                    //               style: TextStyle(
                    //                 fontSize: 12,
                    //                 fontWeight: FontWeight.w700,
                    //                 color: Colors.white,
                    //               ),
                    //             ),
                    //           ),
                    //         ),
                    //       ],
                    //     ),
                    //
                    //     // Default payment option (only if other options not shown)
                    //     if (!_showOtherOptions && Platform.isIOS)
                    //       applePayPaymentOptionDataWidget(
                    //         onTap: () {
                    //           balanceTopUpController.paymentOptionList
                    //               .forEach((opt) {
                    //             opt['value'].value = false;
                    //           });
                    //           balanceTopUpController
                    //               .paymentOptionList[5]['value'].value = true;
                    //           balanceTopUpController.paymentOptionList.refresh();
                    //           balanceTopUpController.paytype.value = "D";
                    //           balanceTopUpController.currantIndex.value = 5;
                    //         },
                    //         isWidget: true,
                    //         isSelected: balanceTopUpController
                    //             .paymentOptionList[5]['value'].value,
                    //         title: "Apple Pay".tr,
                    //         widget: ApplePayPaymentWidget(),
                    //       ),
                    //
                    //     if (!_showOtherOptions &&
                    //         Platform.isAndroid &&
                    //         Constants.MadaPayOpt == "Y")
                    //       newPaymentOptionDataWidget(
                    //         onTap: () {
                    //           balanceTopUpController.paymentOptionList
                    //               .forEach((opt) {
                    //             opt['value'].value = false;
                    //           });
                    //           balanceTopUpController
                    //               .paymentOptionList[2]['value'].value = true;
                    //           balanceTopUpController.paymentOptionList.refresh();
                    //           balanceTopUpController.paytype.value = "D";
                    //           balanceTopUpController.currantIndex.value = 2;
                    //         },
                    //         isWidget: true,
                    //         isSelected: balanceTopUpController
                    //             .paymentOptionList[2]['value'].value,
                    //         title: "MADA".tr,
                    //         image: DefaultImages.madaIMG,
                    //         widget: MadaPaymentWidget(),
                    //       ),
                    //
                    //     // Other payment options shown when toggle is ON
                    //     Visibility(
                    //       visible: _showOtherOptions,
                    //       child: Column(
                    //         crossAxisAlignment: CrossAxisAlignment.start,
                    //         children: [
                    //           if (Platform.isIOS && Constants.MadaPayOpt == "Y")
                    //             newPaymentOptionDataWidget(
                    //               onTap: () {
                    //                 balanceTopUpController.paymentOptionList
                    //                     .forEach((opt) {
                    //                   opt['value'].value = false;
                    //                 });
                    //                 balanceTopUpController
                    //                     .paymentOptionList[2]['value'].value = true;
                    //                 balanceTopUpController.paymentOptionList
                    //                     .refresh();
                    //                 balanceTopUpController.paytype.value = "D";
                    //                 balanceTopUpController.currantIndex.value = 2;
                    //               },
                    //               isWidget: true,
                    //               isSelected: balanceTopUpController
                    //                   .paymentOptionList[2]['value'].value,
                    //               title: "MADA".tr,
                    //               image: DefaultImages.madaIMG,
                    //               widget: MadaPaymentWidget(),
                    //             ),
                    //           if (Platform.isAndroid &&
                    //               Constants.promoPayment == "Y")
                    //             partnersPaymentOptionDataWidget(
                    //               onTap: () {
                    //                 balanceTopUpController.paymentOptionList
                    //                     .forEach((opt) {
                    //                   opt['value'].value = false;
                    //                 });
                    //                 balanceTopUpController
                    //                     .paymentOptionList[3]['value'].value = true;
                    //                 balanceTopUpController.paytype.value = "PRM";
                    //                 balanceTopUpController.currantIndex.value = 3;
                    //                 balanceTopUpController.paymentOptionList
                    //                     .refresh();
                    //               },
                    //               isWidget: true,
                    //               isSelected: balanceTopUpController
                    //                   .paymentOptionList[3]['value'].value,
                    //               title: "Aldrees partners".tr,
                    //               image: Constants.IsAr_App == "false"
                    //                   ? DefaultImages.aldreesPartnersEN
                    //                   : DefaultImages.aldreesPartnersAR,
                    //               widget: AldreesPromotionWidget(),
                    //             ),
                    //           cashPaymentOptionDataWidget(
                    //             onTap: () {
                    //               balanceTopUpController.paymentOptionList
                    //                   .forEach((opt) {
                    //                 opt['value'].value = false;
                    //               });
                    //               balanceTopUpController
                    //                   .paymentOptionList[0]['value'].value = true;
                    //               balanceTopUpController.paytype.value = "C";
                    //               balanceTopUpController.currantIndex.value = 0;
                    //               balanceTopUpController.paymentOptionList
                    //                   .refresh();
                    //             },
                    //             isWidget: true,
                    //             isSelected: balanceTopUpController
                    //                 .paymentOptionList[0]['value'].value,
                    //             title: "Cash".tr,
                    //             image: Constants.IsAr_App == "false"
                    //                 ? DefaultImages.cashEN
                    //                 : DefaultImages.cashAR,
                    //             widget: cashPaymentNoteWidget(),
                    //           ),
                    //           if (Constants.STCEnable == "Y")
                    //             stcPayPaymentOptionDataWidget(
                    //               onTap: () {
                    //                 balanceTopUpController.paymentOptionList
                    //                     .forEach((opt) {
                    //                   opt['value'].value = false;
                    //                 });
                    //                 balanceTopUpController
                    //                     .paymentOptionList[4]['value'].value = true;
                    //                 balanceTopUpController.paytype.value = "Y";
                    //                 balanceTopUpController.currantIndex.value = 4;
                    //                 balanceTopUpController.paymentOptionList
                    //                     .refresh();
                    //               },
                    //               isWidget: true,
                    //               isSelected: balanceTopUpController
                    //                   .paymentOptionList[4]['value'].value,
                    //               title: "STC Pay".tr,
                    //               image: DefaultImages.stcPay,
                    //               widget: stcPayPaymentWidget(),
                    //             ),
                    //           if (Constants.ETrans == "Y")
                    //             eTransferPaymentOptionDataWidget(
                    //               onTap: () {
                    //                 balanceTopUpController.paymentOptionList
                    //                     .forEach((opt) {
                    //                   opt['value'].value = false;
                    //                 });
                    //                 balanceTopUpController
                    //                     .paymentOptionList[1]['value'].value = true;
                    //                 balanceTopUpController.paytype.value = "E";
                    //                 balanceTopUpController.currantIndex.value = 1;
                    //                 balanceTopUpController.paymentOptionList
                    //                     .refresh();
                    //               },
                    //               isWidget: true,
                    //               isSelected: balanceTopUpController
                    //                   .paymentOptionList[1]['value'].value,
                    //               title: "E. Transfer".tr,
                    //               image: Constants.IsAr_App == "false"
                    //                   ? DefaultImages.eTransferEN
                    //                   : DefaultImages.eTransferAR,
                    //               widget: eTransferWidget(),
                    //             ),
                    //           if (Constants.newETrans == "Y")
                    //             newETransferPaymentOptionDataWidget(
                    //               title: "E. Transfer".tr,
                    //               image: Constants.IsAr_App == "false"
                    //                   ? DefaultImages.eTransferEN
                    //                   : DefaultImages.eTransferAR,
                    //               widget: newETransferWidget(),
                    //             ),
                    //         ],
                    //       ),
                    //     ),
                    //   ],
                    // ),
                    // SizedBox(
                    //   width: 150,
                    //   child: ElevatedButton(
                    //     style: ElevatedButton.styleFrom(
                    //       backgroundColor: AppColor.cBlueFont,
                    //       shape: RoundedRectangleBorder(
                    //         borderRadius: BorderRadius.circular(8),
                    //       ),
                    //       foregroundColor: Colors.white,
                    //       shadowColor: Colors.transparent,
                    //       padding: EdgeInsets.symmetric(horizontal: 4),
                    //     ),
                    //     onPressed: () {
                    //       setState(() {
                    //         _showOtherOptions = !_showOtherOptions;
                    //       });
                    //     },
                    //     child: Text(
                    //       'Change Payment Option'.tr,
                    //       style: TextStyle(
                    //         fontSize: 12,
                    //         fontWeight: FontWeight.w700,
                    //         color: Colors.white,
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    /// //////////////////////////////////////////////////////////////////////////////////////////////////

                    /// //////////////////////////////////////////////////////////////////////////////////////////
                    verticalSpace(8),
                    Container(
                      width: Get.width,
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColor.cLightGrey,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Totals".tr,
                            style: pBold24.copyWith(fontSize: 22),
                          ),
                          verticalSpace(16),
                          //totalsDataWidget(title: "Topup amount".tr, value: "1100 SAR"),
                          totalsDataWidget(
                              title: "Topup amount".tr,
                              //value: double.parse(balanceTopUpController.VatExclusiveAmount).toStringAsFixed(2).toString() +" SAR".tr),
                              value: balanceTopUpController.VatExclusiveAmount
                                  .toString()), // + " SAR".tr
                          verticalSpace(16),
                          //totalsDataWidget(title: "VAT".tr, value: "13.04 SAR"),
                          totalsDataWidget(
                              title: "VAT".tr,
                              value: double.parse(balanceTopUpController
                                      .VatAmount.toString())
                                  .toStringAsFixed(2)
                                  .toString()), // + " SAR".tr
                          verticalSpace(16),
                          horizontalDivider(),
                          verticalSpace(16),
                          //totalsDataWidget(title: "Total".tr, value: "1113.04 SAR", fontSize: 17, fontColor: AppColor.cDarkFont),
                          totalsDataWidget(
                              title: "Total".tr,
                              value: double.parse(
                                      balanceTopUpController.AmountValue)
                                  .toStringAsFixed(2)
                                  .toString(),
                              fontSize: 17,
                              fontColor: AppColor.cDarkFont),
                          verticalSpace(16),
                          // CommonButton(
                          //   title: "PLACE ORDER".tr,
                          //   btnColor: AppColor.themeOrangeColor,
                          //   onPressed: () {
                          //     print("balanceTopUpController.Amount=="
                          //                 "===========" +
                          //             balanceTopUpController.Amount ==
                          //         "");
                          //     if (balanceTopUpController.Amount == "") {
                          //       balanceTopUpController.Amount == "0";
                          //     }
                          //     print("Mobile number===================" +
                          //         balanceTopUpController.phoneNumber);
                          //     if (balanceTopUpController.Amount == "0" ||
                          //         double.parse(balanceTopUpController.Amount) <
                          //             1 ||
                          //         balanceTopUpController.toptupamount.text ==
                          //             "0") {
                          //       print(balanceTopUpController.AmountValue
                          //           .toString());
                          //       showDialog(
                          //         barrierDismissible: false,
                          //         context: Get.context!,
                          //         builder: (context) {
                          //           return AlertDialog(
                          //             insetPadding: const EdgeInsets.all(16),
                          //             contentPadding: const EdgeInsets.all(24),
                          //             shape: RoundedRectangleBorder(
                          //                 borderRadius:
                          //                     BorderRadius.circular(12)),
                          //             content: Column(
                          //               mainAxisSize: MainAxisSize.min,
                          //               children: [
                          //                 Text(
                          //                   "Minimum TopUp amount".tr +
                          //                       ": 1 SAR and make sure to select payment option.",
                          //                   style: pBold20,
                          //                   textAlign: TextAlign.center,
                          //                 ),
                          //                 verticalSpace(24),
                          //                 CommonButton(
                          //                   title: "BACK".tr,
                          //                   onPressed: () {
                          //                     Get.back();
                          //                   },
                          //                   btnColor: AppColor.themeOrangeColor,
                          //                 )
                          //               ],
                          //             ),
                          //           );
                          //         },
                          //       );
                          //     } else if (balanceTopUpController
                          //                 .phoneNumber.length !=
                          //             13 &&
                          //         balanceTopUpController.paytype.value ==
                          //             "PRM") {
                          //       showDialog(
                          //         barrierDismissible: false,
                          //         context: Get.context!,
                          //         builder: (context) {
                          //           return AlertDialog(
                          //             insetPadding: const EdgeInsets.all(16),
                          //             contentPadding: const EdgeInsets.all(24),
                          //             shape: RoundedRectangleBorder(
                          //                 borderRadius:
                          //                     BorderRadius.circular(12)),
                          //             content: Column(
                          //               mainAxisSize: MainAxisSize.min,
                          //               children: [
                          //                 Text(
                          //                   "Enter Valid Mobile Number".tr,
                          //                   style: pBold20,
                          //                   textAlign: TextAlign.center,
                          //                 ),
                          //                 verticalSpace(24),
                          //                 CommonButton(
                          //                   title: "BACK".tr,
                          //                   onPressed: () {
                          //                     Get.back();
                          //                   },
                          //                   btnColor: AppColor.themeOrangeColor,
                          //                 )
                          //               ],
                          //             ),
                          //           );
                          //         },
                          //       );
                          //     } else if (balanceTopUpController
                          //             .currantIndex.value ==
                          //         5) {
                          //       balanceTopUpController.prepareApplePay();
                          //     } else if (balanceTopUpController
                          //             .currantIndex.value ==
                          //         4) {
                          //       if (balanceTopUpController
                          //               .stcPayPhoneNumber.length !=
                          //           13) {
                          //         showDialog(
                          //           barrierDismissible: false,
                          //           context: Get.context!,
                          //           builder: (context) {
                          //             return AlertDialog(
                          //               insetPadding: const EdgeInsets.all(16),
                          //               contentPadding:
                          //                   const EdgeInsets.all(24),
                          //               shape: RoundedRectangleBorder(
                          //                   borderRadius:
                          //                       BorderRadius.circular(12)),
                          //               content: Column(
                          //                 mainAxisSize: MainAxisSize.min,
                          //                 children: [
                          //                   Text(
                          //                     "Enter Valid Mobile Number".tr,
                          //                     style: pBold20,
                          //                     textAlign: TextAlign.center,
                          //                   ),
                          //                   verticalSpace(24),
                          //                   CommonButton(
                          //                     title: "BACK".tr,
                          //                     onPressed: () {
                          //                       Get.back();
                          //                     },
                          //                     btnColor:
                          //                         AppColor.themeOrangeColor,
                          //                   )
                          //                 ],
                          //               ),
                          //             );
                          //           },
                          //         );
                          //       } else {
                          //         balanceTopUpController.newBalOrder();
                          //       }
                          //     } else if (balanceTopUpController
                          //                 .currantIndex.value ==
                          //             0 ||
                          //         balanceTopUpController.currantIndex.value ==
                          //             1 ||
                          //         balanceTopUpController.currantIndex.value ==
                          //             3) {
                          //       balanceTopUpController.CreateBalOrder();
                          //     } else if (balanceTopUpController
                          //             .currantIndex.value ==
                          //         2) {
                          //       if (Constants.AlrajhiMADAEnable == "Y") {
                          //         balanceTopUpController.newBalOrder();
                          //       } else {
                          //         balanceTopUpController.CreateBalOrder();
                          //       }
                          //     }
                          //   },
                          // ),
                        ],
                      ),
                    ),
                    verticalSpace(40),
                    // Padding(
                    //   padding:
                    //       const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    //   child: Text(
                    //     "* " +
                    //         "Volume discount for Monthly Service Charge may change as Diesel vehicles will be charged SAR10 each."
                    //             .tr,
                    //     style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    //   ),
                    // ),
                    // Padding(
                    //   padding:
                    //       const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    //   child: Text(
                    //     "* " +
                    //         "The availability of stock is upon confirmation of payment."
                    //             .tr,
                    //     style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                    //   ),
                    // ),
                    /* Padding(
                        padding:
                            const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                        child: Text(
                          "* " +
                              "Enable popups for this page to view/print Order Reports."
                                  .tr,
                          style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                        ),
                      ),*/
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: CommonButton(
                title: "PLACE ORDER".tr,
                btnColor: AppColor.themeOrangeColor,
                onPressed: () {
                  print("balanceTopUpController.Amount=="
                              "===========" +
                          balanceTopUpController.Amount ==
                      "");
                  if (balanceTopUpController.Amount == "") {
                    balanceTopUpController.Amount == "0";
                  }
                  print("Mobile number===================" +
                      balanceTopUpController.phoneNumber);
                  if (balanceTopUpController.Amount == "0" ||
                      double.parse(balanceTopUpController.Amount) < 1 ||
                      balanceTopUpController.toptupamount.text == "0") {
                    print(balanceTopUpController.AmountValue.toString());
                    showDialog(
                      barrierDismissible: false,
                      context: Get.context!,
                      builder: (context) {
                        return AlertDialog(
                          insetPadding: const EdgeInsets.all(16),
                          contentPadding: const EdgeInsets.all(24),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                "Minimum TopUp amount".tr +
                                    ": 1 SAR and make sure to select payment option.",
                                style: pBold20,
                                textAlign: TextAlign.center,
                              ),
                              verticalSpace(24),
                              CommonButton(
                                title: "BACK".tr,
                                onPressed: () {
                                  Get.back();
                                },
                                btnColor: AppColor.themeOrangeColor,
                              )
                            ],
                          ),
                        );
                      },
                    );
                  } else if (balanceTopUpController.phoneNumber.length != 13 &&
                      balanceTopUpController.paytype.value == "PRM") {
                    showDialog(
                      barrierDismissible: false,
                      context: Get.context!,
                      builder: (context) {
                        return AlertDialog(
                          insetPadding: const EdgeInsets.all(16),
                          contentPadding: const EdgeInsets.all(24),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                "Enter Valid Mobile Number".tr,
                                style: pBold20,
                                textAlign: TextAlign.center,
                              ),
                              verticalSpace(24),
                              CommonButton(
                                title: "BACK".tr,
                                onPressed: () {
                                  Get.back();
                                },
                                btnColor: AppColor.themeOrangeColor,
                              )
                            ],
                          ),
                        );
                      },
                    );
                  } else if (balanceTopUpController.currantIndex.value == 5) {
                    balanceTopUpController.prepareApplePay();
                  } else if (balanceTopUpController.currantIndex.value == 4) {
                    if (balanceTopUpController.stcPayPhoneNumber.length != 13) {
                      showDialog(
                        barrierDismissible: false,
                        context: Get.context!,
                        builder: (context) {
                          return AlertDialog(
                            insetPadding: const EdgeInsets.all(16),
                            contentPadding: const EdgeInsets.all(24),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  "Enter Valid Mobile Number".tr,
                                  style: pBold20,
                                  textAlign: TextAlign.center,
                                ),
                                verticalSpace(24),
                                CommonButton(
                                  title: "BACK".tr,
                                  onPressed: () {
                                    Get.back();
                                  },
                                  btnColor: AppColor.themeOrangeColor,
                                )
                              ],
                            ),
                          );
                        },
                      );
                    } else {
                      balanceTopUpController.newBalOrder();
                    }
                  } else if (balanceTopUpController.currantIndex.value == 0 ||
                      balanceTopUpController.currantIndex.value == 1 ||
                      balanceTopUpController.currantIndex.value == 3) {
                    balanceTopUpController.CreateBalOrder();
                  } else if (balanceTopUpController.currantIndex.value == 2) {
                    if (Constants.AlrajhiMADAEnable == "Y") {
                      balanceTopUpController.newBalOrder();
                    } else {
                      balanceTopUpController.CreateBalOrder();
                    }
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget newETransferWidget() {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: Column(
        children: [
          if (Constants.virtualAccountBTN == 'Y')
            CommonButton(
              title: "Generate".tr,
              btnColor: AppColor.themeOrangeColor,
              onPressed: () {
                if (confirmOrderController.bankCode.isEmpty) {
                  showDialog(
                    barrierDismissible: false,
                    context: Get.context!,
                    builder: (context) {
                      return AlertDialog(
                        insetPadding: const EdgeInsets.all(16),
                        contentPadding: const EdgeInsets.all(24),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              "Please make sure to Select Bank.".tr,
                              style: pBold20,
                              textAlign: TextAlign.center,
                            ),
                            verticalSpace(24),
                            CommonButton(
                              title: "OK".tr,
                              onPressed: () async {
                                Get.back();
                              },
                              btnColor: AppColor.themeOrangeColor,
                            )
                          ],
                        ),
                      );
                    },
                  );
                } else {
                  confirmOrderController.generateVirtualAccount();
                }
              },
            ),
          if (Constants.virtualAccountBTN == 'Y') verticalSpace(16),
          if (balanceTopUpController.CustIBAN != "")
            CommonTextField(
              controller: balanceTopUpController.CustIBAN,
              labelText: 'IBAN # *'.tr,
              readOnly: true,
              fillColor: AppColor.cBorder,
              filled: true,
            ),
          verticalSpace(16),
          if (Constants.virtualAccountBTN == 'N')
            CommonTextField(
              readOnly: true,
              fillColor: AppColor.cBorder,
              filled: true,
              labelText: 'Select Bank'.tr,
              hintText: Constants.custB2B_BANK == "ARB"
                  ? "(ARB) AL-RAJHI BANK / مصرف الراجحي"
                  : Constants.custB2B_BANK == "NCB"
                      ? "(NCB) AL AHLI BANK / البنك الأهلي التجاري"
                      : "RIYADH BANK / بنك الرياض",
            ),
          // CommonDropdownButtonWidget(
          //   labelText: "Select Bank".tr,
          //   list: balanceTopUpController.selectBankList,
          //   value: balanceTopUpController.selectedBank.value,
          //   onChanged: (value) {
          //     balanceTopUpController.selectedBank.value = value;
          //   },
          //   filledColor: AppColor.cBorder,
          // ),
          // CommonDropdownButtonWidget(
          //   labelText: "Select Bank".tr,
          //   list: balanceTopUpController.selectBankList,
          //   value: balanceTopUpController.selectedBank.value,
          //   onChanged: (value) {
          //     balanceTopUpController.selectedBank.value = value;
          //   },
          //   isExpanded: false,
          //   filledColor: AppColor.cBorder,
          // ),
          if (Constants.virtualAccountBTN == 'Y')
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Select Bank".tr,
                  style: pRegular12,
                ),
                SizedBox(
                  height: 44,
                  child: DropdownButtonFormField(
                    value: balanceTopUpController.bankList.isNotEmpty
                        ? balanceTopUpController.bankList.first.bankcode
                        : null,
                    items: balanceTopUpController.bankList.map((data) {
                      return DropdownMenuItem(
                        value: data.bankcode,
                        child: Text(
                          data.bankdesc.tr,
                          style: pRegular13,
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    dropdownColor: AppColor.cLightGrey,
                    icon: Padding(
                      padding: const EdgeInsets.only(right: 0),
                      child:
                          assetSvdImageWidget(image: DefaultImages.dropDownIcn),
                    ),
                    onChanged: (value) {
                      print("Select Bank >>>> $value");
                      confirmOrderController.bankCode.value = value.toString();
                    },
                    validator: (value) {
                      return Validator.validateRequired(value.toString());
                    },
                    style: pMedium14.copyWith(
                      color: AppColor.cWhiteFont,
                    ),
                    isExpanded: true,
                    decoration: InputDecoration(
                      fillColor: AppColor.cFilled,
                      filled: true,
                      hintText: "Select Bank".tr,
                      hintStyle: pMedium12.copyWith(color: AppColor.cHintFont),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      contentPadding: EdgeInsets.only(left: 16, right: 16),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      disabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: AppColor.cBorder,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          verticalSpace(8),
          Text(
            "• " +
                "Please transfer to your designated Riyadh/NCB IBAN account."
                    .tr +
                "\n• " +
                "For Riyadh Bank customer kindly use the last 13 digits of the IBAN account."
                    .tr +
                "\n• " +
                "For NCB Bank customer kindly use the last 14 digits of the IBAN account."
                    .tr,
            style: pRegular12,
          )
        ],
      ),
    );
  }

  Widget eTransferWidget() {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: Column(
        children: [
          verticalSpace(20),
          if (balanceTopUpController.CustIBAN != "")
            CommonTextField(
              controller: balanceTopUpController.CustIBAN,
              labelText: 'IBAN # *'.tr,
              readOnly: true,
            ),
          verticalSpace(16),
          CommonTextField(
            readOnly: true,
            fillColor: AppColor.cBorder,
            filled: true,
            labelText: 'Select Bank'.tr,
            hintText: Constants.custB2B_BANK == "ARB"
                ? "(ARB) AL-RAJHI BANK / مصرف الراجحي"
                : Constants.custB2B_BANK == "NCB"
                    ? "(NCB) AL AHLI BANK / البنك الأهلي التجاري"
                    : "RIYADH BANK / بنك الرياض",
          ),
          // CommonDropdownButtonWidget(
          //   labelText: "Select Bank".tr,
          //   list: balanceTopUpController.selectBankList,
          //   value: balanceTopUpController.selectedBank.value,
          //   onChanged: (value) {
          //     balanceTopUpController.selectedBank.value = value;
          //   },
          //   isExpanded: false,
          // ),
          verticalSpace(8),
          Text(
            "• " +
                "Select the items and the quantity you require.".tr +
                "\n• " +
                "Enter the TOPUP Amount (if any).".tr +
                "\n• " +
                "Please transfer to your designated Riyadh/NCB IBAN account."
                    .tr +
                "\n• " +
                "For Riyadh Bank customer kindly use the last 13 digits of the IBAN account."
                    .tr +
                "\n• " +
                "For NCB Bank customer kindly use the last 14 digits of the IBAN account."
                    .tr +
                "\n• " +
                "Please make sure that your transfer amount is sufficient against your order."
                    .tr +
                "\n• " +
                "System will automatically allocate the amount to orders which has total payment not exceeding the transferred amount : [ First Ordered, First Paid ]."
                    .tr +
                "\n• " +
                "Please wait for a call from the technician for installation."
                    .tr,
            style: pRegular12,
          ),
        ],
      ),
    );
  }

  Widget cashPaymentNoteWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Cash Payment Note".tr,
          style: pSemiBold14,
        ),
        verticalSpace(8),
        Text(
          "• " +
              "Enter the TOPUP Amount and click Confirm".tr +
              "\n• " +
              "Print your Order from the TOPUP History for reference.".tr +
              "\n• " +
              "Proceed to the Cashier for payment.".tr,
          style: pRegular14,
        ),
      ],
    );
  }

  Widget MadaPaymentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Online Payment Note :".tr,
          style: pSemiBold14,
        ),
        verticalSpace(8),
        Text(
          "• " +
              "Complete the order details and confirm payment.".tr +
              "\n• " +
              "You will be re-directed to the online portal".tr +
              "\n• " +
              "Print Receipt.".tr,
          style: pRegular14,
        ),
      ],
    );
  }

  Widget ApplePayPaymentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: const [
        // Text(
        //   "Note :".tr,
        //   style: pSemiBold14,
        // ),
        // // Text(
        // //   "Apple Pay Note :".tr,
        // //   style: pSemiBold14,
        // // ),
        // verticalSpace(8),
        // Text(
        //   "• " +
        //       "Complete the order details and confirm payment.".tr +
        //       "\n• " +
        //       "Print Receipt.".tr,
        //   style: pRegular14,
        // ),
      ],
    );
  }

  Widget stcPayPaymentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //verticalSpace(20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Phone Number".tr,
              style: pRegular14,
            ),
            balanceTopUpController.isSTCPaySubmit.value ||
                    balanceTopUpController.isSTCPayValidate.value
                ? SizedBox()
                : Text(
                    "Not Confirmed".tr,
                    style: pSemiBold12.copyWith(color: AppColor.cRedText),
                  ),
          ],
        ),
        verticalSpace(6),
        Directionality(
          textDirection:
              TextDirection.ltr, // Set your desired text direction here
          child: Container(
            height: 44,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                    color:
                        balanceTopUpController.isSTCPaySubmit.value == true ||
                                balanceTopUpController.isSTCPayValidate.value ==
                                    true
                            ? AppColor.cBorder
                            : AppColor.cRedText)),
            child: InternationalPhoneNumberInput(
              onInputChanged: (PhoneNumber num) {
                print("====>${num.phoneNumber}");
                print("---->${num.isoCode}");
                balanceTopUpController.isoCode.value = num.isoCode!;
                balanceTopUpController.stcPayPhoneNumber =
                    num.phoneNumber.toString();
              },
              onInputValidated: (bool value) {
                print(value);
              },
              cursorColor: AppColor.cHintFont,
              selectorConfig: SelectorConfig(
                selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                leadingPadding: 16,
                setSelectorButtonAsPrefixIcon: true,
              ),
              ignoreBlank: false,
              autoValidateMode: AutovalidateMode.disabled,
              textStyle: pRegular14.copyWith(color: AppColor.cLabel),
              initialValue: PhoneNumber(
                  isoCode: balanceTopUpController.isoCode.value,
                  dialCode: balanceTopUpController.isoCode.value),
              inputBorder: OutlineInputBorder(),
              keyboardAction: TextInputAction.done,
              scrollPadding: EdgeInsets.zero,
              selectorTextStyle:
                  pRegular14.copyWith(color: AppColor.cLabel, fontSize: 14),
              textAlign: TextAlign.start,
              textAlignVertical: TextAlignVertical.center,
              inputDecoration: InputDecoration(
                  contentPadding: EdgeInsets.only(left: 16, bottom: 8),
                  isDense: true,
                  prefixText: "|  ",
                  prefixStyle: TextStyle(fontSize: 30, color: AppColor.cBorder),
                  counterText: '',
                  hintText: " " + 'Please enter here'.tr,
                  counterStyle: TextStyle(fontSize: 0, height: 0),
                  errorStyle: TextStyle(fontSize: 0, height: 0),
                  hintStyle: pRegular14.copyWith(
                    color: AppColor.cHintFont,
                  ),
                  border: InputBorder.none),
              onSaved: (PhoneNumber num) {
                print('On Saved: $num');
                print('On Saved:111:: ${num.dialCode}');
                print('On Saved:2222: ${num.phoneNumber}');
              },
            ),
          ),
        ),
        verticalSpace(8),
        Text(
          "• " + "Complete the order details and confirm payment.".tr,
          style: pRegular14,
        ),
      ],
    );
  }

  Widget AldreesPromotionWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSpace(20),
        // Text(
        //   "Aldrees partners".tr,
        //   style: pRegular14,
        // ),
        // verticalSpace(8),
        Row(
          children: [
            promotionWidget(
              title: "Al-Rajhi Mokafaa".tr,
              image: DefaultImages.alrajhiIMG,
              isSelected: balanceTopUpController.isMokafa.value,
              onTap: () {
                balanceTopUpController.prmType = "J";
                balanceTopUpController.prmPayType.value = "J";
                balanceTopUpController.isMokafa.value = true;
                balanceTopUpController.isQitaf.value = false;
                balanceTopUpController.isANB.value = false;
                balanceTopUpController.isALINMA.value = false;
              },
            ),
            horizontalSpace(12),
            promotionWidget(
              title: "STC Qitaf".tr,
              image: DefaultImages.stcIMG,
              isSelected: balanceTopUpController.isQitaf.value,
              onTap: () {
                balanceTopUpController.prmPayType.value = "S";
                balanceTopUpController.prmType = "S";
                balanceTopUpController.isMokafa.value = false;
                balanceTopUpController.isQitaf.value = true;
                balanceTopUpController.isANB.value = false;
                balanceTopUpController.isALINMA.value = false;
              },
            ),
            if (Constants.IsAlinmaEnable == 'N') horizontalSpace(12),
            if (Constants.IsAlinmaEnable == 'N')
              promotionWidget(
                title: "ANB".tr,
                image: DefaultImages.anbIMG,
                isSelected: balanceTopUpController.isANB.value,
                onTap: () {
                  balanceTopUpController.prmPayType.value = "N";
                  balanceTopUpController.prmType = "N";
                  balanceTopUpController.isMokafa.value = false;
                  balanceTopUpController.isQitaf.value = false;
                  balanceTopUpController.isANB.value = true;
                  balanceTopUpController.isALINMA.value = false;
                },
              ),
          ],
        ),
        if (Constants.IsAlinmaEnable == 'Y') verticalSpace(12),
        if (Constants.IsAlinmaEnable == 'Y')
          Row(
            children: [
              promotionWidget(
                title: "ANB".tr,
                image: DefaultImages.anbIMG,
                isSelected: balanceTopUpController.isANB.value,
                onTap: () {
                  balanceTopUpController.prmPayType.value = "N";
                  balanceTopUpController.prmType = "N";
                  balanceTopUpController.isMokafa.value = false;
                  balanceTopUpController.isQitaf.value = false;
                  balanceTopUpController.isANB.value = true;
                  balanceTopUpController.isALINMA.value = false;
                },
              ),
              horizontalSpace(12),
              promotionWidget(
                title: "ALINMA".trr,
                image: DefaultImages.alinmaIMG,
                isSelected: balanceTopUpController.isALINMA.value,
                onTap: () {
                  balanceTopUpController.prmPayType.value = "L";
                  balanceTopUpController.prmType = "L";
                  balanceTopUpController.isMokafa.value = false;
                  balanceTopUpController.isQitaf.value = false;
                  balanceTopUpController.isANB.value = false;
                  balanceTopUpController.isALINMA.value = true;
                },
              ),
            ],
          ),
        verticalSpace(16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Phone Number".tr,
              style: pRegular14,
            ),
            balanceTopUpController.isSubmit.value ||
                    balanceTopUpController.isValidate.value
                ? SizedBox()
                : Text(
                    "Not Confirmed".tr,
                    style: pSemiBold12.copyWith(color: AppColor.cRedText),
                  ),
          ],
        ),
        verticalSpace(6),
        Directionality(
          textDirection:
              TextDirection.ltr, // Set your desired text direction here
          child: Container(
            height: 44,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                    color: balanceTopUpController.isSubmit.value == true ||
                            balanceTopUpController.isValidate.value == true
                        ? AppColor.cBorder
                        : AppColor.cRedText)),
            child: InternationalPhoneNumberInput(
              onInputChanged: (PhoneNumber number) {
                print("====>${number.phoneNumber}");
                print("---->${number.isoCode}");
                balanceTopUpController.isoCode.value = number.isoCode!;
                balanceTopUpController.phoneNumber =
                    number.phoneNumber.toString();
              },
              onInputValidated: (bool value) {
                print(value);
              },
              cursorColor: AppColor.cHintFont,
              selectorConfig: SelectorConfig(
                selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                leadingPadding: 16,
                setSelectorButtonAsPrefixIcon: true,
              ),
              ignoreBlank: false,
              autoValidateMode: AutovalidateMode.disabled,
              textStyle: pRegular14.copyWith(color: AppColor.cLabel),
              initialValue: PhoneNumber(
                  isoCode: balanceTopUpController.isoCode.value,
                  dialCode: balanceTopUpController.isoCode.value),
              inputBorder: OutlineInputBorder(),
              keyboardAction: TextInputAction.done,
              scrollPadding: EdgeInsets.zero,
              selectorTextStyle:
                  pRegular14.copyWith(color: AppColor.cLabel, fontSize: 14),
              textAlign: TextAlign.start,
              textAlignVertical: TextAlignVertical.center,
              inputDecoration: InputDecoration(
                  contentPadding: EdgeInsets.only(left: 16, bottom: 8),
                  isDense: true,
                  prefixText: "|  ",
                  prefixStyle: TextStyle(fontSize: 30, color: AppColor.cBorder),
                  counterText: '',
                  hintText: " " + 'Please enter here'.tr,
                  counterStyle: TextStyle(fontSize: 0, height: 0),
                  errorStyle: TextStyle(fontSize: 0, height: 0),
                  hintStyle: pRegular14.copyWith(
                    color: AppColor.cHintFont,
                  ),
                  border: InputBorder.none),
              onSaved: (PhoneNumber number) {
                print('On Saved: $number');
                print('On Saved:111:: ${number.dialCode}');
                print('On Saved:2222: ${number.phoneNumber}');
              },
            ),
          ),
        ),
        verticalSpace(8),
        if (balanceTopUpController.isMokafa.value == true)
          Text(
            "• " +
                "TOPUP through Al-Rajhi Mokafaa cannot be refunded for cash."
                    .tr +
                "\n• " +
                "265 Al-Rajhi Mokafaa points is equal to 1 SAR.".tr +
                "\n• " +
                "Decimal point is not allowed in this payment type.".tr,
            style: pRegular12,
          ),
        if (balanceTopUpController.isQitaf.value == true)
          Text(
            "• " +
                "TOPUP through STC Qitaf, cannot be refunded for cash.".tr +
                "\n• " +
                "5 STC Qitaf is equal to 1 SAR.".tr +
                "\n• " +
                "Decimal point is not allowed in this payment type.".tr,
            style: pRegular12,
          ),
        if (balanceTopUpController.isANB.value == true)
          Text(
            "• " +
                "TOPUP through ANB Rewards points cannot be refunded for cash."
                    .tr +
                "\n• " +
                "100 ANB points is equal to 1 SAR".tr +
                "\n• " +
                "Decimal point is not allowed in this payment type.".tr,
            style: pRegular12,
          ),
        verticalSpace(15),
        /* verticalSpace(15),
        balanceTopUpController.isValidate.value
            ? Row(
                children: [
                  assetSvdImageWidget(
                      image: DefaultImages.checkCircleIcn,
                      colorFilter:
                          ColorFilter.mode(AppColor.cGreen, BlendMode.srcIn)),
                  horizontalSpace(12),
                  Text(
                    "CONFIRMED".tr,
                    style: pSemiBold12.copyWith(color: AppColor.cGreen),
                  )
                ],
              )
            : CommonButton(
          title: balanceTopUpController.phoneNumber.length>7
              ? "Submit".tr
              : "Try again".tr,

          onPressed: () {
            */ /* if (newOrderController.isSubmit.value == false) {
              newOrderController.isValidate.value = true;
            } else {
              newOrderController.isSubmit.value = false;
            }*/ /*
            if(balanceTopUpController.phoneNumber.length>7)
            {
              balanceTopUpController.isValidate.value = true;
              balanceTopUpController.isSubmit.value == true;
              print("isValidate==========="+"true");
            }
            else
            {
              balanceTopUpController.isValidate.value = false;
              balanceTopUpController.isSubmit.value == false;
              print("isValidate==========="+"false");
            }
          },
          btnColor: AppColor.themeOrangeColor,
        ),*/
      ],
    );
  }

  Widget paymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(8),
                    Text(
                      title,
                      style: pRegular13,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget cashPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(6),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: 45,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget newPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(7),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: 60,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget eTransferPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(5),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: Constants.IsAr_App == "false" ? 80 : 110,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget newETransferPaymentOptionDataWidget({
    required String title,
    required Widget widget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Container(
          width: Get.width,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: AppColor.cBorder)),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
          child: Column(
            children: [
              Row(
                children: [
                  horizontalSpace(5),
                  Image.asset(
                    image,
                    fit: BoxFit.contain,
                    width: Constants.IsAr_App == "false" ? 80 : 110,
                  ),
                ],
              ),
              verticalSpace(20),
              widget,
            ],
          ),
        ),
      );

  Widget partnersPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(5),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: Constants.IsAr_App == "false" ? 120 : 110,
                    ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget applePayPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(8),
                    assetSvdImageWidget(
                        image: DefaultImages.applePayIcnRGB, width: 50),
                    horizontalSpace(5),
                    Text(
                      title,
                      style: pRegular13,
                    ),
                  ],
                ),
                // verticalSpace(isSelected == true
                //     ? isWidget == true
                //         ? 20
                //         : 0
                //     : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget stcPayPaymentOptionDataWidget({
    required Function() onTap,
    required bool isSelected,
    required String title,
    required Widget widget,
    required bool isWidget,
    required String image,
  }) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color: isSelected
                        ? AppColor.themeDarkBlueColor
                        : AppColor.cBorder)),
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Column(
              children: [
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: isSelected == true
                            ? DefaultImages.checkCircleIcn
                            : DefaultImages.circleIcn),
                    horizontalSpace(7),
                    Image.asset(
                      image,
                      fit: BoxFit.contain,
                      width: 60,
                    ),
                    // horizontalSpace(5),
                    // Text(
                    //   title,
                    //   style: pRegular13,
                    // ),
                  ],
                ),
                verticalSpace(isSelected == true
                    ? isWidget == true
                        ? 20
                        : 0
                    : 0),
                isSelected == true ? widget : SizedBox(),
              ],
            ),
          ),
        ),
      );

  Widget currentBalanceWidget({required String balance}) {
    return Container(
      width: Get.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: AppColor.cLightGrey,
      ),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: FutureBuilder<String>(
        future: null,
        builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return CircularProgressIndicator(); // Display a loading indicator while fetching data
          } else if (snapshot.hasError) {
            return Text('Error: ${snapshot.error}');
          } else {
            // return Text(
            //   // 'Current balance : ${snapshot.data} SAR',
            //   // 'Current balance : $balance SAR',
            //   "Current balance".tr + ":" + balance + " SAR".tr,
            //   style: pBold14,
            // );

            return Directionality(
              textDirection: TextDirection.ltr,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "Current balance".tr + ":",
                    // double.parse(value).toStringAsFixed(2),
                    style: pBold14,
                  ),
                  Gap(2),
                  assetSvdImageWidget(
                      image: DefaultImages.saudiRiyal, width: 13, height: 13),
                  Gap(4),
                  Text(
                    balance,
                    // double.parse(value).toStringAsFixed(2),
                    style: pBold14,
                  ),
                ],
              ),
            );
          }
        },
      ), /*Text.rich(
        TextSpan(
          text: "Current balance".tr + ": ",
          style: pBold14,
          children: [
            TextSpan(text: balance, style: pSemiBold14),
          ],
        ),
      ),*/
    );
  }
}

Widget totalsDataWidget(
    {required String title,
    required String value,
    double? fontSize,
    Color? fontColor}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(
        title,
        style: pRegular13.copyWith(
            fontSize: fontSize ?? 13, color: fontColor ?? AppColor.cText),
      ),
      Directionality(
        textDirection: TextDirection.ltr,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            assetSvdImageWidget(
              image: DefaultImages.saudiRiyal,
              width: fontSize == 14
                  ? 13
                  : fontSize == 17
                      ? 16
                      : 13,
              height: fontSize == 14
                  ? 13
                  : fontSize == 17
                      ? 16
                      : 13,
            ),
            Gap(4),
            Text(
              value,
              // double.parse(value).toStringAsFixed(2),
              style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
            ),
          ],
        ),
      ),
      // Text(
      //   value,
      //   // double.parse(value).toStringAsFixed(2),
      //   style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
      // ),
    ],
  );
}
