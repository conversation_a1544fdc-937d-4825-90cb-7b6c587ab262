// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_history_service_menu_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_history_topup_menu_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_service_menu_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_topup_menu_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../utils/constants.dart';

class OrderHistoryMenuScreen extends StatelessWidget {
  const OrderHistoryMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        top: 15,
                        bottom: 15,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          assetSvdImageWidget(
                              image: DefaultImages.backIcn,
                              colorFilter: ColorFilter.mode(
                                  AppColor.cDarkBlueFont, BlendMode.srcIn)),
                          horizontalSpace(10),
                          Text(
                            //"Menu".trr,
                            "Back".trr,
                            style: pRegular18.copyWith(
                                color: AppColor.cDarkBlueFont, fontSize: 17),
                            textAlign: TextAlign.start,
                          )
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        "Refund History".trr,
                        style: pBold20,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  companyItemWidget(
                    image: DefaultImages.companyDetailIcn,
                    title: 'Service'.trr,
                    onTap: () {
                      Get.to(() => OrderHistoryServiceMenuScreen());
                    },
                  ),
                  if (Constants.TopUpBtn == "Y")
                    companyItemWidget(
                      image: DefaultImages.addressIcn,
                      title: 'Topup'.trr,
                      onTap: () {
                        Get.to(() => OrderHistoryTopupMenuScreen());
                      },
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

companyItemWidget({
  required String title,
  required String image,
  required Function() onTap,
  TextStyle? textStyle,
  bool? isIcon,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
      child: Container(
        color: AppColor.cBackGround,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                assetSvdImageWidget(image: image),
                horizontalSpace(16),
                Text(
                  title,
                  style: textStyle ?? pRegular17,
                ),
              ],
            ),
            isIcon == false
                ? SizedBox()
                : assetSvdImageWidget(
                    image: DefaultImages.nextIcn,
                    colorFilter: ColorFilter.mode(
                      AppColor.cText,
                      BlendMode.srcIn,
                    ),
                  ),
          ],
        ),
      ),
    ),
  );
}
