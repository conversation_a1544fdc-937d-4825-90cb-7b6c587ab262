import 'dart:convert';

class Report_Driver {
  final String DRIVERCODE;
  final String DRIVER;

  Report_Driver({
    required this.DRIVERCODE,
    required this.DRIVER,
  });

  Map<String, dynamic> toMap() {
    return {
      'DRIVERCODE': DRIVERCODE,
      'DRIVER': DRIVER,
    };
  }

  factory Report_Driver.fromMap(Map<String, dynamic> map) {
    return Report_Driver(
      DRIVERCODE: map['DRIVERCODE'] ?? '',
      DRIVER: map['DRIVER'] ?? '',
    );
  }
  String toJson() => json.encode(toMap());

  factory Report_Driver.fromJson(String source) =>
      Report_Driver.fromMap(json.decode(source));
}