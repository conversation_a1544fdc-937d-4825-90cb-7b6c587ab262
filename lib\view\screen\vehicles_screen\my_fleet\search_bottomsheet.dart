// ignore_for_file: prefer_const_constructors, prefer_const_constructors_in_immutables

import 'package:waie_app/utils/insert_dictionary.dart';

import 'action_widget.dart';
import 'package:get/get.dart';
import 'my_fleet_screen.dart';
import 'fleet_vehicle_screen.dart';
import 'package:flutter/material.dart';
import 'assign_digital_coupon_widget.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/core/controller/vehicle_controller/search_vehicle_controller.dart';

class SearchBottomSheetWidget extends StatefulWidget {
  SearchBottomSheetWidget({super.key});

  @override
  State<SearchBottomSheetWidget> createState() =>
      _SearchBottomSheetWidgetState();
}

class _SearchBottomSheetWidgetState extends State<SearchBottomSheetWidget> {
  SearchVehicleController searchVehicleController =
      Get.put(SearchVehicleController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    searchVehicleController.itemList.clear();
  }

  void filterSearchResults(String query) {
    searchVehicleController.itemList.value = searchVehicleController
        .myFleetList.value
        .where(
            (item) => item['code'].toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Get.height - 60,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      padding: EdgeInsets.all(16),
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalSpace(16),
            Row(
              children: [
                Expanded(
                  child: CommonTextField(
                    controller: searchVehicleController.searchController.value,
                    labelText: '',
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(12),
                      child: assetSvdImageWidget(
                          image: DefaultImages.searchIcn,
                          width: 24,
                          height: 24),
                    ),
                    hintText: 'Search'.trr,
                    onChanged: (value) {
                      searchVehicleController.searchController.refresh();
                      filterSearchResults(value);
                    },
                  ),
                ),
                searchVehicleController.searchController.value.text.isEmpty
                    ? SizedBox()
                    : cancelButton(
                        () {
                          searchVehicleController.searchController.value
                              .clear();
                          searchVehicleController.searchController.refresh();
                          searchVehicleController.itemList.clear();
                          searchVehicleController.itemList.refresh();
                        },
                      )
              ],
            ),
            verticalSpace(16),
            searchVehicleController.itemList.isEmpty
                ? Expanded(
                    child: Center(
                      child: Text(
                        "We couldn't find that vehicle. Please double-check and try again."
                            .trr,
                        style:
                            pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: searchVehicleController.itemList.length,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      var data = searchVehicleController.itemList[index];
                      return Obx(() {
                        return GestureDetector(
                          onTap: () {
                            Get.to(() => FleetVehicleScreen());
                          },
                          child: fleetWidget(
                            //isShowCheck: false,
                            code: data['code'],
                            status: data['status'].toString().trr,
                            title: data['title'],
                            type: data['type'].toString().trr,
                            driver: data['driver'],
                            quotaTotal: data['quotaTotal'],
                            quotaString: data['quotaString'],
                            division: data['division'],
                            textColor: data['status'] == "Inactive" ||
                                    data['status'] == 'In progress'
                                ? AppColor.cYellow
                                : data['status'] == "New"
                                    ? AppColor.cDarkBlueFont
                                    : AppColor.cGreen,
                            color: data['status'] == "Inactive" ||
                                    data['status'] == 'In progress'
                                ? AppColor.cLightYellow
                                : data['status'] == "New"
                                    ? AppColor.cLightBlueContainer
                                    : AppColor.cLightGreen,
                            tag: data['status'] == "Inactive"
                                ? DefaultImages.inactiveIcn
                                : DefaultImages.tagIcn,
                            viewMore: () {
                              showModalBottomSheet(
                                context: context,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16))),
                                backgroundColor: AppColor.cBackGround,
                                barrierColor: AppColor.cBlackOpacity,
                                isScrollControlled: true,
                                builder: (context) {
                                  return ActionWidget(
                                    code: data['code'],
                                    serviceStatus: data['status'].toString(),
                                    isComplaint: data['status'] == "Active"
                                        ? true
                                        : false,
                                  );
                                },
                              );
                            },
                            scanTap: () {
                              showModalBottomSheet(
                                context: context,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16))),
                                backgroundColor: AppColor.cBackGround,
                                barrierColor: AppColor.cBlackOpacity,
                                isScrollControlled: true,
                                builder: (context) {
                                  return AssignDigitalCouponWidget(
                                    code: data['code'],
                                  );
                                },
                              );
                            },
                          ),
                        );
                      });
                    },
                  ),
          ],
        );
      }),
    );
  }

  cancelButton(Function()? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(left: 8, right: 8),
        child: Container(
          height: 44,
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
              color: AppColor.cLightGrey,
              borderRadius: BorderRadius.circular(6)),
          child: Center(
            child: Text(
              "Cancel".trr,
              style: pRegular13,
            ),
          ),
        ),
      ),
    );
  }
}
