import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/models/loadplaces.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../../utils/constants.dart';

class FleetController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  GetStorage userStorage = GetStorage('User');
  final fleetList = <FleetModel>[].obs;
  List<LoadPlaces> loadPlaces = [];
  RxList placeList = [].obs;
  RxBool chckbox = false.obs;

  @override
  void onInit() async {
    super.onInit();
    print('FleetController');
    await getDatas();
  }

  getDatas() async {
    await getLoadPlaces();
    //await fetchFleets();
    //print("fleetList ${jsonDecode(jsonEncode(fleetList))}");
    // if (fleetList.isNotEmpty) {
    //   await getLoadPlaces();
    //   print("loadPlaces ${jsonDecode(jsonEncode(loadPlaces))}");
    // }
  }

  // void fetchFleets() async {
  //   var fleets = await RemoteServices.fetchFleets();
  //   print("MODEL fleets ====> $fleets");
  //   print("MODEL pasok");
  //   print("MODEL COUNT ===> $fleets");
  //   print("RETURNED fleetList ===> ${fleetList.length}");

  //   if (fleets != null) {
  //     fleetList.value = fleets;
  //   }
  // }

  Future<List<FleetModel>> fetchFleets() async {
    var client = http.Client();
    List<FleetModel> fleets = [];
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = custsData.read('custData');
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");

    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      Map body = {
        "SEARCHBY": "",
        "TOP": "250",
        "SKIP": "2",
        "CUSTID": custid, //"000003944",
        "SERIALID": "",
        "UNAME": custData['USERNAME']
      };
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
          body: jsonEncode(body),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          });

      // print("response body===> ${response.body}");
      List<dynamic> result = jsonDecode(response.body);
      // print("statusCode===> ${response.statusCode}");
      // print("response body===> ${response.body}");
      for (int i = 0; i < result.length; i++) {
        print("i===> $i --- ${result.length}");
      }

      for (int i = 0; i < result.length; i++) {
        try {
          FleetModel fleet =
              FleetModel.fromMap(result[i] as Map<String, dynamic>);
          fleets.add(fleet);
        } catch (e) {
          print("e.toString()===> ${e.toString()}");
        }
      }
      //print("response fleets===> $fleets");
      fleetList.value = fleets;

      print("response fleetList===> ${fleetList.length}");

      return fleetList;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<List<LoadPlaces>> getLoadPlaces() async {
    var custData = custsData.read('custData');
    print("custid>>>>>>> $custData['CUSTID']");
    print("emailid>>>>>>> $custData['EMAILID']");
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadPlaces),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
          body: {
            "CUSTID": custData['CUSTID'],
            "OTHERSTN": custData['OTHERSTN'],
            "StationType": "W",
            "fuelType": "PE910",
            "IsAR": Constants.IsAr_App,
          });
      List result = jsonDecode(response.body);

      // print("resultresultresultresult >>>>> $result");
      // print("===============================================================");
      // print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      // print("===============================================================");
      // print(
      //     "jsonDecode(response.body)['PlaceName'] >>>>> ${jsonDecode(response.body)[0]['PlaceName']}");
      // print(
      //     "jsonDecode(response.body)['PlaceName'] >>>>> ${jsonDecode(response.body)[0]['Stations'][0]}");
      // print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        LoadPlaces place =
            LoadPlaces.fromJson(result[i] as Map<String, dynamic>);
        loadPlaces.add(place);
      }
      // print("===============================================================");
      // print(
      //     "loadPlacesloadPlacesloadPlacesloadPlaces >>>>> ${jsonDecode(jsonEncode(loadPlaces))}");

      return loadPlaces;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
