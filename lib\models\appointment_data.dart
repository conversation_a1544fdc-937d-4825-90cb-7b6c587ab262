import 'dart:convert';

import 'package:flutter/foundation.dart';

class Appointment_Data {
  final String CUSTID;
  final String ORDERID;
  final num ORDEREDQTY;
  final num REMAININGQY;
  final num APPOINTEDQTY;
  final num WAITINGFORUPDATE;

  Appointment_Data(
      {required this.CUSTID,
      required this.OR<PERSON><PERSON><PERSON>,
      required this.ORDEREDQ<PERSON>,
      required this.REMAININGQ<PERSON>,
      required this.APPOINTEDQTY,
      required this.WAITINGFORUPDATE});

  Map<String, dynamic> toMap() {
    return {
      'CUSTID': CUSTID,
      'ORDERID': ORDERID,
      'ORDEREDQTY': ORDEREDQTY,
      'REMAININGQY': REMAININGQY,
      'APPOINTEDQTY': APPOINTEDQTY,
      'WA<PERSON><PERSON><PERSON><PERSON>UPDATE': WAITINGFORUPDATE
    };
  }

  factory Appointment_Data.fromMap(Map<String, dynamic> map) {
    return Appointment_Data(
      CUSTID: map['CUSTID'] ?? '',
      ORDERID: map['ORDERID'] ?? '',
      ORDEREDQTY: map['ORDEREDQTY'] ?? '',
      REMAININGQY: map['REMAININGQY'] ?? '',
      APPOINTEDQTY: map['APPOINTEDQTY'] ?? '',
      WAITINGFORUPDATE: map['WAITINGFORUPDATE'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Appointment_Data.fromJson(String source) =>
      Appointment_Data.fromMap(json.decode(source));
}
