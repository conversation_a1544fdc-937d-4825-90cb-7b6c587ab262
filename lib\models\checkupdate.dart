import 'dart:convert';

List<CheckUpdateModel> checkUpdateModelFromJson(String str) =>
    List<CheckUpdateModel>.from(
        json.decode(str).map((x) => CheckUpdateModel.fromJson(x)));

String checkUpdateModelToJson(List<CheckUpdateModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CheckUpdateModel {
  String result;
  String remarks;
  String url;
  String link;

  CheckUpdateModel({
    required this.result,
    required this.remarks,
    required this.url,
    required this.link,
  });

  // factory CheckUpdateModel.fromJson(Map<String, dynamic> json) =>
  //     CheckUpdateModel(
  //       result: json["RESULT"] ?? "",
  //       remarks: json["REMARKS"] ?? "",
  //     );

  // Map<String, dynamic> toJson() => {
  //       "RESULT": result,
  //       "REMARKS": remarks,
  //     };

  Map<String, dynamic> toMap() {
    return {
      'RESULT': result,
      'REMARKS': remarks,
      'URL': url,
      'LINK': link,
    };
  }

  factory CheckUpdateModel.fromMap(Map<String, dynamic> map) {
    return CheckUpdateModel(
      result: map['RESULT'] ?? '',
      remarks: map['REMARKS'] ?? '',
      url: map['URL'] ?? '',
      link: map['LINK'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory CheckUpdateModel.fromJson(String source) =>
      CheckUpdateModel.fromMap(json.decode(source));
}
