import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:http_parser/http_parser.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/company_affiliates_menu_screen.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/new_affiliate_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_refund_service_menu_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class CompanyAffiliateNewController extends GetxController {
  TextEditingController affiliateCompanyNameController =
      TextEditingController();
  TextEditingController affiliateReasonController = TextEditingController();
  GetStorage custsData = GetStorage('custsData');
  RxString fileName = ''.obs;
  final ImagePicker picker = ImagePicker();
  File? postImage;

  RxBool isAffiliated = true.obs;
  RxBool isHistory = false.obs;

  addAffiliates(nationalID) async {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
    var custData = jsonEncode(custsData.read('custData'));
    var client = http.Client();
    try {
      var nationalid = nationalID.readStream;
      var request = http.MultipartRequest(
          'POST',
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.addAffiliates));
      var headers = {'Content-Type': 'application/pdf; charset=UTF-8'};

      request.files.add(http.MultipartFile(
          'nationalid', nationalid!, nationalID.size,
          filename: nationalID.name,
          contentType: MediaType('application', 'pdf')));

      nationalid.cast();
      request.headers.addAll(headers);
      request.fields['custdata'] = custData;
      request.fields['txtReason'] = affiliateReasonController.text;
      request.fields['isAR'] = "false";
      var res = await request.send();
      return res.stream.bytesToString().asStream().listen((event) {
        var parsedJson = json.decode(event);
        print(
            "===============================================================");
        print(parsedJson['response']['Action']);
        print(parsedJson['response']['Message']);
        print(
            "===============================================================");
        if (parsedJson['response']['Action'] == "EXCEPTION") {
          Navigator.of(Get.context!).pop();
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      parsedJson['response']['Message'].toString(),
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () {
                        clear();
                        Get.offAll(() => CompanyAffiliateMenuScreen());
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
        if (parsedJson['response']['Action'] == "POPUP") {
          Navigator.of(Get.context!).pop();
          var msg = "";
          if (parsedJson['message'] != null) {
            msg = parsedJson['message'].toString();
          } else {
            msg = parsedJson['response']['Message'].toString();
          }
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      msg,
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () {
                        clear();
                        Get.offAll(() => CompanyAffiliateMenuScreen());
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
        if (parsedJson['response']['Action'] == "SUCCESS" ||
            parsedJson['response']['Action'] == "POPUPCUSTOM" ||
            parsedJson['response']['Action'] == "OKRECUPD") {
          Navigator.of(Get.context!).pop();
          clear();
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Your company affiliate request".tr,
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    Center(
                        child: Text(
                            "We'll let you know when it's been approved.".tr,
                            style: pRegular13,
                            textAlign: TextAlign.center)),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () {
                        Get.off(() => CompanyAffiliateMenuScreen(),
                            preventDuplicates: true);
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
      });
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  clear() {
    affiliateReasonController.clear();
    affiliateCompanyNameController.clear();
  }
}
