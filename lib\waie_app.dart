import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/locale_string.dart';
import 'package:waie_app/view/screen/auth/login_with_email_screen.dart';
import 'package:waie_app/view/screen/login_manager/login_manager_screen.dart';
import 'package:waie_app/view/screen/login_manager/login_manager_with_email_screen.dart';
import 'main.dart';
import 'view/screen/splash_screen/splash_screen.dart';

class WaieApp extends StatelessWidget {
  const WaieApp({super.key});
  @override
  Widget build(BuildContext context) {
    String languageCode =
        myStorage!.getString(AppConstant.LANGUAGE_CODE) ?? 'ar';
    String countryCode = myStorage!.getString(AppConstant.COUNTRY_CODE) ?? 'Ar';
    Constants.selected_language = languageCode;
    Constants.IsAr_App = Constants.selected_language == "ar" ? "true" : "false";

    Locale locale = Locale(languageCode, countryCode);
    return GetMaterialApp(
      theme: ThemeData(
        fontFamily: "SF Pro Display",
      ),
      translations: LocaleString(),
      locale: locale,
      fallbackLocale: locale,
      builder: (context, child) {
        if (locale.languageCode == 'ar') {
          DefaultImages.backIcn = "asset/image/svg_image/next.svg";
          DefaultImages.nextIcn = 'asset/image/svg_image/back.svg';
        } else {
          DefaultImages.backIcn = 'asset/image/svg_image/back.svg';
          DefaultImages.nextIcn = 'asset/image/svg_image/next.svg';
        }
        return child!;
      },
      debugShowCheckedModeBanner: false,
      // home: WelcomeScreen(),
      // home: LoginManagerScreen(),
      // home: LoginManagerScreen(),
      
      home:
          // const LoginManagerWithEmailScreen(), //LoginManagerWithEmailScreen(), //LoginWithEmailScreen(),
          const SplashScreen(), //LoginManagerWithEmailScreen(), //LoginWithEmailScreen(),
      // const LoginWithEmailScreen(),
      //home: DashBoardManagerScreen(currantIndex: 0,),
    );
  }
}
