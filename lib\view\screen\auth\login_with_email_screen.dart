// ignore_for_file: must_be_immutable, prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:local_auth/local_auth.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/core/controller/auth/auth_controller.dart';
import 'package:waie_app/core/controller/splash_controller/splash_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/prefer.dart';
import 'package:waie_app/utils/regDB.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/auth_background.dart';
import 'package:waie_app/view/screen/auth/digital_coupon/verify_mobile_number_screen.dart';
import 'package:waie_app/view/screen/auth/forgot_password_screen/forget_password_screen.dart';
import 'package:waie_app/view/screen/auth/sign_up_screen.dart';
import 'package:waie_app/view/screen/auth/tfa_otp_screen.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:http/http.dart';
import 'package:waie_app/models/profile.dart';
import '../../../core/controller/login_manager_controller/login_manager_controller.dart';
import '../../../utils/api_endpoints.dart';
import '../../../utils/images.dart';
import '../../../utils/validator.dart';
import '../../widget/common_snak_bar_widget.dart';
import '../../widget/loading_widget.dart';
import '../login_manager/login_manager_screen.dart';
import 'package:path_provider/path_provider.dart';

class LoginWithEmailScreen extends StatefulWidget {
  const LoginWithEmailScreen({super.key});

  @override
  State<LoginWithEmailScreen> createState() => _LoginWithEmailScreenState();
}

enum SupportState {
  unknown,
  supported,
  unSupported,
}

class _LoginWithEmailScreenState extends State<LoginWithEmailScreen> {
  AuthController authController = Get.put(AuthController());
  SplashController splashController = Get.put(SplashController());
  LoginManagerController loginManagerController =
      Get.put(LoginManagerController());
  final LocalAuthentication auth = LocalAuthentication();
  SupportState supportState = SupportState.unknown;
  List<BiometricType>? availableBiometrics;

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  RegisterDatabase db = RegisterDatabase();
  // reference the hive register box
  final _isReg = Hive.box('isReg_DB');
  final _isRemember = Hive.box('isRemember_DB');
  final _isActivate = Hive.box('isActivate_DB');

  @override
  void initState() {
    auth.isDeviceSupported().then(
          (bool isSupported) => setState(
            () => supportState =
                isSupported ? SupportState.supported : SupportState.unSupported,
          ),
        );

    print("===============================================================");
    print("_isReg");
    //check if there is no current register userinfo, then it is the 1st time ever opening the app
    //then let user register login info
    if (_isReg.get("regUser") == null) {
      //db.createUserInfoData();
    } else {
      db.loadData();
    }
    print("===============================================================");
    print("_isRemember");
    //check if there is no current register userinfo, then it is the 1st time ever opening the app
    //then let user register login info
    if (_isRemember.get("regRememberMe") == null) {
      //db.createUserInfoData();
    } else {
      db.loadRememberMe();
      final regRememberMe = _isRemember.get('regRememberMe');
      print('regRememberMe is $regRememberMe .');
      print('username is ${regRememberMe?['username']}');
      print('active is ${regRememberMe?['status']}');

      setState(() {
        authController.emailController.text = regRememberMe?['username'];
        authController.isRemember.value =
            regRememberMe?['status'] == "Y" ? true : false;
      });
      print(
          "authController.emailController.text >>>> ${authController.emailController.text}");
      print(
          "authController.isRemember.value >>>> ${authController.isRemember.value}");
    }
    print("===============================================================");
    print("_isActivate");
    //check if there is no current register userinfo, then it is the 1st time ever opening the app
    //then let user register login info
    if (_isActivate.get("regActivate") == null) {
      //db.createUserInfoData();
      print('regActivate no data');
    } else {
      db.loadActivate();
      final regActivate = _isActivate.get('regActivate');
      print('active is ${regActivate?['status']}');
    }
    print("===============================================================");
    super.initState();
    startTime();
  }

  Future<void> checkBiometric() async {
    late bool canCheckBiometric;

    try {
      canCheckBiometric = await auth.canCheckBiometrics;
      print("Biometric supported: $canCheckBiometric");
    } on PlatformException catch (e) {
      print(e);
      canCheckBiometric = false;
    }
  }

  Future<void> getAvailableBiometrics() async {
    late List<BiometricType> biometricTypes;
    try {
      biometricTypes = await auth.getAvailableBiometrics();
      print("supported biometrics: $biometricTypes");
    } on PlatformException catch (e) {
      print(e);
    }

    if (!mounted) {
      return;
    }
    setState(() {
      availableBiometrics = biometricTypes;
    });
  }

  Future<void> authenticateWithBiometrics() async {
    try {
      final authenticated = await auth.authenticate(
        localizedReason: 'Authenticate with fingerprint or Face ID',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
          useErrorDialogs: true,
        ),
      );

      if (!mounted) {
        return;
      }

      if (authenticated) {
        // Navigator.push(
        //     context, MaterialPageRoute(builder: (context) => const Home()));

        final regUser = _isReg.get('regUser');
        print('List is $regUser');
        print('username is ${regUser?['username']}');
        print('password is ${regUser?['password']}');
        print('status is ${regUser?['status']}');
        login(regUser?['username'], regUser?['password']);
      }
    } on PlatformException catch (e) {
      print(e);
      return;
    }
  }

  startTime() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    print("connectivityResult");
    print(connectivityResult);
    if (connectivityResult == ConnectivityResult.none) {
      // ignore: use_build_context_synchronously
      // ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
      //     content: Text(
      //         'Please turn on your wifi or mobile data and restart the App')));
      await showDialog(
        barrierDismissible: false,
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(16),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                verticalSpace(14),
                Text("NO INTERNET CONNECTION".trr,
                    style: pBold20.copyWith(
                      color: AppColor.cRedText,
                    )),
                verticalSpace(24),
                Text(
                  "Please turn on your wifi or mobile data".trr,
                  style: pRegular16,
                  textAlign: TextAlign.center,
                ),
                Text(
                  "and restart this application".trr,
                  style: pRegular16,
                  textAlign: TextAlign.center,
                ),
                verticalSpace(24),
                TextButton(
                    onPressed: () {
                      exit(0);
                    },
                    child: Text(
                      "OK".trr,
                      style: pBold18,
                    ))
              ],
            ),
          );
        },
      );
    } else {
      var onResponse = await splashController.checkOldAppTerminate();
      print("onResponse");
      print(onResponse);

      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      // if (onResponse[0]["RESULT"] == "MANDATORY") {
      //   _launchURL();
      // }
      if (onResponse[0]["RESULT"] == "TERMINATED") {
        var terminateMsg = onResponse[0]["REMARKS"];
        var terminateLink = onResponse[0]["LINK"];
        await showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              titlePadding: EdgeInsets.zero,
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    alignment: FractionalOffset.topRight,
                    child: IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: const Icon(Icons.clear),
                    ),
                  ),
                  Text(
                    "NOTICE".trr,
                    style: pBold20.copyWith(
                      color: AppColor.cRedText,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // verticalSpace(14),
                  // Text("APP TERMINATED".trr,
                  //     style: pBold20.copyWith(
                  //       color: AppColor.cRedText,
                  //     )),
                  // verticalSpace(24),
                  Text(
                    "$terminateMsg".trr,
                    style: pRegular16,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "Download now".trr,
                    onPressed: () {
                      if (Platform.isIOS) {
                        _launchNewURL(terminateLink);
                      } else {
                        _launchNewURL(terminateLink);
                      }
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    }
  }

  _launchNewURL(terminateLink) async {
    if (Platform.isIOS) {
      //splashController.updateURLIOS;
      if (await canLaunch("$terminateLink")) {
        await launch("$terminateLink");
      } else {
        throw 'Could not launch ${"$terminateLink"}';
      }
    } else {
      //splashController.updateURLANDROID;
      if (await canLaunch("$terminateLink")) {
        await launch("$terminateLink");
      } else {
        throw 'Could not launch ${"$terminateLink"}';
      }
    }
  }

  _launchURL() async {
    if (Platform.isIOS) {
      splashController.updateURLIOS;
      if (await canLaunch(splashController.updateURLIOS)) {
        await launch(splashController.updateURLIOS);
      } else {
        throw 'Could not launch ${splashController.updateURLIOS}';
      }
    } else {
      splashController.updateURLANDROID;
      if (await canLaunch(splashController.updateURLANDROID)) {
        await launch(splashController.updateURLANDROID);
      } else {
        throw 'Could not launch ${splashController.updateURLANDROID}';
      }
    }
  }

  clearStorage() {
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');
    final tagOrderRefund = GetStorage();
    final topupOrderRefund = GetStorage();
    final vehicle = GetStorage();
    final getOTPDataVerify = GetStorage();
    final isPinblock = GetStorage();
    final isDCBlock = GetStorage();
    Prefs.clear();
    //Get.offAll(() => LoginManagerScreen());
    vehicle.erase();
    tagOrderRefund.erase();
    topupOrderRefund.erase();
    getOTPDataVerify.erase();
    isPinblock.erase();
    isDCBlock.erase();
    userStorage.erase();
    custsData.erase();
    usersData.erase();
  }

  login(String email, String pass) async {
    Loader.showLoader();
    clearStorage();
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');

    db.loadData();

    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ' + base64Encode(utf8.encode('$username:$password'));
      print(email);
      print(pass);

      //String url = "https://localhost:44383//api/CUSTLOGIN";

      print(ApiEndPoints.baseUrl);
      String url =
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.signInEmail;
      //"https://devinttest.aldrees.com/api/CUSTLOGIN";
      log("login url $url");
      print(url);
      // String url="https://devapi.aldrees.com//api/CUSTLOGIN";
      var response = await post(Uri.parse(url), headers: <String, String>{
        'authorization': basicAuth,
        "Access-Control-Allow-Origin": "*", // Required for CORS support to work
        "Access-Control-Allow-Credentials":
            "true", // Required for cookies, authorization headers with HTTPS
        "Access-Control-Allow-Headers":
            "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      }, body: {
        'username': email,
        'password': pass,
        'isEncrypted': "false"
      });

      print(response.statusCode);
      log("login response body ${response.body}");
      // print(basicAuth);
      //Loader.hideLoader();
      print('Loader loads');
      if (response.statusCode == 200) {
        print('Suucessfully Fetch');

        Map cardInfo = json.decode(response.body);
        print("json.decode(response.body) ${json.decode(response.body)}");
        print('1');
        print(cardInfo);
        Profile userData = Profile.fromJson(cardInfo);
        print('2');

        if (userData.returnMessage!.isValidTransaction!) {
          if (userData.returnMessage!.action == 'POPUP') {
            //loginFailWidget(userData.returnMessage!.message);
            commonToast(userData.returnMessage!.message);
            Loader.hideLoader();
          } else {
            String notifUrl =
                ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getNotifCount;
            var notifResponse =
                await post(Uri.parse(notifUrl), headers: <String, String>{
              'authorization': basicAuth,
              "Access-Control-Allow-Credentials": 'true',
              "Access-Control-Allow-Headers":
                  "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "POST, OPTIONS"
            }, body: {
              "custid": userData.auUsers?.custid,
              "isAR": Constants.IsAr_App,
            });
            //print(notifResponse.body);
            print(
                "Constants.notifCount >>>> ${json.decode(notifResponse.body)}");

            var isCount = json.decode(notifResponse.body);

            print("isCount >>>> $isCount");

            Constants.notifCount = isCount;

            print(userData.auUsers?.custid);
            var custData = jsonDecode(jsonEncode(userData.auCust));
            var usrData = jsonDecode(jsonEncode(userData.auUsers));
            print(custData);
            if (custData['IS_VERIFIED'] == 'Y') {
              Constants.custIsVerified == "Y";
            }
            Constants.custAcctStatus = userData.auCust!.acctstatus!;
            Constants.custAcctType = userData.auCust?.accttype;

            print(custData['MOBILENO']);
            print(usrData['CUSTID']);
            print(usrData['EMAILID']);
            print(usrData['USERNAME']);
            print("usrData $usrData");
            print(userData.auUsers!.password);
            SharedPreferences sharedUser =
                await SharedPreferences.getInstance();
            // Map decode_options = jsonDecode(jsonString);
            String user = jsonEncode(cardInfo);
            sharedUser.setString('user', user);
            sharedUser.setString('userid', userData.auCust!.custid!);
            sharedUser.setString('regType', userData.auCust!.regtype);
            sharedUser.setString('username', usrData['USERNAME']);
            userStorage.writeIfNull('custid', userData.auCust!.custid!);
            userStorage.writeIfNull('emailid', userData.auCust!.emailid!);
            userStorage.writeIfNull('accttype', userData.auCust?.accttype);
            usersData.writeIfNull('usrData', usrData);
            custsData.writeIfNull('custData', custData);
            /* Get.to(() => DashBoardManagerScreen(
                currantIndex: 0,
              ));*/

            // if (userData.auUsers!.authflag == 'Y') {
            //   Get.to(() => TFAOTPScreen(userName: usrData['USERNAME']));
            // } else {
            //   authController.getUserAccess();
            // }
            authController.getUserAccess();
          }
        } else {
          Loader.hideLoader();
          print("Invalid ID Password");
          //loginFailWidget("Invalid ID or password");
          //commonToast("Invalid ID or password");
          if (userData.returnMessage!.message!
              .toString()
              .contains("ACCTLOCKEDMSG")) {
            commonToast("Your Account is lock, try after 5 minutes");
            Loader.hideLoader();
          } else if (userData.returnMessage!.message!
              .toString()
              .contains("INVALIDPASSWORD")) {
            commonToast("Invalid ID Password");
            Loader.hideLoader();
          } else {
            commonToast(userData.returnMessage!.message!.toString());
            Loader.hideLoader();
          }
        }
        return userData;
      } else {
        Loader.hideLoader();
        print('Login Fail');
        commonToast('Login Fail');
        print(response.statusCode.toString());
        loginFailWidget(response.statusCode.toString());
      }
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: AuthBackGroundWidget(
            widget: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Obx(() {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      verticalSpace(Get.height * 0.02),
                      Text(
                        "Log in to your account".trr,
                        style: pBold28,
                      ),
                      verticalSpace(24),
                      CommonTextField(
                        controller: authController.emailController,
                        labelText: '',
                        hintText: "Username / Email id".trr,
                        filled: true,
                        fillColor: AppColor.cFilled,
                        // initialValue: '<EMAIL>',
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          return Validator.validateRequired(value);
                          //return Validator.validateEmail(value);
                        },
                      ),
                      verticalSpace(16),
                      CommonTextField(
                          controller: authController.passwordController,
                          labelText: '',
                          hintText: "Password".trr,
                          obscuringCharacter: '*',
                          filled: true,
                          fillColor: AppColor.cFilled,
                          obscureText: authController.isisLoginObscure.value,
                          validator: (value) {
                            return Validator.validatePassword(value);
                          },
                          suffix: GestureDetector(
                              onTap: () {
                                authController.isisLoginObscure.value =
                                    !authController.isisLoginObscure.value;
                              },
                              child: assetSvdImageWidget(
                                  image: authController.isisLoginObscure.value
                                      ? DefaultImages.eyeOffIcn
                                      : DefaultImages.eyeIcn))),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: Checkbox(
                                  value: authController.isRemember.value,
                                  onChanged: (value) {
                                    if (value == false) {
                                      db.deleteRememberMe();
                                    }
                                    setState(() {
                                      authController.isRemember.value = value!;
                                    });
                                  },
                                  activeColor: AppColor.themeBlueColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  materialTapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                ),
                              ),
                              horizontalSpace(8),
                              Text(
                                'Remember me'.trr,
                                style: pRegular14,
                              )
                            ],
                          ),
                          TextButton(
                              onPressed: () {
                                Get.to(() => ForgotPasswordScreen());
                              },
                              child: Text(
                                'Forgot password?'.trr,
                                style: pMedium14.copyWith(
                                    decoration: TextDecoration.underline),
                              ))
                        ],
                      ),

                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     Row(
                      //       children: [
                      //         SizedBox(
                      //           width: 20,
                      //           height: 20,
                      //           child: Checkbox(
                      //             value: authController.isRemember.value,
                      //             onChanged: (value) {
                      //               authController.isRemember.value = value!;
                      //             },
                      //             activeColor: AppColor.themeBlueColor,
                      //             shape: RoundedRectangleBorder(
                      //               borderRadius: BorderRadius.circular(4),
                      //             ),
                      //             materialTapTargetSize:
                      //                 MaterialTapTargetSize.shrinkWrap,
                      //           ),
                      //         ),
                      //         horizontalSpace(8),
                      //         Text(
                      //           'Remember me'.trr,
                      //           style: pRegular14,
                      //         )
                      //       ],
                      //     ),
                      //     TextButton(
                      //         onPressed: () {
                      //           Get.to(() => ForgotPasswordScreen());
                      //         },
                      //         child: Text(
                      //           'Forgot password?'.trr,
                      //           style: pMedium14.copyWith(
                      //               decoration: TextDecoration.underline),
                      //         ))
                      //   ],
                      // ),
                      verticalSpace(10),
                      CommonButton(
                        title: 'Sign In'.trr,
                        onPressed: () async {
                          if (formKey.currentState!.validate()) {
                            log('done');
                            // ignore: unnecessary_null_comparison
                            if (authController.emailController.text != null &&
                                authController.isRemember.value == true) {
                              print(
                                  "===============================================================");
                              //db.createUserInfoData("viajedorryan", "V1ajedor", "Y");
                              db.createRememberMe(
                                  authController.emailController.text
                                      .toString(),
                                  "Y");
                              print(
                                  "===============================================================");
                              db.loadRememberMe();
                              print(
                                  "===============================================================");
                            }
                            var onResponse =
                                await splashController.checkUpdate();
                            print("onResponse");
                            print(onResponse);

                            if (onResponse[0]["RESULT"] == "MANDATORY") {
                              _launchURL();
                            } else {
                              login(
                                  authController.emailController.text
                                      .toString(),
                                  //'<EMAIL>',
                                  authController.passwordController.text
                                      .toString());
                            }
                            // authController.emailController.text =
                            //     'i_custom_rate',
                            // //'<EMAIL>',
                            // authController.passwordController.text =
                            //     '123456');
                            //'alwazeen2018');
                            // postRequest(authController.emailController.text,authController.passwordController.text.toString());
                            // Get.offAll(() => WelcomeScreen());
                            // Get.offAll(()=>DashBoardManagerScreen(currantIndex: 0,));
                          }
                        },
                      ),
                      verticalSpace(10),
                      Text(
                        "or".trr,
                        style: pRegular14,
                      ),
                      verticalSpace(10),
                      Row(
                        children: [
                          if (Platform.isAndroid || Platform.isWindows)
                            if (_isReg.get("regUser") != null)
                              // Expanded(
                              //   flex: 1,
                              //   child: GestureDetector(
                              //     onTap: authenticateWithBiometrics,
                              //     child: Image.asset(
                              //       DefaultImages.fingerprintblack,
                              //       width: 50,
                              //       height: 50,
                              //       fit: BoxFit.cover,
                              //     ),
                              //   ),
                              // ),
                              Material(
                                child: InkWell(
                                  onTap: authenticateWithBiometrics,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: AppColor
                                          .cWhite, // Set your desired background color here
                                      borderRadius: BorderRadius.circular(6.0),
                                      border: Border.all(
                                          color: AppColor.themeDarkBlueColor,
                                          width: 1),
                                    ),
                                    padding: EdgeInsets.all(5.0),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(6.0),
                                      child: Image.asset(
                                        DefaultImages.fingerprintblack,
                                        width: 33,
                                        height: 33,
                                        fit: BoxFit.cover,
                                        color: AppColor.cDarkBlueFont,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          if (Platform.isIOS)
                            if (_isReg.get("regUser") != null)
                              Material(
                                child: InkWell(
                                  onTap: authenticateWithBiometrics,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: AppColor
                                          .cWhite, // Set your desired background color here
                                      borderRadius: BorderRadius.circular(6.0),
                                      border: Border.all(
                                          color: AppColor.themeDarkBlueColor,
                                          width: 1),
                                    ),
                                    padding: EdgeInsets.all(5.0),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(6.0),
                                      child: Image.asset(
                                        DefaultImages.faceidblack,
                                        width: 33,
                                        height: 33,
                                        fit: BoxFit.cover,
                                        color: AppColor.cDarkBlueFont,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          Expanded(
                            flex: 3,
                            child: CommonBorderButton(
                              title: 'Sign in with phone'.trr,
                              onPressed: () async {
                                // Get.back();
                                var onResponse =
                                    await splashController.checkUpdate();
                                print("onResponse");
                                print(onResponse);

                                if (onResponse[0]["RESULT"] == "MANDATORY") {
                                  _launchURL();
                                } else {
                                  Get.to(() => LoginManagerScreen());
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                      verticalSpace(10),
                      Text.rich(
                        TextSpan(
                          text: 'Don’t have an account?'.trr + " ",
                          style: pRegular14,
                          children: <TextSpan>[
                            TextSpan(
                              text: 'Sign up now'.trr,
                              style:
                                  pBold14.copyWith(color: AppColor.cBlueFont),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () async {
                                  var onResponse =
                                      await splashController.checkUpdate();
                                  print("onResponse");
                                  print(onResponse);

                                  if (onResponse[0]["RESULT"] == "MANDATORY") {
                                    _launchURL();
                                  } else {
                                    Get.to(() => CreateAccountScreen());
                                  }
                                },
                            ),
                          ],
                        ),
                      ),
                      //verticalSpace(30),
                      verticalSpace(10),
                      CommonIconBorderButton(
                        iconData: DefaultImages.scannerIcn,
                        title: 'Digital Coupon'.trr,
                        onPressed: () async {
                          var onResponse = await splashController.checkUpdate();
                          print("onResponse");
                          print(onResponse);

                          if (onResponse[0]["RESULT"] == "MANDATORY") {
                            _launchURL();
                          } else {
                            Get.to(() => VerifyMobileNumberScreen());
                          }
                        },
                        bColor: AppColor.cTransparent,
                        btnColor: AppColor.lightDarkBlueColor,
                        width: 180,
                      ),
                    ],
                  );
                }),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

Widget loginFailWidget(String msg) {
  return Container(
    width: Get.width,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(6),
      color: AppColor.cLightGrey,
    ),
    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
    child: FutureBuilder<String>(
      future: null,
      builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return CircularProgressIndicator(); // Display a loading indicator while fetching data
        } else if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        } else {
          return Text(
            msg.trr,
            style: pBold14,
          );
        }
      },
    ), /*Text.rich(
        TextSpan(
          text: "Current balance".trr + ": ",
          style: pBold14,
          children: [
            TextSpan(text: balance, style: pSemiBold14),
          ],
        ),
      ),*/
  );
}
