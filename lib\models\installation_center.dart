import 'dart:convert';

List<InstallationCenterModel> installationCenterModelFromJson(String str) =>
    List<InstallationCenterModel>.from(
        json.decode(str).map((x) => InstallationCenterModel.fromJson(x)));

String installationCenterModelToJson(List<InstallationCenterModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class InstallationCenterModel {
  var stnNo;
  String stationName;
  String stnCoor;
  String latitude;
  String longitude;
  String placeCode;
  String placeDesc;
  String startTime;
  String endTime;
  String stationStatus;
  String wrkDays;

  InstallationCenterModel({
    required this.stnNo,
    required this.stationName,
    required this.stnCoor,
    required this.latitude,
    required this.longitude,
    required this.placeCode,
    required this.placeDesc,
    required this.startTime,
    required this.endTime,
    required this.stationStatus,
    required this.wrkDays,
  });

  factory InstallationCenterModel.fromJson(Map<String, dynamic> json) =>
      InstallationCenterModel(
        stnNo: json["STN_NO"] ?? 0,
        stationName: json["STATION_NAME"] ?? "",
        stnCoor: json["STN_COOR"] ?? "",
        latitude: json["LATITUDE"] ?? "0.0",
        longitude: json["LONGITUDE"] ?? "0.0",
        placeCode: json["PLACE_CODE"] ?? "",
        placeDesc: json["PLACE_DESC"] ?? "",
        startTime: json["START_TIME"] ?? "",
        endTime: json["END_TIME"] ?? "",
        stationStatus: json["STATION_STATUS"] ?? "",
        wrkDays: json["WRK_DAYS"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "STN_NO": stnNo,
        "STATION_NAME": stationName,
        "STN_COOR": stnCoor,
        "LATITUDE": latitude,
        "LONGITUDE": longitude,
        "PLACE_CODE": placeCode,
        "PLACE_DESC": placeDesc,
        "START_TIME": startTime,
        "END_TIME": endTime,
        "STATION_STATUS": stationStatus,
        "WRK_DAYS": wrkDays,
      };
}
