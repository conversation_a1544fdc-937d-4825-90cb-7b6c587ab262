import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../menu_screen/user_management_screen/user_management_screen.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import '../../../utils/colors.dart';
import '../../../utils/images.dart';
import 'package:get/get.dart';
import 'dart:ui' as ui;


Widget gasStationsBottomSheetWidget({
  required String title,
  required String subTitle,
  required String products,
  //required String phoneNo,
  required double latitude,
  required double longitude,
  required String coordinates,
}) {
  return Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
    ),
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: pSemiBold16,
        ),
        verticalSpace(6),
        Text(
          subTitle,
          style: pRegular12,
        ),

        verticalSpace(20),
        userDataRowWidget(title: 'Products', value: products),
        //verticalSpace(20),
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //   children: [
        //     userDataRowWidget(title: 'Phone', value: '+$phoneNo'),
        //     GestureDetector(
        //         onTap: () {
        //           Clipboard.setData(ClipboardData(text: phoneNo));
        //         },
        //         child: assetSvdImageWidget(image: DefaultImages.clipboardIcn)),
        //   ],
        // ),
        verticalSpace(28),
        gotoMapButton(onTap: () async {
          final String googleMapsUrl =
              '${AppConstant.googleMapUrl}$latitude,$longitude';
          final Uri url = Uri.parse(googleMapsUrl);
          if (!await launchUrl(url)) {
            print('Could not launch $googleMapsUrl');
          }
        }),
        verticalSpace(16),
      ],
    ),
  );
}

Widget locationBottomSheetWidget({
  required String title,
  required String subTitle,
  required String openNow,
  required String workingHour,
  required String phoneNo,
  required double latitude,
  required double longitude,
}) {
  return Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
    ),
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: pSemiBold16,
        ),
        verticalSpace(6),
        Text(
          subTitle,
          style: pRegular12,
        ),
        verticalSpace(32),
        userDataRowWidget(title: 'Open now?', value: openNow),
        verticalSpace(20),
        userDataRowWidget(title: 'Opening hours', value: workingHour),
        verticalSpace(20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            userDataRowWidget(title: 'Phone', value: '+$phoneNo'),
            GestureDetector(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: phoneNo));
                },
                child: assetSvdImageWidget(image: DefaultImages.clipboardIcn)),
          ],
        ),
        verticalSpace(28),
        gotoMapButton(onTap: () {
          MapsLauncher.launchCoordinates(latitude, longitude);
        }),
        verticalSpace(16),
      ],
    ),
  );
}

Widget listDataContainer({required String title, required String subTitle}) {
  return Container(
    decoration: BoxDecoration(
        color: AppColor.lightBlueColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppColor.cLightGrey)),
    width: Get.width,
    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: pSemiBold17,
        ),
        verticalSpace(8),
        Text(
          subTitle,
          style: pRegular13,
        ),
      ],
    ),
  );
}

Widget gotoMapButton({required Function() onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      height: 44,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: AppColor.cBlack, width: 1)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(DefaultImages.googleMapIcn, height: 16, width: 15),
          horizontalSpace(11),
          Text(
            'Show on Google Maps'.trr,
            style: pRegular14.copyWith(color: AppColor.cDarkBlueFont),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}

Future<BitmapDescriptor> createMarkerImageFromAsset(
    BuildContext context, String image) async {
  ByteData data = await rootBundle.load(image);
  ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
      targetWidth: 150);
  ui.FrameInfo fi = await codec.getNextFrame();
  (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
      .buffer
      .asUint8List();
  return BitmapDescriptor.fromBytes(
      (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
          .buffer
          .asUint8List());
}
