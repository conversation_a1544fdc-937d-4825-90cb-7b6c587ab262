import 'dart:developer'; // Import the developer package to use the `log` function.

/// An extension on all objects to add logging capabilities.
extension LoggerExtensions on Object {
  /// Logs an informational message.
  void logInfo(String message) {
    _logWithType("INFO", "❕❕❕❕", message);
  }

  /// Logs an error message.
  void logError(String message) {
    _logWithType("ERROR", "🛑🆘🛑🆘", message);
  }

  /// Logs a warning message.
  void logWarning(String message) {
    _logWithType("WARNING", "⚠️⚠️⚠️⚠️", message);
  }

  /// Logs a success message.
  void logSuccess(String message) {
    _logWithType("SUCCESS", "✅✅✅✅", message);
  }

  /// A private helper method to format and log the message.
  void _logWithType(String type, String symbols, String message) {
    final className = runtimeType.toString(); // Gets the name of the calling class.
    final timestamp = DateTime.now().toIso8601String(); // Formats the current time as a string.
    log("$symbols [$type] [$timestamp] From $className: $message $symbols"); // Logs the message with symbols and details.
  }
}
