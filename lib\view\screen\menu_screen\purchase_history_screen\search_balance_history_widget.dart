// ignore_for_file: prefer_const_constructors, prefer_const_constructors_in_immutables

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/core/controller/menu_controller/purchase_history_controller/balancetopup_history_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_order_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'balance_topup_history_screen.dart';
import 'order_screen.dart';

class SearchBalanceHistoryWidget extends StatefulWidget {
  SearchBalanceHistoryWidget({super.key});

  @override
  State<SearchBalanceHistoryWidget> createState() => _SearchBalanceHistoryWidgetState();
}

class _SearchBalanceHistoryWidgetState extends State<SearchBalanceHistoryWidget> {
  BalanceTopUpHistoryController balanceTopUpHistoryController = Get.put(BalanceTopUpHistoryController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    balanceTopUpHistoryController.itemList.clear();
  }

  void filterSearchResults(String query) {
    balanceTopUpHistoryController.itemList.value = balanceTopUpHistoryController.balanceTopUpHistoryList1
        .where((item) => item.CUSTID.toString().toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Get.height - 60,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      padding: EdgeInsets.all(16),
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalSpace(16),
            Row(
              children: [
                Expanded(
                  child: CommonTextField(
                    controller: balanceTopUpHistoryController.searchController.value,
                    labelText: '',
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(12),
                      child: assetSvdImageWidget(image: DefaultImages.searchIcn, width: 24, height: 24),
                    ),
                    hintText: 'Search'.trr,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        balanceTopUpHistoryController.itemList.clear();
                        balanceTopUpHistoryController.itemList.refresh();
                      } else {
                        balanceTopUpHistoryController.searchController.refresh();
                        filterSearchResults(value);
                      }
                    },
                  ),
                ),
                balanceTopUpHistoryController.searchController.value.text.isEmpty
                    ? SizedBox()
                    : cancelButton(
                        () {
                          balanceTopUpHistoryController.searchController.value.clear();
                          balanceTopUpHistoryController.searchController.refresh();
                          balanceTopUpHistoryController.itemList.clear();
                          balanceTopUpHistoryController.itemList.refresh();
                        },
                      )
              ],
            ),
            verticalSpace(16),
            balanceTopUpHistoryController.itemList.isEmpty
                ? Expanded(
                    child: Center(
                        child: Text(
                    "No matches".trr,
                    style: pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                  )))
                : ListView.builder(
                    itemCount: balanceTopUpHistoryController.itemList.length,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      var data = balanceTopUpHistoryController.itemList[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: balanceTopUpWidget(
                          code: data['code'],
                          orderDate: data['orderDate'],
                          status: data['status'].toString().trr,
                          paymentMethod: data['paymentMethod'].toString().trr,
                         // paymentMethod2: data['paymentMethod2'].toString().trr,
                          paymentMethod2: "",
                          amount: data['price'],
                          color: AppColor.cLightGreen,
                          textColor: AppColor.cGreen,
                          onTap: () {
                            showModalBottomSheet(
                              context: context,
                              shape:
                                  RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
                              builder: (context) {
                                return orderActionWidget(
                                  code: data['code'],
                                  isShowCancelOrder: data['status'] == "Claimed",
                                  printOrder: () {},
                                  downloadOrder: () {},
                                  cancelOrder: () {},
                                );
                              },
                            );
                          },
                        ),
                      );
                    },
                  ),
          ],
        );
      }),
    );
  }
}
