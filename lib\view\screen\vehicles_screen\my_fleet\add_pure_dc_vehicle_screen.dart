// ignore_for_file: prefer_const_constructors, must_be_immutable, prefer_const_constructors_in_immutables, avoid_print, invalid_use_of_protected_member

import 'package:chips_choice/chips_choice.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/add_pure_dc_vehicle_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../utils/validator.dart';
import '../../../widget/common_button.dart';
import '../../menu_screen/user_management_screen/new_user_screen.dart';

class AddPureDCVehicleScreen extends StatefulWidget {
  final bool? isAdd;
  final String title;

  AddPureDCVehicleScreen({super.key, this.isAdd = false, required this.title});

  @override
  State<AddPureDCVehicleScreen> createState() => _AddPureDCVehicleScreenState();
}

class _AddPureDCVehicleScreenState extends State<AddPureDCVehicleScreen> {
  AddPureDcVehicleController addPureDcVehicleController =
      Get.put(AddPureDcVehicleController());
  VehicleController vehicleController = Get.put(VehicleController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    addPureDcVehicleController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              simpleMyAppBar(
                  title: widget.title,
                  onTap: () {
                    Get.back();
                  },
                  backString: "Vehicles".trr),
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Obx(() {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          horizontalDivider(),
                          addDcTitleRowWidget(
                            title: "Vehicle details".trr,
                            isSelected: addPureDcVehicleController
                                .isVehicleDetail.value,
                            onTap: () {
                              addPureDcVehicleController.isVehicleDetail.value =
                                  !addPureDcVehicleController
                                      .isVehicleDetail.value;
                            },
                          ),
                          verticalSpace(16),
                          addPureDcVehicleController.isVehicleDetail.value
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "${"Plate".trr}#",
                                      style: pRegular13,
                                    ),
                                    verticalSpace(6),
                                    Container(
                                      height: 44,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                              color: AppColor.cLightGrey)),
                                      child: Row(
                                        children: [
                                          platDropDownWidget(
                                              value: addPureDcVehicleController
                                                  .selectedPlatValue.value,
                                              list: addPureDcVehicleController
                                                  .plateList,
                                              onChanged: (value) {
                                                addPureDcVehicleController
                                                    .selectedPlatValue
                                                    .value = value.toString();
                                              }),
                                          Expanded(
                                            child: plateTextFieldWidget(
                                                addPureDcVehicleController
                                                    .plateController,
                                                'Enter Plate number'.trr),
                                          )
                                        ],
                                      ),
                                    ),
                                    verticalSpace(16),
                                    CommonHintDropdownWidget(
                                      hint: "Select vehicle type".trr,
                                      labelText: "Vehicle type".trr,
                                      value: addPureDcVehicleController
                                          .selectedVehicleValue.value,
                                      list: addPureDcVehicleController
                                          .vehicleTypeList,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedVehicleValue.value = value;
                                      },
                                    ),
                                    verticalSpace(16),
                                    /* CommonHintDropdownWidget(
                                      hint: "Select Make".trr,
                                      labelText: "Make".trr,
                                      list: addPureDcVehicleController.makeList,
                                      value: addPureDcVehicleController
                                          .selectedMakeValue.value,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedMakeValue.value = value;
                                      },
                                    ),
                                    verticalSpace(16),*/
                                    /* CommonHintDropdownWidget(
                                      hint: "Select Model".trr,
                                      labelText: "Model".trr,
                                      list:
                                          addPureDcVehicleController.modelList,
                                      value: addPureDcVehicleController
                                          .selectedModelValue.value,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedModelValue.value = value;
                                      },
                                    ),
                                    verticalSpace(16),*/
                                    CommonHintDropdownWidget(
                                      hint: "Select Fuel".trr,
                                      labelText: "Fuel".trr,
                                      list: addPureDcVehicleController.fuelList,
                                      value: addPureDcVehicleController
                                          .selectedFuelValue.value,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedFuelValue.value = value;
                                      },
                                    ),
                                    verticalSpace(16),
                                    Row(
                                      children: [
                                        Expanded(
                                            flex: 1,
                                            child: CommonIconButton(
                                              title: '1',
                                              iconData:
                                                  addPureDcVehicleController
                                                          .isOne.value
                                                      ? DefaultImages.checkIcn
                                                      : null,
                                              btnColor:
                                                  addPureDcVehicleController
                                                          .isOne.value
                                                      ? AppColor
                                                          .themeDarkBlueColor
                                                      : AppColor.cLightGrey,
                                              textColor:
                                                  addPureDcVehicleController
                                                          .isOne.value
                                                      ? AppColor.cWhiteFont
                                                      : AppColor.cText,
                                              onPressed: () {
                                                addPureDcVehicleController
                                                    .isOne.value = true;
                                                addPureDcVehicleController
                                                    .isTwo.value = false;
                                              },
                                            )),
                                        horizontalSpace(8),
                                        Expanded(
                                            flex: 1,
                                            child: CommonIconButton(
                                              title: '2',
                                              iconData:
                                                  addPureDcVehicleController
                                                          .isTwo.value
                                                      ? DefaultImages.checkIcn
                                                      : null,
                                              btnColor:
                                                  addPureDcVehicleController
                                                          .isTwo.value
                                                      ? AppColor
                                                          .themeDarkBlueColor
                                                      : AppColor.cLightGrey,
                                              textColor:
                                                  addPureDcVehicleController
                                                          .isTwo.value
                                                      ? AppColor.cWhiteFont
                                                      : AppColor.cText,
                                              onPressed: () {
                                                addPureDcVehicleController
                                                    .isOne.value = false;
                                                addPureDcVehicleController
                                                    .isTwo.value = true;
                                              },
                                            )),
                                      ],
                                    ),
                                    verticalSpace(16),
                                    CommonTextField(
                                      controller: addPureDcVehicleController
                                          .driverNameController,
                                      labelText: 'Driver name'.trr,
                                      hintText: 'Enter driver’s name'.trr,
                                    ),
                                    verticalSpace(16),
                                    Text(
                                      "Offline limit per refuel".trr,
                                      style: pRegular13,
                                    ),
                                    verticalSpace(6),
                                    Container(
                                      height: 44,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                              color: AppColor.cLightGrey)),
                                      child: Row(
                                        children: [
                                          platDropDownWidget(
                                              value: addPureDcVehicleController
                                                  .selectedLimitValue.value,
                                              list: addPureDcVehicleController
                                                  .limitList,
                                              onChanged: (value) {
                                                addPureDcVehicleController
                                                    .selectedLimitValue
                                                    .value = value.toString();
                                              }),
                                          Expanded(
                                            child: plateTextFieldWidget(
                                                addPureDcVehicleController
                                                    .limitController,
                                                '00'),
                                          )
                                        ],
                                      ),
                                    ),
                                    verticalSpace(16),
                                    Text(
                                      "* ${"The offline limit is the maximum amount the driver can fill up at one time.".trr}",
                                      style: pRegular10.copyWith(fontSize: 11),
                                    ),
                                    verticalSpace(16),
                                    CommonTextField(
                                      controller: addPureDcVehicleController
                                          .driverPasswordController,
                                      labelText: 'Password'.trr,
                                      hintText: 'Set driver password'.trr,
                                    ),
                                    verticalSpace(16),
                                    CommonTextField(
                                      controller: addPureDcVehicleController
                                          .vehicleReferenceController,
                                      labelText: 'Vehicle reference name'.trr,
                                      hintText: 'e.g. Personal driver'.trr,
                                    ),
                                    verticalSpace(32)
                                  ],
                                )
                              : SizedBox(),
                          horizontalDivider(),
                          addDcTitleRowWidget(
                            title: "Vehicle level".trr,
                            isSelected:
                                addPureDcVehicleController.isVehicleLevel.value,
                            onTap: () {
                              addPureDcVehicleController.isVehicleLevel.value =
                                  !addPureDcVehicleController
                                      .isVehicleLevel.value;
                            },
                          ),
                          verticalSpace(16),
                          addPureDcVehicleController.isVehicleLevel.value
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CommonHintDropdownWidget(
                                      hint: "Select division".trr,
                                      labelText: "Division".trr,
                                      value: addPureDcVehicleController
                                          .selectDivisionValue.value,
                                      list: addPureDcVehicleController
                                          .divisionList,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectDivisionValue.value = value;
                                      },
                                    ),
                                    verticalSpace(16),
                                    CommonHintDropdownWidget(
                                      hint: "Select branch".trr,
                                      labelText: "Branch".trr,
                                      list:
                                          addPureDcVehicleController.branchList,
                                      value: addPureDcVehicleController
                                          .selectedBranchValue.value,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedBranchValue.value = value;
                                      },
                                    ),
                                    verticalSpace(16),
                                    CommonHintDropdownWidget(
                                      hint: "Select department".trr,
                                      labelText: "Department".trr,
                                      list: addPureDcVehicleController
                                          .departmentList,
                                      value: addPureDcVehicleController
                                          .selectedDepartmentValue.value,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedDepartmentValue
                                            .value = value;
                                      },
                                    ),
                                    verticalSpace(16),
                                    CommonHintDropdownWidget(
                                      hint: "Select operation".trr,
                                      labelText: "Operation".trr,
                                      list: addPureDcVehicleController
                                          .operationList,
                                      value: addPureDcVehicleController
                                          .selectedOperationValue.value,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedOperationValue
                                            .value = value;
                                      },
                                    ),
                                    verticalSpace(32)
                                  ],
                                )
                              : SizedBox(),
                          horizontalDivider(),
                          addDcTitleRowWidget(
                            title: "Refuel limits".trr,
                            isSelected:
                                addPureDcVehicleController.isRefuelLimits.value,
                            onTap: () {
                              addPureDcVehicleController.isRefuelLimits.value =
                                  !addPureDcVehicleController
                                      .isRefuelLimits.value;
                            },
                          ),
                          verticalSpace(16),
                          addPureDcVehicleController.isRefuelLimits.value
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CommonDropdownButtonWidget(
                                      labelText: "Quota type".trr,
                                      value: addPureDcVehicleController
                                          .selectedQuotaTypeValue.value,
                                      list: addPureDcVehicleController
                                          .quotaTypeList,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedQuotaTypeValue
                                            .value = value;
                                      },
                                    ),
                                    verticalSpace(16),
                                    CommonDropdownButtonWidget(
                                      labelText: "Quota class".trr,
                                      value: addPureDcVehicleController
                                          .selectedQuotaLimitValue.value,
                                      list: addPureDcVehicleController
                                          .quotaLimitList,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedQuotaLimitValue
                                            .value = value;
                                      },
                                      filledColor: AppColor.lightBlueColor,
                                    ),
                                    verticalSpace(16),
                                    CommonTextField(
                                      controller: addPureDcVehicleController
                                          .quotaValueController,
                                      labelText: "Quota value".trr,
                                      hintText: 'e.g. 00'.trr,
                                    ),
                                    /* CommonDropdownButtonWidget(
                                      labelText: "Quota value".trr,
                                      value: addPureDcVehicleController
                                          .selectedQuotaValue.value,
                                      list: addPureDcVehicleController
                                          .quotaValueList,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedQuotaValue.value = value;
                                      },
                                      filledColor: AppColor.lightBlueColor,
                                    ),*/
                                    verticalSpace(16),
                                    CommonTextField(
                                      controller: addPureDcVehicleController
                                          .WAIEquotaValueController,
                                      labelText: "WAIE quota balance".trr,
                                      hintText: 'e.g. 00'.trr,
                                    ),
                                    /* CommonDropdownButtonWidget(
                                      labelText: "WAIE quota balance".trr,
                                      value: addPureDcVehicleController
                                          .selectedQuotaBalanceValue.value,
                                      list: addPureDcVehicleController
                                          .quotaBalanceList,
                                      onChanged: (value) {
                                        addPureDcVehicleController
                                            .selectedQuotaBalanceValue
                                            .value = value;
                                      },
                                      filledColor: AppColor.lightBlueColor,
                                    ),*/
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 16),
                                      child: Row(
                                        children: [
                                          SizedBox(
                                            height: 24,
                                            width: 24,
                                            child: Checkbox(
                                              value: addPureDcVehicleController
                                                  .isDigitalCoupon.value,
                                              onChanged: (value) {
                                                addPureDcVehicleController
                                                    .isDigitalCoupon
                                                    .value = value!;
                                                if (value == false) {
                                                  addPureDcVehicleController
                                                      .isSticker.value = false;
                                                } else {
                                                  if (addPureDcVehicleController
                                                          .isSticker.value ==
                                                      true) {
                                                    addPureDcVehicleController
                                                        .coupType.value = "S";
                                                  } else {
                                                    addPureDcVehicleController
                                                        .coupType.value = "Q";
                                                  }
                                                }
                                              },
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6)),
                                              activeColor:
                                                  AppColor.themeDarkBlueColor,
                                            ),
                                          ),
                                          horizontalSpace(6),
                                          Text(
                                            "Can use Digital Coupon (DC)".trr,
                                            style: pRegular13,
                                          )
                                        ],
                                      ),
                                    ),
                                    addPureDcVehicleController
                                                .isDigitalCoupon.value ==
                                            true
                                        ? Column(
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 16,
                                                        vertical: 16),
                                                child: Row(
                                                  children: [
                                                    SizedBox(
                                                      height: 24,
                                                      width: 24,
                                                      child: Checkbox(
                                                        value:
                                                            addPureDcVehicleController
                                                                .isSticker
                                                                .value,
                                                        onChanged: (value) {
                                                          addPureDcVehicleController
                                                              .isSticker
                                                              .value = value!;
                                                        },
                                                        shape:
                                                            RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            6)),
                                                        activeColor: AppColor
                                                            .themeDarkBlueColor,
                                                      ),
                                                    ),
                                                    horizontalSpace(6),
                                                    Text(
                                                      "Sticker".trr,
                                                      style: pRegular13,
                                                    )
                                                  ],
                                                ),
                                              ),
                                              addPureDcVehicleController
                                                          .isSticker.value ==
                                                      false
                                                  ? CommonTextField(
                                                      controller:
                                                          addPureDcVehicleController
                                                              .driverMobileNoController,
                                                      labelText:
                                                          "Mobile number".trr,
                                                      hintText: 'XXX XXX XXXX',
                                                      keyboardType:
                                                          TextInputType.phone,
                                                      maxLength: 10,
                                                      validator: (value) {
                                                        return Validator
                                                            .validateMobile(
                                                                value);
                                                      },
                                                    )
                                                  : SizedBox(),
                                              verticalSpace(16),
                                              Row(
                                                children: [
                                                  Expanded(
                                                      child: CommonTextField(
                                                    controller:
                                                        addPureDcVehicleController
                                                            .fuelQuotaController,
                                                    labelText:
                                                        'DC fuel quota (liters)'
                                                            .trr,
                                                    hintText: '0',
                                                    keyboardType:
                                                        TextInputType.number,
                                                  )),
                                                  horizontalSpace(16),
                                                  Expanded(
                                                      child: CommonTextField(
                                                    controller:
                                                        addPureDcVehicleController
                                                            .fuelBalanceController,
                                                    labelText:
                                                        'DC fuel balance (liters)'
                                                            .trr,
                                                    hintText: '0',
                                                    keyboardType:
                                                        TextInputType.number,
                                                    // filled: true,
                                                    // fillColor:
                                                    //     AppColor.lightBlueColor,
                                                  )),
                                                ],
                                              ),
                                              verticalSpace(16),
                                              // Row(
                                              //   children: [
                                              //     Expanded(
                                              //         child: CommonTextField(
                                              //       controller:
                                              //           addPureDcVehicleController
                                              //               .fuelQuotaController,
                                              //       labelText:
                                              //           'DC fuel quota (liters)'
                                              //               .trr,
                                              //       hintText: '0',
                                              //       keyboardType:
                                              //           TextInputType.number,
                                              //     )),
                                              //     horizontalSpace(16),
                                              //     Expanded(
                                              //         child: CommonTextField(
                                              //       controller:
                                              //           addPureDcVehicleController
                                              //               .moneyBalanceController,
                                              //       labelText:
                                              //           'DC money balance (SAR)'
                                              //               .trr,
                                              //       hintText: '0',
                                              //       keyboardType:
                                              //           TextInputType.number,
                                              //       filled: true,
                                              //       fillColor:
                                              //           AppColor.lightBlueColor,
                                              //     )),
                                              //   ],
                                              // ),
                                              // verticalSpace(16),
                                              // Row(
                                              //   children: [
                                              //     Expanded(
                                              //         child: CommonTextField(
                                              //       controller:
                                              //           addPureDcVehicleController
                                              //               .moneyQuotaController,
                                              //       labelText:
                                              //           'DC money quota (SAR)'
                                              //               .trr,
                                              //       hintText: '00.00',
                                              //       keyboardType:
                                              //           TextInputType.number,
                                              //       filled: true,
                                              //       fillColor:
                                              //           AppColor.lightBlueColor,
                                              //     )),
                                              //     horizontalSpace(16),
                                              //     Expanded(
                                              //         child: CommonTextField(
                                              //       controller:
                                              //           addPureDcVehicleController
                                              //               .dcMoneyBalanceController,
                                              //       labelText:
                                              //           'DC money balance (SAR)'
                                              //               .trr,
                                              //       hintText: '00.00',
                                              //       keyboardType:
                                              //           TextInputType.number,
                                              //       filled: true,
                                              //       fillColor:
                                              //           AppColor.lightBlueColor,
                                              //     )),
                                              //   ],
                                              // ),
                                              // verticalSpace(32)
                                            ],
                                          )
                                        : SizedBox()
                                  ],
                                )
                              : SizedBox(),
                          horizontalDivider(),
                          addDcTitleRowWidget(
                            title: "Filling days".trr,
                            isSelected:
                                addPureDcVehicleController.isFillingDays.value,
                            onTap: () {
                              addPureDcVehicleController.isFillingDays.value =
                                  !addPureDcVehicleController
                                      .isFillingDays.value;
                            },
                          ),
                          verticalSpace(16),
                          addPureDcVehicleController.isFillingDays.value
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        addPureDcVehicleController
                                                .isAllDays.value =
                                            !addPureDcVehicleController
                                                .isAllDays.value;
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(
                                            border: Border.all(
                                                color: AppColor.cBorder),
                                            borderRadius:
                                                BorderRadius.circular(6)),
                                        padding: EdgeInsets.symmetric(
                                            vertical: 12, horizontal: 16),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text("All days".trr,
                                                style: pRegular14),
                                            assetSvdImageWidget(
                                                image:
                                                    addPureDcVehicleController
                                                            .isAllDays.value
                                                        ? DefaultImages
                                                            .dropDownIcn
                                                        : DefaultImages
                                                            .arrowUpIcn)
                                          ],
                                        ),
                                      ),
                                    ),
                                    verticalSpace(16),
                                    addPureDcVehicleController.isAllDays.value
                                        ? ChipsChoice<int>.multiple(
                                            value: addPureDcVehicleController
                                                .tag.value,
                                            onChanged: (val) {
                                              addPureDcVehicleController
                                                  .tag.value = val;
                                            },
                                            choiceItems:
                                                C2Choice.listFrom<int, String>(
                                              source: addPureDcVehicleController
                                                  .daysList,
                                              value: (i, v) => i,
                                              label: (i, v) => v,
                                            ),
                                            choiceBuilder: (item, i) {
                                              return customChip(
                                                label: item.label.toString().trr,
                                                selected: item.selected,
                                                onSelect: item.select!,
                                              );
                                            },
                                            wrapped: true,
                                          )
                                        : SizedBox(),
                                    verticalSpace(32)
                                  ],
                                )
                              : SizedBox(),
                          horizontalDivider(),
                          addDcTitleRowWidget(
                            title: "Available stations".trr,
                            isSelected: addPureDcVehicleController
                                .isAvailableStations.value,
                            onTap: () {
                              addPureDcVehicleController
                                      .isAvailableStations.value =
                                  !addPureDcVehicleController
                                      .isAvailableStations.value;
                            },
                          ),
                          verticalSpace(16),
                          addPureDcVehicleController.isAvailableStations.value
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics: NeverScrollableScrollPhysics(),
                                      itemCount: addPureDcVehicleController
                                          .availableStationList.length,
                                      itemBuilder: (context, index) {
                                        var data = addPureDcVehicleController
                                            .availableStationList[index];
                                        return Obx(() {
                                          return Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 8.0),
                                            child: checkBoxWidget(
                                              value: data['value'].value,
                                              onChanged: (value) {
                                                data['value'].value = value!;
                                              },
                                              title:
                                                  data['title'].toString().trr,
                                            ),
                                          );
                                        });
                                      },
                                    ),
                                    verticalSpace(24),
                                    CommonTextField(
                                      labelText: '',
                                      prefixIcon: Padding(
                                        padding: const EdgeInsets.all(12),
                                        child: assetSvdImageWidget(
                                            image: DefaultImages.searchIcn,
                                            width: 24,
                                            height: 24),
                                      ),
                                      hintText: 'Search'.trr,
                                    ),
                                    verticalSpace(24),
                                    Container(
                                      padding: EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                            color: AppColor.cBorder,
                                          )),
                                      child: Column(
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              // addPureDcVehicleController.multipleSelected.clear();
                                              for (var element
                                                  in addPureDcVehicleController
                                                      .stationList) {
                                                if (element["value"] == false) {
                                                  element["value"].value = true;
                                                  addPureDcVehicleController
                                                      .multipleSelected
                                                      .add(element);
                                                } else {
                                                  if (addPureDcVehicleController
                                                      .multipleSelected
                                                      .isNotEmpty) {
                                                    element["value"].value =
                                                        false;
                                                    addPureDcVehicleController
                                                        .multipleSelected
                                                        .remove(element);
                                                  }
                                                  // element["value"].value = false;
                                                  // addPureDcVehicleController.multipleSelected.remove(element);
                                                }
                                              }
                                            },
                                            child: Row(children: [
                                              assetSvdImageWidget(
                                                  image:
                                                      addPureDcVehicleController
                                                              .multipleSelected
                                                              .isNotEmpty
                                                          ? DefaultImages
                                                              .checkboxIcn
                                                          : DefaultImages
                                                              .checkboxDashIcn),
                                              horizontalSpace(8),
                                              Text(
                                                "All stations".trr,
                                                style: pRegular14,
                                              )
                                            ]),
                                          ),
                                          verticalSpace(16),
                                          ListView.builder(
                                            shrinkWrap: true,
                                            physics:
                                                NeverScrollableScrollPhysics(),
                                            itemCount:
                                                addPureDcVehicleController
                                                    .stationList.length,
                                            itemBuilder: (context, index) {
                                              var data =
                                                  addPureDcVehicleController
                                                      .stationList[index];
                                              return Obx(() {
                                                return Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          bottom: 8.0),
                                                  child: GestureDetector(
                                                    onTap: () {
                                                      data['isSelected'].value =
                                                          !data['isSelected']
                                                              .value;
                                                      print(data['isSelected']
                                                          .value);
                                                    },
                                                    child: Container(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              vertical: 10,
                                                              horizontal: 8),
                                                      decoration: BoxDecoration(
                                                        color: AppColor
                                                            .cLightBlueContainer,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(6),
                                                      ),
                                                      child: Column(
                                                        children: [
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            children: [
                                                              Row(
                                                                children: [
                                                                  SizedBox(
                                                                    height: 24,
                                                                    width: 24,
                                                                    child:
                                                                        Checkbox(
                                                                      value: data[
                                                                              "value"]
                                                                          .value,
                                                                      onChanged:
                                                                          (value) {
                                                                        data["value"].value =
                                                                            value;
                                                                      },
                                                                      activeColor:
                                                                          AppColor
                                                                              .themeDarkBlueColor,
                                                                      side: BorderSide(
                                                                          color:
                                                                              AppColor.cBlack),
                                                                      shape: RoundedRectangleBorder(
                                                                          side: BorderSide(
                                                                              color: AppColor
                                                                                  .cBlack),
                                                                          borderRadius:
                                                                              BorderRadius.circular(4)),
                                                                    ),
                                                                  ),
                                                                  horizontalSpace(
                                                                      8),
                                                                  Text(
                                                                      data[
                                                                          "title"],
                                                                      style: pRegular16.copyWith(
                                                                          color:
                                                                              AppColor.cDarkBlueFont)),
                                                                ],
                                                              ),
                                                              assetSvdImageWidget(
                                                                  image: data['isSelected']
                                                                              .value ==
                                                                          true
                                                                      ? DefaultImages
                                                                          .cancelIcn
                                                                      : DefaultImages
                                                                          .addIcn,
                                                                  height: 24,
                                                                  width: 24)
                                                            ],
                                                          ),
                                                          data['isSelected']
                                                                      .value ==
                                                                  true
                                                              ? Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .only(
                                                                          left:
                                                                              10,
                                                                          top:
                                                                              6,
                                                                          bottom:
                                                                              16),
                                                                  child: ListView
                                                                      .builder(
                                                                    itemCount: data[
                                                                            'data']
                                                                        .length,
                                                                    physics:
                                                                        NeverScrollableScrollPhysics(),
                                                                    shrinkWrap:
                                                                        true,
                                                                    itemBuilder:
                                                                        (context,
                                                                            i) {
                                                                      var myData =
                                                                          data['data']
                                                                              [
                                                                              i];
                                                                      return Obx(
                                                                          () {
                                                                        return Padding(
                                                                          padding: const EdgeInsets
                                                                              .only(
                                                                              top: 10),
                                                                          child:
                                                                              Row(
                                                                            children: [
                                                                              SizedBox(
                                                                                height: 24,
                                                                                width: 24,
                                                                                child: Checkbox(
                                                                                  value: myData["value"].value,
                                                                                  onChanged: (value) {
                                                                                    myData["value"].value = value;
                                                                                  },
                                                                                  activeColor: AppColor.themeDarkBlueColor,
                                                                                  side: BorderSide(color: AppColor.cBlack),
                                                                                  shape: RoundedRectangleBorder(side: BorderSide(color: AppColor.cBlack), borderRadius: BorderRadius.circular(4)),
                                                                                ),
                                                                              ),
                                                                              horizontalSpace(8),
                                                                              Text(myData["title"], style: pRegular16.copyWith(color: AppColor.cDarkBlueFont)),
                                                                            ],
                                                                          ),
                                                                        );
                                                                      });
                                                                    },
                                                                  ),
                                                                )
                                                              : SizedBox(),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              });
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    verticalSpace(32),
                                  ],
                                )
                              : SizedBox(),
                        ],
                      );
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(color: AppColor.cLightGrey),
          child: Row(
            children: [
              Expanded(
                child: CommonButton(
                  title: 'Cancel'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  textColor: AppColor.cText,
                  btnColor: AppColor.cBackGround,
                ),
              ),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Save'.trr,
                  onPressed: () {
                    if (widget.isAdd == true) {
                      vehicleController.myFleetList.value =
                          vehicleController.dummyFleetList.value;
                      vehicleController.myFleetList.refresh();
                    }
                    // Get.back();
                    addPureDcVehicleController.addFleets(1);
                  },
                  textColor: AppColor.cWhiteFont,
                  btnColor: AppColor.themeOrangeColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget customChip({
    required final String label,
    required final bool selected,
    required final Function(bool selected) onSelect,
  }) {
    return GestureDetector(
      onTap: () => onSelect(!selected),
      child: Container(
        height: 44,
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color:
                selected ? AppColor.themeDarkBlueColor : AppColor.cLightGrey),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(label,
                style: pRegular14.copyWith(
                    color: selected ? AppColor.cWhiteFont : AppColor.cText)),
            horizontalSpace(8),
            assetSvdImageWidget(
                image:
                    selected ? DefaultImages.cancelIcn : DefaultImages.addIcn,
                colorFilter: ColorFilter.mode(
                    selected ? AppColor.cWhiteFont : AppColor.cText,
                    BlendMode.srcIn),
                height: 16,
                width: 16),
          ],
        ),
      ),
    );
  }

  Widget platDropDownWidget(
      {List? list, String? value, Function(Object?)? onChanged}) {
    return Container(
      width: 120,
      decoration: BoxDecoration(
          color: AppColor.cLightGrey,
          borderRadius: BorderRadius.horizontal(left: Radius.circular(6)),
          border: Border.all(color: AppColor.cLightGrey)),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: DropdownButton(
        items: list!.map((data) {
          return DropdownMenuItem(
            value: data,
            child: Text(
              data,
              style: pRegular13.copyWith(color: AppColor.cText),
              overflow: TextOverflow.ellipsis,
            ),
          );
        }).toList(),
        onChanged: onChanged,
        value: value,
        dropdownColor: AppColor.cLightGrey,
        icon: assetSvdImageWidget(
            image: DefaultImages.blueArrowDownIcn,
            colorFilter: ColorFilter.mode(AppColor.cText, BlendMode.srcIn)),
        padding: EdgeInsets.zero,
        isExpanded: true,
        underline: Container(),
      ),
    );
  }

  Widget plateTextFieldWidget(TextEditingController controller, String hint) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: TextField(
        controller: controller,
        cursorColor: AppColor.cDarkGreyFont,
        style: pRegular13,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 10),
        ),
      ),
    );
  }
}

Widget addDcTitleRowWidget(
    {required String title, required bool isSelected, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      color: AppColor.cBackGround,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pBold20,
          ),
          assetSvdImageWidget(
              image: isSelected == true
                  ? DefaultImages.arrowUpIcn
                  : DefaultImages.dropDownIcn,
              width: 24,
              height: 24)
        ],
      ),
    ),
  );
}
