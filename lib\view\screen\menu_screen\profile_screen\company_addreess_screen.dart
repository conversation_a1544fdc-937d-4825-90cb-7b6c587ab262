// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/profile_controller/address_load_controller.dart';
import 'package:waie_app/core/controller/menu_controller/profile_controller/company_address_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class CompanyAddressScreen extends StatefulWidget {
  const CompanyAddressScreen({super.key});

  @override
  State<CompanyAddressScreen> createState() => _CompanyAddressScreenState();
}

class _CompanyAddressScreenState extends State<CompanyAddressScreen> {
  CompanyAddressController companyAddressController =
      Get.put(CompanyAddressController());
  Address_Data_Controller addressLoadController =
      Get.put(Address_Data_Controller());
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  String errorString = '';

  @override
  void initState() {
    super.initState();
    companyAddressController.getstoredCompanyAddress();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            children: [
              simpleMyAppBar(
                  title: "Address".trr,
                  onTap: () {
                    Get.back();
                  },
                  backString: "Back".trr),
              Expanded(
                child: Form(
                  key: _formKey,
                  child: ListView(
                    shrinkWrap: true,
                    padding:
                        const EdgeInsets.only(top: 24, left: 16, right: 16),
                    children: [
                      CommonDropdownButtonWidget(
                          hint: '',
                          labelText: 'Country'.trr,
                          list: companyAddressController.countryList,
                          value: companyAddressController.selectedCountry.value,
                          onChanged: (value) {},
                          fontColor: AppColor.cDarkGreyFont,
                          filledColor: AppColor.cFilled),
                      verticalSpace(16),
                      // CommonDropdownButtonWidget(
                      //   labelText: '${"Region".trr}*',
                      //   list: companyAddressController.regionList,
                      //   value: companyAddressController.selectedRegion.value,
                      //   onChanged: (value) {
                      //     companyAddressController.selectedRegion.value =
                      //         value;
                      //   },
                      //   validator: (value) {
                      //     return Validator.validateRequired(value);
                      //   },
                      // ),
                      // verticalSpace(16),
                      // CommonDropdownButtonWidget(
                      //   labelText: "${'District'.trr}*",
                      //   list: companyAddressController.districtsList,
                      //   value:
                      //       companyAddressController.selectedDistricts.value,
                      //   onChanged: (value) {
                      //     companyAddressController.selectedDistricts.value =
                      //         value;
                      //   },
                      //   validator: (value) {
                      //     return Validator.validateRequired(value);
                      //   },
                      // ),
                      // verticalSpace(16),
                      // CommonDropdownButtonWidget(
                      //   labelText: '${"City".trr}*',
                      //   list: companyAddressController.cityList,
                      //   value: companyAddressController.selectedCity.value,
                      //   onChanged: (value) {
                      //     companyAddressController.selectedCity.value = value;
                      //   },
                      //   validator: (value) {
                      //     return Validator.validateRequired(value);
                      //   },
                      // ),
                      Text(
                        '${"Region".trr}*',
                        style: pRegular13,
                      ),
                      verticalSpace(10),
                      DropdownButtonFormField(
                        items: addressLoadController.provList.map((data) {
                          return DropdownMenuItem(
                            value: data.TYPECODE,
                            child: Text(
                              data.TYPEDESC,
                              style: pMedium12,
                              textAlign: TextAlign.center,
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          companyAddressController.selectedRegion.value =
                              value.toString();
                          companyAddressController.regionController.text =
                              companyAddressController.selectedRegion.value;
                        },
                        validator: (value) {
                          return Validator.validateRequired(value.toString());
                        },
                        style: pRegular14.copyWith(color: AppColor.cLabel),
                        borderRadius: BorderRadius.circular(6),
                        dropdownColor: AppColor.cLightGrey,
                        icon: assetSvdImageWidget(
                            image: DefaultImages.dropDownIcn),
                        decoration: InputDecoration(
                          hintText: 'Region'.trr,
                          hintStyle:
                              pRegular14.copyWith(color: AppColor.cHintFont),
                          contentPadding: EdgeInsets.only(left: 16, right: 16),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          disabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                        ),
                      ),
                      errorString == ''
                          ? SizedBox()
                          : Text(
                              errorString,
                              style:
                                  pRegular12.copyWith(color: AppColor.cRedText),
                            ),
                      verticalSpace(16),
                      Text(
                        '${"City".trr}*',
                        style: pRegular13,
                      ),
                      verticalSpace(10),
                      DropdownButtonFormField(
                        items: addressLoadController.cityList.map((data) {
                          return DropdownMenuItem(
                            value: data.TYPECODE,
                            child: Text(
                              data.TYPEDESC,
                              style: pMedium12,
                              textAlign: TextAlign.center,
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          companyAddressController.selectedCity.value =
                              value.toString();
                          companyAddressController.cityController.text =
                              companyAddressController.selectedCity.value;
                        },
                        validator: (value) {
                          return Validator.validateRequired(value.toString());
                        },
                        style: pRegular14.copyWith(color: AppColor.cLabel),
                        borderRadius: BorderRadius.circular(6),
                        dropdownColor: AppColor.cLightGrey,
                        icon: assetSvdImageWidget(
                            image: DefaultImages.dropDownIcn),
                        decoration: InputDecoration(
                          hintText: 'City'.trr,
                          hintStyle:
                              pRegular14.copyWith(color: AppColor.cHintFont),
                          contentPadding: EdgeInsets.only(left: 16, right: 16),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          disabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                        ),
                      ),
                      errorString == ''
                          ? SizedBox()
                          : Text(
                              errorString,
                              style:
                                  pRegular12.copyWith(color: AppColor.cRedText),
                            ),
                      verticalSpace(16),
                      Text(
                        '${"District".trr}*',
                        style: pRegular13,
                      ),
                      verticalSpace(10),
                      DropdownButtonFormField(
                        items: addressLoadController.distList.map((data) {
                          return DropdownMenuItem(
                            value: data.TYPECODE,
                            child: Text(
                              data.TYPEDESC,
                              style: pMedium12,
                              textAlign: TextAlign.center,
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          companyAddressController.selectedDistricts.value =
                              value.toString();
                          companyAddressController.districtController.text =
                              companyAddressController.selectedDistricts.value;
                        },
                        validator: (value) {
                          return Validator.validateRequired(value.toString());
                        },
                        style: pRegular14.copyWith(color: AppColor.cLabel),
                        borderRadius: BorderRadius.circular(6),
                        dropdownColor: AppColor.cLightGrey,
                        icon: assetSvdImageWidget(
                            image: DefaultImages.dropDownIcn),
                        decoration: InputDecoration(
                          hintText: 'District'.trr,
                          hintStyle:
                              pRegular14.copyWith(color: AppColor.cHintFont),
                          contentPadding: EdgeInsets.only(left: 16, right: 16),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          disabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(
                              color: AppColor.cBorder,
                            ),
                          ),
                        ),
                      ),
                      errorString == ''
                          ? SizedBox()
                          : Text(
                              errorString,
                              style:
                                  pRegular12.copyWith(color: AppColor.cRedText),
                            ),
                      verticalSpace(16),
                      CommonTextField(
                        controller:
                            companyAddressController.cpPhoneNoController,
                        labelText: '${"Company phone number".trr}*',
                        hintText: 'Enter phone number'.trr,
                        maxLength: 12,
                        keyboardType: TextInputType.numberWithOptions(
                            signed: true, decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        validator: (v) {
                          return Validator.validateMobile(v);
                        },
                      ),
                      verticalSpace(16),
                      CommonTextField(
                        controller:
                            companyAddressController.buildingNoController,
                        labelText: '${"Building No".trr}*',
                        hintText: 'Enter building no'.trr,
                        keyboardType: TextInputType.numberWithOptions(
                            signed: true, decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        validator: (v) {
                          return Validator.validateRequired(v);
                        },
                      ),
                      verticalSpace(16),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: CommonTextField(
                              controller:
                                  companyAddressController.postalCodeController,
                              labelText: '${"Postal Code".trr}*',
                              hintText: 'Enter postal code'.trr,
                              keyboardType: TextInputType.numberWithOptions(
                                  signed: true, decimal: true),
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              validator: (v) {
                                return Validator.validateRequired(v);
                              },
                            ),
                          ),
                          horizontalSpace(16),
                          Expanded(
                            child: CommonTextField(
                              controller:
                                  companyAddressController.poBoxController,
                              labelText: 'PO Box'.trr,
                              hintText: 'Enter po box'.trr,
                              keyboardType: TextInputType.numberWithOptions(
                                  signed: true, decimal: true),
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              validator: (v) {
                                return '';
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        bottomNavigationBar: Container(
          color: AppColor.cLightGrey,
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: CommonButton(
                  title: 'Cancel'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  textColor: AppColor.cText,
                  btnColor: AppColor.cBackGround,
                ),
              ),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Save'.trr,
                  onPressed: () {
                    companyAddressController.updateProfileDetail();
                  },
                  textColor: AppColor.cWhiteFont,
                  btnColor: AppColor.themeOrangeColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
