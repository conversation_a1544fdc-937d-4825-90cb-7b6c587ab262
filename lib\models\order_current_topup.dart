import 'dart:convert';

List<OrderCurrentTopupModel> orderCurrentTopupModelFromJson(String str) =>
    List<OrderCurrentTopupModel>.from(
        json.decode(str).map((x) => OrderCurrentTopupModel.fromJson(x)));

String orderCurrentTopupModelToJson(List<OrderCurrentTopupModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OrderCurrentTopupModel {
  int line;
  String orderid;
  String orderdate;
  String invno;
  String paidon;
  double addltopup;
  double vat;
  DateTime paidOn;
  bool isvalue;

  OrderCurrentTopupModel({
    required this.line,
    required this.orderid,
    required this.orderdate,
    required this.invno,
    required this.paidon,
    required this.addltopup,
    required this.vat,
    required this.paidOn,
    required this.isvalue,
  });

  factory OrderCurrentTopupModel.fromJson(Map<String, dynamic> json) =>
      OrderCurrentTopupModel(
        line: json["LINE"] ?? 0,
        orderid: json["ORDERID"] ?? "",
        orderdate: json["ORDERDATE"] ?? "",
        invno: json["INVNO"] ?? "",
        paidon: json["PAIDON"] ?? "",
        addltopup: json["ADDLTOPUP"]?.toDouble() ?? 0.0,
        vat: json["VAT"]?.toDouble() ?? 0.0,
        paidOn: DateTime.parse(json["PAID_ON"] ?? ""),
        isvalue: json["ISVALUE"] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "LINE": line,
        "ORDERID": orderid,
        "ORDERDATE": orderdate,
        "INVNO": invno,
        "PAIDON": paidon,
        "ADDLTOPUP": addltopup,
        "VAT": vat,
        "PAID_ON": paidOn.toIso8601String(),
        "ISVALUE": isvalue,
      };
}
