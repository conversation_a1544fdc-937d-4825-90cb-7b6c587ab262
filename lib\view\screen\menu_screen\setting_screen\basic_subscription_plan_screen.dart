// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/setting_screen/subscription_plan_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class BasicSubscriptionPlanScreen extends StatelessWidget {
  const BasicSubscriptionPlanScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            simpleMyAppBar(
              onTap: () {
                Get.back();
              },
              title: "Subscription plan".trr,
              backString: "Back".trr,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Subscription Settings".trr,
                    style: pSemiBold17,
                  ),
                  verticalSpace(30),
                  Container(
                    width: Get.width,
                    padding: EdgeInsets.symmetric(vertical: 14, horizontal: 16),
                    decoration: BoxDecoration(
                      color: AppColor.themeDarkBlueColor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Subscription plan".trr,
                          style: pRegular13.copyWith(color: AppColor.cWhiteFont),
                        ),
                        verticalSpace(4),
                        Text(
                          'Basic'.trr,
                          style: pBold28.copyWith(color: AppColor.cWhiteFont),
                        ),
                        verticalSpace(24),
                        Text(
                          "10 SAR / month",
                          style: pSemiBold17.copyWith(color: AppColor.cWhiteFont),
                        ),
                      ],
                    ),
                  ),
                  verticalSpace(24),
                  Text.rich(
                    TextSpan(
                      text: '💎 '.trr,
                      style: pRegular14.copyWith(color: AppColor.cWhiteFont),
                      children: <TextSpan>[
                        TextSpan(
                          text: 'Maximize your account efficiency with our Premium plan'.trr,
                          style: pRegular14.copyWith(color: AppColor.cBlackFont),
                        ),
                      ],
                    ),
                  ),
                  verticalSpace(28),
                  CommonBorderButton(
                    title: 'Learn more'.trr,
                    onPressed: () {
                      Get.off(() => SubscriptionPlanScreen());
                    },
                    textColor: AppColor.themeDarkBlueColor,
                    bColor: AppColor.themeDarkBlueColor,
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
