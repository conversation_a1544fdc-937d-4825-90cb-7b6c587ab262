/**
 * Created by Büşra Afşar <PERSON>.
 */
package com.waie.aldrees.mobile.interface_classes;

import com.mastercard.gateway.android.sdk.GatewayCallback;
import com.mastercard.gateway.android.sdk.GatewayMap;

public interface IMadaApiService {

    void createSession(String amount, String orderId, CreateSessionCallback callback);

    void updateSession(String sessionId, String apiVersion, GatewayMap request, GatewayCallback gatewayCallback);

    void completeSession(String jsonText, String orderId, String transactionId, final String threeDSecureId,
            CompleteSessionCallback callback);

    void check3dsEnrollment(String sessionId, String amount, String currency, String threeDSecureId,
            Check3DSecureEnrollmentCallback enrollmentCallback);

    String checkTransaction(String orderId);

    String getRequestText();

    String getResponseText();
}
