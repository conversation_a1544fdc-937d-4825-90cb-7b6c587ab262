// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_history_tag_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_history_card_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_history_tag_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/order_history_topup_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../utils/constants.dart';
import '../../location_screen/location_screen.dart';
import 'submit_refund_screen.dart';

class OrderHistoryServiceMenuScreen extends StatelessWidget {
  const OrderHistoryServiceMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    RefundsController refundsController = Get.put(RefundsController());
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 16, right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                          top: 15,
                          bottom: 15,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            assetSvdImageWidget(
                                image: DefaultImages.backIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cDarkBlueFont, BlendMode.srcIn)),
                            horizontalSpace(10),
                            Text(
                              "Back".trr,
                              style: pRegular18.copyWith(
                                  color: AppColor.cDarkBlueFont, fontSize: 17),
                              textAlign: TextAlign.start,
                            )
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "Service Order History".trr,
                          style: pBold20,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Obx(() {
                  return Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      // shrinkWrap: true,
                      // padding: EdgeInsets.all(16),
                      children: [
                        SizedBox(
                          //height: Get.height * 0.06,
                          child: Row(
                            children: [
                              tabWidget(
                                  title: 'Tag'.trr,
                                  onTap: () {
                                    refundsController.isOrderRefund.value =
                                        true;
                                    refundsController.isTopUpRefund.value =
                                        false;
                                    refundsController.isRefundHistory.value =
                                        false;
                                  },
                                  isSelected:
                                      refundsController.isOrderRefund.value),
                              if (Constants.SCrbBtn == "Y")
                                tabWidget(
                                    title: 'Smart Card'.trr,
                                    onTap: () {
                                      refundsController.isOrderRefund.value =
                                          false;
                                      refundsController.isTopUpRefund.value =
                                          true;
                                      refundsController.isRefundHistory.value =
                                          false;
                                    },
                                    isSelected:
                                        refundsController.isTopUpRefund.value),
                            ],
                          ),
                        ),
                        refundsController.isOrderRefund.value == true
                            ? OrderHistoryTagScreen()
                            : OrderHistoryCardScreen(),
                      ],
                    ),
                  );
                }),
              )
            ],
          ),
        ),
      ),
    );
  }
}

Widget tabWidget({
  String? title,
  // Color? indicatorColor,
  // Color? fontColor,
  // double? indicatorSize,
  Function()? onTap,
  bool? isSelected,
}) {
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          FittedBox(
            child: Text(
              title!,
              style: pSemiBold17.copyWith(
                  color: isSelected == true
                      ? AppColor.cText
                      : AppColor.cDarkGreyFont),
            ),
          ),
          verticalSpace(8),
          Container(
            // width: Get.width/2,
            height: isSelected == true ? 3 : 1,
            color: isSelected == true
                ? AppColor.themeOrangeColor
                : AppColor.cIndicator,
          )
        ],
      ),
    ),
  );
}
