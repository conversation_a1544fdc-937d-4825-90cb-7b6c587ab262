// ignore_for_file: must_be_immutable, prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:hive/hive.dart';
import 'package:http/http.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/auth/auth_controller.dart';
import 'package:waie_app/core/controller/splash_controller/splash_controller.dart';
import 'package:waie_app/models/profile.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/prefer.dart';
import 'package:waie_app/utils/regDB.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/screen/auth/auth_background.dart';
import 'package:waie_app/view/screen/auth/digital_coupon/verify_mobile_number_screen.dart';
import 'package:waie_app/view/screen/auth/login_with_email_screen.dart';
import 'package:waie_app/view/screen/auth/otp_screen.dart';
import 'package:waie_app/view/screen/auth/sign_up_screen.dart';
import 'package:waie_app/view/screen/login_manager/login_manager_with_email_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../core/controller/login_manager_controller/login_manager_controller.dart';
import '../../../main.dart';
import '../../../utils/constant.dart';
import '../login_manager/login_manager_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

enum SupportState {
  unknown,
  supported,
  unSupported,
}

class _LoginScreenState extends State<LoginScreen> {
  AuthController authController = Get.put(AuthController());
  String languageCode = myStorage!.getString(AppConstant.LANGUAGE_CODE) ?? 'ar';
  LoginManagerController loginManagerController =
      Get.put(LoginManagerController());
  final LocalAuthentication auth = LocalAuthentication();
  SupportState supportState = SupportState.unknown;
  List<BiometricType>? availableBiometrics;

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  RegisterDatabase db = RegisterDatabase();
  // reference the hive register box
  final _isReg = Hive.box('isReg_DB');

  @override
  void initState() {
    _loadLoginImage();
    auth.isDeviceSupported().then(
          (bool isSupported) => setState(
            () => supportState =
                isSupported ? SupportState.supported : SupportState.unSupported,
          ),
        );

    print("===============================================================");
    print("_isReg");
    //check if there is no current register userinfo, then it is the 1st time ever opening the app
    //then let user register login info
    if (_isReg.get("regUser") == null) {
      //db.createUserInfoData();
    } else {
      db.loadData();
    }
    print("===============================================================");
    super.initState();
  }

  Future<void> checkBiometric() async {
    late bool canCheckBiometric;

    try {
      canCheckBiometric = await auth.canCheckBiometrics;
      print("Biometric supported: $canCheckBiometric");
    } on PlatformException catch (e) {
      print(e);
      canCheckBiometric = false;
    }
  }

  Future<void> getAvailableBiometrics() async {
    late List<BiometricType> biometricTypes;
    try {
      biometricTypes = await auth.getAvailableBiometrics();
      print("supported biometrics: $biometricTypes");
    } on PlatformException catch (e) {
      print(e);
    }

    if (!mounted) {
      return;
    }
    setState(() {
      availableBiometrics = biometricTypes;
    });
  }

  Future<void> authenticateWithBiometrics() async {
    try {
      final authenticated = await auth.authenticate(
        localizedReason: 'Authenticate with fingerprint or Face ID',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
          useErrorDialogs: true,
        ),
      );

      if (!mounted) {
        return;
      }

      if (authenticated) {
        // Navigator.push(
        //     context, MaterialPageRoute(builder: (context) => const Home()));

        final regUser = _isReg.get('regUser');
        print('List is $regUser');
        print('username is ${regUser?['username']}');
        print('password is ${regUser?['password']}');
        print('status is ${regUser?['status']}');
        login(regUser?['username'], regUser?['password']);
      }
    } on PlatformException catch (e) {
      print(e);
      return;
    }
  }

  clearStorage() {
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');
    final tagOrderRefund = GetStorage();
    final topupOrderRefund = GetStorage();
    final vehicle = GetStorage();
    final getOTPDataVerify = GetStorage();
    final isPinblock = GetStorage();
    final isDCBlock = GetStorage();
    Prefs.clear();
    //Get.offAll(() => LoginManagerScreen());
    vehicle.erase();
    tagOrderRefund.erase();
    topupOrderRefund.erase();
    getOTPDataVerify.erase();
    isPinblock.erase();
    isDCBlock.erase();
    userStorage.erase();
    custsData.erase();
    usersData.erase();
  }

  login(String email, String pass) async {
    Loader.showLoader();
    clearStorage();
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');

    db.loadData();

    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ' + base64Encode(utf8.encode('$username:$password'));
      print(email);
      print(pass);

      //String url = "https://localhost:44383//api/CUSTLOGIN";
      String url =
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.signInEmail;
      print(url);
      // String url="https://devapi.aldrees.com//api/CUSTLOGIN";
      var response = await post(Uri.parse(url), headers: <String, String>{
        'authorization': basicAuth,
        "Access-Control-Allow-Credentials": 'true',
        "Access-Control-Allow-Headers":
            "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      }, body: {
        'username': email,
        'password': pass
      });

      print(response.statusCode);
      print(response.body);
      // print(basicAuth);
      //Loader.hideLoader();
      print('Loader loads');
      if (response.statusCode == 200) {
        print('Suucessfully Fetch');

        Map cardInfo = json.decode(response.body);
        print("json.decode(response.body) ${json.decode(response.body)}");
        print('1');
        print(cardInfo);
        Profile userData = Profile.fromJson(cardInfo);
        print('2');

        String notifUrl =
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getNotifCount;
        var notifResponse =
            await post(Uri.parse(notifUrl), headers: <String, String>{
          'authorization': basicAuth,
          "Access-Control-Allow-Credentials": 'true',
          "Access-Control-Allow-Headers":
              "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        }, body: {
          "custid": userData.auUsers?.custid,
          "isAR": Constants.IsAr_App,
        });
        //print(notifResponse.body);
        print("Constants.notifCount >>>> ${json.decode(notifResponse.body)}");

        var isCount = json.decode(notifResponse.body);

        print("isCount >>>> $isCount");

        Constants.notifCount = isCount;

        if (userData.returnMessage!.isValidTransaction!) {
          if (userData.returnMessage!.action == 'POPUP') {
            Loader.hideLoader();
            //loginFailWidget(userData.returnMessage!.message);
            commonToast(userData.returnMessage!.message);
          } else {
            print(userData.auUsers?.custid);
            var custData = jsonDecode(jsonEncode(userData.auCust));
            var usrData = jsonDecode(jsonEncode(userData.auUsers));
            print(custData);
            if (custData['IS_VERIFIED'] == 'Y') {
              Constants.custIsVerified == "Y";
            }
            Constants.custAcctStatus = userData.auCust!.acctstatus!;

            print(custData['MOBILENO']);
            print(usrData['CUSTID']);
            print(usrData['EMAILID']);
            print(usrData['USERNAME']);
            print("usrData $usrData");
            print(userData.auUsers!.password);
            SharedPreferences sharedUser =
                await SharedPreferences.getInstance();
            // Map decode_options = jsonDecode(jsonString);
            String user = jsonEncode(cardInfo);
            sharedUser.setString('user', user);
            sharedUser.setString('userid', userData.auCust!.custid!);
            userStorage.writeIfNull('custid', userData.auCust!.custid!);
            userStorage.writeIfNull('emailid', userData.auCust!.emailid!);
            userStorage.writeIfNull('accttype', userData.auCust?.accttype);
            usersData.writeIfNull('usrData', usrData);
            custsData.writeIfNull('custData', custData);
            /* Get.to(() => DashBoardManagerScreen(
                currantIndex: 0,
              ));*/
            authController.getUserAccess();
          }
        } else {
          print("Invalid ID Password");
          //loginFailWidget("Invalid ID or password");
          //commonToast("Invalid ID or password");
          if (userData.returnMessage!.message!
              .toString()
              .contains("ACCTLOCKEDMSG")) {
            Loader.hideLoader();
            commonToast("Your Account is lock, try after 5 minutes");
          } else if (userData.returnMessage!.message!
              .toString()
              .contains("INVALIDPASSWORD")) {
            Loader.hideLoader();
            commonToast("Invalid ID Password");
          } else {
            Loader.hideLoader();
            commonToast(userData.returnMessage!.message!.toString());
          }
        }
        return userData;
      } else {
        print('Login Fail');
        commonToast('Login Fail');
        print(response.statusCode.toString());
        loginFailWidget(response.statusCode.toString());
      }
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  SplashController splashController = Get.put(SplashController());

  String loginImagePath = '';

  Future<void> _loadLoginImage() async {
    String path = await splashController.loadImage('MOBLOGINIMGS');
    setState(() {
      loginImagePath = path;
      log(loginImagePath);
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Container(
            color: AppColor.themeBlueColor,
            child: Stack(
              // fit: StackFit.expand,
              alignment: Alignment.topCenter,
              children: [
                Stack(
                  children: [
                    assetSvdImageWidget(
                        image: DefaultImages.loginBg, width: Get.width),
                    loginImagePath.isEmpty
                        ? assetSvdImageWidget(
                            image: DefaultImages.loginBg, width: Get.width)
                        : Image(
                            image: splashController.splashImage,
                            fit: BoxFit.cover), //Image.network(loginImagePath),
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Obx(() {
                                WidgetsBinding.instance
                                    .addPostFrameCallback((timeStamp) {
                                  if (languageCode == 'en') {
                                    authController.selectedLanguage.value =
                                        "English";
                                    authController.selectedLanguageImage.value =
                                        DefaultImages.englishImg;
                                  } else {
                                    authController.selectedLanguage.value =
                                        "Arabic";
                                    authController.selectedLanguageImage.value =
                                        DefaultImages.arabicImg;
                                  }
                                  authController.selectedLanguage.refresh();
                                  authController.selectedLanguageImage
                                      .refresh();
                                });
                                return PopupMenuButton(
                                  onSelected: (item) {
                                    print("+123456789---$item");
                                  },
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6)),
                                  itemBuilder: (BuildContext context) =>
                                      authController.languageList.map((data) {
                                    print(data['title']);
                                    return PopupMenuItem(
                                      value: data['title'],
                                      child: GestureDetector(
                                        onTap: () {
                                          // authController
                                          //     .selectedLanguageIndex
                                          //     .value = i;
                                          languageCode = data['languageCode'];
                                          authController.selectedLanguage
                                              .value = data['title'];
                                          authController.selectedLanguageImage
                                              .value = data['image'];
                                          authController.selectedLanguageIndex
                                              .refresh();
                                          print("++${data['local']}");
                                          authController
                                              .updateLanguage(data['local']);
                                          Get.back();
                                        },
                                        child: languageWidget(
                                          image: data['image'],
                                          title: data['title'],
                                          color: data['languageCode'] ==
                                                  languageCode
                                              ? AppColor.themeBlueColor
                                              : AppColor.cWhite,
                                          bColor: data['languageCode'] ==
                                                  languageCode
                                              ? AppColor.themeBlueColor
                                              : AppColor.cBorder,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  child: Row(
                                    children: [
                                      ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          child: Image.asset(
                                            authController
                                                .selectedLanguageImage.value,
                                            width: 28,
                                            height: 21,
                                          )),
                                      horizontalSpace(6),
                                      Text(
                                        authController.selectedLanguage.value,
                                        style: pSemiBold14.copyWith(
                                            color: loginImagePath.isEmpty
                                                ? AppColor.cWhiteFont
                                                : AppColor.cBlackFont),
                                      )
                                    ],
                                  ),
                                );
                              }),
                              // totalNotificationWidget(totalNotification: '0')
                            ],
                          ),
                          verticalSpace(20),
                          if (loginImagePath.isEmpty) ...[
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                assetSvdImageWidget(
                                    image: DefaultImages.loginLogoImage),
                                horizontalSpace(24),
                                assetSvdImageWidget(
                                    image: DefaultImages.loginLogoImage2),
                              ],
                            )
                          ]
                        ],
                      ),
                    )
                  ],
                ),
                Padding(
                  padding: EdgeInsets.only(
                      top: loginImagePath.isEmpty
                          ? MediaQuery.of(context).size.height * 0.23
                          : MediaQuery.of(context).size.height * 0.28),
                  child: Container(
                    height: double.infinity,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColor.cBackGround,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    padding: EdgeInsets.only(right: 16.0, left: 16.0),
                    child: Form(
                      key: formKey,
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            verticalSpace(Get.height * 0.02),
                            Text(
                              "Log in to your account".trr,
                              // "Log in to your account" is the translation key
                              style: pBold28,
                            ),
                            verticalSpace(24),
                            /* CommonTextField(
                            controller: authController.phoneNoController,
                            labelText: '',
                            hintText: ""
                                .trr, // "Enter your phone number" is the translation key
                            filled: true,
                            fillColor: AppColor.cFilled,
                            keyboardType: TextInputType.number,
                            maxLength: 12,
                            onChanged: (value) {
                              Validator.validateMobile(value);
                            },
                            validator: (value) {
                              return Validator.validateMobile(value);
                            },
                          ),*/
                            Directionality(
                              textDirection: TextDirection.ltr,
                              child: Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6),
                                  color: AppColor.cBorder,
                                  border: Border.all(
                                    color: AppColor.cBorder,
                                  ),
                                ),
                                child: InternationalPhoneNumberInput(
                                  onInputChanged: (PhoneNumber number) {
                                    authController.isoCode.value =
                                        number.isoCode!;
                                    authController.phoneNoController.text =
                                        number.phoneNumber.toString();
                                    print("Entered Number=======" +
                                        number.phoneNumber.toString());
                                  },
                                  // maxLength:9,
                                  /* onInputValidated: (bool value) {
                                  print(value);
                                },*/
                                  cursorColor: AppColor.cHintFont,
                                  selectorConfig: SelectorConfig(
                                    selectorType:
                                        PhoneInputSelectorType.BOTTOM_SHEET,
                                    leadingPadding: 16,
                                    setSelectorButtonAsPrefixIcon: true,
                                  ),
                                  ignoreBlank: false,
                                  // autoValidateMode: AutovalidateMode.disabled,
                                  textStyle: pRegular14.copyWith(
                                    color: AppColor.cLabel,
                                  ),
                                  initialValue: PhoneNumber(
                                      isoCode: authController.isoCode.value,
                                      dialCode: authController.isoCode.value),
                                  inputBorder: OutlineInputBorder(),
                                  keyboardAction: TextInputAction.done,
                                  scrollPadding: EdgeInsets.zero,
                                  selectorTextStyle: pRegular14.copyWith(
                                      color: AppColor.cLabel, fontSize: 14),
                                  textAlign: TextAlign.start,
                                  textAlignVertical: TextAlignVertical.center,
                                  inputDecoration: InputDecoration(
                                    contentPadding:
                                        EdgeInsets.only(left: 16, bottom: 8),
                                    isDense: true,
                                    prefixText: "|  ",
                                    prefixStyle: TextStyle(
                                        fontSize: 30, color: AppColor.cBorder),
                                    counterText: '',
                                    hintText: " " + 'Please enter here'.trr,
                                    counterStyle:
                                        TextStyle(fontSize: 0, height: 0),
                                    errorStyle:
                                        TextStyle(fontSize: 0, height: 0),
                                    hintStyle: pRegular14.copyWith(
                                      color: AppColor.cHintFont,
                                    ),
                                    border: InputBorder.none,
                                    filled: true,
                                    fillColor: AppColor.cFilled,
                                  ),
                                  /*  onSaved: (PhoneNumber number) {
                                },*/
                                ),
                              ),
                            ),
                            verticalSpace(16),
                            CommonButton(
                              title: 'Send Code'
                                  .trr, //"Log In" is the translation key
                              onPressed: () async {
                                authController.phoneNoController.value.text
                                    .replaceAll("+", "");
                                //  if (formKey.currentState!.validate()) {
                                log('done');

                                //authController.mobileNo=authController.phoneNoController.text;
                                /* String pattern = r'^[0-9]+$';
                                RegExp regExp = RegExp(pattern);
                                if (authController
                                    .phoneNoController.value.text == null || authController
                                    .phoneNoController.value.text.isEmpty) {
                                  return 'Please enter your phone number';
                                } else if (!regExp.hasMatch(authController
                                    .phoneNoController.value.text.replaceAll("+", ""))) {
                                  showDialog(
                                    barrierDismissible: false,
                                    context: Get.context!,
                                    builder: (context) {
                                      return AlertDialog(
                                        insetPadding: const EdgeInsets.all(16),
                                        contentPadding: const EdgeInsets.all(24),
                                        shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(12)),
                                        content: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              "Please enter english numbers only "+authController.phoneNoController.value.text,
                                              style: pBold20,
                                              textAlign: TextAlign.center,
                                            ),
                                            verticalSpace(24),
                                            CommonButton(
                                              title: "BACK".trr,
                                              onPressed: () {
                                                Get.back();
                                              },
                                              btnColor: AppColor.themeOrangeColor,
                                            )
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                  //return 'Please enter numbers only';
                                }
                                else {*/
                                authController.phonenumber.value =
                                    authController.phoneNoController.value.text;
                                if (authController
                                        .phoneNoController.value.text.length ==
                                    13) {
                                  authController.mobileLogin();
                                } else {
                                  showDialog(
                                    barrierDismissible: false,
                                    context: Get.context!,
                                    builder: (context) {
                                      return AlertDialog(
                                        insetPadding: const EdgeInsets.all(16),
                                        contentPadding:
                                            const EdgeInsets.all(24),
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12)),
                                        content: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              "Please enter your phone number"
                                                  .trr,
                                              style: pBold20,
                                              textAlign: TextAlign.center,
                                            ),
                                            verticalSpace(24),
                                            CommonButton(
                                              title: "BACK".trr,
                                              onPressed: () {
                                                Get.back();
                                              },
                                              btnColor:
                                                  AppColor.themeOrangeColor,
                                            )
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                }
                                //}
                                /* String pattern = r'^[0-9]+$';
                                RegExp regExp = RegExp(pattern);
                                String? phoneNumberError =
                                    validatePhoneNumber(authController
                                        .phoneNoController.value.text);
                                if (phoneNumberError != null) {
                                  showDialog(
                                    barrierDismissible: false,
                                    context: Get.context!,
                                    builder: (context) {
                                      return AlertDialog(
                                        insetPadding: const EdgeInsets.all(16),
                                        contentPadding: const EdgeInsets.all(24),
                                        shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(12)),
                                        content: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              "Please enter english numbers only "+authController.phoneNoController.value.text,
                                              style: pBold20,
                                              textAlign: TextAlign.center,
                                            ),
                                            verticalSpace(24),
                                            CommonButton(
                                              title: "BACK".trr,
                                              onPressed: () {
                                                Get.back();
                                              },
                                              btnColor: AppColor.themeOrangeColor,
                                            )
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                } else {
                                  authController.phonenumber.value =
                                      authController
                                          .phoneNoController.value.text;
                                  authController.mobileLogin();
                                }*/
                                //Get.to(() => OtpScreen());
                                // authController.clear();
                                // }
                              },
                            ),
                            verticalSpace(16),
                            Text(
                              "or".trr,
                              style: pRegular14,
                            ),
                            verticalSpace(16),
                            Row(
                              children: [
                                if (Platform.isAndroid || Platform.isWindows)
                                  if (_isReg.get("regUser") != null)
                                    // Expanded(
                                    //   flex: 1,
                                    //   child: GestureDetector(
                                    //     onTap: authenticateWithBiometrics,
                                    //     child: Image.asset(
                                    //       DefaultImages.fingerprintblack,
                                    //       width: 50,
                                    //       height: 50,
                                    //       fit: BoxFit.cover,
                                    //     ),
                                    //   ),
                                    // ),
                                    Material(
                                      child: InkWell(
                                        onTap: authenticateWithBiometrics,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: AppColor
                                                .cWhite, // Set your desired background color here
                                            borderRadius:
                                                BorderRadius.circular(6.0),
                                            border: Border.all(
                                                color:
                                                    AppColor.themeDarkBlueColor,
                                                width: 1),
                                          ),
                                          padding: EdgeInsets.all(5.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(6.0),
                                            child: Image.asset(
                                              DefaultImages.fingerprintblack,
                                              width: 33,
                                              height: 33,
                                              fit: BoxFit.cover,
                                              color: AppColor.cDarkBlueFont,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                if (Platform.isIOS)
                                  if (_isReg.get("regUser") != null)
                                    Material(
                                      child: InkWell(
                                        onTap: authenticateWithBiometrics,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: AppColor
                                                .cWhite, // Set your desired background color here
                                            borderRadius:
                                                BorderRadius.circular(6.0),
                                            border: Border.all(
                                                color:
                                                    AppColor.themeDarkBlueColor,
                                                width: 1),
                                          ),
                                          padding: EdgeInsets.all(5.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(6.0),
                                            child: Image.asset(
                                              DefaultImages.faceidblack,
                                              width: 33,
                                              height: 33,
                                              fit: BoxFit.cover,
                                              color: AppColor.cDarkBlueFont,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                Expanded(
                                  flex: 3,
                                  child: CommonBorderButton(
                                    title: 'SIGN IN WITH USERNAME'.trr,
                                    onPressed: () {
                                      Get.to(
                                          () => LoginManagerWithEmailScreen());
                                      authController.clear();
                                    },
                                  ),
                                ),
                              ],
                            ),
                            verticalSpace(40),
                            Text.rich(
                              TextSpan(
                                text: "Don’t have an account?".trr + " ",
                                style: pRegular14,
                                children: <TextSpan>[
                                  TextSpan(
                                    text: 'Sign up now'.trr,
                                    style: pBold14.copyWith(
                                        color: AppColor.cBlueFont),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () {
                                        Get.to(() => CreateAccountScreen());
                                      },
                                  ),
                                ],
                              ),
                            ),
                            verticalSpace(40),
                            CommonIconBorderButton(
                              iconData: DefaultImages.scannerIcn,
                              title: 'Digital Coupon'.trr,
                              onPressed: () {
                                Get.to(() => VerifyMobileNumberScreen());
                              },
                              bColor: AppColor.cTransparent,
                              btnColor: AppColor.lightDarkBlueColor,
                              width: 250,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String? validatePhoneNumber(String input) {
    String pattern = r'^[0-9]+$';
    RegExp regExp = RegExp(pattern);

    // Trim input to remove spaces and then check
    String trimmedInput = input.trim().replaceAll("+", "");

    if (trimmedInput.isEmpty) {
      return 'Please enter your phone number';
    } else if (!regExp.hasMatch(trimmedInput)) {
      return 'Please enter a valid phone number using digits 0-9';
    }
    return null; // return null if the input is valid
  }
}
