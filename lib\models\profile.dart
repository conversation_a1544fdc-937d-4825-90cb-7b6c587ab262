class Profile {
  Profile({
    String? customer,
    String? accStatus,
    dynamic regActivated,
    String? readOnly,
    String? childCustId,
    dynamic regType,
    bool? completeOnboarding,
    dynamic lastName,
    bool? chkTerm,
    bool? isCaptchaCodeValid,
    dynamic compType,
    dynamic city,
    AuUsers? auUsers,
    AuCust? auCust,
    ReturnMessage? returnMessage,
    dynamic language,
  }) {
    _customer = customer;
    _accStatus = accStatus;
    _regActivated = regActivated;
    _readOnly = readOnly;
    _childCustId = childCustId;
    _regType = regType;
    _completeOnboarding = completeOnboarding;
    _lastName = lastName;
    _chkTerm = chkTerm;
    _isCaptchaCodeValid = isCaptchaCodeValid;
    _compType = compType;
    _city = city;
    _auUsers = auUsers;
    _auCust = auCust;
    _returnMessage = returnMessage;
    _language = language;
  }

  Profile.fromJson(dynamic json) {
    _customer = json['Customer'];
    _accStatus = json['AccStatus'];
    _regActivated = json['RegActivated'];
    _readOnly = json['ReadOnly'];
    _childCustId = json['ChildCustId'];
    _regType = json['RegType'];
    _completeOnboarding = json['CompleteOnboarding'];
    _lastName = json['LastName'];
    _chkTerm = json['ChkTerm'];
    _isCaptchaCodeValid = json['isCaptchaCodeValid'];
    _compType = json['CompType'];
    _city = json['City'];
    _auUsers =
        json['AuUsers'] != null ? AuUsers.fromJson(json['AuUsers']) : null;
    _auCust = json['AuCust'] != null ? AuCust.fromJson(json['AuCust']) : null;
    _returnMessage = json['ReturnMessage'] != null
        ? ReturnMessage.fromJson(json['ReturnMessage'])
        : null;
    _language = json['Language'];
  }
  String? _customer;
  String? _accStatus;
  dynamic _regActivated;
  String? _readOnly;
  String? _childCustId;
  dynamic _regType;
  bool? _completeOnboarding;
  dynamic _lastName;
  bool? _chkTerm;
  bool? _isCaptchaCodeValid;
  dynamic _compType;
  dynamic _city;
  AuUsers? _auUsers;
  AuCust? _auCust;
  ReturnMessage? _returnMessage;
  dynamic _language;

  String? get customer => _customer;
  String? get accStatus => _accStatus;
  dynamic get regActivated => _regActivated;
  String? get readOnly => _readOnly;
  String? get childCustId => _childCustId;
  dynamic get regType => _regType;
  bool? get completeOnboarding => _completeOnboarding;
  dynamic get lastName => _lastName;
  bool? get chkTerm => _chkTerm;
  bool? get isCaptchaCodeValid => _isCaptchaCodeValid;
  dynamic get compType => _compType;
  dynamic get city => _city;
  AuUsers? get auUsers => _auUsers;
  AuCust? get auCust => _auCust;
  ReturnMessage? get returnMessage => _returnMessage;
  dynamic get language => _language;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['Customer'] = _customer;
    map['AccStatus'] = _accStatus;
    map['RegActivated'] = _regActivated;
    map['ReadOnly'] = _readOnly;
    map['ChildCustId'] = _childCustId;
    map['RegType'] = _regType;
    map['CompleteOnboarding'] = _completeOnboarding;
    map['LastName'] = _lastName;
    map['ChkTerm'] = _chkTerm;
    map['isCaptchaCodeValid'] = _isCaptchaCodeValid;
    map['CompType'] = _compType;
    map['City'] = _city;
    if (_auUsers != null) {
      map['AuUsers'] = _auUsers?.toJson();
    }
    if (_auCust != null) {
      map['AuCust'] = _auCust?.toJson();
    }
    if (_returnMessage != null) {
      map['ReturnMessage'] = _returnMessage?.toJson();
    }
    map['Language'] = _language;
    return map;
  }
}

class ReturnMessage {
  ReturnMessage({
    dynamic title,
    dynamic message,
    dynamic subMessage,
    String? messageType,
    dynamic responseAction,
    dynamic redirect,
    String? actionParam,
    String? action,
    bool? isValidTransaction,
  }) {
    _title = title;
    _message = message;
    _subMessage = subMessage;
    _messageType = messageType;
    _responseAction = responseAction;
    _redirect = redirect;
    _actionParam = actionParam;
    _action = action;
    _isValidTransaction = isValidTransaction;
  }

  ReturnMessage.fromJson(dynamic json) {
    _title = json['Title'];
    _message = json['Message'];
    _subMessage = json['SubMessage'];
    _messageType = json['MessageType'];
    _responseAction = json['ResponseAction'];
    _redirect = json['Redirect'];
    _actionParam = json['ActionParam'];
    _action = json['Action'];
    _isValidTransaction = json['isValidTransaction'];
  }
  dynamic _title;
  dynamic _message;
  dynamic _subMessage;
  String? _messageType;
  dynamic _responseAction;
  dynamic _redirect;
  String? _actionParam;
  String? _action;
  bool? _isValidTransaction=false;

  dynamic get title => _title;
  dynamic get message => _message;
  dynamic get subMessage => _subMessage;
  String? get messageType => _messageType;
  dynamic get responseAction => _responseAction;
  dynamic get redirect => _redirect;
  String? get actionParam => _actionParam;
  String? get action => _action;
  bool? get isValidTransaction => _isValidTransaction;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['Title'] = _title;
    map['Message'] = _message;
    map['SubMessage'] = _subMessage;
    map['MessageType'] = _messageType;
    map['ResponseAction'] = _responseAction;
    map['Redirect'] = _redirect;
    map['ActionParam'] = _actionParam;
    map['Action'] = _action;
    map['isValidTransaction'] = _isValidTransaction;
    return map;
  }
}

class AuCust {
  AuCust({
    String? custid,
    dynamic emailid,
    dynamic mobileno,
    dynamic firstname,
    dynamic midname,
    dynamic lastname,
    dynamic tel,
    dynamic pobox,
    dynamic zipcode,
    dynamic gps,
    dynamic addr,
    dynamic fuel91,
    dynamic fuel95,
    dynamic diesel,
    dynamic serviceknown,
    String? regactivated,
    dynamic regvalcode,
    dynamic regactdate,
    String? acctstatus,
    dynamic acctactdate,
    dynamic regtype,
    dynamic accttype,
    dynamic balamt,
    dynamic contractno,
    dynamic contractfrom,
    dynamic contractto,
    dynamic sysdate,
    dynamic sysuserid,
    dynamic compcode,
    dynamic loccode,
    dynamic whcode,
    dynamic crno,
    dynamic companyname,
    dynamic contactperson,
    dynamic designation,
    dynamic companytel,
    dynamic companyfax,
    dynamic rfqno,
    dynamic comptype,
    dynamic consumption,
    dynamic payment,
    dynamic bankguarfile,
    dynamic bank,
    dynamic bankamt,
    dynamic bankguarexpiry,
    dynamic bankrefno,
    dynamic firstnamear,
    dynamic middlenamear,
    dynamic lastnamear,
    dynamic street,
    dynamic buildingno,
    dynamic postalcode,
    dynamic city,
    dynamic district,
    dynamic region,
    dynamic custsubid,
    dynamic guidcode,
    dynamic servicefee,
    dynamic others,
    dynamic profupdt,
    dynamic companynamear,
    dynamic fax,
    dynamic directphone,
    dynamic houseno,
    dynamic acccustid,
    dynamic salesman,
    dynamic salesmanname,
    dynamic b2bbank,
    dynamic b2biban,
    dynamic b2bbban,
    dynamic b2bclientid,
    dynamic b2bbal,
    dynamic vatno,
    dynamic mothercompany,
    dynamic cardscflag,
    dynamic lang,
    dynamic usertype,
    dynamic origin,
    dynamic password,
    dynamic oldpassword,
    dynamic role,
    dynamic custdistricts,
    dynamic otherstn,
    dynamic showtc,
    dynamic empno,
    dynamic placesallow,
    dynamic sceffectivedate,
    dynamic scprocess,
    dynamic otherstnallow,
    dynamic salesrep,
    dynamic ispartner,
    dynamic otherbal,
    dynamic minusflag,
    dynamic custtype,
    String? expro,
    dynamic idtype,
    dynamic idnumber,
    dynamic country,
    dynamic regioncode,
    dynamic citycode,
    dynamic districtcode,
    dynamic isverified,
  }) {
    _custid = custid;
    _emailid = emailid;
    _mobileno = mobileno;
    _firstname = firstname;
    _midname = midname;
    _lastname = lastname;
    _tel = tel;
    _pobox = pobox;
    _zipcode = zipcode;
    _gps = gps;
    _addr = addr;
    _fuel91 = fuel91;
    _fuel95 = fuel95;
    _diesel = diesel;
    _serviceknown = serviceknown;
    _regactivated = regactivated;
    _regvalcode = regvalcode;
    _regactdate = regactdate;
    _acctstatus = acctstatus;
    _acctactdate = acctactdate;
    _regtype = regtype;
    _accttype = accttype;
    _balamt = balamt;
    _contractno = contractno;
    _contractfrom = contractfrom;
    _contractto = contractto;
    _sysdate = sysdate;
    _sysuserid = sysuserid;
    _compcode = compcode;
    _loccode = loccode;
    _whcode = whcode;
    _crno = crno;
    _companyname = companyname;
    _contactperson = contactperson;
    _designation = designation;
    _companytel = companytel;
    _companyfax = companyfax;
    _rfqno = rfqno;
    _comptype = comptype;
    _consumption = consumption;
    _payment = payment;
    _bankguarfile = bankguarfile;
    _bank = bank;
    _bankamt = bankamt;
    _bankguarexpiry = bankguarexpiry;
    _bankrefno = bankrefno;
    _firstnamear = firstnamear;
    _middlenamear = middlenamear;
    _lastnamear = lastnamear;
    _street = street;
    _buildingno = buildingno;
    _postalcode = postalcode;
    _city = city;
    _district = district;
    _region = region;
    _custsubid = custsubid;
    _guidcode = guidcode;
    _servicefee = servicefee;
    _others = others;
    _profupdt = profupdt;
    _companynamear = companynamear;
    _fax = fax;
    _directphone = directphone;
    _houseno = houseno;
    _acccustid = acccustid;
    _salesman = salesman;
    _salesmanname = salesmanname;
    _b2bbank = b2bbank;
    _b2biban = b2biban;
    _b2bbban = b2bbban;
    _b2bclientid = b2bclientid;
    _b2bbal = b2bbal;
    _vatno = vatno;
    _mothercompany = mothercompany;
    _cardscflag = cardscflag;
    _lang = lang;
    _usertype = usertype;
    _origin = origin;
    _password = password;
    _oldpassword = oldpassword;
    _role = role;
    _custdistricts = custdistricts;
    _otherstn = otherstn;
    _showtc = showtc;
    _empno = empno;
    _placesallow = placesallow;
    _sceffectivedate = sceffectivedate;
    _scprocess = scprocess;
    _otherstnallow = otherstnallow;
    _salesrep = salesrep;
    _ispartner = ispartner;
    _otherbal = otherbal;
    _minusflag = minusflag;
    _custtype = custtype;
    _expro = expro;
    _idtype = idtype;
    _idnumber = idnumber;
    _country = country;
    _regioncode = regioncode;
    _citycode = citycode;
    _districtcode = districtcode;
    _isverified = isverified;
  }

  AuCust.fromJson(dynamic json) {
    _custid = json['CUSTID'];
    _emailid = json['EMAILID'];
    _mobileno = json['MOBILENO'];
    _firstname = json['FIRSTNAME'];
    _midname = json['MIDNAME'];
    _lastname = json['LASTNAME'];
    _tel = json['TEL'];
    _pobox = json['POBOX'];
    _zipcode = json['ZIPCODE'];
    _gps = json['GPS'];
    _addr = json['ADDR'];
    _fuel91 = json['FUEL91'];
    _fuel95 = json['FUEL95'];
    _diesel = json['DIESEL'];
    _serviceknown = json['SERVICEKNOWN'];
    _regactivated = json['REGACTIVATED'];
    _regvalcode = json['REGVALCODE'];
    _regactdate = json['REGACTDATE'];
    _acctstatus = json['ACCTSTATUS'];
    _acctactdate = json['ACCTACTDATE'];
    _regtype = json['REGTYPE'];
    _accttype = json['ACCTTYPE'];
    _balamt = json['BALAMT'];
    _contractno = json['CONTRACTNO'];
    _contractfrom = json['CONTRACTFROM'];
    _contractto = json['CONTRACTTO'];
    _sysdate = json['SYS_DATE'];
    _sysuserid = json['SYSUSERID'];
    _compcode = json['COMPCODE'];
    _loccode = json['LOCCODE'];
    _whcode = json['WHCODE'];
    _crno = json['CRNO'];
    _companyname = json['COMPANYNAME'];
    _contactperson = json['CONTACTPERSON'];
    _designation = json['DESIGNATION'];
    _companytel = json['COMPANYTEL'];
    _companyfax = json['COMPANYFAX'];
    _rfqno = json['RFQNO'];
    _comptype = json['COMPTYPE'];
    _consumption = json['CONSUMPTION'];
    _payment = json['PAYMENT'];
    _bankguarfile = json['BANKGUARFILE'];
    _bank = json['BANK'];
    _bankamt = json['BANKAMT'];
    _bankguarexpiry = json['BANKGUAREXPIRY'];
    _bankrefno = json['BANKREFNO'];
    _firstnamear = json['FIRSTNAMEAR'];
    _middlenamear = json['MIDDLENAMEAR'];
    _lastnamear = json['LASTNAMEAR'];
    _street = json['STREET'];
    _buildingno = json['BUILDING_NO'];
    _postalcode = json['POSTALCODE'];
    _city = json['CITY'];
    _district = json['DISTRICT'];
    _region = json['REGION'];
    _custsubid = json['CUSTSUBID'];
    _guidcode = json['GUIDCODE'];
    _servicefee = json['SERVICE_FEE'];
    _others = json['OTHERS'];
    _profupdt = json['PROF_UPDT'];
    _companynamear = json['COMPANYNAMEAR'];
    _fax = json['FAX'];
    _directphone = json['DIRECT_PHONE'];
    _houseno = json['HOUSE_NO'];
    _acccustid = json['ACC_CUSTID'];
    _salesman = json['SALESMAN'];
    _salesmanname = json['SALESMAN_NAME'];
    _b2bbank = json['B2B_BANK'];
    _b2biban = json['B2B_IBAN'];
    _b2bbban = json['B2B_BBAN'];
    _b2bclientid = json['B2B_CLIENTID'];
    _b2bbal = json['B2B_BAL'];
    _vatno = json['VAT_NO'];
    _mothercompany = json['MOTHER_COMPANY'];
    _cardscflag = json['CARD_SC_FLAG'];
    _lang = json['LANG'];
    _usertype = json['USERTYPE'];
    _origin = json['ORIGIN'];
    _password = json['PASSWORD'];
    _oldpassword = json['OLDPASSWORD'];
    _role = json['ROLE'];
    _custdistricts = json['CUST_DISTRICTS'];
    _otherstn = json['OTHERSTN'];
    _showtc = json['SHOWTC'];
    _empno = json['EMP_NO'];
    _placesallow = json['PLACES_ALLOW'];
    _sceffectivedate = json['SC_EFFECTIVE_DATE'];
    _scprocess = json['SC_PROCESS'];
    _otherstnallow = json['OTHER_STN_ALLOW'];
    _salesrep = json['SALESREP'];
    _ispartner = json['IS_PARTNER'];
    _otherbal = json['OTHER_BAL'];
    _minusflag = json['MINUS_FLAG'];
    _custtype = json['CUST_TYPE'];
    _expro = json['EXPRO'];
    _idtype = json['ID_TYPE'];
    _idnumber = json['ID_NUMBER'];
    _country = json['COUNTRY'];
    _regioncode = json['REGION_CODE'];
    _citycode = json['CITY_CODE'];
    _districtcode = json['DISTRICT_CODE'];
    _isverified = json['IS_VERIFIED'];
  }
  String? _custid;
  dynamic _emailid;
  dynamic _mobileno;
  dynamic _firstname;
  dynamic _midname;
  dynamic _lastname;
  dynamic _tel;
  dynamic _pobox;
  dynamic _zipcode;
  dynamic _gps;
  dynamic _addr;
  dynamic _fuel91;
  dynamic _fuel95;
  dynamic _diesel;
  dynamic _serviceknown;
  String? _regactivated;
  dynamic _regvalcode;
  dynamic _regactdate;
  String? _acctstatus;
  dynamic _acctactdate;
  dynamic _regtype;
  dynamic _accttype;
  dynamic _balamt;
  dynamic _contractno;
  dynamic _contractfrom;
  dynamic _contractto;
  dynamic _sysdate;
  dynamic _sysuserid;
  dynamic _compcode;
  dynamic _loccode;
  dynamic _whcode;
  dynamic _crno;
  dynamic _companyname;
  dynamic _contactperson;
  dynamic _designation;
  dynamic _companytel;
  dynamic _companyfax;
  dynamic _rfqno;
  dynamic _comptype;
  dynamic _consumption;
  dynamic _payment;
  dynamic _bankguarfile;
  dynamic _bank;
  dynamic _bankamt;
  dynamic _bankguarexpiry;
  dynamic _bankrefno;
  dynamic _firstnamear;
  dynamic _middlenamear;
  dynamic _lastnamear;
  dynamic _street;
  dynamic _buildingno;
  dynamic _postalcode;
  dynamic _city;
  dynamic _district;
  dynamic _region;
  dynamic _custsubid;
  dynamic _guidcode;
  dynamic _servicefee;
  dynamic _others;
  dynamic _profupdt;
  dynamic _companynamear;
  dynamic _fax;
  dynamic _directphone;
  dynamic _houseno;
  dynamic _acccustid;
  dynamic _salesman;
  dynamic _salesmanname;
  dynamic _b2bbank;
  dynamic _b2biban;
  dynamic _b2bbban;
  dynamic _b2bclientid;
  dynamic _b2bbal;
  dynamic _vatno;
  dynamic _mothercompany;
  dynamic _cardscflag;
  dynamic _lang;
  dynamic _usertype;
  dynamic _origin;
  dynamic _password;
  dynamic _oldpassword;
  dynamic _role;
  dynamic _custdistricts;
  dynamic _otherstn;
  dynamic _showtc;
  dynamic _empno;
  dynamic _placesallow;
  dynamic _sceffectivedate;
  dynamic _scprocess;
  dynamic _otherstnallow;
  dynamic _salesrep;
  dynamic _ispartner;
  dynamic _otherbal;
  dynamic _minusflag;
  dynamic _custtype;
  String? _expro;
  dynamic _idtype;
  dynamic _idnumber;
  dynamic _country;
  dynamic _regioncode;
  dynamic _citycode;
  dynamic _districtcode;
  dynamic _isverified;

  String? get custid => _custid;
  dynamic get emailid => _emailid;
  dynamic get mobileno => _mobileno;
  dynamic get firstname => _firstname;
  dynamic get midname => _midname;
  dynamic get lastname => _lastname;
  dynamic get tel => _tel;
  dynamic get pobox => _pobox;
  dynamic get zipcode => _zipcode;
  dynamic get gps => _gps;
  dynamic get addr => _addr;
  dynamic get fuel91 => _fuel91;
  dynamic get fuel95 => _fuel95;
  dynamic get diesel => _diesel;
  dynamic get serviceknown => _serviceknown;
  String? get regactivated => _regactivated;
  dynamic get regvalcode => _regvalcode;
  dynamic get regactdate => _regactdate;
  String? get acctstatus => _acctstatus;
  dynamic get acctactdate => _acctactdate;
  dynamic get regtype => _regtype;
  dynamic get accttype => _accttype;
  dynamic get balamt => _balamt;
  dynamic get contractno => _contractno;
  dynamic get contractfrom => _contractfrom;
  dynamic get contractto => _contractto;
  dynamic get sysdate => _sysdate;
  dynamic get sysuserid => _sysuserid;
  dynamic get compcode => _compcode;
  dynamic get loccode => _loccode;
  dynamic get whcode => _whcode;
  dynamic get crno => _crno;
  dynamic get companyname => _companyname;
  dynamic get contactperson => _contactperson;
  dynamic get designation => _designation;
  dynamic get companytel => _companytel;
  dynamic get companyfax => _companyfax;
  dynamic get rfqno => _rfqno;
  dynamic get comptype => _comptype;
  dynamic get consumption => _consumption;
  dynamic get payment => _payment;
  dynamic get bankguarfile => _bankguarfile;
  dynamic get bank => _bank;
  dynamic get bankamt => _bankamt;
  dynamic get bankguarexpiry => _bankguarexpiry;
  dynamic get bankrefno => _bankrefno;
  dynamic get firstnamear => _firstnamear;
  dynamic get middlenamear => _middlenamear;
  dynamic get lastnamear => _lastnamear;
  dynamic get street => _street;
  dynamic get buildingno => _buildingno;
  dynamic get postalcode => _postalcode;
  dynamic get city => _city;
  dynamic get district => _district;
  dynamic get region => _region;
  dynamic get custsubid => _custsubid;
  dynamic get guidcode => _guidcode;
  dynamic get servicefee => _servicefee;
  dynamic get others => _others;
  dynamic get profupdt => _profupdt;
  dynamic get companynamear => _companynamear;
  dynamic get fax => _fax;
  dynamic get directphone => _directphone;
  dynamic get houseno => _houseno;
  dynamic get acccustid => _acccustid;
  dynamic get salesman => _salesman;
  dynamic get salesmanname => _salesmanname;
  dynamic get b2bbank => _b2bbank;
  dynamic get b2biban => _b2biban;
  dynamic get b2bbban => _b2bbban;
  dynamic get b2bclientid => _b2bclientid;
  dynamic get b2bbal => _b2bbal;
  dynamic get vatno => _vatno;
  dynamic get mothercompany => _mothercompany;
  dynamic get cardscflag => _cardscflag;
  dynamic get lang => _lang;
  dynamic get usertype => _usertype;
  dynamic get origin => _origin;
  dynamic get password => _password;
  dynamic get oldpassword => _oldpassword;
  dynamic get role => _role;
  dynamic get custdistricts => _custdistricts;
  dynamic get otherstn => _otherstn;
  dynamic get showtc => _showtc;
  dynamic get empno => _empno;
  dynamic get placesallow => _placesallow;
  dynamic get sceffectivedate => _sceffectivedate;
  dynamic get scprocess => _scprocess;
  dynamic get otherstnallow => _otherstnallow;
  dynamic get salesrep => _salesrep;
  dynamic get ispartner => _ispartner;
  dynamic get otherbal => _otherbal;
  dynamic get minusflag => _minusflag;
  dynamic get custtype => _custtype;
  String? get expro => _expro;
  dynamic get idtype => _idtype;
  dynamic get idnumber => _idnumber;
  dynamic get country => _country;
  dynamic get regioncode => _regioncode;
  dynamic get citycode => _citycode;
  dynamic get districtcode => _districtcode;
  dynamic get isverified => _isverified;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['CUSTID'] = _custid;
    map['EMAILID'] = _emailid;
    map['MOBILENO'] = _mobileno;
    map['FIRSTNAME'] = _firstname;
    map['MIDNAME'] = _midname;
    map['LASTNAME'] = _lastname;
    map['TEL'] = _tel;
    map['POBOX'] = _pobox;
    map['ZIPCODE'] = _zipcode;
    map['GPS'] = _gps;
    map['ADDR'] = _addr;
    map['FUEL91'] = _fuel91;
    map['FUEL95'] = _fuel95;
    map['DIESEL'] = _diesel;
    map['SERVICEKNOWN'] = _serviceknown;
    map['REGACTIVATED'] = _regactivated;
    map['REGVALCODE'] = _regvalcode;
    map['REGACTDATE'] = _regactdate;
    map['ACCTSTATUS'] = _acctstatus;
    map['ACCTACTDATE'] = _acctactdate;
    map['REGTYPE'] = _regtype;
    map['ACCTTYPE'] = _accttype;
    map['BALAMT'] = _balamt;
    map['CONTRACTNO'] = _contractno;
    map['CONTRACTFROM'] = _contractfrom;
    map['CONTRACTTO'] = _contractto;
    map['SYS_DATE'] = _sysdate;
    map['SYSUSERID'] = _sysuserid;
    map['COMPCODE'] = _compcode;
    map['LOCCODE'] = _loccode;
    map['WHCODE'] = _whcode;
    map['CRNO'] = _crno;
    map['COMPANYNAME'] = _companyname;
    map['CONTACTPERSON'] = _contactperson;
    map['DESIGNATION'] = _designation;
    map['COMPANYTEL'] = _companytel;
    map['COMPANYFAX'] = _companyfax;
    map['RFQNO'] = _rfqno;
    map['COMPTYPE'] = _comptype;
    map['CONSUMPTION'] = _consumption;
    map['PAYMENT'] = _payment;
    map['BANKGUARFILE'] = _bankguarfile;
    map['BANK'] = _bank;
    map['BANKAMT'] = _bankamt;
    map['BANKGUAREXPIRY'] = _bankguarexpiry;
    map['BANKREFNO'] = _bankrefno;
    map['FIRSTNAMEAR'] = _firstnamear;
    map['MIDDLENAMEAR'] = _middlenamear;
    map['LASTNAMEAR'] = _lastnamear;
    map['STREET'] = _street;
    map['BUILDING_NO'] = _buildingno;
    map['POSTALCODE'] = _postalcode;
    map['CITY'] = _city;
    map['DISTRICT'] = _district;
    map['REGION'] = _region;
    map['CUSTSUBID'] = _custsubid;
    map['GUIDCODE'] = _guidcode;
    map['SERVICE_FEE'] = _servicefee;
    map['OTHERS'] = _others;
    map['PROF_UPDT'] = _profupdt;
    map['COMPANYNAMEAR'] = _companynamear;
    map['FAX'] = _fax;
    map['DIRECT_PHONE'] = _directphone;
    map['HOUSE_NO'] = _houseno;
    map['ACC_CUSTID'] = _acccustid;
    map['SALESMAN'] = _salesman;
    map['SALESMAN_NAME'] = _salesmanname;
    map['B2B_BANK'] = _b2bbank;
    map['B2B_IBAN'] = _b2biban;
    map['B2B_BBAN'] = _b2bbban;
    map['B2B_CLIENTID'] = _b2bclientid;
    map['B2B_BAL'] = _b2bbal;
    map['VAT_NO'] = _vatno;
    map['MOTHER_COMPANY'] = _mothercompany;
    map['CARD_SC_FLAG'] = _cardscflag;
    map['LANG'] = _lang;
    map['USERTYPE'] = _usertype;
    map['ORIGIN'] = _origin;
    map['PASSWORD'] = _password;
    map['OLDPASSWORD'] = _oldpassword;
    map['ROLE'] = _role;
    map['CUST_DISTRICTS'] = _custdistricts;
    map['OTHERSTN'] = _otherstn;
    map['SHOWTC'] = _showtc;
    map['EMP_NO'] = _empno;
    map['PLACES_ALLOW'] = _placesallow;
    map['SC_EFFECTIVE_DATE'] = _sceffectivedate;
    map['SC_PROCESS'] = _scprocess;
    map['OTHER_STN_ALLOW'] = _otherstnallow;
    map['SALESREP'] = _salesrep;
    map['IS_PARTNER'] = _ispartner;
    map['OTHER_BAL'] = _otherbal;
    map['MINUS_FLAG'] = _minusflag;
    map['CUST_TYPE'] = _custtype;
    map['EXPRO'] = _expro;
    map['ID_TYPE'] = _idtype;
    map['ID_NUMBER'] = _idnumber;
    map['COUNTRY'] = _country;
    map['REGION_CODE'] = _regioncode;
    map['CITY_CODE'] = _citycode;
    map['DISTRICT_CODE'] = _districtcode;
    map['IS_VERIFIED'] = _isverified;
    return map;
  }
}

class AuUsers {
  AuUsers({
    String? custid,
    String? emailid,
    dynamic oldemailid,
    String? password,
    String? loginstatus,
    dynamic loginctr,
    String? lockexpire,
    String? resetpass,
    String? activecode,
    String? primary,
    String? role,
    dynamic rcvemail,
    dynamic username,
    dynamic authflag,
  }) {
    _custid = custid;
    _emailid = emailid;
    _oldemailid = oldemailid;
    _password = password;
    _loginstatus = loginstatus;
    _loginctr = loginctr;
    _lockexpire = lockexpire;
    _resetpass = resetpass;
    _activecode = activecode;
    _primary = primary;
    _role = role;
    _rcvemail = rcvemail;
    _username = username;
    _authflag = authflag;
  }

  AuUsers.fromJson(dynamic json) {
    _custid = json['CUSTID'];
    _emailid = json['EMAILID'];
    _oldemailid = json['OLDEMAILID'];
    _password = json['PASSWORD'];
    _loginstatus = json['LOGINSTATUS'];
    _loginctr = json['LOGINCTR'];
    _lockexpire = json['LOCKEXPIRE'];
    _resetpass = json['RESETPASS'];
    _activecode = json['ACTIVECODE'];
    _primary = json['PRIMARY'];
    _role = json['ROLE'];
    _rcvemail = json['RCV_EMAIL'];
    _username = json['USERNAME'];
    _authflag = json['AUTH_FLAG'];
  }
  String? _custid;
  String? _emailid;
  dynamic _oldemailid;
  String? _password;
  String? _loginstatus;
  dynamic _loginctr;
  String? _lockexpire;
  String? _resetpass;
  String? _activecode;
  String? _primary;
  String? _role;
  dynamic _rcvemail;
  dynamic _username;
  dynamic _authflag;

  String? get custid => _custid;
  String? get emailid => _emailid;
  dynamic get oldemailid => _oldemailid;
  String? get password => _password;
  String? get loginstatus => _loginstatus;
  dynamic get loginctr => _loginctr;
  String? get lockexpire => _lockexpire;
  String? get resetpass => _resetpass;
  String? get activecode => _activecode;
  String? get primary => _primary;
  String? get role => _role;
  dynamic get rcvemail => _rcvemail;
  dynamic get username => _username;
  dynamic get authflag => _authflag;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['CUSTID'] = _custid;
    map['EMAILID'] = _emailid;
    map['OLDEMAILID'] = _oldemailid;
    map['PASSWORD'] = _password;
    map['LOGINSTATUS'] = _loginstatus;
    map['LOGINCTR'] = _loginctr;
    map['LOCKEXPIRE'] = _lockexpire;
    map['RESETPASS'] = _resetpass;
    map['ACTIVECODE'] = _activecode;
    map['PRIMARY'] = _primary;
    map['ROLE'] = _role;
    map['RCV_EMAIL'] = _rcvemail;
    map['USERNAME'] = _username;
    map['AUTH_FLAG'] = _authflag;
    return map;
  }
}
