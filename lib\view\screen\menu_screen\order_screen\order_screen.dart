// ignore_for_file: must_be_immutable, prefer_const_constructors, avoid_print,, sized_box_for_whitespace, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:waie_app/core/controller/menu_controller/order_controller/new_order_controller.dart';
import 'package:waie_app/core/controller/menu_controller/order_controller/price_tag_card_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/order_widget.dart';
import 'package:waie_app/view/screen/notification_screen/show_tag_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/order_controller/confirm_order_controller.dart';
import '../../../../core/controller/menu_controller/order_controller/order_controller.dart';
import '../../../widget/common_button.dart';
import '../balance_topup_screen/balance_topup_tab_screen.dart';

class OrderScreen extends StatefulWidget {
  const OrderScreen({super.key});

  @override
  State<OrderScreen> createState() => _OrderScreenState();
}

class _OrderScreenState extends State<OrderScreen> {
  Controller controller = Get.put(Controller());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              simpleAppBar(
                title: "New service order".trr,
                onTap: () {
                  Get.back();
                },
                backString: "Back".trr,
              ),
              // GetBuilder<Controller>(
              //   id: 'aVeryUniqueID', // here
              //   init: Controller(),
              //   builder: (value) => Text(
              //     '${value.counter}', // this will update
              //   ),
              // ),
              // GetBuilder<Controller>(
              //   id: 'someOtherID', // here
              //   init: Controller(),
              //   builder: (value) => Text(
              //     '${value.counter}', // this won't update
              //   ),
              // ),
              // ElevatedButton(
              //     onPressed: () {
              //       controller.increment();
              //     },
              //     child: Text("Click"))
            ],
          ),
        ),
      ),
    );
  }
}
