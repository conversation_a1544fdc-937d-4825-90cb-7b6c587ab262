import 'package:hive_flutter/hive_flutter.dart';

// reference a box
final _isReg = Hive.box('isReg_DB');
final _isRemember = Hive.box('isRemember_DB');
final _isActivate = Hive.box('isActivate_DB');

class RegisterDatabase {
  // check reg userinfo
  void createUserInfoData(
      String username, String password, String active) async {
    await _isReg.put('regUser', {
      'username': username,
      'password': password,
      'status': active,
    });
    print(_isReg.getAt(0));
  }

  // load data if it is already exists
  void loadData() async {
    final regUser = _isReg.get('regUser');
    print('Loki is $regUser years old.');
    print('username is ${regUser?['username']}');
    print('password is ${regUser?['password']}');
    print('active is ${regUser?['status']}');
  }

  // update database
  void deleteDatabase() async {
    await _isReg.delete('regUser');
    await _isReg.clear();
  }

  // get status
  getStatus() async {
    final regUser = _isReg.get('regUser');

    bool stat = regUser?['status'];
    return stat;
  }

  // ===============================================

  // check Remember Me
  void createRememberMe(String username, String active) async {
    await _isRemember.put('regRememberMe', {
      'username': username,
      'status': active,
    });
    print(_isRemember.getAt(0));
  }

  // load data if it is already exists
  void loadRememberMe() async {
    final regRememberMe = _isRemember.get('regRememberMe');
    print('regRememberMe is $regRememberMe .');
    print('username is ${regRememberMe?['username']}');
    print('active is ${regRememberMe?['status']}');
  }

  // update database
  void deleteRememberMe() async {
    await _isRemember.delete('regRememberMe');
    await _isRemember.clear();
  }

  // ===============================================

  // check Activate
  void createActivate(String active) async {
    await _isActivate.put('regActivate', {
      'status': active,
    });
    print(_isActivate.getAt(0));
  }

  // load data if it is already exists
  void loadActivate() async {
    final regActivate = _isActivate.get('regActivate');
    print('regActivate is $regActivate .');
    print('active is ${regActivate?['status']}');
  }

  // update database
  void deleteActivate() async {
    await _isActivate.delete('regActivate');
    await _isActivate.clear();
  }
}
