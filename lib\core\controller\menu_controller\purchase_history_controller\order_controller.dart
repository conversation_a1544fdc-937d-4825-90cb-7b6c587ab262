import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/new_purchase_history_screen.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/purchse_history_screen.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/refund_menu_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/loading_widget.dart';
import 'package:quickalert/quickalert.dart';

import '../../../../models/order_history.dart';
import '../../../../models/profile.dart';
import '../../../../utils/api_endpoints.dart';

class OrderController extends GetxController {
  var searchController = TextEditingController().obs;

  RxList orderList = [
    {
      "code": "00000876345",
      "orderDate": "23.06.2023",
      "status": "Pending",
      "orderType": "Tag",
      "paymentMethod": "Aldrees promotion ",
      "paymentMethod2": "(STC Qitaf)",
      "price": "100.00 SAR",
      "quantity": "5",
      "totalValue": "500.00 SAR",
    },
    {
      "code": "00000876346",
      "orderDate": "23.06.2023",
      "status": "Claimed",
      "orderType": "Tag",
      "paymentMethod": "Aldrees promotion ",
      "paymentMethod2": "(STC Qitaf)",
      "price": "100.00 SAR",
      "quantity": "5",
      "totalValue": "500.00 SAR",
    },
    {
      "code": "00000876347",
      "orderDate": "23.06.2023",
      "status": "Claimed",
      "orderType": "Tag",
      "paymentMethod": "Aldrees promotion ",
      "paymentMethod2": "(STC Qitaf)",
      "price": "100.00 SAR",
      "quantity": "5",
      "totalValue": "500.00 SAR",
    },
  ].obs;
  var orderHistoryList = <OrderHistory>[].obs;
  RxList itemList = [].obs;
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    searchController.value = TextEditingController();
    getOrderHistory();
  }

  getOrderHistory() async {
    await fetchOrderHistory(1);
  }

  Future<dynamic> fetchOrderHistory(int pageNo) async {
    orderHistoryList.refresh();
    orderHistoryList.clear();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    print("pageNo >> $pageNo");
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getOrderHistory),
          body: {
            "userid": userid,
            "serviceType": "A",
            "orderType": "S",
            "PageNo": pageNo.toString(),
            "orderstatus": "",
            "paytype": "",
            "IsAr": Constants.IsAr_App
          });

      print("Order History ==============${response.body}");

      Iterable list = jsonDecode(response.body);
      List<OrderHistory> orderHistory = List<OrderHistory>.from(
          list.map((model) => OrderHistory.fromJson(model)));
      for (OrderHistory orderHis in orderHistory) {
        await Future.delayed(
            const Duration(seconds: 0), () => orderHistoryList.add(orderHis));
      }

      print("Order History ==============${orderHistoryList[0].ORDERID}");
      return orderHistoryList;
    } catch (e) {
      log("Order History Error  : $e");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  cancelPurchaseHistory(orderid) async {
    Loader.showLoader();
    print("cancelPurchaseHistory orderid>>>>>>> $orderid");
    Navigator.of(Get.context!).pop();
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.cancelPurchaseHistory),
          body: {
            "orderid": orderid,
            "isAr": Constants.IsAr_App,
          });
      if (jsonDecode(response.body)["MessageType"] == "success") {
        Loader.hideLoader();
        orderHistoryList.refresh();
        getOrderHistory();
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.success,
          text: jsonDecode(response.body)['Message'].toString(),
        );
        await Get.offAll(() => const NewPurchaseHistoryScreen());
        //Get.back();
        // Get.back();
        // showDialog(
        //   barrierDismissible: false,
        //   context: Get.context!,
        //   builder: (context) {
        //     return AlertDialog(
        //       insetPadding: const EdgeInsets.all(16),
        //       contentPadding: const EdgeInsets.all(24),
        //       shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12)),
        //       content: Column(
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           Text(
        //             jsonDecode(response.body)['Message'].toString(),
        //             style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
        //             textAlign: TextAlign.center,
        //           ),
        //           verticalSpace(24),
        //           CommonButton(
        //             title: "OK".tr,
        //             onPressed: () async {
        //               orderHistoryList.refresh();
        //               getOrderHistory();
        //               //Get.offAll(() => const RefundMenuScreen());
        //               await Get.off(() => PurchaseHistoryScreen(),
        //                   preventDuplicates: true);
        //               Get.back();
        //               Get.back();
        //             },
        //             btnColor: AppColor.themeOrangeColor,
        //           )
        //         ],
        //       ),
        //     );
        //   },
        // );
      } else {
        Loader.hideLoader();
        orderHistoryList.refresh();
        getOrderHistory();
        await Get.offAll(() => PurchaseHistoryScreen());
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          text: jsonDecode(response.body)['Message'].toString(),
        );
        Get.back();
        // showDialog(
        //   barrierDismissible: false,
        //   context: Get.context!,
        //   builder: (context) {
        //     return AlertDialog(
        //       insetPadding: const EdgeInsets.all(16),
        //       contentPadding: const EdgeInsets.all(24),
        //       shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12)),
        //       content: Column(
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           Text(
        //             jsonDecode(response.body)['Message'].toString(),
        //             style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
        //             textAlign: TextAlign.center,
        //           ),
        //           verticalSpace(24),
        //           CommonButton(
        //             title: "OK".tr,
        //             onPressed: () async {
        //               //Get.offAll(() => const RefundMenuScreen());
        //               orderHistoryList.refresh();
        //               getOrderHistory();
        //               //Get.offAll(() => const RefundMenuScreen());
        //               await Get.off(() => PurchaseHistoryScreen(),
        //                   preventDuplicates: true);
        //               Get.back();
        //               Get.back();
        //             },
        //             btnColor: AppColor.themeOrangeColor,
        //           )
        //         ],
        //       ),
        //     );
        //   },
        // );
      }

      return response;
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
