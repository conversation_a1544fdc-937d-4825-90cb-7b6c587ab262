// ignore_for_file: prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/purchse_history_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

class CardFailedBalanceScreen extends StatelessWidget {
  const CardFailedBalanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            simpleAppBar(
                title: "Balance top up".trr,
                onTap: () {
                  Get.back();
                },
                backString: "Back".trr),
            Expanded(
                child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 25, 16, 16),
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4), border: Border.all(color: AppColor.cBorder)),
                      padding: EdgeInsets.all(32),
                      child: Column(
                        children: [
                          Text.rich(
                              TextSpan(text: 'Your order'.trr+' ', style: pRegular16.copyWith(), children: [
                                TextSpan(
                                  text: '#***********',
                                  style: pBold16.copyWith(),
                                ),
                                TextSpan(text: ' '+'is pending'.trr+' ')
                              ]),
                              textAlign: TextAlign.center),
                          verticalSpace(16),
                          Text(
                            "In some banks, the transaction may take a little longer. Check your order status in a few minutes.".trr,
                            style: pRegular14,
                            textAlign: TextAlign.center,
                          ),
                          verticalSpace(20),
                          CommonButton(
                            title: 'go to purchase history'.trr.toUpperCase(),
                            onPressed: () {
                              Get.to(PurchaseHistoryScreen());
                            },
                            btnColor: AppColor.themeOrangeColor,
                          ),
                        ],
                      ),
                    ),
                    verticalSpace(20),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Tags".trr,
                            style: pRegular14,
                          ),
                          Text(
                            "0",
                            style: pRegular14,
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 32),
                      child: horizontalDivider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Total".trr,
                            style: pRegular14,
                          ),
                          Text(
                            "0.00" + " SAR".trr,
                            style: pRegular14,
                          ),
                        ],
                      ),
                    ),
                    verticalSpace(26),
                    CommonButton(
                      title: 'Order will be automatically confirmed after successful payment'.trr,
                      btnColor: AppColor.cLightGrey,
                      textColor: AppColor.cText,
                      horizontalPadding: 1,
                    )
                  ],
                ),
              ),
            ))
          ],
        ),
      ),
    );
  }
}
