// ignore_for_file: prefer_const_constructors

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:webview_flutter_plus/webview_flutter_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../widget/loading_widget.dart';

class AboutWebViewScreen extends StatefulWidget {
  final String url;

  const AboutWebViewScreen({
    super.key,
    required this.url,
  });

  @override
  AboutWebViewScreenState createState() => AboutWebViewScreenState();
}

class AboutWebViewScreenState extends State<AboutWebViewScreen> {
  late WebViewController _controler;
  final double _height = 1;
  bool isLoading = true;

  @override
  void initState() {
    _controler = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse('https://www.aldrees.com/about-overview'))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (progress) {
            print("progress---> $progress");
            if (progress == 100) {
              setState(() {
                isLoading = false;
              });
            }
          },
        ),
      );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColor.themeBlueColor,
        title: Text(
          "About".trr,
          style: pBold20.copyWith(color: AppColor.cWhiteFont),
          textAlign: TextAlign.center,
        ),
      ),
      body: isLoading
          ? const Center(
              child: LoadingWidget(),
            )
          : WebViewWidget(
              controller: _controler,
            ),
    );
  }
}
