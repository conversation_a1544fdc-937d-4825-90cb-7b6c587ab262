// ignore_for_file: prefer_const_constructors

import 'dart:convert';
import 'dart:developer';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:waie_app/models/gas_station.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/constants.dart';

import '../../../models/installation_center.dart';
import '../../../models/sales_station.dart';

class InstallationCenterController extends GetxController {
  RxBool isGasStations = true.obs;
  RxBool isSalesOffice = false.obs;
  RxBool isInstallationCenters = false.obs;
  RxBool isMap = true.obs;
  RxBool isDefaultMap = true.obs;
  RxBool isList = false.obs;
  List<LatLng> latLongList = [
    LatLng(21.44484793949768, 53.295109691390245),
    LatLng(20.582120409829233, 44.96018559757371),
    LatLng(23.899725409740984, 54.046233681296975),
    LatLng(22.302502578679334, 47.422575301577865),
    LatLng(25.169169066706516, 38.833075072755804),
    LatLng(23.359337577784572, 45.01153009917833),
    LatLng(20.950188013577115, 49.39292756943918),
  ];
  List<LatLng> salesOfficeList = [
    LatLng(21.44484793949768, 53.295109691390245),
    LatLng(20.582120409829233, 44.96018559757371),
    LatLng(23.899725409740984, 54.046233681296975),
    LatLng(22.302502578679334, 47.422575301577865),
    LatLng(25.169169066706516, 38.833075072755804),
    LatLng(23.359337577784572, 45.01153009917833),
    LatLng(20.950188013577115, 49.39292756943918),
  ];
  List<LatLng> installationList = [
    LatLng(21.44484793949768, 53.295109691390245),
    LatLng(20.582120409829233, 44.96018559757371),
    LatLng(23.899725409740984, 54.046233681296975),
    LatLng(22.302502578679334, 47.422575301577865),
    LatLng(25.169169066706516, 38.833075072755804),
    LatLng(23.359337577784572, 45.01153009917833),
    LatLng(20.950188013577115, 49.39292756943918),
  ];
  List listData = [
    {
      'data': "Riyadh",
      'list': [
        {
          "title": "Al Reef",
          'subTitle': "RAFB7699، 7699 Riyadh 13314, Saudi Arabia",
        },
        {
          "title": "Asment Exit - 18",
          'subTitle': "REFA7322, 7322 Mahail, 4063",
        },
      ]
    },
    {
      'data': "Al Ahsa",
      'list': [
        {
          "title": "Al Makhaita 2",
          'subTitle':
              "Al Qadisiyah, Al Mubarraz 36422, Saudi Arabia ا,،,لملك سعود",
        },
      ]
    },
    {
      'data': "Duraidah",
      'list': [
        {
          "title": "Al Faizy",
          'subTitle':
              "QBWE7235، 7235 عمر بن الخطاب، 2639، حي النهضة, Buraydah 52388, Saudi Arabia",
        },
      ]
    },
  ];
  RxList itemList = [].obs;

  var instCenterList = <InstallationCenterModel>[].obs;

  List<LatLng> instCenters = [];

  getAllInstallationCenters() async {
    List<InstallationCenterModel> insts = [];
    instCenters.clear();
    instCenterList.clear();
    var client = http.Client();
    try {
      var response = await client.post(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getCenterLocation));
      List result = jsonDecode(response.body);

      print("InstallationCenterController result >>>>> $result");
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        InstallationCenterModel inscntr =
            InstallationCenterModel.fromJson(result[i] as Map<String, dynamic>);
        insts.add(inscntr);
        instCenters.add(LatLng(
            double.parse(inscntr.latitude), double.parse(inscntr.longitude)));
      }

      instCenterList.value = insts;

      print("salesStats >>>>> $instCenters");
      print("===============================================================");
      print(
          "loadinstCenterList >>>>> ${jsonDecode(jsonEncode(instCenterList))}");
      print("===============================================================");

      return instCenterList;
    } catch (e) {
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('InstallationCenterController');
    if (instCenterList.isEmpty) {
      print("sulod");
      // await getAllInstallationCenters();
    }
    //Navigator.of(Get.context!).pop();
  }

   double? userLatitude;
  double? userLongitude;
  double radiusInKm = 200.0;

  List<InstallationCenterModel> gasStationListFiltered = [];

  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double R = 6371;
    final double dLat = _degToRad(lat2 - lat1);
    final double dLon = _degToRad(lon2 - lon1);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degToRad(lat1)) *
            cos(_degToRad(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return R * c;
  }

  double _degToRad(double deg) => deg * (pi / 180);

  var allStationsList = <InstallationCenterModel>[].obs;

  getInstallationCenterStations200kmRadius(
      double userLatitude, double userLongitude, double radiusInKm) async {
    List<InstallationCenterModel> gasses = [];
    List<InstallationCenterModel> allStations = [];
    print('yahan aya 200km');
    var client = http.Client();
    instCenters.clear();
    instCenterList.clear();
    allStationsList.clear();

    try {
      var response = await client.post(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getCenterLocation),
          body: {
            "IsAR": Constants.IsAr_App,
          });
      List result = jsonDecode(response.body);
      print('resultofstations $result');
      for (int i = 0; i < result.length; i++) {
        InstallationCenterModel gas =
            InstallationCenterModel.fromJson(result[i] as Map<String, dynamic>);

        if (gas.latitude != "" && gas.longitude != "") {
          double stationLatitude = double.parse(gas.latitude);
          double stationLongitude = double.parse(gas.longitude);

          allStations.add(gas);

          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);

          if (distance <= radiusInKm) {
            gasses.add(gas);

            instCenters.add(LatLng(stationLatitude, stationLongitude));
          }
        }
      }

      instCenterList.value = gasses;

      allStationsList.value = allStations;

      print("Filtered gasStats >>>>> ${jsonEncode(instCenters)}");

      return instCenterList;
    } catch (e) {
      print("getAllGasStations Error: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }
}
