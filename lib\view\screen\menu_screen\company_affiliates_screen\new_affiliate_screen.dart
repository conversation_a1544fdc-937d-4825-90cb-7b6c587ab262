// ignore_for_file: prefer_const_constructors, must_be_immutable, invalid_use_of_protected_member, prefer_interpolation_to_compose_strings

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:waie_app/core/controller/menu_controller/company_affiliate_controller/company_affiliate_controller.dart';
import 'package:waie_app/core/controller/menu_controller/company_affiliate_controller/company_affiliate_new_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/company_affiliates_menu_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class NewAffiliateScreen extends StatefulWidget {
  const NewAffiliateScreen({super.key});

  @override
  State<NewAffiliateScreen> createState() => _NewAffiliateScreenState();
}

class _NewAffiliateScreenState extends State<NewAffiliateScreen> {
  FilePickerResult? nationalID;

  CompanyAffiliateNewController companyAffiliateNewController =
      Get.put(CompanyAffiliateNewController());

  final FocusNode noteFocus = FocusNode();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                simpleAppBar(
                  title: "New affiliate".trr,
                  onTap: () {
                    Get.offAll(CompanyAffiliateMenuScreen());
                  },
                  backString: "Back".trr,
                ),
                Expanded(
                  child: Obx(() {
                    return ListView(
                      scrollDirection: Axis.vertical,
                      shrinkWrap: true,
                      padding: EdgeInsets.all(16),
                      children: [
                        Text(
                          "Affiliate request".trr,
                          style: pSemiBold17,
                        ),
                        verticalSpace(24),
                        CommonTextField(
                          controller: companyAffiliateNewController
                              .affiliateReasonController,
                          labelText:"Request Details".trr,
                          // hintText: "Write a few words".trr,
                          maxLines: 6,
                          validator: (value) {
                            return Validator.validateRequired(value);
                          },
                        ),

                       /* CommonTextField(
                          controller: companyAffiliateNewController
                              .affiliateCompanyNameController,
                          labelText: "Affiliate account ID name of company".trr,
                          hintText:
                              "Enter the account id/name of the affiliate company"
                                  .trr,
                          validator: (value) {
                            return Validator.validateRequired(value);
                          },
                        ),*/
                        verticalSpace(14),
                        Text(
                          "Supported Document".trr,
                          style: pRegular12,
                        ),
                        verticalSpace(6),
                        GestureDetector(
                          child: Container(
                            height: 44,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(color: AppColor.cBorder)),
                            padding: EdgeInsets.symmetric(
                              horizontal: 6,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  height: 32,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(6),
                                    color: AppColor.lightBlueColor,
                                  ),
                                  padding: EdgeInsets.only(right: 12, left: 8),
                                  child: Row(
                                    children: [
                                      assetSvdImageWidget(
                                        image: DefaultImages.fileIcn,
                                      ),
                                      horizontalSpace(8),
                                      Text(
                                        "Choose file".trr,
                                        style: pRegular14,
                                      ),
                                    ],
                                  ),
                                ),
                                horizontalSpace(8),
                                Expanded(
                                  child: Text(
                                    companyAffiliateNewController
                                            .fileName.isEmpty
                                        ? "No file chosen".trr
                                        : companyAffiliateNewController
                                            .fileName.value
                                            .split("/")
                                            .last,
                                    style: pRegular14.copyWith(
                                      color: AppColor.cDarkGreyFont,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          onTap: () async {
                            nationalID = await FilePicker.platform.pickFiles(
                              type: FileType.custom,
                              allowedExtensions: ['jpg', 'pdf', 'doc'],
                              withReadStream: true,
                            );
                            if (nationalID == null) {
                              print("No file selected");
                            } else {
                              setState(() {
                                for (var element in nationalID!.files) {
                                  print(element.name);
                                  companyAffiliateNewController.fileName.value =
                                      element.name;
                                }
                              });
                            }
                          },
                        ),
                        verticalSpace(14),
                      /*  CommonTextField(
                          controller: companyAffiliateNewController
                              .affiliateReasonController,
                          labelText:"Is there anything else we need to know?".trr,
                          hintText: "Write a few words".trr,
                          maxLines: 6,
                          validator: (value) {
                            return Validator.validateRequired(value);
                          },
                        ),*/
                      /*  CommonTextField(
                          controller: companyAffiliateNewController
                              .affiliateReasonController,
                          labelText:"Request Details".trr,
                         // hintText: "Write a few words".trr,
                          maxLines: 6,
                          validator: (value) {
                            return Validator.validateRequired(value);
                          },
                        ),
                        verticalSpace(24),*/
                        CommonButton(
                          title: "Submit request".trr,
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              if (nationalID == null) {
                                commonToast('Please Upload National ID');
                              } else {
                                PlatformFile nationalid =
                                    nationalID!.files.first;
                                companyAffiliateNewController
                                    .addAffiliates(nationalid);
                              }
                            }
                            // showDialog(
                            //   context: context,
                            //   builder: (context) {
                            //     return AlertDialog(
                            //       shape: RoundedRectangleBorder(
                            //           borderRadius: BorderRadius.circular(12)),
                            //       insetPadding: EdgeInsets.all(16),
                            //       contentPadding: EdgeInsets.all(24),
                            //       content: successDialogWidget(
                            //         title: "Your company affiliate request".trr,
                            //         subTitle:
                            //             "We'll let you know when it's been approved."
                            //                 .trr,
                            //         () {
                            //           // companyAffiliateController
                            //           //         .companyAffiliateList.value =
                            //           //     companyAffiliateController
                            //           //         .dummyAffiliateList.value;
                            //           // companyAffiliateController
                            //           //     .companyAffiliateList
                            //           //     .refresh();
                            //           // Get.back();
                            //           Get.back();
                            //         },
                            //       ),
                            //     );
                            //   },
                            // );
                          },
                          btnColor: AppColor.themeOrangeColor,
                        ),
                      ],
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

Widget successDialogWidget(Function() onTap,
    {bool isBorderBtn = false, String? title, String? subTitle}) {
  return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: assetSvdImageWidget(image: DefaultImages.cancelIcn)),
          ],
        ),
        verticalSpace(24),
        Text(title!, style: pBold20, textAlign: TextAlign.center),
        verticalSpace(14),
        Center(
            child: Text(
                subTitle ?? "We’ll notify you about the status change.".trr,
                style: pRegular13,
                textAlign: TextAlign.center)),
        verticalSpace(24),
        CommonButton(
          title: "OK".trr,
          onPressed: onTap,
          textColor: isBorderBtn == true
              ? AppColor.cDarkBlueFont
              : AppColor.cWhiteFont,
          btnColor: isBorderBtn == true
              ? AppColor.cBackGround
              : AppColor.themeOrangeColor,
          bColor: isBorderBtn == true
              ? AppColor.themeDarkBlueColor
              : AppColor.cTransparent,
        )
      ]);
}
