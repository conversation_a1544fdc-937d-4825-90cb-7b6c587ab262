import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/setting_controller/subsciption_plan_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/setting_screen/subs_activation_widget.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../utils/images.dart';
import '../../../widget/icon_and_image.dart';
import 'cancel_plan_widget.dart';

class SubscriptionPlanScreen extends StatelessWidget {
  /*const SubscriptionPlanScreen({super.key});*/
  Subscription_Plan_Controller subsciptionPlaController =
      Get.put(Subscription_Plan_Controller());

  SubscriptionPlanScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            simpleMyAppBar(
              onTap: () {
                Get.back();
              },
              title: "Subscription plan".trr,
              backString: "Back".trr,
            ),
            Obx(
              () => subsciptionPlaController.data.isNotEmpty
                  ? Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 13, horizontal: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Available Subscription Plans".trr,
                                style: pSemiBold17,
                              ),
                              verticalSpace(30),
                              Container(
                                width: Get.width,
                                padding: const EdgeInsets.symmetric(
                                    vertical: 14, horizontal: 16),
                                decoration: BoxDecoration(
                                  color: AppColor.themeDarkBlueColor,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    verticalSpace(24),
                                    Text(
                                      'Basic'.trr,
                                      style: pBold28.copyWith(
                                          color: AppColor.cWhiteFont),
                                    ),
                                    !subsciptionPlaController.basicPlan.value
                                        ? assetSvdImageWidget(
                                            image: DefaultImages
                                                .whiteCheckCircleIcn)
                                        : const SizedBox(),
                                    verticalSpace(24),
                                    Text(
                                      "Free".trr,
                                      style: pSemiBold17.copyWith(
                                          color: AppColor.cWhiteFont),
                                    ),
                                    verticalSpace(24),
                                    Obx(() {
                                      // Check the value of isDataAvailable for the condition
                                      if (subsciptionPlaController
                                          .basicPlan.value) {
                                        return CommonBorderButton(
                                          title: 'Go Free'.trr,
                                          onPressed: () {
                                            showModalBottomSheet(
                                              context: context,
                                              shape:
                                                  const RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.vertical(
                                                              top: Radius
                                                                  .circular(
                                                                      16))),
                                              backgroundColor:
                                                  AppColor.cBackGround,
                                              barrierColor:
                                                  AppColor.cBlackOpacity,
                                              isScrollControlled: true,
                                              builder: (context) {
                                                //return const CancelPlanWidget();
                                                return SubsPlanWidget(false);
                                              },
                                            );
                                          },
                                        );
                                      } else {
                                        return const Text(
                                          '',
                                        );
                                      }
                                    }),
                                  ],
                                ),
                              ),
                              verticalSpace(24),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 13, horizontal: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: Get.width,
                                padding: const EdgeInsets.symmetric(
                                    vertical: 14, horizontal: 16),
                                decoration: BoxDecoration(
                                  color: AppColor.themeDarkBlueColor,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    verticalSpace(24),
                                    Text(
                                      'Premium'.trr,
                                      style: pBold28.copyWith(
                                          color: AppColor.cWhiteFont),
                                    ),
                                    subsciptionPlaController.basicPlan.value
                                        ? assetSvdImageWidget(
                                            image: DefaultImages
                                                .whiteCheckCircleIcn)
                                        : const SizedBox(),
                                    verticalSpace(24),
                                    Directionality(
                                      textDirection: TextDirection.ltr,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          assetSvdImageWidget(
                                              image: DefaultImages.saudiRiyal,
                                              backgroundColor:
                                                  AppColor.cWhiteFont,
                                              width: 16,
                                              height: 16),
                                          Gap(4),
                                          Text(
                                            "10 / month".trr,
                                            style: pSemiBold17.copyWith(
                                                color: AppColor.cWhiteFont),
                                          ),
                                        ],
                                      ),
                                    ),
                                    // Text(
                                    //   "10 SAR / month".trr,
                                    //   style: pSemiBold17.copyWith(
                                    //       color: AppColor.cWhiteFont),
                                    // ),
                                    Text(
                                      'per month for each services type and it will be effective immediately. Premium customer will be able to use full system functions with reports.'
                                          .trr,
                                      style: pRegular14.copyWith(
                                          color: AppColor.cWhiteFont),
                                    ),
                                    verticalSpace(24),
                                    Obx(() {
                                      // Check the value of isDataAvailable for the condition
                                      if (subsciptionPlaController
                                          .premiumPlan.value) {
                                        return CommonBorderButton(
                                          title: 'Go Premium'.trr,
                                          onPressed: () {
                                            showModalBottomSheet(
                                              context: context,
                                              shape:
                                                  const RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.vertical(
                                                              top: Radius
                                                                  .circular(
                                                                      16))),
                                              backgroundColor:
                                                  AppColor.cBackGround,
                                              barrierColor:
                                                  AppColor.cBlackOpacity,
                                              isScrollControlled: true,
                                              builder: (context) {
                                                // return const CancelPlanWidget();
                                                return SubsPlanWidget(true);
                                              },
                                            );
                                          },
                                        );
                                      } else {
                                        return const Text(
                                          '',
                                        );
                                      }
                                    }),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    )
                  : const Center(child: CircularProgressIndicator()),
            ),
          ],
        ),
      ),
    );
  }
}
