// ignore_for_file: prefer_const_constructors_in_immutables, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/file_complaint_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/set_quota_limits_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import '../../../core/controller/notification_controller/notification_controller.dart';
import '../../../core/controller/vehicle_controller/activate_pinblock_controller.dart';
import '../menu_screen/order_screen/new_order_screen_replacement.dart';

class ActivityScreen extends StatelessWidget {
  final NotificationController notificationController;
  ActivatePinblockController activatePinblockController =
      Get.put(ActivatePinblockController());

  ActivityScreen({
    super.key,
    required this.notificationController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        verticalSpace(24),
        FutureBuilder<dynamic>(
            future: notificationController.fetchNotification(),
            builder: (context, AsyncSnapshot snapshot) {
              if (snapshot.connectionState != ConnectionState.done) {
                return const Center(child: CircularProgressIndicator());
              } else {
                return ListView.builder(
                  itemCount: notificationController.genNotification.length,
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    var data = notificationController.genNotification[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: buildShowNotyWidget(
                        title: data.notyDesc,
                        time: data.notifydate,
                        notyType: data.notifytype,
                        doc_no: data.docNo,
                        notyDesc: data.notyDesc,
                        function: () {},
                      ),
                    );
                  },
                );
              }
            }),
      ],
    );
  }

  Widget buildShowNotyWidget(
      {String? title,
      String? time,
      String? notyType,
      String? doc_no,
      String? notyDesc,
      Function()? function}) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 13, horizontal: 16),
      decoration: BoxDecoration(
        color: AppColor.cLightGrey,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title!,
                  style: pBold14.copyWith(fontSize: 13),
                  softWrap: true,
                  maxLines: 3, // Maximum number of lines before wrapping
                  overflow: TextOverflow.ellipsis,
                ),
                verticalSpace(12),
                Text(
                  time!,
                  style: pRegular10.copyWith(
                      fontSize: 11, color: AppColor.cDarkGreyFont),
                )
              ],
            ),
          ),
          horizontalSpace(24),
          //notificationButton(onTap: function!, name: 'Mark as read'.trr),
          if (notyType!.contains("PINBLOCK"))
            GestureDetector(
              onTap: () {
                // Get.to(NewOrderScreenreplacement());

                showDialog(
                  context: Get.context!,
                  builder: (context) {
                    return AlertDialog(
                      insetPadding: const EdgeInsets.all(16),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)),
                      content: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            notyDesc!,
                            style: pRegular14.copyWith(
                                color: AppColor.cDarkGreyFont),
                            textAlign: TextAlign.center,
                          ),
                          verticalSpace(12),
                          Text(
                            "Are you want to UNBLOCK?",
                            style: pRegular14.copyWith(
                                color: AppColor.cDarkGreyFont),
                            textAlign: TextAlign.center,
                          ),
                          verticalSpace(24),
                          Row(
                            children: [
                              Expanded(
                                  flex: 1,
                                  child: CommonBorderButton(
                                    title: 'Close'.trr,
                                    onPressed: () {
                                      Get.back();
                                    },
                                    textColor: AppColor.cDarkBlueFont,
                                    bColor: AppColor.themeDarkBlueColor,
                                  )),
                              horizontalSpace(12),
                              Expanded(
                                  flex: 2,
                                  child: CommonButton(
                                    title: "UnBlock".trr,
                                    onPressed: () {
                                      activatePinblockController
                                          .pinblockController.text = "N";
                                      activatePinblockController
                                          .activatePinblock(doc_no);
                                    },
                                    btnColor: AppColor.themeOrangeColor,
                                    horizontalPadding: 16,
                                  ))
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
              child: Container(
                // color: Colors.blue,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: AppColor.cDarkBlueBorder),
                ),
                padding: EdgeInsets.all(6),
                child: Row(
                  children: [
                    // assetSvdImageWidget(image: icon),
                    // horizontalSpace(8),
                    Text(
                      "View",
                      style: pRegular13,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

Widget notificationButton({
  Function()? onTap,
  String? name,
  Color? color,
  Color? bColor,
  Color? textColor,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      height: 32,
      padding: EdgeInsets.symmetric(vertical: 0, horizontal: 16),
      decoration: BoxDecoration(
        color: color ?? AppColor.themeDarkBlueColor,
        border: Border.all(color: bColor ?? AppColor.themeDarkBlueColor),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Center(
        child: Text(name ?? "Show tags".trr,
            style:
                pRegular13.copyWith(color: textColor ?? AppColor.cWhiteFont)),
      ),
    ),
  );
}
/*
Widget makeAllReadButton(Function() onTap) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      color: AppColor.cBackGround,
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          assetSvdImageWidget(image: DefaultImages.outlineCheckCircleIcn),
          horizontalSpace(8),
          Text(
            "Mark all as read".trr,
            style: pRegular13.copyWith(color: AppColor.cDarkBlueFont),
          )
        ],
      ),
    ),
  );
}*/
