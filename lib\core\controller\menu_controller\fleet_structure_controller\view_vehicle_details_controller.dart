import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/models/loadplaces.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/final_view_vehicle_details_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../models/newfleet.dart';
import '../../../../utils/constants.dart';
import '../../../../view/widget/loading_widget.dart';

class ViewVehicleDetailsController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  GetStorage userStorage = GetStorage('User');
  List<LoadPlaces> loadPlaces = [];
  RxList placeList = [].obs;
  RxBool chckbox = false.obs;
  final List<ServiceObj> vehicleDetails = [];

  getVehicleDetails(serialid) async {
    print("serialid>>>>>>> $serialid");
    Loader.showLoader();
    var client = http.Client();
    List<ServiceObj> details = [];
    var custData = custsData.read('custData');
    print("custid>>>>>>> $custData['CUSTID']");
    print("ViewVehicleDetailsController>>>>>>> getVehicleDetails");
    //print("emailid>>>>>>> $custData['EMAILID']");
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");

    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      Map body = {
        "SEARCHBY": "",
        "TOP": "1",
        "SKIP": "0",
        "CUSTID": custid, //"000003944",
        "SERIALID": serialid,
        "UNAME": custData['USERNAME']
      };
      var vehicleResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
          body: jsonEncode(body),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          });

      //List vehicleResult = jsonDecode(vehicleResponse.body);

      var wResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadPlaces),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
          body: {
            "CUSTID": custid,
            "OTHERSTN": custData['OTHERSTN'],
            "StationType": "W",
            "fuelType": "PE910",
            "IsAR": Constants.IsAr_App,
          });
      List wResult = jsonDecode(wResponse.body);

      final vehicleResult = json.decode(vehicleResponse.body);
      inspect(vehicleResult);
      final dataList = vehicleResult["serviceObj"] as List<dynamic>;

      final List<ServiceObj> newData =
          dataList.map((item) => ServiceObj.fromJson(item)).toList();

      vehicleDetails.addAll(newData);

      // for (int i = 0; i < vehicleResult.length; i++) {
      //   print("i===> $i --- ${vehicleResult.length}");
      // }

      // for (int i = 0; i < vehicleResult.length; i++) {
      //   try {
      //     FleetModel detail =
      //         FleetModel.fromMap(vehicleResult[i] as Map<String, dynamic>);
      //     details.add(detail);
      //   } catch (e) {
      //     print("e.toString()===> ${e.toString()}");
      //   }
      // }

      for (int i = 0; i < wResult.length; i++) {
        LoadPlaces place =
            LoadPlaces.fromJson(wResult[i] as Map<String, dynamic>);
        loadPlaces.add(place);
      }

      print("response vehicleDetails===> ${vehicleDetails.length}");

      if (wResponse.statusCode == 200) {
        Loader.hideLoader();
        await Get.to(() => const FinalViewVehicleDetailsScreen());
      } else {
        Loader.hideLoader();
        print('Failed');
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Failed to Load Vehicle Details",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }

      return vehicleDetails;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
