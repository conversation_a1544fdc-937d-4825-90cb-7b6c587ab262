// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/current_affiliate_screen.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_installation/tag_installation_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../core/controller/menu_controller/company_affiliate_controller/company_affiliate_controller.dart';

class CompanyAffiliateDataScreen extends StatelessWidget {
  final CompanyAffiliateController companyAffiliateController;

  const CompanyAffiliateDataScreen(
      {super.key, required this.companyAffiliateController});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Expanded(
        child: ListView(
          scrollDirection: Axis.vertical,
          shrinkWrap: true,
          padding: EdgeInsets.only(top: 15, left: 16, right: 16, bottom: 16),
          physics: BouncingScrollPhysics(),
          children: [
            Row(
              children: [
                tabWidget(
                  title: 'Current Affiliates'.trr,
                  onTap: () {
                    companyAffiliateController.isAffiliated.value = true;
                    companyAffiliateController.isHistory.value = false;
                  },
                  isSelected: companyAffiliateController.isAffiliated.value,
                ),
                tabWidget(
                  title: 'Request history'.trr,
                  onTap: () {
                    companyAffiliateController.isAffiliated.value = false;
                    companyAffiliateController.isHistory.value = true;
                  },
                  isSelected: companyAffiliateController.isHistory.value,
                ),
              ],
            ),
            verticalSpace(24),
            companyAffiliateController.isAffiliated.value == true
                ? CurrentAffiliatesScreen(
                    companyAffiliateController: companyAffiliateController,
                  )
                : RequestHistoryScreen()
          ],
        ),
      );
    });
  }
}
