// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/auth_background.dart';
import 'package:waie_app/view/screen/menu_screen/setting_screen/privacy_security_screen.dart';
import 'package:waie_app/view/screen/menu_screen/setting_screen/subscription_plan_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/auth/auth_controller.dart';
import '../../../../main.dart';
import '../../../../utils/constant.dart';
import '../menu_screen.dart';
import '../menu_screen_widgets/menu_item_widget.dart';
import 'email_notification_screen.dart';

class SettingScreen extends StatelessWidget {
  SettingScreen({super.key});

  AuthController authController = Get.put(AuthController());

  @override
  Widget build(BuildContext context) {
    String languageCode =
        myStorage!.getString(AppConstant.LANGUAGE_CODE) ?? 'ar';
    // if (languageCode == 'en') {
    //   authController.selectedLanguage.value = "English";
    //   authController.selectedLanguageImage.value = DefaultImages.englishImg;
    // }
    // if ((languageCode == 'ar')) {
    //   authController.selectedLanguage.value = "Arabic";
    //   authController.selectedLanguageImage.value = DefaultImages.arabicImg;
    // }
    // authController.selectedLanguage.refresh();
    // authController.selectedLanguageImage.refresh();
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            // simpleAppBar(
            //     title: "Account Settings".trr,
            //     onTap: () {
            //       Get.back();
            //     },
            //     // backString: "Menu".trr),
            //     backString: "Back".trr),
            Container(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        top: 15,
                        bottom: 16,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          assetSvdImageWidget(
                              image: DefaultImages.backIcn,
                              colorFilter: ColorFilter.mode(
                                  AppColor.cDarkBlueFont, BlendMode.srcIn)),
                          horizontalSpace(10),
                          Text(
                            // "Menu".trr,
                            "Back".trr,
                            style: pRegular18.copyWith(
                                color: AppColor.cDarkBlueFont, fontSize: 17),
                            textAlign: TextAlign.start,
                          )
                        ],
                      ),
                    ),
                  ),
                  // horizontalSpace(35),
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        "Account Settings".trr,
                        style: pBold20,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  if (Constants.custRegType != "C")
                    menuItemWidget(
                      title: "Subscription plan".trr,
                      onTap: () {
                        Get.to(() => SubscriptionPlanScreen());
                      },
                    ),
                  verticalSpace(16),
                  menuItemWidget(
                    title: "Notifications".trr,
                    onTap: () {
                      Get.to(() => EmailNotificationScreen());
                    },
                  ),
                  verticalSpace(16),
                  menuItemWidget(
                    title: "Privacy and security".trr,
                    onTap: () {
                      Get.to(() => PrivacySecurityScreen());
                    },
                  ),
                  verticalSpace(16),
                  PopupMenuButton(
                    onSelected: (item) {
                      print("+123456789---$item");
                    },
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6)),
                    itemBuilder: (BuildContext context) =>
                        authController.languageList.map((data) {
                      print(data['title']);

                      return PopupMenuItem(
                        value: data['title'],
                        child: GestureDetector(
                          onTap: () {
                            // authController
                            //     .selectedLanguageIndex
                            //     .value = i;
                            languageCode = data['languageCode'];
                            authController.selectedLanguage.value =
                                data['title'];
                            authController.selectedLanguageImage.value =
                                data['image'];
                            authController.selectedLanguageIndex.refresh();
                            print("++${data['local']}");
                            authController.updateLanguage(data['local']);
                            Get.back();
                          },
                          child: languageWidget(
                            image: data['image'],
                            title: data['title'],
                            color: data['languageCode'] == languageCode
                                ? AppColor.themeBlueColor
                                : AppColor.cWhite,
                            bColor: data['languageCode'] == languageCode
                                ? AppColor.themeBlueColor
                                : AppColor.cBorder,
                          ),
                        ),
                      );
                    }).toList(),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            authController.selectedLanguage.value,
                            style: pRegular13,
                          ),
                          horizontalSpace(6),
                          ClipRRect(
                              borderRadius: BorderRadius.circular(6),
                              child: Image.asset(
                                authController.selectedLanguageImage.value,
                                width: 28,
                                height: 21,
                              )),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
