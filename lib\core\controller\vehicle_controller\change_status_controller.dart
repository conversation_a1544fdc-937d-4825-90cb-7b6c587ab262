import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:quickalert/quickalert.dart';

import '../../../utils/colors.dart';
import '../../../utils/constants.dart';
import '../../../utils/text_style.dart';
import '../../../view/screen/vehicles_screen/my_fleet/final_fleet_screen.dart';
import '../../../view/widget/common_button.dart';
import '../../../view/widget/common_space_divider_widget.dart';
import '../../../view/widget/loading_widget.dart';

class ChangeStatusController extends GetxController {
  RxBool isPassword = true.obs;
  RxBool isConfirmPass = true.obs;
  VehicleController vehicleController = Get.put(VehicleController());
  final vehicle = GetStorage();
  final isPinblock = GetStorage();
  final isDCBlock = GetStorage();
  GetStorage custsData = GetStorage('custsData');
  GetStorage usersData = GetStorage('usersData');
  TextEditingController companyIdController = TextEditingController();
  TextEditingController commentController = TextEditingController();
  TextEditingController searchController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  List searchList = ['Company', 'Date'];
  RxString searchValue = 'Company'.obs;
  RxBool isActive = true.obs;
  RxBool isDeactivate = false.obs;
  RxBool isTerminate = false.obs;
  RxList tagTransferDataList = [
    {
      "value": false.obs,
      "title": "2FK/012/25/5853849",
      "status": "Pending",
      "totalTag": '3',
      "companyName": "Microsoft",
      "date": "06.04.2023",
    }.obs,
    {
      "value": false.obs,
      "title": "2FK/012/25/5853849",
      "status": "Completed",
      "totalTag": '3',
      "companyName": "Microsoft",
      "date": "06.04.2023",
    }.obs,
  ].obs;
  List incomingTagDataList = [
    {
      "id": '1',
      "plat": "0797DDA",
      "make": 'Chevrolet',
      "model": 'Equinox',
    },
    {
      "id": '2',
      "plat": "1013ANA",
      "make": 'Chevrolet',
      "model": 'Express',
    },
    {
      "id": '3',
      "plat": "111ZBD",
      "make": 'Chevrolet',
      "model": 'Suburban',
    },
  ];

  changeServiceStatus(status, vehicleid, serialid) async {
    // showDialog(
    //     context: Get.context!,
    //     builder: (context) {
    //       return const Center(
    //         child: CircularProgressIndicator(),
    //       );
    //     });
    Loader.showLoader();
    print("status>>>>>>> $status");
    print("vehicle>>>>>>> $vehicleid");

    var custData = jsonEncode(custsData.read('custData'));
    var userData = jsonEncode(usersData.read('usrData'));
    print("custid>>>>>>> $custData['CUSTID']");
    print("userData>>>>>>> $userData['USERNAME']");
    var client = http.Client();

    var password =
        passwordController.text == "" ? "NA" : passwordController.text;
    var confirmPassword =
        confirmPasswordController.text == "" ? "NA" : passwordController.text;

    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints
                  .setServiceStatusByRFID), //authEndpoints.setServiceStatus
          body: {
            "custdata": custData,
            "userData": userData,
            "status": status,
            "vehicleid": vehicleid,
            "serialid": serialid,
            "password": password,
            "confirmPassword": confirmPassword,
          });
      //print("jsonDecode(response.body >>>>> ${jsonEncode(vehicleid)}");
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("jsonDecode(response.statusCode >>>>> ${response.statusCode}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        vehicle.remove('vehicleID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('complaintJobID');
        isPinblock.remove('isPinActivate');
        isDCBlock.remove('isdcBlock');
        vehicleController.selectedSerialList.clear();
        vehicleController.selectedVehicleList.clear();
        vehicleController.selectedFleetList.clear();
        vehicleController.filterValueList.refresh();
        vehicleController.selectedVehicleList.refresh();
        vehicleController.selectedSerialList.refresh();
        vehicleController.selectedFleetList.refresh();
        // Get.offAll(
        //   () => DashBoardManagerScreen(
        //     currantIndex: 0,
        //   ),
        //   //preventDuplicates: false,
        // );
        //Get.back();
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.success,
          text: 'The Vehicle is now updated!',
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
      } else {
        Loader.hideLoader();
        print('Failed');
        Get.back(result: true);
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          title: 'Error',
          text: jsonDecode(response.body)["ExceptionMessage"],
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
        // showDialog(
        //   barrierDismissible: false,
        //   context: Get.context!,
        //   builder: (context) {
        //     return AlertDialog(
        //       insetPadding: const EdgeInsets.all(16),
        //       contentPadding: const EdgeInsets.all(24),
        //       shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12)),
        //       content: Column(
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           Text(
        //             jsonDecode(response.body)["ExceptionMessage"],
        //             style: pBold20,
        //             textAlign: TextAlign.center,
        //           ),
        //           verticalSpace(24),
        //           CommonButton(
        //             title: "OK".tr,
        //             onPressed: () async {
        //               Get.back();
        //             },
        //             btnColor: AppColor.themeOrangeColor,
        //           )
        //         ],
        //       ),
        //     );
        //   },
        // );
      }
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
