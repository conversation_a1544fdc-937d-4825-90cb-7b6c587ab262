// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/vehicle_controller/choose_gas_station_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../menu_screen/user_management_screen/new_user_screen.dart';

class ChooseGasStationsWidget extends StatelessWidget {
  ChooseGasStationsWidget({super.key});

  ChooseGasStationController chooseGasStationController =
      Get.put(ChooseGasStationController());

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Get.height - 60,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 10),
            child: Row(
              children: [
                GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: AppColor.cLightBlueContainer,
                      child: Center(
                          child: assetSvdImageWidget(
                              image: DefaultImages.backIcn)),
                    )),
                Expanded(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(
                        "Choose gas stations".trr,
                        style: pBold20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Obx(() {
            return Expanded(
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: Column(
                  children: [
                    verticalSpace(16),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: chooseGasStationController
                          .availableStationList.length,
                      itemBuilder: (context, index) {
                        var data = chooseGasStationController
                            .availableStationList[index];
                        return Obx(() {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8.0),
                            child: checkBoxWidget(
                              value: data['value'].value,
                              onChanged: (value) {
                                data['value'].value = value!;
                              },
                              title: data['title'].toString().trr,
                            ),
                          );
                        });
                      },
                    ),
                    verticalSpace(24),
                    CommonTextField(
                      labelText: '',
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(12),
                        child: assetSvdImageWidget(
                            image: DefaultImages.searchIcn,
                            width: 24,
                            height: 24),
                      ),
                      hintText: 'Search'.trr,
                    ),
                    verticalSpace(24),
                    GestureDetector(
                      onTap: () {
                        // addPureDcVehicleController.multipleSelected.clear();
                        for (var element
                            in chooseGasStationController.stationList) {
                          if (element["value"] == false) {
                            element["value"].value = true;
                            chooseGasStationController.multipleSelected
                                .add(element);
                          } else {
                            if (chooseGasStationController
                                .multipleSelected.isNotEmpty) {
                              element["value"].value = false;
                              chooseGasStationController.multipleSelected
                                  .remove(element);
                            }
                            // element["value"].value = false;
                            // addPureDcVehicleController.multipleSelected.remove(element);
                          }
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.only(bottom: 16),
                        child: Row(children: [
                          assetSvdImageWidget(
                              image: chooseGasStationController
                                      .multipleSelected.isNotEmpty
                                  ? DefaultImages.checkboxIcn
                                  : DefaultImages.checkboxDashIcn),
                          horizontalSpace(8),
                          Text(
                            "All stations".trr,
                            style: pRegular14,
                          )
                        ]),
                      ),
                    ),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: chooseGasStationController.stationList.length,
                      itemBuilder: (context, index) {
                        var data =
                            chooseGasStationController.stationList[index];
                        return Obx(() {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8.0),
                            child: GestureDetector(
                              onTap: () {
                                data['isSelected'].value =
                                    !data['isSelected'].value;
                                print(data['isSelected'].value);
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 10, horizontal: 8),
                                decoration: BoxDecoration(
                                  color: AppColor.cLightBlueContainer,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            SizedBox(
                                              height: 24,
                                              width: 24,
                                              child: Checkbox(
                                                value: data["value"].value,
                                                onChanged: (value) {
                                                  data["value"].value = value;
                                                },
                                                activeColor:
                                                    AppColor.themeDarkBlueColor,
                                                side: BorderSide(
                                                    color: AppColor.cBlack),
                                                shape: RoundedRectangleBorder(
                                                    side: BorderSide(
                                                        color: AppColor.cBlack),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            4)),
                                              ),
                                            ),
                                            horizontalSpace(8),
                                            Text(data["title"],
                                                style: pRegular16.copyWith(
                                                    color: AppColor
                                                        .cDarkBlueFont)),
                                          ],
                                        ),
                                        assetSvdImageWidget(
                                            image: data['isSelected'].value ==
                                                    true
                                                ? DefaultImages.blueArrowUpIcn
                                                : DefaultImages
                                                    .blueArrowDownIcn,
                                            height: 24,
                                            width: 24)
                                      ],
                                    ),
                                    data['isSelected'].value == true
                                        ? Padding(
                                            padding: const EdgeInsets.only(
                                                left: 10, top: 6, bottom: 16),
                                            child: ListView.builder(
                                              itemCount: data['data'].length,
                                              physics:
                                                  NeverScrollableScrollPhysics(),
                                              shrinkWrap: true,
                                              itemBuilder: (context, i) {
                                                var myData = data['data'][i];
                                                return Obx(() {
                                                  return Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            top: 10),
                                                    child: Row(
                                                      children: [
                                                        SizedBox(
                                                          height: 24,
                                                          width: 24,
                                                          child: Checkbox(
                                                            value:
                                                                myData["value"]
                                                                    .value,
                                                            onChanged: (value) {
                                                              myData["value"]
                                                                      .value =
                                                                  value;
                                                            },
                                                            activeColor: AppColor
                                                                .themeDarkBlueColor,
                                                            side: BorderSide(
                                                                color: AppColor
                                                                    .cBlack),
                                                            shape: RoundedRectangleBorder(
                                                                side: BorderSide(
                                                                    color: AppColor
                                                                        .cBlack),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            4)),
                                                          ),
                                                        ),
                                                        horizontalSpace(8),
                                                        Text(myData["title"],
                                                            style: pRegular16.copyWith(
                                                                color: AppColor
                                                                    .cDarkBlueFont)),
                                                      ],
                                                    ),
                                                  );
                                                });
                                              },
                                            ),
                                          )
                                        : SizedBox(),
                                  ],
                                ),
                              ),
                            ),
                          );
                        });
                      },
                    ),
                  ],
                ),
              ),
            );
          }),
          verticalSpace(16),
          Row(
            children: [
              Expanded(
                child: CommonBorderButton(
                  title: 'Cancel'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  textColor: AppColor.cDarkBlueFont,
                  bColor: AppColor.cDarkBlueFont,
                  btnColor: AppColor.cBackGround,
                ),
              ),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Save'.trr,
                  onPressed: () {
                    Get.back();
                  },
                  textColor: AppColor.cWhiteFont,
                  btnColor: AppColor.themeOrangeColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
