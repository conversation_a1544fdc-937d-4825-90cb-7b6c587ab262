// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_installation/tag_installation_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';

import '../../../widget/common_space_divider_widget.dart';
import 'fuel_history_screen.dart';
import 'overview_screen.dart';

class FleetVehicleScreen extends StatefulWidget {
  const FleetVehicleScreen({super.key});

  @override
  State<FleetVehicleScreen> createState() => _FleetVehicleScreenState();
}

class _FleetVehicleScreenState extends State<FleetVehicleScreen> {
  VehicleController vehicleController = Get.find();

  final vehicle = GetStorage();
  final isPinblock = GetStorage();
  final isDCBlock = GetStorage();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            children: [
              simpleAppBar(
                  title: "Vehicle".trr,
                  onTap: () {
                    vehicle.remove('vehicleID');
                    vehicle.remove('vehicleSerialID');
                    vehicle.remove('vehicleSerialID');
                    vehicle.remove('complaintJobID');
                    isPinblock.remove('isPinActivate');
                    isDCBlock.remove('isdcBlock');
                    Get.back();
                  },
                  backString: "Back".trr),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Obx(() {
                    return Column(
                      children: [
                        Row(
                          // shrinkWrap: true,
                          // scrollDirectiontion: Axis.horizontal,
                          children: [
                            tabWidget(
                              title: 'Overview'.trr,
                              onTap: () {
                                vehicleController.isOverview.value = true;
                                vehicleController.isFuelHistory.value = false;
                              },
                              isSelected: vehicleController.isOverview.value,
                            ),
                            // tabWidget(
                            //   title: 'Fuel history'.trr,
                            //   onTap: () {
                            //     vehicleController.isOverview.value = false;
                            //     vehicleController.isFuelHistory.value = true;
                            //   },
                            //   isSelected: vehicleController.isFuelHistory.value,
                            // ),
                          ],
                        ),
                        verticalSpace(24),
                        vehicleController.isOverview.value == true
                            ? OverviewScreen()
                            : SizedBox() //FuelHistoryScreen()
                      ],
                    );
                  }),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
