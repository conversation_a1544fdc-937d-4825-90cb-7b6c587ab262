import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:in_app_review/in_app_review.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:waie_app/utils/logger_extention/logger_extension.dart';
import '../../../models/review_config_model.dart';
import '../../../utils/api_endpoints.dart';

class ReviewController extends GetxController {
  final InAppReview _inAppReview = InAppReview.instance;
  static const String _lastReviewDateKey = "last_review_date";
  static const String _reviewConfigKey = "review_config";

  var isLoading = false.obs;
  var reviewConfig = ReviewConfigModel(isEnabled: false, delayInDays: 0).obs;

  @override
  void onInit() {
    super.onInit();
    fetchReviewConfig();
  }

  Future<void> fetchReviewConfig() async {
    logInfo("Fetching Review Config data...");
    isLoading.value = true;

    try {
      final response = await http.get(Uri.parse(
        ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.inAppReviewState,
      ));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        if (jsonData is List && jsonData.isNotEmpty) {
          reviewConfig.value = ReviewConfigModel.fromJson(jsonData[0]);
          logSuccess("Review config loaded: ${reviewConfig.value}");
          await _saveReviewConfig(reviewConfig.value);
        } else {
          logError("API returned unexpected or empty data.");
          await _loadReviewConfig();
        }
      } else {
        logError("Failed to fetch review config, Status Code: ${response.statusCode}");
        await _loadReviewConfig();
      }
    } catch (e) {
      logError("Error fetching review config: $e");
      await _loadReviewConfig();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _saveReviewConfig(ReviewConfigModel config) async {
    final prefs = await SharedPreferences.getInstance();
    final configMap = {
      'isEnabled': config.isEnabled,
      'delayInDays': config.delayInDays,
    };
    await prefs.setString(_reviewConfigKey, json.encode(configMap));
    logInfo("Review config saved locally.");
  }

  Future<void> _loadReviewConfig() async {
    final prefs = await SharedPreferences.getInstance();

    if (prefs.containsKey(_reviewConfigKey)) {
      final jsonData = json.decode(prefs.getString(_reviewConfigKey)!);
      reviewConfig.value = ReviewConfigModel.fromJson(jsonData);
      logSuccess("Loaded review config from cache: ${reviewConfig.value}");
    } else {
      logWarning("No review config found in cache.");
    }
  }

  Future<void> saveLastReviewDate(DateTime date) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_lastReviewDateKey, date.millisecondsSinceEpoch);
    logInfo("Last review date saved: $date");
  }

  Future<DateTime?> getLastReviewDate() async {
    final prefs = await SharedPreferences.getInstance();
    final timestamp = prefs.getInt(_lastReviewDateKey);

    if (timestamp != null) {
      final lastDate = DateTime.fromMillisecondsSinceEpoch(timestamp);
      logInfo("Retrieved last review date: $lastDate");
      return lastDate;
    }

    logWarning("No last review date found.");
    return null;
  }

  Future<bool> canShowReview() async {
    if (!reviewConfig.value.isEnabled) {
      logWarning("In-app reviews are disabled in config.");
      return false;
    }

    final lastReviewDate = await getLastReviewDate();

    if (lastReviewDate == null) {
      logInfo("No previous review date found. Review can be shown.");
      return true;
    }

    final currentDate = DateTime.now();
    final difference = currentDate.difference(lastReviewDate).inDays;

    logInfo("Days since last review: $difference");
    return difference >= reviewConfig.value.delayInDays;
  }

  Future<void> triggerReview() async {
    try {
      if (await _inAppReview.isAvailable()) {
        if (await canShowReview()) {
          logSuccess("Triggering in-app review...");
          await _inAppReview.requestReview();
          await saveLastReviewDate(DateTime.now());
        } else {
          logWarning("Cannot trigger in-app review (time delay not met).");
        }
      } else {
        logWarning("In-app reviews are not available on this device.");
      }
    } catch (e) {
      logError("Error triggering in-app review: $e");
    }
  }
}

