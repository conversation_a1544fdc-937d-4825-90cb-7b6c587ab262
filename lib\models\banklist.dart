import 'dart:convert';

List<BankListModel> bankListModelFromJson(String str) =>
    List<BankListModel>.from(
        json.decode(str).map((x) => BankListModel.fromJson(x)));

String bankListModelToJson(List<BankListModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class BankListModel {
  String typecode;
  String typedesc;

  BankListModel({
    required this.typecode,
    required this.typedesc,
  });

  factory BankListModel.fromJson(Map<String, dynamic> json) => BankListModel(
        typecode: json["TYPECODE"],
        typedesc: json["TYPEDESC"],
      );

  Map<String, dynamic> toJson() => {
        "TYPECODE": typecode,
        "TYPEDESC": typedesc,
      };

  Map<String, dynamic> toMap() {
    return {
      'TYPECODE': typecode,
      'TYPEDESC': typedesc,
    };
  }

  factory BankListModel.fromMap(Map<String, dynamic> map) {
    return BankListModel(
      typecode: map['TYPECODE'] ?? '',
      typedesc: map['TYPEDESC'] ?? '',
    );
  }
}
