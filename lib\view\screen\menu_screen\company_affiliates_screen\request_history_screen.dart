// ignore_for_file: prefer_const_constructors

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/company_affiliate_controller/company_affiliate_controller.dart';
import 'package:waie_app/core/controller/menu_controller/company_affiliate_controller/company_affiliate_history_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/company_affiliates_menu_screen.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/no_affiliates_widget.dart';
import 'package:waie_app/view/screen/menu_screen/refunds_screen/no_refund_found_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class RequestHistoryScreen extends StatefulWidget {
  const RequestHistoryScreen({super.key});

  @override
  State<RequestHistoryScreen> createState() => _RequestHistoryScreenState();
}

class _RequestHistoryScreenState extends State<RequestHistoryScreen> {
  CompanyAffiliateController companyAffiliateController =
      Get.put(CompanyAffiliateController());
  CompanyAffiliateHistoryController companyAffiliateHistoryController =
      Get.put(CompanyAffiliateHistoryController());

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalSpace(12),
            Obx(
              () => companyAffiliateHistoryController
                      .affiliateRequestHistoryList.isEmpty
                  ? NoAffiliatesWidget()
                  : ListView.builder(
                      scrollDirection: Axis.vertical,
                      shrinkWrap: true,
                      itemCount: companyAffiliateHistoryController
                          .affiliateRequestHistoryList.length,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = companyAffiliateHistoryController
                            .affiliateRequestHistoryList[index];
                        return requestDataWidget(
                            name: data.reqId,
                            status: data.status,
                            requestType: data.reqDesc,
                            requestDate: data.reqDate,
                            request: data.reqSubType,
                            isRequest: true,
                            isBtn: data.status == "NEW" ? true : false,
                            textColor: data.status == "NEW"
                                ? AppColor.cDarkBlueFont
                                : AppColor.cGreen,
                            conColor: data.status == "NEW"
                                ? AppColor.cLightBlueContainer
                                : AppColor.cLightGreen,
                            btnName: "Cancel request".trr,
                            btnImage: DefaultImages.cancelRequestIcn,
                            onPressed: () {
                             /* companyAffiliateController
                                  .cancelCompanyAffiliateRequest(data.reqId);*/
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return AlertDialog(
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                        BorderRadius.circular(12)),
                                    contentPadding: EdgeInsets.all(24),
                                    insetPadding: EdgeInsets.all(16),
                                    content: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                          MainAxisAlignment.end,
                                          children: [
                                            GestureDetector(
                                                onTap: () {
                                                  Get.back();
                                                },
                                                child: assetSvdImageWidget(
                                                    image: DefaultImages
                                                        .cancelIcn)),
                                          ],
                                        ),
                                        verticalSpace(24),
                                        Text(
                                            "${"Are you sure you want to cancel affiliates request".trr} ${data.reqId}?",
                                            style: pBold20,
                                            textAlign: TextAlign.center),
                                        verticalSpace(14),
                                        Center(
                                            child: Text(
                                                "You can't undo this. If you still need a problem to be resolved, you'll need to submit a new affiliates request."
                                                    .trr,
                                                style: pRegular13,
                                                textAlign: TextAlign.center)),
                                        verticalSpace(24),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: CommonButton(
                                                title: "NO".trr,
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                textColor:
                                                AppColor.cDarkBlueFont,
                                                btnColor: AppColor.cBackGround,
                                                bColor: AppColor.cDarkBlueFont,
                                              ),
                                            ),
                                            horizontalSpace(16),
                                            Expanded(
                                              child: CommonButton(
                                                title: "Yes, cancel".trr,
                                                onPressed: () {
                                                  //Get.back();
                                                  //Loader.showLoader();
                                                  companyAffiliateController
                                                      .cancelCompanyAffiliateRequest(data.reqId);
                                                  //Get.back();
                                                },
                                                textColor: AppColor.cWhiteFont,
                                                btnColor: AppColor.cRedText,
                                                bColor: AppColor.cTransparent,
                                                horizontalPadding: 16,
                                              ),
                                            ),
                                          ],
                                        )
                                      ],
                                    ),
                                  );
                                },
                              );
                            });
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget requestDataWidget({
  String? name,
  String? status,
  String? requestType,
  String? requestDate,
  String? request,
  bool? isRequest,
  bool? isBtn,
  Color? textColor,
  Color? conColor,
  String? btnName,
  String? btnImage,
  Function()? onPressed,
}) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8.0),
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 10),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: AppColor.lightBlueColor,
          border: Border.all(color: AppColor.cLightGrey)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                height: 24,
                child: Center(
                  child: Text(
                    name!,
                    style: pSemiBold17.copyWith(color: AppColor.cDarkBlueFont),
                  ),
                ),
              ),
              verticalSpace(8),
              newWidget(color: conColor, textColor: textColor, text: status),
              // assetSvdImageWidget(image: DefaultImages.verticleMoreIcn),
            ],
          ),
          verticalSpace(18),
          dataRowWidget("Request type".trr, requestType!),
          verticalSpace(12),
          dataRowWidget("Request date".trr, requestDate!),
          verticalSpace(12),
          isRequest == true
              ? dataRowWidget("${"Request"} #", request!)
              : SizedBox(),
          verticalSpace(isBtn == true ? 14 : 0),
          isBtn == true
              ? CommonIconBorderButton(
                  iconData: btnImage ?? DefaultImages.cancelRequestIcn,
                  title: btnName ?? 'Cancel request'.trr,
                  onPressed: onPressed,
                )
              : SizedBox()
        ],
      ),
    ),
  );
}

Row dataRowWidget(String title, String data) {
  return Row(
    children: [
      SizedBox(
          width: 156,
          child: Text(
            title,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          )),
      Text(data, style: pRegular13),
    ],
  );
}

Container newWidget({Color? color, String? text, Color? textColor}) {
  return Container(
    height: 24,
    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(4),
      color: color ?? AppColor.cLightBlueContainer,
    ),
    child: Center(
      child: Text(
        text ?? "New",
        style: pSemiBold12.copyWith(color: textColor ?? AppColor.cDarkBlueFont),
      ),
    ),
  );
}

Widget successDialogWidget(Function() onTap,
    {bool isBorderBtn = false, String? title, String? subTitle}) {
  return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSpace(24),
        Text(title!, style: pBold20, textAlign: TextAlign.center),
        verticalSpace(14),
        Center(
            child: Text(
                subTitle ??
                    "Are you sure you want to cancel Company Affiliate request?",
                style: pRegular13,
                textAlign: TextAlign.center)),
        verticalSpace(24),
        CommonButton(
          title: "OK".trr,
          onPressed: onTap,
          textColor: isBorderBtn == true
              ? AppColor.cDarkBlueFont
              : AppColor.cWhiteFont,
          btnColor: isBorderBtn == true
              ? AppColor.cBackGround
              : AppColor.themeOrangeColor,
          bColor: isBorderBtn == true
              ? AppColor.themeDarkBlueColor
              : AppColor.cTransparent,
        )
      ]);
}
