// ignore_for_file: prefer_const_constructors

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_current_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_reserved_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_tag_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../user_management_screen/user_management_screen.dart';
import 'no_refund_found_screen.dart';

class OrderRefundCurrentScreen extends StatefulWidget {
  const OrderRefundCurrentScreen({super.key});

  @override
  State<OrderRefundCurrentScreen> createState() =>
      _OrderRefundCurrentScreenState();
}

class _OrderRefundCurrentScreenState extends State<OrderRefundCurrentScreen> {
  @override
  void initState() {
    super.initState();
    // for (var item in orderRefundCurrentController.refundableCurrentTopups) {
    //   item.isvalue = false;
    // }

    for (var item in orderRefundCurrentController.refundableCurrentTopups) {
      item.isvalue = refundsController.selectedTopupList.contains(item);
    }
  }

  final topupOrderRefund = GetStorage();

//added by fuzail 05-15-2025
  final tagOrderRefund = GetStorage();

  RefundsController refundsController = Get.find();

  OrderRefundCurrentController orderRefundCurrentController =
      Get.put(OrderRefundCurrentController());

  @override
  Widget build(BuildContext context) {
    print("OLD CHKRES ${topupOrderRefund.read('chkRES')}");
    topupOrderRefund.remove('chkRES');
    topupOrderRefund.write('chkRES', "false");
    print("NEW CHKRES ${topupOrderRefund.read('chkRES')}");

    print("OLD STYPE ${tagOrderRefund.read('orderType')}");
    tagOrderRefund.remove('orderType');
    tagOrderRefund.write('orderType', "T");
    print("NEW STYPE ${tagOrderRefund.read('orderType')}");

    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () => orderRefundCurrentController.refundableCurrentTopups.isEmpty
                  ? Center(
                      child: NoRefundFoundScreen(),
                    ) //NoRefundFoundScreen()
                  : ListView.builder(
                      itemCount: orderRefundCurrentController
                          .refundableCurrentTopups.length,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = orderRefundCurrentController
                            .refundableCurrentTopups[index];
                        // print(
                        //     "orderRefundTagController.refundableTagServices.length ${orderRefundTagController.refundableTagServices.length}");
                        //original commented code read if there is problem

                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              bool newValue = !(data.isvalue ?? false);

                              setState(() {
                                data.isvalue = newValue;
                              });

                              if (newValue == true) {
                                // Added by fuzail 5-15-2025
                                refundsController.selectedTopupList.add(data);
                                refundsController.selectedReservedList
                                    .clear(); // clear reserved list because current topup selected

                                // You can uncomment these if still needed:
                                // refundsController.selectedOrderRefundTagList.add(data);
                                // refundsController.selectedOrderRefundCardList.clear();

                                refundsController.selectedOrderRefundList
                                    .add(data);
                                refundsController
                                    .selectedOrderRefundCurrentOrderIDList
                                    .add(data.orderid);

                                orderRefundCurrentController.topupTotaltAmt +=
                                    data.addltopup;
                                orderRefundCurrentController.topupTotaltVAT +=
                                    data.vat;

                                print(
                                    "topupTotaltAmt ${orderRefundCurrentController.topupTotaltAmt.toStringAsFixed(2)}");
                                print(
                                    "topupTotaltVAT ${orderRefundCurrentController.topupTotaltVAT.toStringAsFixed(2)}");
                              } else {
                                refundsController.selectedTopupList
                                    .remove(data);
                                refundsController.selectedReservedList.clear();

                                refundsController.selectedOrderRefundList
                                    .removeAt(index);
                                refundsController
                                    .selectedOrderRefundCurrentOrderIDList
                                    .removeAt(index);

                                orderRefundCurrentController.topupTotaltAmt -=
                                    data.addltopup;
                                orderRefundCurrentController.topupTotaltVAT -=
                                    data.vat;

                                print(
                                    "topupTotaltAmt ${orderRefundCurrentController.topupTotaltAmt.toStringAsFixed(2)}");
                                print(
                                    "topupTotaltVAT ${orderRefundCurrentController.topupTotaltVAT.toStringAsFixed(2)}");
                              }

                              refundsController.selectedOrderRefundList
                                  .refresh();
                              refundsController
                                  .selectedOrderRefundCurrentOrderIDList
                                  .refresh();

                              print(
                                  'Topup selected: ${refundsController.selectedTopupList.length}');
                              refundsController.selectedTopupList.refresh();

                              print("*************************************");
                              print(
                                  "refundsController.selectedOrderRefundCurrentOrderIDList.length >>>> ${refundsController.selectedOrderRefundCurrentOrderIDList.length}");
                              print("*************************************");

                              String orderRefundCurrentOrderIDList =
                                  refundsController
                                      .selectedOrderRefundCurrentOrderIDList
                                      .join(",");

                              print("*************************************");
                              print(
                                  "orderRefundCurrentOrderIDList >>>> $orderRefundCurrentOrderIDList");

                              tagOrderRefund.write('orderRefundTagSerialIDList',
                                  orderRefundCurrentOrderIDList);

                              topupOrderRefund.write(
                                  'tAmt',
                                  orderRefundCurrentController.topupTotaltAmt
                                      .toStringAsFixed(2));
                              topupOrderRefund.write(
                                  'tVAT',
                                  orderRefundCurrentController.topupTotaltVAT
                                      .toStringAsFixed(2));

                              print("*************************************");

                              log("topupOrderRefund.write('orderRefundCurrentOrderIDList' >>>> ${tagOrderRefund.read('orderRefundTagSerialIDList')}");
                              print(
                                  "topupOrderRefund.write('tAmt' >>>> ${topupOrderRefund.read('tAmt')}"); // corrected key
                              print(
                                  "topupOrderRefund.write('tVAT' >>>> ${topupOrderRefund.read('tVAT')}");
                            },
                            child: refundCurrentTopupDataWidget(
                              isShowCheckBox: true,
                              onChanged:
                                  null, // disable direct checkbox toggling
                              value: data.isvalue,
                              code: data.orderid,
                              invNo: data.invno,
                              orderDate: data.orderdate,
                              paidDate: data.paidon,
                              amount: data.addltopup.toStringAsFixed(2),
                              vat: data.vat.toStringAsFixed(2),
                            ),
                          ),
                        );
//original commented code read if there is problem

//                         return Padding(
//                           padding: const EdgeInsets.only(bottom: 8.0),
//                           child: refundCurrentTopupDataWidget(
//                             isShowCheckBox: true,
//                             onChanged: (value) {
//                               setState(() {
//                                 data.isvalue = value ?? false;
//                                 print("data.isvalue ${data.isvalue}");
//                                 print("value $value");
//                               });
//                               if (value == true) {
//                                 //addedd by fuzail 5-15-2025

//                                 refundsController.selectedTopupList
//                                     .add(data); // Added by fuzail
//                                 refundsController.selectedReservedList
//                                     .clear(); //clear reserved list bcoz current topup  is selected
//                                 /////////////////////////////////
//                                 // refundsController.selectedOrderRefundTagList
//                                 //     .add(data); // Added by fuzail
//                                 // refundsController.selectedOrderRefundCardList
//                                 //     .clear(); //clear smart card list bcoz tags is selected

//                                 /////////////////////////////////////////
//                                 // refundsController.selectedOrderRefundTagList
//                                 //     .add(data); // Added by fuzail
//                                 // refundsController.selectedOrderRefundCardList
//                                 //     .clear(); //clear reserved card list bcoz topups is selected

//                                 //////
//                                 ///

//                                 // added by fuzail 5-13-2025
//                                 // if (!refundsController.selectedTopupList
//                                 //     .contains(data)) {
//                                 //   refundsController.selectedTopupList.add(data);
//                                 // }

//                                 // // Clear reserved state
//                                 // for (var item
//                                 //     in Get.find<OrderRefundReservedController>()
//                                 //         .refundableReservedTopups) {
//                                 //   item.isvalue = false;
//                                 // }
//                                 // refundsController.selectedReservedList.clear();

//                                 //////////////////////////end

//                                 refundsController.selectedOrderRefundList
//                                     .add(data);
//                                 refundsController
//                                     .selectedOrderRefundCurrentOrderIDList
//                                     .add(data.orderid);

//                               orderRefundCurrentController.  topupTotaltAmt += data.addltopup;
//                                 orderRefundCurrentController. topupTotaltVAT += data.vat;
//                                 print(
//                                     "topupTotaltAmt ${ orderRefundCurrentController.topupTotaltAmt.toStringAsFixed(2)}");
//                                 print(
//                                     "topupTotaltAmt ADD ${ orderRefundCurrentController.topupTotaltAmt.toStringAsFixed(2)}");
//                                 print(
//                                     "topupTotaltVAT ${ orderRefundCurrentController.topupTotaltVAT.toStringAsFixed(2)}");
//                                 print(
//                                     "topupTotaltVAT ADD ${ orderRefundCurrentController.topupTotaltVAT.toStringAsFixed(2)}");
//                               } else {
//                                 //added by fuzail 5-15-2025

//                                 refundsController.selectedTopupList
//                                     .remove(data); // added by fuzail
//                                 refundsController.selectedReservedList
//                                     .clear(); //clear smart card list bcoz tags is selected

//                                 // refundsController.selectedOrderRefundTagList
//                                 //     .remove(data); // added by fuzail
//                                 // refundsController.selectedOrderRefundCardList
//                                 //     .clear(); //clear smart card list bcoz tags is selected

//                                 /////////////////////////////////
// //added by fuzail 5-13-2025
//                                 // refundsController.selectedTopupList
//                                 //     .remove(data);

//                                 //end by fuzail 5-13-2025

//                                 refundsController.selectedOrderRefundList
//                                     .removeAt(index);
//                                 refundsController
//                                     .selectedOrderRefundCurrentOrderIDList
//                                     .removeAt(index);

//                                 orderRefundCurrentController. topupTotaltAmt -= data.addltopup;
//                                  orderRefundCurrentController.topupTotaltVAT -= data.vat;
//                                 print(
//                                     "topupTotaltAmt ${ orderRefundCurrentController.topupTotaltAmt.toStringAsFixed(2)}");
//                                 print(
//                                     "topupTotaltAmt MINUS ${ orderRefundCurrentController.topupTotaltAmt.toStringAsFixed(2)}");
//                                 print(
//                                     "topupTotaltVAT ${ orderRefundCurrentController.topupTotaltVAT.toStringAsFixed(2)}");
//                                 print(
//                                     "topupTotaltVAT MINUS ${ orderRefundCurrentController.topupTotaltVAT.toStringAsFixed(2)}");
//                               }
//                               refundsController.selectedOrderRefundList
//                                   .refresh();
//                               refundsController
//                                   .selectedOrderRefundCurrentOrderIDList
//                                   .refresh();
//                               print(
//                                   'Topup selected: ${refundsController.selectedTopupList.length}');
//                               refundsController.selectedTopupList
//                                   .refresh(); // just to be safe
//                               print("*************************************");
//                               print(
//                                   "refundsController.selectedOrderRefundCurrentOrderIDList.lenght >>>> ${refundsController.selectedOrderRefundCurrentOrderIDList.length}");
//                               print("*************************************");

//                               String orderRefundCurrentOrderIDList =
//                                   refundsController
//                                       .selectedOrderRefundCurrentOrderIDList
//                                       .join(",");

//                               print("*************************************");
//                               print(
//                                   "orderRefundCurrentOrderIDList >>>> $orderRefundCurrentOrderIDList");
//                                   //commented by fuzail

//                               // topupOrderRefund.write(
//                               //     'orderRefundCurrentOrderIDList',
//                               //     orderRefundCurrentOrderIDList);

//                               //added by fuzail 5-15-2025

//                               tagOrderRefund.write('orderRefundTagSerialIDList',
//                                   orderRefundCurrentOrderIDList);
//                               // tagOrderRefund.write('orderRefundTagOrderIDList',
//                               //     orderRefundCurrentOrderIDList);
//                               /////ended

//                               topupOrderRefund.write(
//                                   'tAmt',  orderRefundCurrentController.topupTotaltAmt.toStringAsFixed(2));
//                               topupOrderRefund.write(
//                                   'tVAT',  orderRefundCurrentController.topupTotaltVAT.toStringAsFixed(2));

//                               print("*************************************");

//                               log(
//                                   "topupOrderRefund.write('orderRefundCurrentOrderIDList' >>>> ${tagOrderRefund.read('orderRefundTagSerialIDList')}");

//                               print(
//                                   "topupOrderRefund.write('tAmt' >>>> ${topupOrderRefund.read('topupTotaltAmt')}");
//                               print(
//                                   "topupOrderRefund.write('tVAT' >>>> ${topupOrderRefund.read('topupTotaltVAT')}");
//                             },
//                             value: data.isvalue,
//                             code: data.orderid,
//                             invNo: data.invno,
//                             orderDate: data.orderdate,
//                             paidDate: data.paidon,
//                             amount: data.addltopup.toStringAsFixed(2),
//                             vat: data.vat.toStringAsFixed(2),
//                           ),
//                         );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget refundCurrentTopupDataWidget({
  required String code,
  String? status,
  Color? color,
  Color? textColor,
  bool? value,
  ValueChanged<bool?>? onChanged,
  required String invNo,
  required String orderDate,
  required String paidDate,
  required String amount,
  required String vat,
  bool? isShowButton = false,
  bool? isShowCheckBox = false,
  Function()? cancelReqOnTap,
}) {
  return Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(4),
      border: Border.all(
          color: value == true ? AppColor.cDarkBlueFont : AppColor.cLightGrey),
    ),
    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                isShowCheckBox == false
                    ? SizedBox()
                    : SizedBox(
                        height: 24,
                        width: 24,
                        child: Checkbox(
                          value: value,
                          onChanged: (on){},
                          activeColor: AppColor.themeDarkBlueColor,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                              side: BorderSide(color: AppColor.cBorder)),
                        ),
                      ),
                horizontalSpace(isShowCheckBox == false ? 0 : 8),
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.cLightBlueBorder),
                        borderRadius: BorderRadius.circular(4)),
                    padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 13, color: AppColor.cDarkBlueText),
                      ),
                    )),
              ],
            ),
            // assetSvdImageWidget(image: DefaultImages.verticleMoreIcn)
          ],
        ),
        verticalSpace(18),
        userDataRowWidget(
          title: "Invoice #".trr,
          value: invNo,
        ),
        verticalSpace(12),
        userDataRowWidget(title: "Order date".trr, value: orderDate),
        verticalSpace(12),
        userDataRowWidget(title: "Paid date".trr, value: paidDate),
        verticalSpace(12),
        userTotalValueWidget(title: "Amount".trr, value: amount),
        verticalSpace(12),
        userTotalValueWidget(title: "VAT".trr, value: vat),
        verticalSpace(isShowButton == true ? 14 : 0),
        isShowButton == true
            ? CommonIconBorderButton(
                iconData: DefaultImages.cancelRequestIcn,
                title: "Cancel request".trr,
                onPressed: cancelReqOnTap,
              )
            : SizedBox()
      ],
    ),
  );
}
