// ignore_for_file: must_be_immutable, prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../../../../core/controller/menu_controller/profile_controller/personal_detail_controller.dart';

class PersonalDetailScreen extends StatefulWidget {
  const PersonalDetailScreen({super.key});

  @override
  State<PersonalDetailScreen> createState() => _PersonalDetailScreenState();
}

class _PersonalDetailScreenState extends State<PersonalDetailScreen> {
  final firstname = '';
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  PersonalDetailController personalDetailController =
      Get.put(PersonalDetailController());

  @override
  void initState() {
    super.initState();
    personalDetailController.getstoredProfileDetails();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            simpleMyAppBar(
                title: "Personal details".trr,
                onTap: () async {
                  SharedPreferences sharedProfileDetails =
                      await SharedPreferences.getInstance();
                  SharedPreferences sharedProfileDetail =
                      await SharedPreferences.getInstance();
                  await sharedProfileDetails.clear();
                  await sharedProfileDetail.clear();
                  Get.back();
                },
                backString: "Back".trr),
            Padding(
              padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
              child: Form(
                key: formKey,
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    CommonTextField(
                      controller: personalDetailController.fNameController,
                      labelText: "First name".trr + '*',
                      hintText: 'Enter first name'.trr,
                      validator: (value) {
                        return Validator.validateName(value, 'First name'.trr);
                      },
                    ),
                    verticalSpace(16),
                    CommonTextField(
                      controller: personalDetailController.mNameController,
                      labelText: 'Middle Name'.trr,
                      hintText: 'Enter Middle Name'.trr,
                      validator: (value) {
                        return '';
                      },
                    ),
                    verticalSpace(16),
                    CommonTextField(
                      controller: personalDetailController.lNameController,
                      labelText: 'Last name'.trr + '*',
                      hintText: 'Enter Last name'.trr,
                      validator: (value) {
                        return Validator.validateName(value, 'Last name'.trr);
                      },
                    ),
                    verticalSpace(16),
                    CommonTextField(
                      controller: personalDetailController.mobileNoController,
                      labelText: "Mobile number".trr + '*',
                      hintText: 'Enter Mobile number'.trr,
                      keyboardType: TextInputType.numberWithOptions(
                          signed: true, decimal: true),
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      maxLength: 10,
                      validator: (value) {
                        return ""; //Validator.validateMobile(value);
                      },
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: CommonButton(
                title: 'Cancel'.trr,
                onPressed: () {
                  Get.back();
                },
                textColor: AppColor.cText,
                btnColor: AppColor.cBackGround,
              ),
            ),
            horizontalSpace(16),
            Expanded(
              child: CommonButton(
                title: 'Save'.trr,
                onPressed: () {
                  if (formKey.currentState!.validate()) {
                    personalDetailController.updateProfileDetail();
                  }
                },
                textColor: AppColor.cWhiteFont,
                btnColor: AppColor.themeOrangeColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
