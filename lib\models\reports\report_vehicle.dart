import 'dart:convert';

class Report_Vehicle {
  final String CODE;
  final String TYPEDESC;

  Report_Vehicle({
    required this.CODE,
    required this.TYPEDESC,
  });

  Map<String, dynamic> toMap() {
    return {
      'CODE': CODE,
      'TYPEDESC': TYPEDESC,
    };
  }

  factory Report_Vehicle.fromMap(Map<String, dynamic> map) {
    return Report_Vehicle(
      CODE: map['CODE'] ?? '',
      TYPEDESC: map['TYPEDESC'] ?? '',
    );
  }
  String toJson() => json.encode(toMap());

  factory Report_Vehicle.fromJson(String source) =>
      Report_Vehicle.fromMap(json.decode(source));
}