// ignore_for_file: must_be_immutable

import 'dart:convert';
//import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:get_storage/get_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/menu_controller/setting_controller/privacy_security_controller.dart';
import 'package:waie_app/models/profile.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/regDB.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/screen/menu_screen/setting_screen/close_account_screen.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/new_user_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../../utils/colors.dart';
import '../../../../utils/constants.dart';
import '../../../../utils/images.dart';
import '../../../../utils/text_style.dart';
import '../../../widget/common_appbar_widget.dart';
import '../../../widget/common_space_divider_widget.dart';
import '../../../widget/icon_and_image.dart';

class PrivacySecurityScreen extends StatefulWidget {
  const PrivacySecurityScreen({super.key});

  @override
  State<PrivacySecurityScreen> createState() => _PrivacySecurityScreenState();
}

enum SupportState {
  unknown,
  supported,
  unSupported,
}

class _PrivacySecurityScreenState extends State<PrivacySecurityScreen> {
  PrivacySecurityController privacySecurityController =
      Get.put(PrivacySecurityController());
  GlobalKey<FormState> emailKey = GlobalKey<FormState>();
  GlobalKey<FormState> passwordKey = GlobalKey<FormState>();
  GetStorage custsData = GetStorage('custsData');
  GetStorage usersData = GetStorage('usersData');
  final LocalAuthentication auth = LocalAuthentication();
  SupportState supportState = SupportState.unknown;
  List<BiometricType>? availableBiometrics;

  RegisterDatabase db = RegisterDatabase();
  // reference the hive register box
  final _isReg = Hive.box('isReg_DB');

  final regUser = "";

  @override
  void initState() {
    auth.isDeviceSupported().then(
          (bool isSupported) => setState(
            () => supportState =
                isSupported ? SupportState.supported : SupportState.unSupported,
          ),
        );
    // check if there is no current register userinfo, then it is the 1st time ever opening the app
    // then let user register login info
    // if (_isReg.get("CURRENT_USERINFO") == null) {
    //   //db.createUserInfoData();
    // } else {
    //   db.loadData();
    // }

    authCheck() async {
      SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
      var user = sharedUser2.getString('user');
      Map cardInfo = json.decode(user!);
      Profile userData = Profile.fromJson(cardInfo);
      if (_isReg.get("regUser") != null) {
        final regUser = _isReg.get('regUser');
        print('regUser is $regUser years old.');
        print('username is ${regUser?['username']}');
        print('password is ${regUser?['password']}');
        print('status is ${regUser?['status']}');
        print("userData['USERNAME'] >>> ${userData.auUsers!.username}");
        setState(() {
          privacySecurityController.isAuth.value =
              regUser?['status'] == "Y" ? true : false;
          privacySecurityController.isAuth.value =
              regUser?['status'] == "Y" ? true : false;
        });
        if (userData.auUsers!.username == regUser?['username']) {
          setState(() {
            privacySecurityController.isMatch.value = true;
            privacySecurityController.isAuth.value =
                regUser?['status'] == "Y" ? true : false;
          });
        }
      }
    }

    print("RUNNING");
    super.initState();
    authCheck();
    checkBiometric();
    getAvailableBiometrics();
  }

  Future<void> checkBiometric() async {
    late bool canCheckBiometric;

    try {
      canCheckBiometric = await auth.canCheckBiometrics;
      print("Biometric supported: $canCheckBiometric");
    } on PlatformException catch (e) {
      print(e);
      canCheckBiometric = false;
    }
  }

  Future<void> getAvailableBiometrics() async {
    late List<BiometricType> biometricTypes;
    try {
      biometricTypes = await auth.getAvailableBiometrics();
      print("supported biometrics: $biometricTypes");
    } on PlatformException catch (e) {
      print(e);
    }

    if (!mounted) {
      return;
    }
    setState(() {
      availableBiometrics = biometricTypes;
    });
  }

  Future<void> authenticateWithBiometrics(bool value) async {
    try {
      final authenticated = await auth.authenticate(
        localizedReason: 'Authenticate with fingerprint or Face ID',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
          useErrorDialogs: true,
        ),
      );

      if (!mounted) {
        return;
      }

      if (authenticated) {
        Loader.hideLoader();
        print("onChanged >> $value");
        if (value) {
          // setState(() {
          //   privacySecurityController.isAuth.value = value;
          //   //privacySecurityController.isOnTap.value = value;
          // });
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return CheckPasswordDialog(
                active: value,
              ); // passing value here
            },
          );
        } else {
          setState(() {
            privacySecurityController.isAuth.value = false;
            //privacySecurityController.isOnTap.value = false;
          });
        }
        // Navigator.push(
        //     context, MaterialPageRoute(builder: (context) => const Home()));
      }
    } on PlatformException catch (e) {
      print(e);
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    var custData = custsData.read('custData');
    var userData = usersData.read('usrData');
    print("=============================");
    print("custData");
    print(custData);
    print(custData["EMAILID"]);
    print("=============================");
    print("userData");
    print(userData);
    print(userData["PASSWORD"]);
    print("=============================");
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              simpleMyAppBar(
                  title: "Privacy and security".trr,
                  onTap: () {
                    Get.back();
                  },
                  backString: " "),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(
                      top: 24, right: 16, left: 16, bottom: 16),
                  child: Obx(() {
                    return ListView(
                      shrinkWrap: true,
                      children: [
                        if (Constants.custRegType != "C")
                          Text(
                            "Change your email".trr,
                            style: pSemiBold17,
                          ),
                        verticalSpace(16),
                        if (Constants.custRegType != "C")
                          Form(
                            key: emailKey,
                            child: CommonTextField(
                              labelText: '',
                              hintText: custData["EMAILID"].toString(),
                              validator: (value) {
                                return Validator.validateEmail(value);
                              },
                              keyboardType: TextInputType.emailAddress,
                              fillColor: AppColor.cFilled,
                              filled: true,
                              readOnly: true,
                            ),
                          ),
                        if (Constants.custRegType != "C") verticalSpace(16),
                        if (Constants.custRegType != "C")
                          CommonButton(
                              title: 'Change'.trr,
                              onPressed: () {
                                /*if (emailKey.currentState!.validate()) {}*/
                                showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return const EmailChangeDialog();
                                  },
                                );
                              },
                              btnColor: AppColor.themeOrangeColor),
                        if (Constants.custRegType != "C") verticalSpace(32),
                        if (Constants.custRegType != "C") horizontalDivider(),
                        if (Constants.custRegType != "C") verticalSpace(32),
                        Text(
                          "Change your password".trr,
                          style: pSemiBold17,
                        ),
                        verticalSpace(16),
                        Form(
                          key: passwordKey,
                          child: CommonTextField(
                            controller:
                                privacySecurityController.passwordController,
                            labelText: '',
                            hintText: "********".trr,
                            obscureText:
                                privacySecurityController.isObSecure.value,
                            obscuringCharacter: '*',
                            validator: (value) {
                              return Validator.validatePassword(value);
                            },
                            fillColor: AppColor.cFilled,
                            filled: true,
                            readOnly: true,
                            suffix: GestureDetector(
                              onTap: () {
                                privacySecurityController.isObSecure.value =
                                    !privacySecurityController.isObSecure.value;
                              },
                              child: assetSvdImageWidget(
                                image:
                                    privacySecurityController.isObSecure.value
                                        ? DefaultImages.eyeOffIcn
                                        : DefaultImages.eyeIcn,
                                colorFilter: ColorFilter.mode(
                                    AppColor.cLightBlue, BlendMode.srcIn),
                              ),
                            ),
                          ),
                        ),
                        verticalSpace(16),
                        CommonButton(
                            title: 'Change'.trr,
                            onPressed: () {
                              /* if (passwordKey.currentState!.validate()) {

                              }*/
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return PasswordChangeDialog();
                                },
                              );
                            },
                            btnColor: AppColor.themeOrangeColor),
                        if (_isReg.get("regUser") == null ||
                            privacySecurityController.isMatch.value == true)
                          verticalSpace(32),
                        if (_isReg.get("regUser") == null ||
                            privacySecurityController.isMatch.value == true)
                          horizontalDivider(),
                        verticalSpace(32),
                        if (_isReg.get("regUser") == null ||
                            privacySecurityController.isMatch.value == true)
                          tagSwitchWidget(
                            value: privacySecurityController.isAuth.value,
                            onChanged: (bool value) {
                              if (value) {
                                Loader.showLoader();
                                authenticateWithBiometrics(value);
                              } else {
                                db.deleteDatabase();
                                setState(() {
                                  privacySecurityController.isAuth.value =
                                      false;
                                  //privacySecurityController.isOnTap.value = false;
                                });
                              }
                              //emailNotificationController.updateCustNoty();
                            },
                            label: 'Login using Fingerprint / Face ID'.trr,
                            // onTap: () {
                            //   print(
                            //       "onTap >> ${privacySecurityController.isOnTap.value}");
                            //   if (privacySecurityController.isOnTap.value ==
                            //       true) {
                            //     Loader.showLoader();
                            //     authenticateWithBiometrics(
                            //         privacySecurityController.isOnTap.value);
                            //   } else {
                            //     setState(() {
                            //       privacySecurityController.isAuth.value = false;
                            //       privacySecurityController.isOnTap.value = false;
                            //     });
                            //   }
                            //   //emailNotificationController.updateCustNoty();
                            // },
                          ),
                        verticalSpace(32),
                        horizontalDivider(),
                        verticalSpace(32),
                        Text(
                          "Close your account".trr,
                          style: pSemiBold17,
                        ),
                        verticalSpace(4),
                        Text(
                          "Your data will be deleted permanently.".trr,
                          style: pRegular13,
                        ),
                        verticalSpace(16),
                        CommonButton(
                            title: 'Close my account'.trr,
                            onPressed: () {
                              Get.to(() => const CloseAccountScreen());
                            },
                            btnColor: AppColor.cRedText),
                      ],
                    );
                  }),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  tagSwitchWidget({
    required bool value,
    required void Function(bool)? onChanged,
    required String label,
    Function()? onTap,
  }) {
    return ListTile(
      title: Text(label, style: pRegular14),
      leading: CustomSwitch(
        value: value,
        onChanged: onChanged!,
      ),
      horizontalTitleGap: 8,
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }
}

class CheckPasswordDialog extends StatelessWidget {
  final bool active;
  const CheckPasswordDialog({
    super.key,
    required this.active,
  });

  @override
  Widget build(BuildContext context) {
    // TextEditingController oldPasswordController = TextEditingController();
    // TextEditingController newPasswordController = TextEditingController();
    PrivacySecurityController privacySecurityController =
        Get.put(PrivacySecurityController());
    GlobalKey<FormState> passwordKey = GlobalKey<FormState>();

    return AlertDialog(
      title: Center(
          child: Text(
        'Confirmation'.trr,
        style: pBold18,
      )),
      content: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  'Please enter password to verify your account'.trr,
                  style: pRegular14,
                  textAlign: TextAlign.center,
                ),
              ),
              verticalSpace(10),
              Form(
                key: passwordKey,
                child: CommonTextField(
                    controller:
                        privacySecurityController.checkPasswordController,
                    labelText: 'Password'.trr,
                    hintText: "Please enter here".trr,
                    obscuringCharacter: '*',
                    filled: true,
                    fillColor: AppColor.cFilled,
                    obscureText: privacySecurityController.isCheckPass.value,
                    validator: (value) {
                      return Validator.validatePassword(value);
                    },
                    suffix: GestureDetector(
                        onTap: () {
                          privacySecurityController.isCheckPass.value =
                              !privacySecurityController.isCheckPass.value;
                        },
                        child: assetSvdImageWidget(
                            image: privacySecurityController.isCheckPass.value
                                ? DefaultImages.eyeOffIcn
                                : DefaultImages.eyeIcn))),
              ),
              verticalSpace(8),
            ],
          )),
      actions: [
        CommonButton(
            title: "Confirm".trr,
            onPressed: () async {
              Loader.showLoader();
              //privacySecurityController.changeUserPass();
              print(active);
              print(privacySecurityController.checkPasswordController.text);
              privacySecurityController.checkUserPassword(active);
              //Get.back();
              // await Future.delayed(const Duration(seconds: 5), () {
              //   Loader.hideLoader();
              //   Get.back();
              // });
            },
            btnColor: AppColor.themeOrangeColor),
      ],
    );
  }

  Widget validateTextRow({required String image, required String title}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          assetSvdImageWidget(image: image),
          horizontalSpace(13),
          Expanded(
            child: Text(
              title,
              style: pRegular13,
              maxLines: 3,
            ),
          )
        ],
      ),
    );
  }
}

class PasswordChangeDialog extends StatelessWidget {
  PasswordChangeDialog({super.key});

  String pattern =
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&-_])[A-Za-z\d@$!%*?&-_]{8,}$';
  RxBool isPatternMatch = false.obs;
  RxBool isPasswordLength = false.obs;

  @override
  Widget build(BuildContext context) {
    // TextEditingController oldPasswordController = TextEditingController();
    // TextEditingController newPasswordController = TextEditingController();
    PrivacySecurityController privacySecurityController =
        Get.put(PrivacySecurityController());
    GlobalKey<FormState> emailKey = GlobalKey<FormState>();
    GlobalKey<FormState> passwordKey = GlobalKey<FormState>();
    GlobalKey<FormState> oldpasswordKey = GlobalKey<FormState>();

    return AlertDialog(
      title: const Text('Change Password'),
      content: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /*TextFormField(
                  controller: oldPasswordController,
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'Old Password',
                    hintText: 'Enter old password',
                  ),
                ),*/
              Form(
                key: oldpasswordKey,
                child: CommonTextField(
                  controller: privacySecurityController.oldPasswordController,
                  labelText: 'Old Password',
                  hintText: "Please enter here".trr,
                  obscureText: privacySecurityController.isOldObSecure.value,
                  obscuringCharacter: '*',
                  validator: (value) {
                    return Validator.validatePassword(value);
                  },
                  suffix: GestureDetector(
                    onTap: () {
                      print("Old Password");
                      print(privacySecurityController.isOldObSecure.value);
                      privacySecurityController.isOldObSecure.value =
                          !privacySecurityController.isOldObSecure.value;
                    },
                    child: assetSvdImageWidget(
                      image: privacySecurityController.isOldObSecure.value
                          ? DefaultImages.eyeOffIcn
                          : DefaultImages.eyeIcn,
                      colorFilter: ColorFilter.mode(
                          AppColor.cLightBlue, BlendMode.srcIn),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Form(
                key: passwordKey,
                child: CommonTextField(
                  controller: privacySecurityController.newPasswordController,
                  labelText: 'Confirm New Password',
                  hintText: "Please enter here".trr,
                  obscureText: privacySecurityController.isNewObSecure.value,
                  obscuringCharacter: '*',
                  validator: (value) {
                    return Validator.validatePassword(value);
                  },
                  onChanged: (value) {
                    RegExp regex = RegExp(pattern);
                    if (regex.hasMatch(value)) {
                      isPatternMatch.value = true;
                    } else {
                      isPatternMatch.value = false;
                    }
                    if (value.length > 7) {
                      isPasswordLength.value = true;
                    } else {
                      isPasswordLength.value = false;
                    }
                  },
                  suffix: GestureDetector(
                    onTap: () {
                      print("New Password");
                      print(privacySecurityController.isNewObSecure.value);
                      privacySecurityController.isNewObSecure.value =
                          !privacySecurityController.isNewObSecure.value;
                    },
                    child: assetSvdImageWidget(
                      image: privacySecurityController.isNewObSecure.value
                          ? DefaultImages.eyeOffIcn
                          : DefaultImages.eyeIcn,
                      colorFilter: ColorFilter.mode(
                          AppColor.cLightBlue, BlendMode.srcIn),
                    ),
                  ),
                ),
              ),
              verticalSpace(8),
              // Column(
              //   crossAxisAlignment: CrossAxisAlignment.start,
              //   children: [
              //     verticalSpace(15),
              //     Text(
              //       "Your password should be...",
              //       style: pSemiBold14,
              //     ),
              //     verticalSpace(8),
              //     Text(
              //       "• ${"At least 8 characters long.".trr}\n• ${"A combination of uppercase letters, lowercase letters, numbers, and symbols.".trr}",
              //       style: pRegular12,
              //     ),
              //   ],
              // ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  verticalSpace(15),
                  Text(
                    "Your password should be...",
                    style: pSemiBold14,
                  ),
                  verticalSpace(8),
                  validateTextRow(
                      image: isPasswordLength.value
                          ? DefaultImages.validateIcn
                          : DefaultImages.notValidateIcn,
                      title: "At least 8 characters long.".trr),
                  validateTextRow(
                      image: isPatternMatch.value
                          ? DefaultImages.validateIcn
                          : DefaultImages.notValidateIcn,
                      title:
                          "A combination of uppercase letters, lowercase letters, numbers, and symbols."
                              .trr),
                  // Text(
                  //   "• " +
                  //       "At least 8 characters long.".trr +
                  //       "\n• " +
                  //       "A combination of uppercase letters, lowercase letters, numbers, and symbols."
                  //           .trr,
                  //   style: pRegular12,
                  // ),
                ],
              ),
            ],
          )),
      actions: [
        CommonButton(
            title: "Change",
            onPressed: () {
              privacySecurityController.changeUserPass();
            },
            btnColor: AppColor.themeOrangeColor),
      ],
    );
  }

  Widget validateTextRow({required String image, required String title}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          assetSvdImageWidget(image: image),
          horizontalSpace(13),
          Expanded(
            child: Text(
              title,
              style: pRegular13,
              maxLines: 3,
            ),
          )
        ],
      ),
    );
  }
}

class EmailChangeDialog extends StatelessWidget {
  const EmailChangeDialog({super.key});

  @override
  Widget build(BuildContext context) {
    PrivacySecurityController privacySecurityController =
        Get.put(PrivacySecurityController());

    return AlertDialog(
      title: const Center(child: Text('Confirm New Email')),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Form(
            child: CommonTextField(
              controller: privacySecurityController.newEmailController,
              labelText: '',
              hintText: "Please enter here".trr,
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
      actions: [
        CommonButton(
            title: "Confirm",
            onPressed: () {
              privacySecurityController.changeUserEmail();
            },
            btnColor: AppColor.themeOrangeColor),
      ],
    );
  }
}
