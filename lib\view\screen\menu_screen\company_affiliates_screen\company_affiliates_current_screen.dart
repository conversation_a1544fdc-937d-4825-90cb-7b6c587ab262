// ignore_for_file: prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/promotion_controller/promotion_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';

class CompanyAffiliateCurrrentScreen extends StatefulWidget {
  const CompanyAffiliateCurrrentScreen({super.key});

  @override
  State<CompanyAffiliateCurrrentScreen> createState() =>
      _CompanyAffiliateCurrrentScreenState();
}

class _CompanyAffiliateCurrrentScreenState
    extends State<CompanyAffiliateCurrrentScreen> {
  PromotionController promotionalController = Get.put(PromotionController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        verticalSpace(24),
        Container(
          decoration: BoxDecoration(
            color: AppColor.cLightGrey,
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Image.asset(DefaultImages.tawuniyaImage),
                  Text("Tawuniya\nPromotions",
                      style: pSemiBold18,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.justify)
                ],
              ),
              verticalSpace(24),
              Text(
                'Promo Smart Card'.trr,
                style: pRegular12,
              ),
              verticalSpace(4),
              CommonTextField(
                controller: promotionalController.PromoFieldValue,
                /* onChanged: (value) {
                  // Listen to text changes and update enteredText variable
                  setState(() {
                   promotionalController.PromoFieldValue.value;
                  });
                },*/
                labelText: '',
                hintText: 'Please enter Card ID here'.trr + '...',
                fillColor: AppColor.cBackGround,
                filled: true,
              ),
              verticalSpace(8),
              CommonButton(
                title: 'Activate'.trr,
                btnColor: AppColor.themeOrangeColor,
                onPressed: () {
                  promotionalController.activePromoCode("CODE", "TW");
                },
              ),
              verticalSpace(24),
              Text(
                'Promo Topup Code'.trr,
                style: pRegular12,
              ),
              verticalSpace(4),
              CommonTextField(
                labelText: '',
                hintText: 'Please enter code here'.trr + '...',
                fillColor: AppColor.cBackGround,
                filled: true,
              ),
              verticalSpace(8),
              CommonButton(
                title: 'Activate'.trr,
                btnColor: AppColor.themeOrangeColor,
                onPressed: () {
                  promotionalController.activePromoCode("CARD", "TW");
                },
              ),
            ],
          ),
        ),
        verticalSpace(24),
        Container(
          decoration: BoxDecoration(
            color: AppColor.cLightGrey,
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Image.asset(DefaultImages.walaplusImage),
                  Expanded(
                    child: Text(
                      "WALA Plus\n Promotions",
                      style: pSemiBold18,
                      maxLines: 2,
                      textAlign: TextAlign.end,
                    ),
                  )
                ],
              ),
              verticalSpace(24),
              Text(
                'Promo Topup Code'.trr,
                style: pRegular12,
              ),
              verticalSpace(4),
              CommonTextField(
                labelText: '',
                hintText: 'Please enter code here'.trr + '...',
                fillColor: AppColor.cBackGround,
                filled: true,
              ),
              verticalSpace(8),
              CommonButton(
                title: 'Activate'.trr,
                btnColor: AppColor.themeOrangeColor,
                onPressed: () {
                  promotionalController.activePromoCode("CODE", "WP");
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
