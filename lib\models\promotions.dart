import 'dart:convert';

class Promotions {
  final int LINE;
  final String ACT_CODE;
  final String ACTDATE;
  final double AFTER_VAT;
  final String PROMO;
  final String PROMOTYPE;

  Promotions(
      {required this.LINE,
      required this.ACT_CODE,
      required this.ACTDATE,
      required this.AFTER_VAT,
      required this.PROMO,
      required this.PROMOTYPE});

  Map<String, dynamic> toMap() {
    return {
      'LINE': LINE,
      'ACT_CODE': ACT_CODE,
      'ACTDATE': ACTDATE,
      'AFTER_VAT': AFTER_VAT,
      'PROMO': PROMO,
      'PROMOTYPE': PROMOTYPE,
    };
  }

  factory Promotions.fromMap(Map<String, dynamic> map) {
    return Promotions(
      LINE: map['LINE'] ?? '',
      ACT_CODE: map['ACT_CODE'] ?? '',
      ACTDATE: map['ACTDATE'] ?? '',
      AFTER_VAT: map['AFTER_VAT'] ?? 0,
      PROMO: map['PROMO'] ?? '',
      PROMOTYPE: map['PROMOTYPE'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Promotions.fromJson(String source) =>
      Promotions.fromMap(json.decode(source));
}
