class Locations {
  Locations({
    required this.srNo,
    required this.stnNo,
    required this.stationName,
    required this.placeDesc,
    required this.areaName,
    required this.stationStatus,
    required this.district,
    required this.stnOwners,
    required this.stnAllCustFlag,
    required this.stationAddress,
    required this.stationFrom,
    required this.stationTo,
    required this.isOffice,
    required this.latitude,
    required this.longitude,
    required this.stnMosque,
    required this.stationCoordinates,
    required this.placeCode,
    required this.mainbranch,
    required this.branchName,
    required this.products,
    required this.prods,
    required this.stnCarService,
    required this.stnFoodRest,
    required this.stnCarRent,
    required this.stnAtm,
  });

  final int? srNo;
  final int? stnNo;
  final String? stationName;
  final String? placeDesc;
  final String? areaName;
  final String? stationStatus;
  final String? district;
  final String? stnOwners;
  final String? stnAllCustFlag;
  final String? stationAddress;
  final String? stationFrom;
  final String? stationTo;
  final String? isOffice;
  final String? latitude;
  final String? longitude;
  final String? stnMosque;
  final String? stationCoordinates;
  final String? placeCode;
  final String? mainbranch;
  final String? branchName;
  final String? products;
  final String? prods;
  final String? stnCarService;
  final String? stnFoodRest;
  final String? stnCarRent;
  final String? stnAtm;

  factory Locations.fromJson(Map<String, dynamic> json){
    return Locations(
      srNo: json["SR_NO"],
      stnNo: json["STN_NO"],
      stationName: json["STATION_NAME"],
      placeDesc: json["PLACE_DESC"],
      areaName: json["AREA_NAME"],
      stationStatus: json["STATION_STATUS"],
      district: json["DISTRICT"],
      stnOwners: json["STN_OWNERS"],
      stnAllCustFlag: json["STN_ALL_CUST_FLAG"],
      stationAddress: json["STATION_ADDRESS"],
      stationFrom: json["STATION_FROM"],
      stationTo: json["STATION_TO"],
      isOffice: json["IS_OFFICE"],
      latitude: json["LATITUDE"],
      longitude: json["LONGITUDE"],
      stnMosque: json["STN_MOSQUE"],
      stationCoordinates: json["STATION_COORDINATES"],
      placeCode: json["PLACE_CODE"],
      mainbranch: json["MAINBRANCH"],
      branchName: json["BRANCH_NAME"],
      products: json["PRODUCTS"],
      prods: json["PRODS"],
      stnCarService: json["STN_CAR_SERVICE"],
      stnFoodRest: json["STN_FOOD_REST"],
      stnCarRent: json["STN_CAR_RENT"],
      stnAtm: json["STN_ATM"],
    );
  }

}
