import 'dart:convert';

List<ComplaintCityModel> complaintCityModelFromJson(String str) =>
    List<ComplaintCityModel>.from(
        json.decode(str).map((x) => ComplaintCityModel.fromJson(x)));

String complaintCityModelToJson(List<ComplaintCityModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ComplaintCityModel {
  String placeCode;
  String place;

  ComplaintCityModel({
    required this.placeCode,
    required this.place,
  });

  factory ComplaintCityModel.fromJson(Map<String, dynamic> json) =>
      ComplaintCityModel(
        placeCode: json["PLACE_CODE"],
        place: json["PLACE"],
      );

  Map<String, dynamic> toJson() => {
        "PLACE_CODE": placeCode,
        "PLACE": place,
      };
}
