// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/company_affiliate_history.dart';
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../../utils/constants.dart';

class CompanyAffiliateHistoryController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  var affiliateRequestHistoryList = <CompanyAffiliateHistoryModel>[].obs;

  // fetch() async {
  //   var custData = jsonEncode(custsData.read('custData'));
  //   var client = http.Client();
  //   var response = await client.post(
  //       Uri.parse(ApiEndPoints.baseUrl +
  //           ApiEndPoints.authEndpoints.getAffiliateRequestHistory),
  //       body: {
  //         "custdata": custData,
  //         "IsAR": Constants.IsAr_App,
  //       });
  //   List result = jsonDecode(response.body);
  //   print("CompanyAffiliateHistoryController response >>>>> $response");
  //   print(
  //       "CompanyAffiliateHistoryController STATUS >>>>> ${response.statusCode}");

  //   print("CompanyAffiliateHistoryController result >>>>> $result");
  //   print("CompanyAffiliateHistoryController COUNT >>>>> ${result.length}");
  // }

  Future<List<CompanyAffiliateHistoryModel>>
      getAffiliateRequestHistory() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custData = jsonEncode(custsData.read('custData'));
    var client = http.Client();
    try {
      if (affiliateRequestHistoryList.isEmpty) {
        var response = await client.post(
            Uri.parse(ApiEndPoints.baseUrl +
                ApiEndPoints.authEndpoints.getAffiliateRequestHistory),
            body: {
              "custdata": custData,
              "IsAR": Constants.IsAr_App,
            });
        List result = jsonDecode(response.body);
        print("CompanyAffiliateHistoryController response >>>>> $response");
        print(
            "CompanyAffiliateHistoryController STATUS >>>>> ${response.statusCode}");

        print("CompanyAffiliateHistoryController result >>>>> $result");
        print("CompanyAffiliateHistoryController COUNT >>>>> ${result.length}");
        print(
            "===============================================================");
        print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
        print(
            "===============================================================");

        for (int i = 0; i < result.length; i++) {
          CompanyAffiliateHistoryModel data =
              CompanyAffiliateHistoryModel.fromJson(
                  result[i] as Map<String, dynamic>);
          affiliateRequestHistoryList.add(data);
        }
        print(
            "===============================================================");
        print(
            "CompanyAffiliateHistoryController >>>>> ${jsonDecode(jsonEncode(affiliateRequestHistoryList))}");
        print(
            "===============================================================");

        return affiliateRequestHistoryList;
      }
      print("ERROR: NO DATA");
      return [];
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('CompanyAffiliateHistoryController');
    print(jsonDecode(jsonEncode(affiliateRequestHistoryList)));
    if (affiliateRequestHistoryList.isEmpty) {
      print("sulod");
      await getAffiliateRequestHistory();
    }
    //Navigator.of(Get.context!).pop();
    // await fetch();
  }
}
