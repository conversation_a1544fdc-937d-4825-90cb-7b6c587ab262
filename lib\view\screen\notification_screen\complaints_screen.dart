// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/notification_controller/notification_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/new_order_screen_replacement.dart';
import 'package:waie_app/view/screen/notification_screen/show_tag_screen.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import '../../../utils/constants.dart';
import 'activity_screen.dart';

class ComplaintsScreen extends StatelessWidget {
  final NotificationController notificationController;

  const ComplaintsScreen({super.key, required this.notificationController});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        verticalSpace(24),
        FutureBuilder<dynamic>(
            future: notificationController.fetchComplaintNotification(),
            builder: (context, AsyncSnapshot snapshot) {
              if (snapshot.connectionState != ConnectionState.done) {
                return const Center(child: CircularProgressIndicator());
              } else {
                return ListView.builder(
                  itemCount: notificationController.genCompNotification.length,
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    var data =
                        notificationController.genCompNotification[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: buildShowNotyWidget(
                        title: data.notyDesc,
                        time: data.notifydate,
                        notyType: data.notifytype,
                        doc_no: data.docNo,
                        function: () {},
                      ),
                    );
                  },
                );
              }
            }),
      ],
    );
  }

  Widget buildShowNotyWidget(
      {String? title, String? time, String? notyType,String? doc_no, Function()? function}) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 13, horizontal: 16),
      decoration: BoxDecoration(
        color: AppColor.cLightGrey,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            // Changed to Expanded to prevent overflow
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title!,
                  style: pBold14.copyWith(fontSize: 13),
                  softWrap: true,
                  maxLines: 3, // Maximum number of lines before wrapping
                  overflow: TextOverflow.ellipsis,
                ),
                verticalSpace(12),
                Text(
                  time!,
                  style: pRegular10.copyWith(
                      fontSize: 11, color: AppColor.cDarkGreyFont),
                )
              ],
            ),
          ),
          horizontalSpace(24),
          // Text("NotyType=="+notyType.toString()),
          if (notyType!.contains("REPLACEMENT"))
            GestureDetector(
              onTap: () {
                Constants.requestSITemp=doc_no.toString();
                Get.to(NewOrderScreenreplacement());
              },
              child: Container(
               // color: Colors.blue,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: AppColor.cDarkBlueBorder),
                ),
                padding: EdgeInsets.all(6),
                child: Row(
                  children: [
                    // assetSvdImageWidget(image: icon),
                    // horizontalSpace(8),
                    Text(
                      "View",
                      style: pRegular13,
                    ),
                  ],
                ),
              ),
            ),

          // Ensure this button or any other widgets here do not cause overflow
        ],
      ),
    );
  }
}
