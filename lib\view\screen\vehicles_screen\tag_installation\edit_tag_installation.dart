// ignore_for_file: avoid_print, prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:draggable_bottom_sheet/draggable_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:waie_app/core/controller/vehicle_controller/edit_tag_installation_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/location_screen/gase_station_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/add_pure_dc_vehicle_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

class EditTagInstallationWidget extends StatefulWidget {
  const EditTagInstallationWidget({Key? key}) : super(key: key);

  @override
  State<EditTagInstallationWidget> createState() => _EditTagInstallationWidgetState();
}

class _EditTagInstallationWidgetState extends State<EditTagInstallationWidget> {
  EditTagInstallationController editTagInstallationController = Get.put(EditTagInstallationController());

  // VehicleController vehicleController = Get.find();

  late GoogleMapController mapController;
  late CameraPosition cameraPosition;

  final LatLng _center = const LatLng(21.44484793949768, 53.295109691390245);

  Map<MarkerId, Marker> markers = <MarkerId, Marker>{};

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;
    _add();
  }

  Future<void> _add() async {
    for (int i = 0; i < editTagInstallationController.latLongList.length; i++) {
      var markerIdVal = "$i";
      final MarkerId markerId = MarkerId(markerIdVal);
      final Marker marker = Marker(
        markerId: markerId,
        position: editTagInstallationController.latLongList[i],
        icon: await createMarkerImageFromAsset(context, DefaultImages.gasStationMarker),
        onTap: () {
          showModalBottomSheet(
            context: context,
            barrierColor: AppColor.cBlackOpacity,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
            isScrollControlled: true,
            builder: (context) {
              return locationBottomSheetWidget(
                title: "RIYADH",
                subTitle: "ASMENT EXIT - 18",
                openNow: "Yes".trr,
                workingHour: "24/7",
                phoneNo: "966920002667",
                latitude: editTagInstallationController.latLongList[i].latitude,
                longitude: editTagInstallationController.latLongList[i].longitude,
              );
            },
          );
        },
      );
      setState(() {
        markers[markerId] = marker;
      });
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    cameraPosition = CameraPosition(target: _center, zoom: 7.0);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: DraggableBottomSheet(
        maxExtent: Get.height / 1.2,
        minExtent: Get.height / 2.3,
        expansionExtent: Get.height / 4,
        useSafeArea: true,
        barrierDismissible: false,
        barrierColor: AppColor.cTransparent,
        curve: Curves.easeIn,
        previewWidget: _previewWidget(),
        expandedWidget: _previewWidget(),
        backgroundWidget: _backgroundWidget(),
        onDragging: (pos) {},
      ),
    );
  }

  Widget _backgroundWidget() {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.only(right: 16, left: 16, bottom: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    padding: EdgeInsets.only(
                      top: 15,
                      bottom: 15,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        assetSvdImageWidget(image: DefaultImages.backIcn, colorFilter: ColorFilter.mode(AppColor.cDarkBlueFont, BlendMode.srcIn)),
                        horizontalSpace(10),
                        Text(
                          "Back".trr,
                          style: pRegular18.copyWith(color: AppColor.cDarkBlueFont, fontSize: 17),
                          textAlign: TextAlign.start,
                        )
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.center,
                    child: Text(
                      "Edit tag Installation".trr,
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                assetSvdImageWidget(image: DefaultImages.searchCircleIcn)
              ],
            ),
          ),
          Expanded(
            child: GoogleMap(
              onMapCreated: _onMapCreated,
              markers: Set<Marker>.of(markers.values),
              // YOUR MARKS IN MAP
              initialCameraPosition: cameraPosition,
              scrollGesturesEnabled: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _previewWidget() {
    return Container(
      height: Get.height,
      decoration: BoxDecoration(color: AppColor.cBackGround, borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Obx(() {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CommonDropdownButtonWidget(
              hint: "Please select here".trr+"...",
              labelText: 'Select City'.trr,
              value: editTagInstallationController.selectedCity.value,
              list: editTagInstallationController.cityList,
              onChanged: (value) {
                editTagInstallationController.selectedCity.value = value;
              },
            ),
            verticalSpace(16),
            Expanded(
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: editTagInstallationController.getTagList.length,
                    itemBuilder: (context, index) {
                      var data = editTagInstallationController.getTagList[index];
                      // return Obx(() {
                      return editScheduleDetailWidget(
                        context,
                        title: data['title'],
                        address: data['address'],

                        // timeOnChanged: (value) {
                        //   // tagInstallationController.selectedTime.value = value.toString();
                        //   data['selectedTime'].value = value.toString();
                        // },
                        time: data['time'],
                        plat: data['plat'],
                        // platOnChanged: (value) {
                        //   data['selectedPlat'].value = value;
                        // },
                        vehicleList: data['vehicle'],
                        addVehicle: () {
                          Get.to(() => AddPureDCVehicleScreen(
                                title: "Add Vehicle".trr
                            ,
                              ));
                        },
                      );
                      // });
                    },
                  ),
                  verticalSpace(16),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      assetSvdImageWidget(image: DefaultImages.truckIcn),
                      horizontalSpace(12),
                      Text(
                        '${editTagInstallationController.getTagList.first["vehicle"].length} Scheduled',
                        style: pBold12,
                      ),
                      horizontalSpace(24),
                      Expanded(
                        child: CommonIconButton(
                          height: 40,
                          title: "CONFIRM SCHEDULE".trr,
                          iconData: DefaultImages.scheduleInstallationIcn,
                          btnColor: AppColor.themeOrangeColor,
                          fontSize: 12,
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                                  contentPadding: EdgeInsets.all(16),
                                  insetPadding: EdgeInsets.all(16),
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  content: tagInstallationScheduleSuccessWidget(),
                                );
                              },
                            );
                          },
                        ),
                      )
                    ],
                  )
                ]),
              ),
            )
          ],
        );
      }),
    );
  }

  Container tagInstallationScheduleSuccessWidget() {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.center, children: [
        GestureDetector(
          onTap: () {
            Get.back();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [assetSvdImageWidget(image: DefaultImages.cancelIcn)],
          ),
        ),
        verticalSpace(24),
        Text(
          "Tag installation has been edited".trr,
          style: pSemiBold17,
        ),
        verticalSpace(8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40),
          child: Text("We’ve sent you an email with the appointment details".trr, style: pRegular14, textAlign: TextAlign.center),
        ),
        verticalSpace(24),
        CommonButton(
          title: 'Okay'.trr,
          onPressed: () {
            Get.back();
            Get.back();
          },
          btnColor: AppColor.themeOrangeColor,
        )
      ]),
    );
  }

  editScheduleDetailWidget(
    BuildContext context, {
    String? title,
    String? address,
    List? time,
    List? plat,
    // ValueChanged? platOnChanged,
    Function()? addVehicle,
    required List vehicleList,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        width: Get.width,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(6), color: AppColor.cLightGrey),
        padding: EdgeInsets.all(22),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title!,
                  style: pBold24,
                ),
                assetSvdImageWidget(image: DefaultImages.circleMapPinIcn)
              ],
            ),
            verticalSpace(8),
            Text(
              address!,
              style: pMedium14,
            ),
            verticalSpace(16),
            Row(
              children: [
                Expanded(
                    child: Text(
                  "Sun-Thu",
                  style: pRegular14,
                )),
                Expanded(
                    child: Text(
                  "8AM - 8PM",
                  style: pMedium14,
                )),
              ],
            ),
            Row(
              children: [
                Expanded(
                    child: Text(
                  "Fri",
                  style: pRegular14,
                )),
                Expanded(
                    child: Text(
                  "9AM - 7PM",
                  style: pMedium14,
                )),
              ],
            ),
            verticalSpace(24),
            ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: vehicleList.length,
              itemBuilder: (context, index) {
                var data = vehicleList[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 24),
                  child: Obx(() {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                             "Vehicle".trr+ " ${index + 1}",
                              style: pRegular14,
                            ),
                            GestureDetector(
                                onTap: () {
                                  vehicleList.removeAt(index);
                                  editTagInstallationController.getTagList.refresh();
                                },
                                child: assetSvdImageWidget(image: DefaultImages.deleteIcn)),
                          ],
                        ),
                        verticalSpace(8),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: CommonTextField(
                                controller: TextEditingController()..text = data['dateController'],
                                labelText: '',
                                suffix: assetSvdImageWidget(image: DefaultImages.calendarIcn),
                                fillColor: AppColor.cWhite,
                                filled: true,
                                readOnly: true,
                                onTap: () async {
                                  DateTime? pickedDate = await showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now(),
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime(2101),
                                    builder: (context, child) {
                                      return Theme(
                                        data: Theme.of(context).copyWith(
                                          colorScheme: ColorScheme.light(
                                            primary: AppColor.themeOrangeColor,
                                          ),
                                          textButtonTheme: TextButtonThemeData(
                                            style: TextButton.styleFrom(
                                              foregroundColor: AppColor.themeDarkBlueColor, // button text color
                                            ),
                                          ),
                                        ),
                                        child: child!,
                                      );
                                    },
                                  );
                                  if (pickedDate != null) {
                                    print(pickedDate);
                                    String formattedDate = DateFormat('MM/dd/yy').format(pickedDate);
                                    print(formattedDate);
                                    data['dateController'] = formattedDate;
                                    editTagInstallationController.getTagList.refresh();
                                  } else {
                                    print("Date is not selected");
                                  }
                                },
                              ),
                            ),
                            horizontalSpace(11),
                            Expanded(
                              // flex: 1,
                              child: Container(
                                height: 44,
                                decoration: BoxDecoration(color: AppColor.cWhite, borderRadius: BorderRadius.circular(8), border: Border.all(color: AppColor.cBorder)),
                                // padding: EdgeInsets.only(left: 10),
                                child: Center(
                                  child: DropdownButton(
                                    value: data['selectedTime'].value,
                                    items: time!.map((data) {
                                      return DropdownMenuItem(
                                        value: data,
                                        child: Text(
                                          data,
                                          style: pRegular13.copyWith(),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      data['selectedTime'].value = value.toString();
                                    },
                                    style: pMedium14.copyWith(
                                      color: AppColor.cWhiteFont,
                                    ),
                                    underline: Container(),
                                    dropdownColor: AppColor.cLightGrey,
                                    icon: assetSvdImageWidget(image: DefaultImages.dropDownIcn),
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                        verticalSpace(12),
                        CommonDropdownButtonWidget(
                            hint:"Plat".trr+ " #",
                            labelText: '',
                            value: data['selectedPlat'].value,
                            list: plat,
                            onChanged: (value) {
                              data['selectedPlat'].value = value;
                            },
                            filledColor: AppColor.cWhite),
                      ],
                    );
                  }),
                );
              },
            ),
            verticalSpace(24),
            GestureDetector(
              onTap: addVehicle,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  assetSvdImageWidget(image: DefaultImages.plusIcn, colorFilter: ColorFilter.mode(AppColor.cOrangeFont, BlendMode.srcIn), width: 14, height: 14),
                  horizontalSpace(8),
                  Text(
                    "Add Vehicle".trr,
                    style: pMedium12.copyWith(color: AppColor.cOrangeFont, fontSize: 13),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
