class Menulist {
  Menulist({
    required this.menuCode,
    required this.menuName,
    required this.menu,
  });

  final String? menuCode;
  final String? menuName;
  final List<Menu> menu;

  factory Menulist.fromJson(Map<String, dynamic> json){
    return Menulist(
      menuCode: json["MenuCode"],
      menuName: json["MenuName"],
      menu: json["Menu"] == null ? [] : List<Menu>.from(json["Menu"]!.map((x) => Menu.fromJson(x))),
    );
  }

}

class Menu {
  Menu({
    required this.menuCode,
    required this.controlCode,
    required this.controlName,
    required this.subCont,
  });

  final String? menuCode;
  final String? controlCode;
  final String? controlName;
  final List<Menu> subCont;

  factory Menu.fromJson(Map<String, dynamic> json){
    return Menu(
      menuCode: json["MenuCode"],
      controlCode: json["ControlCode"],
      controlName: json["ControlName"],
      subCont: json["SubCont"] == null ? [] : List<Menu>.from(json["SubCont"]!.map((x) => Menu.fromJson(x))),
    );
  }

}
