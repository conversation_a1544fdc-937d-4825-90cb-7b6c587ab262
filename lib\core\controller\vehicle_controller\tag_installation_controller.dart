import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class TagInstallationController extends GetxController {
  RxBool isUpcoming = true.obs;
  RxBool isHistory = false.obs;
  RxList upcomimgDataList = [].obs;
  List<LatLng> latLongList = [
    const LatLng(21.44484793949768, 53.295109691390245),
    const <PERSON>t<PERSON><PERSON>(20.582120409829233, 44.96018559757371),
    const Lat<PERSON><PERSON>(23.899725409740984, 54.046233681296975),
    const Lat<PERSON><PERSON>(22.302502578679334, 47.422575301577865),
    const <PERSON>t<PERSON><PERSON>(25.169169066706516, 38.833075072755804),
    const <PERSON><PERSON><PERSON><PERSON>(23.359337577784572, 45.01153009917833),
    const Lat<PERSON><PERSON>(20.950188013577115, 49.39292756943918),
  ];
  RxList dummyUpcomimgDataList = [
    {
      "title": "Al Reef Car Maintenance (3 Vehicles)",
      "totalVehicle": '3',
      "type": "Diesel",
      "scheduledOn": 'Monday, 18 April 2023',
      "location": 'Old Industrial 87, Riyadh, Saudi Arabia',
      "referenceId": '***********',
      "latlng": const LatLng(21.44484793949768, 53.295109691390245),
    },
    {
      "title": "Al Reef Car Maintenance (3 Vehicles)",
      "totalVehicle": '3',
      "type": "Diesel",
      "scheduledOn": 'Monday, 18 April 2023',
      "location": 'Old Industrial 87, Riyadh, Saudi Arabia',
      "referenceId": '***********',
      "latlng": const LatLng(21.44484793949768, 53.295109691390245),
    },
  ].obs;
  RxList tagInstallationHistoryList = [
    {
      "title": "Al Reef Car Maintenance (3 Vehicles)",
      "totalVehicle": '3',
      "type": "Diesel",
      "scheduledOn": 'Monday, 18 April 2023',
      "location": 'Old Industrial 87, Riyadh, Saudi Arabia',
      "referenceId": '***********',
      "latlng": const LatLng(21.44484793949768, 53.295109691390245),
    },
    {
      "title": "Al Reef Car Maintenance (3 Vehicles)",
      "totalVehicle": '3',
      "type": "Diesel",
      "scheduledOn": 'Monday, 18 April 2023',
      "location": 'Old Industrial 87, Riyadh, Saudi Arabia',
      "referenceId": '***********',
      "latlng": const LatLng(21.44484793949768, 53.295109691390245),
    },
    {
      "title": "Al Reef Car Maintenance (3 Vehicles)",
      "totalVehicle": '3',
      "type": "Diesel",
      "scheduledOn": 'Monday, 18 April 2023',
      "location": 'Old Industrial 87, Riyadh, Saudi Arabia',
      "referenceId": '***********',
      "latlng": const LatLng(21.44484793949768, 53.295109691390245),
    },
  ].obs;

  List<String> cityList = [
    'Al-Darb',
    'Deira',
    'Bur Dubai',
    'Jumeirah',
    'Dubai Marina',
    'Downtown Dubai',
    'Palm Jumeirah',
    'Al Barsha',
    'Business Bay',
    'Jumeirah Lakes Towers (JLT)',
    'Dubai Silicon Oasis',
  ];
  RxString selectedCity = 'Al-Darb'.obs;
  var dateController = TextEditingController().obs;
  List<String> timeList = [
    "08:15 AM",
    "08:30 AM",
    "09:15 AM",
    "09:30 AM",
    "10:15 AM",
    "11:15 AM",
    "12:00 AM",
    "12:30 AM",
    "01:30 AM",
    "10:15 PM",
  ];
  RxString selectedTime = '10:15 AM'.obs;
  List<String> platList = [
    "Plat#",
    "Plat#1",
    "Plat#2",
    "Plat#3",
    "Plat#4",
    "Plat#5",
  ];
  RxString selectedPlat = 'Plat#'.obs;
  RxList scheduleDataList = [
    {
      "title": "Al Reef",
      "address": "Old Industrial 87, Riyadh, Saudi Arabia",
      "dateController": TextEditingController().obs,
      'time': [
        "08:15 AM",
        "08:30 AM",
        "09:15 AM",
        "09:30 AM",
        "10:15 AM",
        "11:15 AM",
        "12:00 AM",
        "12:30 AM",
        "01:30 AM",
        "10:15 PM",
      ],
      'plat': [
        "Plat#",
        "Plat#1",
        "Plat#2",
        "Plat#3",
        "Plat#4",
        "Plat#5",
      ],
      "selectedPlat": 'Plat#'.obs,
      "selectedTime": '10:15 AM'.obs,
    },
    {
      "title": "Al Reef",
      "address": "Old Industrial 87, Riyadh, Saudi Arabia",
      "dateController": TextEditingController().obs,
      'time': [
        "08:15 AM",
        "08:30 AM",
        "09:15 AM",
        "09:30 AM",
        "10:15 AM",
        "11:15 AM",
        "12:00 AM",
        "12:30 AM",
        "01:30 AM",
        "10:15 PM",
      ],
      'plat': [
        "Plat#",
        "Plat#1",
        "Plat#2",
        "Plat#3",
        "Plat#4",
        "Plat#5",
      ],
      "selectedPlat": 'Plat#'.obs,
      "selectedTime": '10:15 AM'.obs,
    },
  ].obs;
  List orderList = ['1000001365'];
  RxString selectedOrder = '1000001365'.obs;
  List placeList = ['1000001365'];
  RxString selectedPlace = '1000001365'.obs;
  List centerList = ['1000001365'];
  RxString selectedCenter = '1000001365'.obs;
  List vehicleTypeList = ['1000001365'];
  RxString selectedvehicleType = '1000001365'.obs;
  List tankTypeList = ['1000001365'];
  RxString selectedTankType = '1000001365'.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    dateController.value = TextEditingController();
  }
}
