import 'dart:developer';
import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationServices {
  FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  initFlutterNotificationPlugin(
      BuildContext context, RemoteMessage message) async {
    var androidInitialization =
        const AndroidInitializationSettings("@mipmap/ic_launcher");
    var iosInitialization = const DarwinInitializationSettings();

    var initializeSetting = InitializationSettings(
        android: androidInitialization, iOS: iosInitialization);

    await flutterLocalNotificationsPlugin.initialize(initializeSetting,
        onDidReceiveNotificationResponse: (payload) async {
      log('payload data');
      log(payload.notificationResponseType.toString());
      log(payload.actionId.toString());
      log(payload.id.toString());
      log(payload.input.toString());
      log(payload.payload.toString());

      //handleMessages(message);
    });
  }

  void firebaseInit(BuildContext context) {
    FirebaseMessaging.onMessage.listen((message) {
      RemoteNotification? notification = message.notification;
      AndroidNotification? android = message.notification!.android;

      if (kDebugMode) {
        log("notifications title:${notification!.title}");
        log("notifications body:${notification.body}");
        //log('count:${android!.count}');
        log('data:${message.data.toString()}');
      }

      if (Platform.isIOS) {
        foreGroundMessage();
      }

      if (Platform.isAndroid) {
        initFlutterNotificationPlugin(context, message);
        showNotificationFlutter(message);
      }
    });

    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  }

  Future<void> showNotificationFlutter(RemoteMessage message) async {
    log("Received message data: ${message.data}");

    // Get the sound from the payload (ensure it exists)
    String? sound =
        message.data['sound'] ?? message.notification?.android?.sound;

    if (sound != null && sound != 'default') {
      sound = sound.replaceAll('.mp3', ''); // Ensure no file extension
    } else {
      sound = 'default'; // Use default if no custom sound is provided
    }

    log("Playing sound: $sound");

    // Android Notification Channel
    AndroidNotificationChannel androidNotificationChannel =
        const AndroidNotificationChannel(
      "high_importance_channel",
      "high_importance_channel",
      description: "aldrees-notification",
      importance: Importance.max,
    );

    // Android Notification Details
    AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      androidNotificationChannel.id,
      androidNotificationChannel.name,
      channelDescription: androidNotificationChannel.description,
      importance: Importance.max,
      priority: Priority.high,
      ticker: 'ticker',
      sound: sound != 'default'
          ? RawResourceAndroidNotificationSound(sound)
          : null, // Use custom sound if available
    );

    // iOS Notification Details
    DarwinNotificationDetails iosNotificationDetails =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: sound, // iOS sound
    );

    NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: iosNotificationDetails,
    );

    flutterLocalNotificationsPlugin.show(
      0,
      message.notification!.title.toString(),
      message.notification!.body.toString(),
      notificationDetails,
    );
  }

  requestNotification() async {
    NotificationSettings settings = await firebaseMessaging.requestPermission(
        alert: true,
        announcement: true,
        badge: true,
        sound: true,
        carPlay: true,
        criticalAlert: true,
        provisional: false);
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      log('permission granted');
    }
    // else if (settings.authorizationStatus ==
    //     AuthorizationStatus.provisional) {
    //   log('permission denied');
    //   //AppSettings.openAppSettings();
    // }
  }

  Future<String?> getDeviceToken() async {
    String? deviceToken = await firebaseMessaging.getToken();

    return deviceToken;
  }

  refreshDeviceToken() async {
    firebaseMessaging.onTokenRefresh.listen((event) {
      event.toString();
    });
  }

  Future<void> setupInteractMessage() async {
    // when app is terminated

    RemoteMessage? message =
        await FirebaseMessaging.instance.getInitialMessage();

    if (message != null) {}

    FirebaseMessaging.onMessageOpenedApp.listen((event) {});
  }

  Future foreGroundMessage() async {
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
            alert: true, badge: true, sound: true);
  }

  Future<void> firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    var androidInitialization =
        const AndroidInitializationSettings("@mipmap/ic_launcher");
    var iosInitialization = const DarwinInitializationSettings();

    var initializeSetting = InitializationSettings(
        android: androidInitialization, iOS: iosInitialization);
    await flutterLocalNotificationsPlugin.initialize(initializeSetting);

    String? sound =
        message.data['sound'] ?? message.notification?.android?.sound;

    if (sound != null && sound != 'default') {
      sound = sound.replaceAll('.mp3', '');
    } else {
      sound = 'default';
    }

    AndroidNotificationChannel androidNotificationChannel =
        const AndroidNotificationChannel(
      "high_importance_channel",
      "high_importance_channel",
      description: "aldrees-notification",
      importance: Importance.max,
    );

    // Android Notification Details
    AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      androidNotificationChannel.id,
      androidNotificationChannel.name,
      channelDescription: androidNotificationChannel.description,
      importance: Importance.max,
      priority: Priority.high,
      sound: sound != 'default'
          ? RawResourceAndroidNotificationSound(sound)
          : null,
    );

    // iOS Notification Details
    DarwinNotificationDetails iosNotificationDetails =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: sound,
    );

    NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: iosNotificationDetails,
    );

    // Show the notification
    flutterLocalNotificationsPlugin.show(
      0,
      message.notification!.title.toString(),
      message.notification!.body.toString(),
      notificationDetails,
    );
  }
}
