import 'dart:convert';
import 'dart:core';
import 'package:json_annotation/json_annotation.dart';

//part 'serviceCountModel.g.dart';
// @JsonSerializable()
// class ServiceCountModel {
//    String ActiveTags;
//    String ActiveCards;
//    String TagsToInstall;
//    ServiceCountModel({
//      required this.ActiveTags,
//      required this.ActiveCards,
//      required this.TagsToInstall,
//    });

//    factory ServiceCountModel.fromJson(Map<String, dynamic> json)
//    {
//      return ServiceCountModel(
//          ActiveTags:json["ActiveTags"] as String,
//        ActiveCards:json["ActiveCards"] as String,
//        TagsToInstall:json["TagsToInstall"] as String,
//      );
//    }
// }

class ServiceCountModel {
  String activeTags;
  String activeCards;
  String tagsToInstall;
  ServiceCountModel({
    required this.activeTags,
    required this.activeCards,
    required this.tagsToInstall,
  });

  Map<String, dynamic> toMap() {
    return {
      'ActiveTags': activeTags,
      'ActiveCards': activeCards,
      'TagsToInstall': tagsToInstall,
    };
  }

  factory ServiceCountModel.fromMap(Map<String, dynamic> map) {
    return ServiceCountModel(
      activeTags: map['ActiveTags'] ?? '',
      activeCards: map['ActiveCards'] ?? '',
      tagsToInstall: map['TagsToInstall'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory ServiceCountModel.fromJson(String source) =>
      ServiceCountModel.fromMap(json.decode(source));
}
