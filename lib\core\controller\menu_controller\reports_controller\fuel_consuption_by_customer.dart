import 'dart:convert';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/reports/report_dept.dart';
import 'package:waie_app/models/reports/report_division.dart';
import 'package:waie_app/models/reports/report_places.dart';
import 'package:waie_app/models/reports/report_product.dart';
import 'package:waie_app/models/reports/report_stations.dart';
import 'package:waie_app/models/reports/report_vehicle.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';

import '../../../../models/profile.dart';
import '../../../../models/reports/report_branch.dart';
import '../../../../models/reports/report_driver.dart';
import '../../../../models/reports/report_plates.dart';
import '../../../../models/usemenulist.dart';
import '../../../../utils/api_endpoints.dart';

class FuelConsuptionByCustomerController extends GetxController {
  List<String> sortByList = [
    "Station",
    "Place",
    "Branch",
    "Area",
  ];
  List<String> ServicesList = [
    "All",
    "Card",
    "Digital Coupon",
    "Sticker",
    "Tag",
  ];
  List<String> connectionStatusList = [
    "All",
    "Online",
    "Offline",
  ];
  RxString selectedconnectionStatusList = 'All'.obs;
  RxString selectedServicesList = 'All'.obs;

  bool isCheckedShiftClose = false;
  bool isCheckedGroupBy = false;

  RxString selectedDiv = ''.obs;
  RxString selectedBranch = ''.obs;

  RxString selectedDepartment = ''.obs;
  RxString selectedOperation = ''.obs;
  RxString selectedStn = ''.obs;
  RxString selectedConnection = ''.obs;
  RxString selectedDriver = ''.obs;
  RxString selectedPlateNo = ''.obs;
  RxString selectedVehicle = ''.obs;
  RxString selectedProduct = ''.obs;
  RxString selectedService = ''.obs;

  List vehicleTypeList = [''];
  RxString selectedVehicleType = ''.obs;
  final TextEditingController datePickerController = TextEditingController();
  RxBool isGroupByPlate = false.obs;
  RxBool isShiftClose = false.obs;

  //Fleet Wise Fuel Usage
  final TextEditingController datePickerFleetFromController =
      TextEditingController();
  final TextEditingController datePickerFleetToController =
      TextEditingController();

  //RxList reportProductList = [].obs;
  // RxList reportPlacesList = [].obs;
  List<Report_Division> reportDivList = [];
  // List<Report_Branch> reportBranchList = [];
  final reportBranchList = <Report_Branch>[].obs;
  final reportDeptList = <Report_Dept>[].obs;
  final reportOprList = <Report_Division>[].obs;
  final reportSTNList = <Report_Stations>[].obs;
  final reportDriverList = <Report_Driver>[].obs;
  final reportPlatesList = <Report_Plates>[].obs;
  final reportVehicleList = <Report_Vehicle>[].obs;
  final reportProductList = <Report_Products>[].obs;
  // RxList<String> reportDivList2 = <String>[].obs;
  final reportDivList2 = <Report_Division>[].obs;
  //final provList = <Load_Data_Model>[].obs;
  RxList<String> reportBranchList2 = <String>[].obs;

  //Station Info List

  //Monthly Quota Variance Summary
  final TextEditingController datePickerMonthyController =
      TextEditingController();
  final TextEditingController selectedPlaceController = TextEditingController();
  final TextEditingController selectedProductController =
      TextEditingController();
  final TextEditingController selectedDivController = TextEditingController();
  List placeList = [''];
  RxString selectedPlace = ''.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    loadDivision();
    loadStation();
    loadDriver();
    loadPlateNo();
    loadProducts();
    loadVehicle();
  }

  Future<List<Report_Division>> loadDivision() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadDivision),
          body: {"CUSTID": userid, "EMAILID": userData.auCust!.emailid!});
      // body: {"CUSTID": "000000054A","EMAILID": "<EMAIL>"});
      print("Report Division List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Division count =
            Report_Division.fromMap(result[i] as Map<String, dynamic>);
        reportDivList.add(count);
        reportDivList2.add(count);
      }
      //reportDivList2.value = reportDivList.map((item) => item.TYPEDESC).toList();
      //reportDivList2.value=reportDivList
      print(" reportDivList2.value===> $reportDivList2");

      return reportDivList;
    } catch (e) {
      log("Report Places List Error $e");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<List<Report_Branch>> loadBranch(String parent) async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadBranch),
          body: {
            "CUSTID": userid,
            "EMAILID": userData.auCust!.emailid!,
            "PARENT": parent
          });
      // body: {"CUSTID": "000000054A","EMAILID": "<EMAIL>","PARENT":"'"+parent+"'"});
      print("Report loadBranch List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Branch count =
            Report_Branch.fromMap(result[i] as Map<String, dynamic>);
        reportBranchList.add(count);
      }
      //reportDivList2.value = reportDivList.map((item) => item.TYPEDESC).toList();
      //reportDivList2.value=reportDivList
      print(" reportDivList2.value===> $reportDivList2");

      return reportBranchList;
    } catch (e) {
      log("Report Places List Error $e");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void loadDept(String parent) async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.reportLoadDept),
          body: {"CUSTID": userid, "PARENT": parent});
      //  body: {"CUSTID": "000000054A","PARENT":parent});
      print("Report Division List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Dept count =
            Report_Dept.fromMap(result[i] as Map<String, dynamic>);
        reportDeptList.add(count);
      }
      //reportDivList2.value = reportDivList.map((item) => item.TYPEDESC).toList();
      //reportDivList2.value=reportDivList
      print(" reportDeptList.value===> $reportDeptList");

      //return "";
    } catch (e) {
      log("Report Places List Error $e");
      //return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void loadOperation(String parent) async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadOperation),
          body: {"CUSTID": userid, "PARENT": parent});
      // body: {"CUSTID": "000000054A","PARENT":parent});
      print("Report Division List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Division count =
            Report_Division.fromMap(result[i] as Map<String, dynamic>);
        reportOprList.add(count);
      }
      //reportDivList2.value = reportDivList.map((item) => item.TYPEDESC).toList();
      //reportDivList2.value=reportDivList
      print(" reportDeptList.value===> $reportOprList");

      //return "";
    } catch (e) {
      log("Report Places List Error $e");
      //return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void loadStation() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadStation),
          body: {"CUSTID": userid});
      //body: {"CUSTID": userid,"PARENT":parent});
      //  body: {"CUSTID": "000000054A","PARENT":parent});
      print("Report Division List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Stations count =
            Report_Stations.fromMap(result[i] as Map<String, dynamic>);
        reportSTNList.add(count);
      }
      //reportDivList2.value = reportDivList.map((item) => item.TYPEDESC).toList();
      //reportDivList2.value=reportDivList
      print(" reportDeptList.value===> $reportOprList");

      //return "";
    } catch (e) {
      log("Report Places List Error $e");
      //return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void loadDriver() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadDriver),
          body: {"CUSTID": userid});
      // body: {"CUSTID": "000000054A","PARENT":parent});
      print("Report Division List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Driver count =
            Report_Driver.fromMap(result[i] as Map<String, dynamic>);
        reportDriverList.add(count);
      }
      //reportDivList2.value = reportDivList.map((item) => item.TYPEDESC).toList();
      //reportDivList2.value=reportDivList
      print(" reportDeptList.value===> $reportOprList");

      //return "";
    } catch (e) {
      log("Report Places List Error $e");
      //return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void loadPlateNo() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadPlates),
          body: {
            "CUSTID": userid,
          });
      // body: {"CUSTID": "000000054A","PARENT":parent});
      print("Report Division List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Plates count =
            Report_Plates.fromMap(result[i] as Map<String, dynamic>);
        reportPlatesList.add(count);
      }
      print(" reportDeptList.value===> $reportPlatesList");

      //return "";
    } catch (e) {
      log("Report Places List Error $e");
      //return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void loadVehicle() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadVehicle),
          body: {"CUSTID": userid});
      // body: {"CUSTID": "000000054A","PARENT":parent});
      print("Report Division List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Vehicle count =
            Report_Vehicle.fromMap(result[i] as Map<String, dynamic>);
        reportVehicleList.add(count);
      }
      //reportDivList2.value = reportDivList.map((item) => item.TYPEDESC).toList();
      //reportDivList2.value=reportDivList
      print(" reportDeptList.value===> $reportVehicleList");

      //return "";
    } catch (e) {
      log("Report Places List Error $e");
      //return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void loadProducts() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadProducts),
          body: {"CUSTID": userid});
      // body: {"CUSTID": "000000054A","PARENT":parent});
      print("Report reportProductList ============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Products count =
            Report_Products.fromMap(result[i] as Map<String, dynamic>);
        reportProductList.add(count);
      }
      //reportDivList2.value = reportDivList.map((item) => item.TYPEDESC).toList();
      //reportDivList2.value=reportDivList
      print(" reportDeptList.value===> $reportOprList");

      //return "";
    } catch (e) {
      log("Report Places List Error $e");
      //return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  void loadServices(String parent) async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportLoadDriver),
          body: {"CUSTID": userid, "PARENT": parent});
      //  body: {"CUSTID": "000000054A","PARENT":parent});
      print("Report Division List============${response.body}");
      List result = jsonDecode(response.body);
      for (int i = 0; i < result.length; i++) {
        Report_Driver count =
            Report_Driver.fromMap(result[i] as Map<String, dynamic>);
        reportDriverList.add(count);
      }
      //reportDivList2.value = reportDivList.map((item) => item.TYPEDESC).toList();
      //reportDivList2.value=reportDivList
      print(" reportDeptList.value===> $reportOprList");

      //return "";
    } catch (e) {
      log("Report Places List Error $e");
      //return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  reportRequestSubmit() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    String sorted = "";

    String servisetypecode = "";
    if (selectedService.value == "All") {
      servisetypecode = "A";
    } else if (selectedService.value == "Card") {
      servisetypecode = "C";
    } else if (selectedService.value == "Sticker") {
      servisetypecode = "DC";
    } else if (selectedService.value == "Tag") {
      servisetypecode = "T";
    } else if (selectedService.value == "Digital Coupon") {
      servisetypecode = "ST";
    }

    String con = "A";

    if (selectedService.value == "Online") {
      con = "0";
    } else if (selectedService.value == "Offline") {
      con = "1";
    } else {
      con = "2";
    }

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map dataBody = {};
    dataBody = {
      "CUSTID": userid,
      "PeriodFrom": "01/${datePickerFleetFromController.text}",
      "GroupByPlate": isGroupByPlate.value,
      "ShiftClose": isShiftClose.value,
      "ConnectionStatus": con,
      "LIST_PLATENO": selectedPlateNo.value,
      "LIST_DIVISION": selectedDiv.value,
      "LIST_BRANCH": selectedBranch.value,
      "LIST_DEPARTMENT": selectedDepartment.value,
      "LIST_STATION": selectedStn.value,
      "LIST_DRIVER": selectedDriver.value,
      "LIST_FUELTYPE": selectedProduct.value,
      "LIST_SERVICETYPE": servisetypecode,
      //"LIST_SERVICETYPE":selectedService.value,
      "LIST_VEHICLETYPE": selectedVehicle.value,
      "REPORTTYPE": "FuelConsumptionByCustomer"
    };

    print("Json Value==================$dataBody");
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.reportReqSubmit),
          //  body: jsonEncode(dataBody));
          //body: dataBody,
          body: jsonEncode(dataBody),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          });
      print("REPORT RESPONSE============${response.body}");
      //List result = jsonDecode(response.body);
      commonToast(response.body);
      Get.back();
      return "";
    } catch (e) {
      log(e.toString());
      commonToast(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
