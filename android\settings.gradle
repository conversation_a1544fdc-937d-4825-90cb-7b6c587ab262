// include ':app'

// def localPropertiesFile = new File(rootProject.projectDir, "local.properties")
// def properties = new Properties()

// assert localPropertiesFile.exists()
// localPropertiesFile.withReader("UTF-8") { reader -> properties.load(reader) }

// def flutterSdkPath = properties.getProperty("flutter.sdk")
// assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
// apply from: "$flutterSdkPath/packages/flutter_tools/gradle/app_plugin_loader.gradle"
pluginManagement {
    includeBuild("D:/RMV2025/flutter/flutter/packages/flutter_tools/gradle")

    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }

    plugins {
        id "com.android.application" version "8.0.2"
        id "org.jetbrains.kotlin.android" version "1.9.10"
        id "dev.flutter.flutter-gradle-plugin" version "1.0.0"
        id "com.google.gms.google-services" version "4.3.15"
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        jcenter()
    }
}

rootProject.name = "waie_mobile_flutter"
include(":app")

