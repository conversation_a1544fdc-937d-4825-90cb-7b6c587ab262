import 'dart:convert';

List<SalesStationModel> salesStationModelFromJson(String str) =>
    List<SalesStationModel>.from(
        json.decode(str).map((x) => SalesStationModel.fromJson(x)));

String salesStationModelToJson(List<SalesStationModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SalesStationModel {
  String offCode;
  String officeDesc;
  String placeDesc;
  String parentid;
  String offCoor;
  String latitude;
  String longitude;
  String offStatus;

  SalesStationModel({
    required this.offCode,
    required this.officeDesc,
    required this.placeDesc,
    required this.parentid,
    required this.offCoor,
    required this.latitude,
    required this.longitude,
    required this.offStatus,
  });

  factory SalesStationModel.fromJson(Map<String, dynamic> json) =>
      SalesStationModel(
        offCode: json["OFF_CODE"] ?? "",
        officeDesc: json["OFFICE_DESC"] ?? "",
        placeDesc: json["PLACE_DESC"] ?? "",
        parentid: json["PARENTID"] ?? "",
        offCoor: json["OFF_COOR"] ?? "",
        latitude: json["LATITUDE"] ?? "0.0",
        longitude: json["LONGITUDE"] ?? "0.0",
        offStatus: json["OFF_STATUS"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "OFF_CODE": offCode,
        "OFFICE_DESC": officeDesc,
        "PLACE_DESC": placeDesc,
        "PARENTID": parentid,
        "OFF_COOR": offCoor,
        "LATITUDE": latitude,
        "LONGITUDE": longitude,
        "OFF_STATUS": offStatus,
      };
}
