import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/News.dart';
import 'package:waie_app/models/stationslides.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/regDB.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/add_pure_dc_vehicle_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../models/profile.dart';
import '../../../utils/api_endpoints.dart';

class HomeController extends GetxController {
  TextEditingController checkPasswordController = TextEditingController();
  GetStorage custsData = GetStorage('custsData');
  RegisterDatabase db = RegisterDatabase();
  RxBool isCheckPass = true.obs;
/*  RxList stationList = [
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
  ].obs;*/

  RxList stationList = [
    /* {'image': DefaultImages.image3, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image4, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image5, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image6, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image7, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image8, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image9, 'title': 'Aldrees new gas stations'},*/

    {'image': DefaultImages.image3, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image4, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image5, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image6, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image7, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image8, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image9, 'title': 'Aldrees new gas stations'},
  ].obs;

  /*RxList stationList = [
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
    {'image': DefaultImages.image, 'title': 'Aldrees new gas stations'},
  ].obs;*/
  RxInt stationIndex = 0.obs;
  var aldreesNewsList = <News>[].obs;
  RxInt profileStep = 1.obs;
  RxBool isShowProfile = true.obs;
  List profileStepList = [
    {
      "title": "Tell us about your company",
      "sub_title":
          "For legal reasons, we have to collect some basic details about your company. You'll need to do this before you can use the WAIE platform.",
      'btn_name': "My account",
      "page": DashBoardManagerScreen(currantIndex: 4),
    },
    {
      "title": "Check the Top Up balance",
      "sub_title":
          "With topup balance, you can pay for all purchases in our system: tags, smart cards, premium subscription, etc.",
      'btn_name': "Balance Topup",
      "page": DashBoardManagerScreen(currantIndex: 1),
    },
    {
      "title": "Order tags",
      "sub_title":
          "Order the required number of automatic fueling tags to install in your fleet",
      'btn_name': "Tag order",
      "page": DashBoardManagerScreen(currantIndex: 2),
    },
    {
      "title": "Add vehicles to fleet",
      "sub_title":
          "Start equipping your company's fleet of cars. Add vehicles and fill information about each vehicle and its driver.",
      'btn_name': "Add car to fleet",
      "page": AddPureDCVehicleScreen(
        title: 'Add vehicles',
      ),
    },
    {
      "title": "Schedule tag installation",
      "sub_title": "Book a time and place to tag your vehicles",
      'btn_name': "Book tag installation",
      "page": DashBoardManagerScreen(currantIndex: 1),
    },
  ];

  void updateStepsStatus() async {
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    sharedUser.setBool("isNewUser", true);
    isShowProfile.value = false;
  }

  Future<List<News>> fetchNews() async {
    var client = http.Client();
    List<News> news = [];
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getNews),
          body: {});

      print("News Result==============${response.body}");
      List result = jsonDecode(response.body);
      aldreesNewsList.clear();
      aldreesNewsList.refresh();
      for (int i = 0; i < result.length; i++) {
        News newsList = News.fromMap(result[i] as Map<String, dynamic>);
        await Future.delayed(
            const Duration(seconds: 0), () => aldreesNewsList.add(newsList));
      }

      return aldreesNewsList;
    } catch (e) {
      log("Home_Controller $e");
      return [];
    }
  }

  UpdateOnboardingSteps() async {
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.loginMobileUsers),
          body: {"userid": userData.auCust?.sysuserid, "steps": "STEP6"});
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  checkUserPassword() async {
    //Loader.showLoader();
    print("checkUserPassword");
    var client = http.Client();
    var custData = custsData.read('custData');
    var jsonCustData = jsonEncode(custsData.read('custData'));

    print("custid>>>>>>> ${custData['CUSTID']}");
    print(
        "checkPasswordController.text>>>>>>> ${checkPasswordController.text}");
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.checkPassword),
          body: {
            "currentpass": checkPasswordController.text,
            "custdata": jsonCustData,
          });
      print("===============================================================");
      print("jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      String message = "Credentials are created Successfully";
      checkPasswordController.clear();
      if (jsonDecode(response.body)["MessageType"] == "success") {
        Get.back();
        //isAuth.value = active;
        String message = "Credentials are created Successfully";
        String msg = jsonDecode(response.body)["message"];
        checkPasswordController.clear();
        Loader.hideLoader();
        print(
            "===============================================================");
        //db.createUserInfoData("viajedorryan", "V1ajedor", "Y");
        db.createUserInfoData(jsonDecode(response.body)["username"],
            jsonDecode(response.body)["password"], "Y");
        print(
            "===============================================================");
        db.loadData();
        print(
            "===============================================================");
        db.createActivate("Y");
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Activate Successful'.tr,
                    style: pBold20,
                  ),
                  verticalSpace(24),
                  // Text(
                  //   message.toString(),
                  //   style: pRegular16,
                  //   textAlign: TextAlign.center,
                  // ),
                  // verticalSpace(24),
                  CommonButton(
                    title: 'CONTINUE'.tr,
                    onPressed: () {
                      Get.back();
                      //Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        Loader.hideLoader();
        //isAuth.value = false;
        commonToast(jsonDecode(response.body)["message"]);
        Get.back();
      }
    } catch (e) {
      log("authentication error: $e");
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  final PageController outerPageController = PageController();
  PageController? innerPageController;

  RxInt currentCardIndex = 0.obs;

  void scrollLeft(int cardsLength) {
    if (currentCardIndex.value > 0) {
      currentCardIndex.value--;
      innerPageController!.animateToPage(
        currentCardIndex.value,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void scrollRight(int cardsLength) {
    if (currentCardIndex.value < cardsLength - 1) {
      currentCardIndex.value++;
      innerPageController!.animateToPage(
        currentCardIndex.value,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
}
