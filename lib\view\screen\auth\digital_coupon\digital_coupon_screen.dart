// ignore_for_file: prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/digital_coupon/confirm_dialog.dart';
import 'package:waie_app/view/screen/auth/digital_coupon/create_dc_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../utils/constants.dart';
import '../login_with_email_screen.dart';

class DigitalCouponScreen extends StatelessWidget {
  final bool isBack;

  const DigitalCouponScreen({super.key, required this.isBack});

  @override
  Widget build(BuildContext context) {
    print('===========$isBack===');

    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Digital Coupon".trr,
                    style: pBold20,
                  ),
                  CommonBorderButton(
                    title: 'New coupon'.trr,
                    onPressed: () {
                      if (isBack == true) {
                        Get.off(() => CreateDCScreen(
                              isBack: isBack,
                            ));
                      } else {
                        Get.back();
                      }
                    },
                    height: 32,
                    width: 120,
                  )
                ],
              ),
              verticalSpace(38),
              Container(
                decoration: BoxDecoration(
                    border: Border.all(
                      color: AppColor.cBorder,
                    ),
                    borderRadius: BorderRadius.circular(12)),
                padding: EdgeInsets.symmetric(vertical: 22, horizontal: 44),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "User ID".trr,
                          style: pRegular17,
                        ),
                        Text(
                          //"1-222-A23523",
                          Constants.driver[0].custid,
                          style: pSemiBold16.copyWith(fontSize: 17),
                        )
                      ],
                    ),
                    verticalSpace(16),
                    GestureDetector(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return AlertDialog(
                              content: ConfirmDialogWidget(isBack: isBack),
                              contentPadding: EdgeInsets.zero,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                            );
                          },
                        );
                      },
                      child: CustomPaint(
                        size: Size.square(224),
                        painter: QrPainter(
                         // data: "qrString",
                          data: Constants.QRCodeString,
                          version: QrVersions.auto,
                          eyeStyle: QrEyeStyle(
                            eyeShape: QrEyeShape.square,
                            color: AppColor.cBlackFont,
                          ),
                          dataModuleStyle: QrDataModuleStyle(
                            dataModuleShape: QrDataModuleShape.square,
                            color: AppColor.cBlackFont,
                          ),
                          gapless: true,
                        ),
                      ),
                    ),
                    verticalSpace(32),
                    Text(
                      "717022",
                      style: pSemiBold16.copyWith(fontSize: 17),
                    ),
                  ],
                ),
              ),
              verticalSpace(24),
              Text(
                "Digital Coupon Note".trr+" :",
                style: pSemiBold16.copyWith(fontSize: 17),
              ),
              verticalSpace(16),
              Text(
                  "• "+"Use this digital coupon to refill your vehicle at participating gas stations. You can see a list of participating gas stations here. (link 'here' to the list)".trr,
                  style: pRegular13),
              verticalSpace(4),
              Text(
                  "• "+"You can only have one digital coupon at a time. Use or delete this one to create another.".trr,
                  style: pRegular13),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(left: 24, right: 24, bottom: 15),
        child: CommonBorderButton(
            title: isBack ? "Back to Menu".trr : "Back to Login".trr,
            onPressed: () {
             // isBack ? Get.back() : Get.offAll(() => LoginManagerScreen());
              isBack ? Get.back() : Get.offAll(() => LoginWithEmailScreen());
            },
            bColor: AppColor.themeDarkBlueColor),
      ),
    );
  }
}
