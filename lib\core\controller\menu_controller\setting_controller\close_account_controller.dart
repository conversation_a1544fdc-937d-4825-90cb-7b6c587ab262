// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:file_picker/src/file_picker_result.dart';
import 'package:http_parser/http_parser.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/menu_controller/setting_controller/privacy_security_controller.dart';
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/models/loadplaces.dart';
import 'package:waie_app/models/profile.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/locale_string.dart';
import 'package:waie_app/utils/prefer.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:mime/mime.dart';
import 'package:waie_app/view/screen/auth/login_with_email_screen.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/final_view_vehicle_details_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:quickalert/quickalert.dart';

import '../../../../models/load_data.dart';
import '../../../../models/newfleet.dart';
import '../../../../utils/constants.dart';
import '../../../../view/screen/vehicles_screen/my_fleet/final_edit_vehicle_details_screen.dart';
import '../../../../view/widget/loading_widget.dart';

class CloseAccountController extends GetxController {
  PrivacySecurityController privacySecurityController =
      Get.put(PrivacySecurityController());
  final vehicle = GetStorage();
  GetStorage custsData = GetStorage('custsData');
  GetStorage usersData = GetStorage('usersData');
  GetStorage userStorage = GetStorage('User');
  List<LoadPlaces> loadPlaces = [];
  RxList placeList = [].obs;
  RxBool chckbox = false.obs;
  final closeAccntList = <Load_Data_Model>[].obs;
  final bankList = <Load_Data_Model>[].obs;
  List<Load_Data_Model> closeAccntModelList = [];
  List<Load_Data_Model> bankModelList = [];

  RxString companyReg = ''.obs;
  RxString bankLetter = ''.obs;
  RxString nationalid = ''.obs;

  RxString email = "".obs;
  RxString password = "".obs;

  TextEditingController reasonLeaveController = TextEditingController();
  TextEditingController accountHolderNameController = TextEditingController();
  TextEditingController bankController = TextEditingController();
  TextEditingController iBanNoController = TextEditingController();
  TextEditingController remarksController = TextEditingController();

  loadVehicleDetails() {}

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    getCloseAccountDetails();
  }

  getCloseAccountDetails() async {
    var client = http.Client();
    List<ServiceObj> details = [];
    var custData = custsData.read('custData');
    print("custid>>>>>>> $custData['CUSTID']");
    print("ViewVehicleDetailsController>>>>>>> getVehicleDetails");
    //print("emailid>>>>>>> $custData['EMAILID']");
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");

    try {
      var closeAccntResponse = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
        body: {
          "IsAR": Constants.IsAr_App,
          "TypeId": "ACCTCLOS",
          //"CustId": custData["CUSTID"],
        },
      );

      var bankResponse = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
        body: {
          "IsAR": Constants.IsAr_App,
          "TypeId": "WAIEBANK",
          //"CustId": custData["CUSTID"],
        },
      );

      print("closeAccntResponse===> ${jsonDecode(closeAccntResponse.body)}");

      List closeAccntResult = jsonDecode(closeAccntResponse.body);

      for (int i = 0; i < closeAccntResult.length; i++) {
        Load_Data_Model loadData = Load_Data_Model.fromMap(
            closeAccntResult[i] as Map<String, dynamic>);
        closeAccntModelList.add(loadData);
        print("closeAccntResult ===============${loadData.TYPEDESC}");
      }

      closeAccntList.value = closeAccntModelList;

      print("bankResponse===> ${jsonDecode(bankResponse.body)}");

      List bankResult = jsonDecode(bankResponse.body);

      for (int i = 0; i < bankResult.length; i++) {
        Load_Data_Model loadData =
            Load_Data_Model.fromMap(bankResult[i] as Map<String, dynamic>);
        bankModelList.add(loadData);
        print("closeAccntResult ===============${loadData.TYPEDESC}");
      }

      bankList.value = bankModelList;

      if (closeAccntResponse.statusCode == 200) {
        // await Get.to(() => const FinalEditVehicleDetailsScreen());
        return closeAccntList;
      } else {
        print('Failed');
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Failed to Load Data",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  submitCloseAccountRequest(bankLetter, docFile) async {
    Loader.showLoader();
    var custid = userStorage.read('custid');
    print("submitFile bankLetter $bankLetter");
    print("submitFile bankLetter ${bankLetter.name}");
    print("submitFile bankLetter ${bankLetter.readStream}");
    print("submitFile docFile $docFile");
    print("submitFile docFile ${docFile.name}");
    print("submitFile docFile ${docFile.readStream}");
    final file = bankLetter;
    final mimeType = lookupMimeType(file.name) ?? '';
    var client = http.Client();
    try {
      var bankletter = bankLetter.readStream;
      var docfile = docFile.readStream;

      print("bankletter $bankletter");
      print("docfile $docfile");

      var request = http.MultipartRequest(
          'POST',
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.submitCloseAccountRequest));
      var headers = {'Content-Type': 'application/pdf; charset=UTF-8'};

      request.files.add(http.MultipartFile(
          'bankletter', bankletter!, bankLetter.size,
          filename: bankLetter.name,
          contentType: MediaType('application', 'pdf')));

      bankletter.cast();

      request.files.add(http.MultipartFile('docFile', docfile!, docFile.size,
          filename: docFile.name,
          contentType: MediaType('application', 'pdf')));
      docfile.cast();
      request.headers.addAll(headers);
      request.fields['custid'] = custid;
      request.fields['regtype'] = Constants.custRegType;
      request.fields['IsAR'] = 'false';
      request.fields['reason'] = reasonLeaveController.text;
      request.fields['remarks'] = remarksController.text;
      request.fields['bank'] = bankController.text;
      request.fields['iban'] = iBanNoController.text;
      request.fields['accountHolder'] = accountHolderNameController.text;
      var res = await request.send();

      return res.stream.bytesToString().asStream().listen((event) async {
        var parsedJson = json.decode(event);
        print(
            "===============================================================");
        print(parsedJson['Message']);
        print(json.decode(event));
        // print(parsedJson['response']['Action']);
        // print(parsedJson['response']['Message']);
        print(
            "===============================================================");
        if (parsedJson['MessageType'] == "error") {
          Loader.hideLoader();

          await QuickAlert.show(
            context: Get.context!,
            type: QuickAlertType.error,
            text: parsedJson['Message'],
          );
          Get.back();
          // Get.offAll(
          //   () => DashBoardManagerScreen(
          //     currantIndex: 0,
          //   ),
          //   //preventDuplicates: false,
          // );
        }
        if (parsedJson['MessageType'] == "success") {
          Loader.hideLoader();

          Prefs.clear();
          vehicle.erase();
          userStorage.erase();
          custsData.erase();
          usersData.erase();
          privacySecurityController.login(
              parsedJson["username"], parsedJson["password"]);

          // await QuickAlert.show(
          //   context: Get.context!,
          //   type: QuickAlertType.success,
          //   text: parsedJson['Message'],
          // );
          // Get.offAll(
          //   () => DashBoardManagerScreen(
          //     currantIndex: 0,
          //   ),
          //   //preventDuplicates: false,
          // );
        }
      });
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
