// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/user_management_controller/user_management_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';

class InviteUserWidget extends StatelessWidget {
  final String email;

  InviteUserWidget({super.key, required this.email});

  UserManagementController userManagementController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: EdgeInsets.all(16),
        child: Obx(
          () {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Text(
                    "Invite user",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                ),
                verticalSpace(20),
                CommonTextField(
                  controller: userManagementController.emailController..text = email,
                  hintText: 'Email'.trr,
                  labelText: 'Email ID'.trr,
                ),
                verticalSpace(24),
                Text(
                  "User role".trr,
                  style: pRegular12.copyWith(fontSize: 11),
                ),
                verticalSpace(4),
                Row(
                  children: userManagementController.userRoleList.value
                      .map((e) => Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: GestureDetector(
                              onTap: () {
                                userManagementController.selectedUserRole.value=e;
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(vertical: 13, horizontal: 14),
                                decoration: BoxDecoration(borderRadius: BorderRadius.circular(4), color:userManagementController.selectedUserRole.value==e?AppColor.themeOrangeColor: AppColor.cLightGrey),
                                child: Text(e.toString().trr, style: pRegular13.copyWith(color: userManagementController.selectedUserRole.value==e?AppColor.cWhiteFont:AppColor.cText)),
                              ),
                            ),
                          ))
                      .toList(),
                ),
                verticalSpace(40),
                Row(
                  children: [
                    Expanded(
                        child: CommonBorderButton(
                          title: 'Cancel'.trr,
                          onPressed: () {
                            Get.back();
                          },
                          bColor: AppColor.themeDarkBlueColor,
                          textColor: AppColor.cDarkBlueFont,
                        )),
                    horizontalSpace(8),
                    Expanded(
                        child: CommonButton(
                          title: 'Confirm'.trr,
                          onPressed: () {
                            Get.back();
                          },
                          btnColor: AppColor.themeOrangeColor,
                        ))
                  ],
                ),
                verticalSpace(16),
              ],
            );
          }
        ),
      ),
    );
  }
}
