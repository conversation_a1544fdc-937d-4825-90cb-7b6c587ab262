// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/order_current_topup.dart';
import 'package:waie_app/models/refundable_service.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../../view/widget/common_snak_bar_widget.dart';
import '../../../../view/widget/loading_widget.dart';

class OrderRefundCurrentController extends GetxController {
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  var refundableCurrentTopups = <OrderCurrentTopupModel>[].obs;

  double topupTotaltAmt = 0.0;
  double topupTotaltVAT = 0.0;
  getRefundablesTopUp() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = jsonEncode(custsData.read('custData'));
    print("OrderRefundCurrentController custid>>>>>>> $custid");
    print("OrderRefundCurrentController emailid>>>>>>> $emailid");
    var client = http.Client();
    try {
      if (refundableCurrentTopups.isEmpty) {
        var response = await client.post(
            Uri.parse(ApiEndPoints.baseUrl +
                ApiEndPoints.authEndpoints.getRefundablesTopUp),
            body: {
              "custdata": custData,
              "chkRes": "false",
              // "orderId": "",
              // "rblRefundOptions": "",

            });

        log("url ${ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getRefundablesTopUp}");

        log("custData $custData");
        log("chkRes false");
        log("orderId ");
        log("rblRefundOptions ");

        var result = jsonDecode(response.body);
        log("responseBody ${response.body}");
        log("responseStatusCode ${response.statusCode}");

        print("OrderRefundCurrentController result >>>>> $result");
        print("OrderRefundCurrentController COUNT >>>>> ${result.length}");
        print(
            "===============================================================");
        print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");

        print(
            "===============================================================");

        if (result.isEmpty) {
          commonToast("No Data Found, Please Check Internet Connection.");
        } else {
          for (int i = 0; i < result.length; i++) {
            OrderCurrentTopupModel order = OrderCurrentTopupModel.fromJson(
                result[i] as Map<String, dynamic>);
            refundableCurrentTopups.add(order);
          }
        }
        print(
            "===============================================================");
        print(
            "OrderRefundCurrentController >>>>> ${jsonDecode(jsonEncode(refundableCurrentTopups))}");
        print(
            "===============================================================");

        return refundableCurrentTopups;
      }
      print("ERROR: NO DATA");
      return [];
    } catch (e) {
      log(e.toString());
      print("ERROR: ${e.toString()}");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() async {
    super.onInit();
    print('OrderRefundCurrentController');
    print(jsonDecode(jsonEncode(refundableCurrentTopups)));
    if (refundableCurrentTopups.isEmpty) {
      print("sulod");
      await getRefundablesTopUp();
    }
    //Navigator.of(Get.context!).pop();
  }
}
