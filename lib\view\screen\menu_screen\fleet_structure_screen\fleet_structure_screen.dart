// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_structure_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/fleet_structure_screen/delete_division_widget.dart';
import 'package:waie_app/view/screen/menu_screen/fleet_structure_screen/edit_branch_widget.dart';
import 'package:waie_app/view/screen/menu_screen/fleet_structure_screen/group_process_add_widget.dart';
import 'package:waie_app/view/screen/menu_screen/fleet_structure_screen/group_process_delete_widget.dart';
import 'package:waie_app/view/screen/menu_screen/fleet_structure_screen/group_process_edit_widget.dart';
import 'package:waie_app/view/screen/menu_screen/fleet_structure_screen/group_process_hold_widget.dart';
import 'package:waie_app/view/screen/menu_screen/menu_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../widget/common_appbar_widget.dart';
import '../../dashboard_manager/dashboard_manager.dart';
import 'add_division_widget.dart';
import 'no_fleet_structure_screen.dart';
import 'search_fleet_widget.dart';

class FleetStructureScreen extends StatefulWidget {
  const FleetStructureScreen({super.key});

  @override
  State<FleetStructureScreen> createState() => _FleetStructureScreenState();
}

class _FleetStructureScreenState extends State<FleetStructureScreen> {
  FleetStructureController fleetStructureController =
      Get.put(FleetStructureController());

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Obx(() {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildAppTitleWidget(searchTap: () {
                  showModalBottomSheet(
                    context: context,
                    barrierColor: AppColor.cBlackOpacity,
                    shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(12))),
                    isScrollControlled: true,
                    builder: (context) {
                      return SearchFleetStructureWidget();
                    },
                  );
                }),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: CommonIconButton(
                                  iconData: DefaultImages.addDivisionIcn,
                                  title: 'Add Division'.trr,
                                  onPressed: () {
                                    showModalBottomSheet(
                                      context: context,
                                      shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(16))),
                                      backgroundColor: AppColor.cBackGround,
                                      barrierColor: AppColor.cBlackOpacity,
                                      isScrollControlled: true,
                                      builder: (context) {
                                        return GroupProcessAddWidget(
                                          name: "Division",
                                          typeid: "CUSTDIVISION",
                                          parentid: "NA",
                                        );
                                      },
                                    );
                                  },
                                  btnColor: AppColor.themeOrangeColor,
                                ),
                              ),
                              // horizontalSpace(10),
                              // Expanded(
                              //   child: GestureDetector(
                              //       onTap: () {
                              //         showModalBottomSheet(
                              //           context: context,
                              //           shape: RoundedRectangleBorder(
                              //               borderRadius:
                              //                   BorderRadius.vertical(
                              //                       top: Radius.circular(
                              //                           16))),
                              //           backgroundColor:
                              //               AppColor.cBackGround,
                              //           barrierColor:
                              //               AppColor.cBlackOpacity,
                              //           isScrollControlled: true,
                              //           builder: (context) {
                              //             return AddSubElementsWidget();
                              //           },
                              //         );
                              //       },
                              //       child: addSUbElementWidget()),
                              // ),
                            ],
                          ),
                          verticalSpace(24),
                          fleetStructureController.divisionList.isEmpty
                              ? const Center(child: CircularProgressIndicator())
                              : Container(
                                  child: ListView.builder(
                                    itemCount: fleetStructureController
                                        .divisionList.length,
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      var data = fleetStructureController
                                          .divisionList[index];
                                      return Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 8.0),
                                        child: Container(
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  color: AppColor
                                                      .cLightBlueContainer),
                                              borderRadius:
                                                  BorderRadius.circular(4)),
                                          child: Column(
                                            children: [
                                              fleetStructureDataWidget(
                                                  context: context,
                                                  title: data.typedesc,
                                                  status: data.ishold == "Y"
                                                      ? "HOLD"
                                                      : "ACTIVE",
                                                  // dropDownIcn:
                                                  //     data.isSelected.value ==
                                                  //             true
                                                  //         ? DefaultImages
                                                  //             .blackArrowDownIcn
                                                  //         : DefaultImages
                                                  //             .arrowRightIcn,
                                                  divisionImage: DefaultImages
                                                      .addDivisionIcn,
                                                  divisionTitle: "Division",
                                                  division: data.typecode,
                                                  // onTap: () {
                                                  //   data.isSelected.value =
                                                  //       !data.isSelected.value;
                                                  //   setState(() {
                                                  //     Get.to(() =>
                                                  //         BranchFleetStructureScreen(
                                                  //           parentID:
                                                  //               data.typecode,
                                                  //         ));
                                                  //   });
                                                  //   // data['branch']['isBranch'].value = false;
                                                  //   // data['department']['isDepartment'].value = false;
                                                  // },
                                                  tooltipItemList: [
                                                    "View Branch".trr,
                                                    "Edit Division".trr,
                                                    "Delete Division".trr,
                                                    data.ishold == "Y"
                                                        ? "UnHold Division".trr
                                                        : "Hold Division".trr,
                                                  ],
                                                  moreOnTap: (String value) {
                                                    print(
                                                        'You Click on po up menu item $value');
                                                    if (value ==
                                                        'edit division'.trr) {
                                                      showModalBottomSheet(
                                                        context: context,
                                                        shape: RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius.vertical(
                                                                    top: Radius
                                                                        .circular(
                                                                            16))),
                                                        backgroundColor:
                                                            AppColor
                                                                .cBackGround,
                                                        barrierColor: AppColor
                                                            .cBlackOpacity,
                                                        isScrollControlled:
                                                            true,
                                                        builder: (context) {
                                                          return GroupProcessEditWidget(
                                                            name: "Division",
                                                            typecode:
                                                                data.typecode,
                                                            typeid:
                                                                "CUSTDIVISION",
                                                            typedesc:
                                                                data.typedesc,
                                                            parentid: "NA",
                                                          );
                                                        },
                                                      );
                                                    } else if (value ==
                                                        'view branch'.trr) {
                                                      setState(() {
                                                        fleetStructureController
                                                            .loadBranch(
                                                                data.typecode);
                                                      });
                                                    } else if (value ==
                                                        'delete division'.trr) {
                                                      showDialog(
                                                        context: context,
                                                        builder: (context) {
                                                          return AlertDialog(
                                                            contentPadding:
                                                                EdgeInsets.all(
                                                                    16),
                                                            insetPadding:
                                                                EdgeInsets.all(
                                                                    15),
                                                            shape: RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12)),
                                                            content:
                                                                GroupProcessDeleteWidget(
                                                              name: "Division",
                                                              typecode:
                                                                  data.typecode,
                                                              typeid:
                                                                  "CUSTDIVISION",
                                                              typedesc:
                                                                  data.typedesc,
                                                              parentid: "NA",
                                                            ),
                                                          );
                                                        },
                                                      );
                                                    } else {
                                                      showDialog(
                                                        context: context,
                                                        builder: (context) {
                                                          return AlertDialog(
                                                            contentPadding:
                                                                EdgeInsets.all(
                                                                    16),
                                                            insetPadding:
                                                                EdgeInsets.all(
                                                                    15),
                                                            shape: RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12)),
                                                            content:
                                                                GroupProcessHoldWidget(
                                                              name: "Division",
                                                              typecode:
                                                                  data.typecode,
                                                              typeid:
                                                                  "CUSTDIVISION",
                                                              typedesc:
                                                                  data.typedesc,
                                                              isHold:
                                                                  data.ishold ==
                                                                          "Y"
                                                                      ? "N"
                                                                      : "Y",
                                                            ),
                                                          );
                                                        },
                                                      );
                                                    }
                                                  }),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                        ],
                      ),
                    ),
                  ),
                )
              ],
            );
          }),
        ),
      ),
    );
  }
}

Widget addSUbElementWidget() {
  return Container(
    height: 44,
    decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppColor.themeDarkBlueColor)),
    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Add Sub-Elements".trr,
          style: pRegular13.copyWith(color: AppColor.cDarkBlueFont),
        ),
        horizontalSpace(6),
        assetSvdImageWidget(image: DefaultImages.dropDownIcn)
      ],
    ),
  );
}

buildAppTitleWidget({Function()? searchTap}) {
  return Container(
    padding: EdgeInsets.only(left: 16, right: 16),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        simpleMyAppBar(
            title: "Fleet structure".trr,
            onTap: () {
              Get.offAll(() => DashBoardManagerScreen(
                    currantIndex: Constants.TopUpBtn == 'Y' ? 4 : 3,
                  ));
            },
            backString: "Back".trr),
        /*  GestureDetector(
          onTap: () {
            Get.offAll(() => MenuScreen());
          },
          child: Container(
            padding: EdgeInsets.only(
              top: 15,
              bottom: 15,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                assetSvdImageWidget(
                    image: DefaultImages.backIcn,
                    colorFilter: ColorFilter.mode(
                        AppColor.cDarkBlueFont, BlendMode.srcIn)),
                horizontalSpace(10),
                Text(
                 // "Menu".trr,
                  "Back".trr,
                  style: pRegular18.copyWith(
                      color: AppColor.cDarkBlueFont, fontSize: 17),
                  textAlign: TextAlign.start,
                )
              ],
            ),
          ),
        ),*/
        /* Expanded(
          child: Align(
            alignment: Alignment.center,
            child: Text(
              "Fleet structure".trr,
              style: pBold20,
              textAlign: TextAlign.center,
            ),
          ),
        ),*/
        // GestureDetector(
        //     onTap: searchTap,
        //     child: assetSvdImageWidget(image: DefaultImages.searchCircleIcn))
      ],
    ),
  );
}

fleetStructureDataWidget({
  required BuildContext context,
  String? title,
  String? status,
  //String? dropDownIcn,
  String? divisionImage,
  String? divisionTitle,
  String? division,
  Function()? onTap,
  Function(String)? moreOnTap,
  List? tooltipItemList,
  Color? bgColor,
  TextStyle? textStyle,
  //bool? isDropDown = true,
}) {
  return Container(
    width: Get.width,
    decoration: BoxDecoration(
        color: bgColor ?? AppColor.cLightBlueContainer,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: AppColor.cLightGrey)),
    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // isDropDown == false
            //     ? SizedBox()
            //     : Padding(
            //         padding: const EdgeInsets.all(8.0),
            //         child: assetSvdImageWidget(
            //             image: dropDownIcn,
            //             colorFilter: ColorFilter.mode(
            //                 AppColor.cText, BlendMode.srcIn)),
            //       ),
            // isDropDown == false ? SizedBox() : horizontalSpace(12),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title!,
                      style: pSemiBold14,
                    ),
                    horizontalSpace(18),
                    statusWidget(text: status, horizontalSpace: 4, left: 4),
                  ],
                ),
                verticalSpace(10),
                Row(
                  children: [
                    assetSvdImageWidget(
                        image: divisionImage,
                        colorFilter:
                            ColorFilter.mode(AppColor.cText, BlendMode.srcIn)),
                    horizontalSpace(8),
                    Text(
                      divisionTitle!.trr,
                      style: textStyle ?? pRegular13,
                    ),
                    division == ''
                        ? SizedBox()
                        : Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: assetSvdImageWidget(
                                image: DefaultImages.dotIcn),
                          ),
                    division == ''
                        ? SizedBox()
                        : Text(
                            division!,
                            style: pRegular14,
                          )
                  ],
                )
              ],
            ),
          ],
        ),
        // GestureDetector(onTap: moreOnTap, child: assetSvdImageWidget(image: DefaultImages.verticleMoreIcn))
        PopupMenuButton(
          itemBuilder: (context) {
            return tooltipItemList!
                .map(
                  (e) => PopupMenuItem(
                    value: e.toString().toLowerCase(),
                    child: Text(e.toString().trr),
                  ),
                )
                .toList();
            // return [
            //   PopupMenuItem(
            //     value: 'edit',
            //     child: Text('Edit'),
            //   ),
            //   PopupMenuItem(
            //     value: 'delete',
            //     child: Text('Delete'),
            //   )
            // ];
          },
          onSelected: moreOnTap,
          child: assetSvdImageWidget(image: DefaultImages.verticleMoreIcn),
        )
      ],
    ),
  );
}

Widget statusWidget(
    {String? text,
    Color? textColor,
    Color? color,
    double? horizontalSpace,
    double? left}) {
  return Container(
    height: 24,
    decoration: BoxDecoration(
        color: text == "ACTIVE"
            ? AppColor.cLightGreen
            : AppColor.cLightRedContainer,
        borderRadius: BorderRadius.circular(4)),
    padding: EdgeInsets.only(right: 8, left: left ?? 8),
    child: Center(
      child: Text(
        text!,
        style: pSemiBold12.copyWith(
          color: text == "ACTIVE" ? AppColor.cGreen : AppColor.cRedText,
        ),
      ),
    ),
  );
}
