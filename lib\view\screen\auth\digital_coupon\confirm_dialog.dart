// ignore_for_file: prefer_const_constructors

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/digital_coupon/fual_confirm_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/utils/colors.dart';

class ConfirmDialogWidget extends StatelessWidget {
  final bool isBack;

  const ConfirmDialogWidget({super.key, required this.isBack});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      decoration: BoxDecoration(
        color: AppColor.cBackGround,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "Amount".trr,
            style: pSemiBold16.copyWith(
                fontSize: 17, color: AppColor.cDarkGreyFont),
          ),
          verticalSpace(4),
          Text("30 Liters", style: pBold28),
          verticalSpace(4),
          Text("Petrol 91", style: pSemiBold16.copyWith(fontSize: 17)),
          verticalSpace(19),
          Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColor.cLightGrey),
            padding: EdgeInsets.symmetric(vertical: 24, horizontal: 16),
            child: Column(
              children: [
                Text(
                  "177",
                  style: pBold28,
                ),
                verticalSpace(16),
                Text(
                  "The attendant has scanned your QR code. Press 'confirm' to go ahead with your purchase, or 'reject' if there's a problem.".trr,
                  style: pRegular16,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          verticalSpace(16),
          Row(
            children: [
              Expanded(
                  child: CommonBorderButton(
                title: "Reject".trr,
                onPressed: () {
                  Get.back();
                },
                bColor: AppColor.themeDarkBlueColor,
                    textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(8),
              Expanded(
                child: CommonButton(
                  title: "Confirm".trr,
                  btnColor: AppColor.themeOrangeColor,
                  onPressed: () {
                    Get.back();
                    Get.off(() => FuelConfirmScreen(isBack: isBack,));
                  },
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
