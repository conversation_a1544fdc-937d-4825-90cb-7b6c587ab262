// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/change_plate_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/change_plate_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/file_complaint_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/incoming_tags_transfer_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/incoming_tags_transfer_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/view_tag_transfer_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/bulk_actions_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/view_tag_transfer_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/vehicle_controller/vehicle_controller.dart';

class TagActionWidget extends StatelessWidget {
  final String code;
  final String status;
  TagActionWidget({super.key, required this.code, required this.status});

  VehicleController vehicleController = Get.find();
  FileComplaintController fileComplaintController =
      Get.put(FileComplaintController());
  ChangePlateController changePlateController =
      Get.put(ChangePlateController());
  ViewTagsTransferController viewTagsTransferController =
      Get.put(ViewTagsTransferController());
  IncomingTagsTransferController incomingTagsTransferController =
      Get.put(IncomingTagsTransferController());
  final vehicle = GetStorage();

  @override
  Widget build(BuildContext context) {
    print("code +++++++++ $code");
    print("status +++++++++ $status");
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              children: [
                GestureDetector(
                    onTap: () {
                      vehicle.remove('vehicleID');
                      vehicle.remove('vehicleSerialID');
                      vehicle.remove('vehicleSerialID');
                      vehicle.remove('complaintJobID');
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.offAll(
                        () => DashBoardManagerScreen(
                          currantIndex: 0,
                        ),
                        //preventDuplicates: false,
                      );
                    },
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: AppColor.cLightBlueContainer,
                      child: Center(
                          child: assetSvdImageWidget(
                              image: DefaultImages.backIcn)),
                    )),
                Expanded(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(
                        "Actions".trr,
                        style: pBold20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          verticalSpace(22),
          status == "NEW"
              ? Column(
                  children: [
                    bulkActionWidget(
                      title: "Cancel Tag Transfer".trr,
                      onTap: () {
                        Get.back();
                        showDialog(
                          barrierDismissible: false,
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              insetPadding: EdgeInsets.all(16),
                              contentPadding: EdgeInsets.all(24),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              content: cancelTagTransferWidget(
                                code: code,
                                onTap: () {
                                  print("Req ID +++++++++ $code");
                                  incomingTagsTransferController
                                      .tagTransferCancel(code);
                                  //changePlateController.changePlate(code);

                                  // vehicleController.myFleetList.refresh();
                                  // fileComplaintController.cancelComplaint(serialid);
                                },
                              ),
                            );
                          },
                        );
                      },
                    ),
                    verticalSpace(22),
                    bulkActionWidget(
                      title: "View Tag Transfer".trr,
                      onTap: () {
                        viewTagsTransferController.getDatas(code);
                      },
                    ),
                  ],
                )
              : bulkActionWidget(
                  title: "View Tag Transfer".trr,
                  onTap: () {
                    viewTagsTransferController.getDatas(code);
                    // Get.back();
                    // showModalBottomSheet(
                    //   context: Get.context!,
                    //   shape: const RoundedRectangleBorder(
                    //       borderRadius:
                    //           BorderRadius.vertical(top: Radius.circular(16))),
                    //   backgroundColor: AppColor.cBackGround,
                    //   barrierColor: AppColor.cBlackOpacity,
                    //   isScrollControlled: true,
                    //   builder: (context) {
                    //     return ViewTagTransferWidget(code: code);
                    //   },
                    // );
                  },
                ),
        ],
      ),
    );
  }

  cancelTagTransferWidget({required String code, required Function() onTap}) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text("Cancel Tag Transfer".trr,
              style: pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
              textAlign: TextAlign.center),
          verticalSpace(8),
          Text("Are you sure you want to cancel your Tag Transfer Request?",
              style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
              textAlign: TextAlign.center),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonButton(
                title: 'No'.trr,
                onPressed: () {
                  vehicle.remove('vehicleID');
                  vehicle.remove('vehicleSerialID');
                  vehicle.remove('vehicleSerialID');
                  vehicle.remove('complaintJobID');
                  vehicleController.selectedSerialList.clear();
                  vehicleController.selectedVehicleList.clear();
                  vehicleController.selectedFleetList.clear();
                  vehicleController.filterValueList.refresh();
                  vehicleController.selectedVehicleList.refresh();
                  vehicleController.selectedSerialList.refresh();
                  vehicleController.selectedFleetList.refresh();
                  Get.offAll(
                    () => DashBoardManagerScreen(
                      currantIndex: 0,
                    ),
                    //preventDuplicates: false,
                  );
                },
                btnColor: AppColor.cBackGround,
                bColor: AppColor.themeDarkBlueColor,
                textColor: AppColor.cDarkBlueFont,
              )),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: 'Yes'.trr,
                  onPressed: onTap,
                  btnColor: AppColor.themeOrangeColor,
                  horizontalPadding: 16,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
