// ignore_for_file: must_be_immutable, prefer_interpolation_to_compose_strings

import 'dart:developer';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/auth/forgot_password_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/auth_background.dart';
import 'package:waie_app/view/screen/auth/forgot_password_screen/set_new_password_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_otp_textfield.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:pinput/pinput.dart';

class ResetPasswordOtpScreen extends StatefulWidget {
  String mobileno;
  ResetPasswordOtpScreen({
    super.key,
    required this.mobileno,
  });

  @override
  State<ResetPasswordOtpScreen> createState() => _ResetPasswordOtpScreenState();
}

class _ResetPasswordOtpScreenState extends State<ResetPasswordOtpScreen> {
  ForgotPasswordController forgotPasswordController =
      Get.put(ForgotPasswordController());
  final pinController = TextEditingController();
  final focusNode = FocusNode();
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    pinController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const focusedBorderColor = Color.fromRGBO(252, 100, 35, 1);
    const fillColor = Color.fromRGBO(243, 246, 249, 0);
    const borderColor = Color.fromRGBO(117, 36, 1, 1);

    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: const TextStyle(
        fontSize: 22,
        color: Color.fromRGBO(30, 60, 87, 1),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(19),
        border: Border.all(color: borderColor),
      ),
    );
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Scaffold(
          backgroundColor: AppColor.cBackGround,
          body: SafeArea(
            child: AuthBackGroundWidget(
              widget: SingleChildScrollView(
                  child: Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    verticalSpace(Get.height * 0.02),
                    simpleMyAppBar(
                        title: "",
                        onTap: () {
                          Get.back();
                        },
                        backString: "Back to Login".trr),
                    verticalSpace(25),
                    Text(
                      "SMS Code Confirmation".trr,
                      style: pBold28,
                    ),
                    verticalSpace(16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 25),
                      child: Text(
                        // "A six-digit OTP is sent to".trr + " +1 XXX XXX XXX 57." + "Please enter it below.".trr,
                        "We've sent a 4-digit confirmation code to your phone numbers ${widget.mobileno}. Please enter it below."
                            .trr,
                        style: pRegular17,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Form(
                      key: formKey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          verticalSpace(24),
                          Directionality(
                            // Specify direction if desired
                            textDirection: TextDirection.ltr,
                            child: Pinput(
                              controller: pinController,
                              focusNode: focusNode,
                              androidSmsAutofillMethod:
                                  AndroidSmsAutofillMethod.smsUserConsentApi,
                              listenForMultipleSmsOnAndroid: true,
                              defaultPinTheme: defaultPinTheme,
                              separatorBuilder: (index) =>
                                  const SizedBox(width: 8),
                              // validator: (value) {
                              //   return value == '2222' ? null : 'Pin is incorrect';
                              // },
                              // onClipboardFound: (value) {
                              //   debugPrint('onClipboardFound: $value');
                              //   pinController.setText(value);
                              // },
                              hapticFeedbackType:
                                  HapticFeedbackType.lightImpact,
                              onCompleted: (pin) {
                                debugPrint('onCompleted: $pin');
                              },
                              onChanged: (value) {
                                debugPrint('onChanged: $value');
                              },
                              cursor: Column(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 9),
                                    width: 22,
                                    height: 1,
                                    color: focusedBorderColor,
                                  ),
                                ],
                              ),
                              focusedPinTheme: defaultPinTheme.copyWith(
                                decoration:
                                    defaultPinTheme.decoration!.copyWith(
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: focusedBorderColor),
                                ),
                              ),
                              submittedPinTheme: defaultPinTheme.copyWith(
                                decoration:
                                    defaultPinTheme.decoration!.copyWith(
                                  color: fillColor,
                                  borderRadius: BorderRadius.circular(19),
                                  border: Border.all(color: focusedBorderColor),
                                ),
                              ),
                              // errorPinTheme: defaultPinTheme.copyBorderWith(
                              //   border: Border.all(color: Colors.redAccent),
                              // ),
                            ),
                          ),
                          verticalSpace(16),
                          CommonButton(
                              title: 'CONFIRM'.trr,
                              onPressed: pinController.value.text != ''
                                  ? () {
                                      log('done');
                                      print(pinController.value.text);

                                      forgotPasswordController.verificationCode
                                          .value = pinController.value.text;

                                      if (pinController.value == '') {
                                        commonToast("Enter CODE".trr);
                                      } else {
                                        //  otpController.sample(code);
                                        forgotPasswordController.login();
                                      }
                                      // Get.to(() => SetNewPasswordScreen());

                                      focusNode.unfocus();
                                      formKey.currentState!.validate();
                                    }
                                  : null,
                              btnColor: AppColor.themeOrangeColor
                              //: AppColor.cLightOrange,
                              ),
                        ],
                      ),
                    ),
                    // Obx(() {
                    //   print(forgotPasswordController.verificationCode.value);
                    //   return Padding(
                    //     padding: const EdgeInsets.only(bottom: 16, top: 24),
                    //     child: FittedBox(
                    //       fit: BoxFit.fill,
                    //       child: CommonOtpTextField(
                    //         numberOfFields: 4,
                    //         borderColor: AppColor.cBorder,
                    //         enabledBorderColor: AppColor.cBorder,
                    //         disabledBorderColor: AppColor.cBorder,
                    //         focusedBorderColor: AppColor.cBorder,
                    //         fieldWidth: 54,
                    //         filled: true,
                    //         fillColor: AppColor.cLightGrey,
                    //         borderRadius: BorderRadius.circular(6),
                    //         textStyle: pRegular14.copyWith(
                    //             color: AppColor.cDarkGreyFont),
                    //         keyboardType:
                    //             const TextInputType.numberWithOptions(
                    //                 signed: true, decimal: true),
                    //         decoration: const InputDecoration(),
                    //         showFieldAsBox: true,
                    //         hintText: 'X',
                    //         onCodeChanged: (String code) {
                    //           print("code==> $code");
                    //         },
                    //         onSubmit: (String verificationCode) {
                    //           print("verificationCode==> $verificationCode");
                    //           forgotPasswordController
                    //               .verificationCode.value = verificationCode;
                    //         }, // end onSubmit
                    //       ),
                    //     ),
                    //   );
                    // }),
                    verticalSpace(24),
                    Text.rich(
                      TextSpan(
                        text: 'Didn`t get the code?'.trr + ' ',
                        style: pRegular14,
                        children: <TextSpan>[
                          TextSpan(
                            text: 'Resend code again'.trr,
                            style: pBold14.copyWith(color: AppColor.cBlueFont),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                forgotPasswordController.resendCode();
                              },
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )),
            ),
          ),
          /*resizeToAvoidBottomInset: false,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
          floatingActionButton: Padding(
            padding: const EdgeInsets.only(left: 24, bottom: 10, right: 24),
            child: Obx(() {
              return CommonButton(
                title: 'CONFIRM'.trr,
                onPressed: forgotPasswordController.verificationCode.value != ''
                    ? () {
                        log('done');
                        // Get.to(() => SetNewPasswordScreen());
                        forgotPasswordController.login();
                      }
                    : null,
                btnColor: forgotPasswordController.verificationCode.value != ''
                    ? AppColor.themeOrangeColor
                    : AppColor.cLightOrange,
              );
            }),
          ),*/
        ),
      ),
    );
  }
}
