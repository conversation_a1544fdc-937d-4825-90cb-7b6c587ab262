// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/promotion_controller/promotion_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/view/screen/menu_screen/promotions_screen/new_promotion_screen.dart';
import 'package:waie_app/view/screen/menu_screen/promotions_screen/promotion_history_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';

import '../../vehicles_screen/tag_installation/tag_installation_screen.dart';

class PromotionsScreen extends StatelessWidget {
  PromotionsScreen({Key? key}) : super(key: key);
  PromotionController promotionController = Get.put(PromotionController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            simpleAppBar(
                title: "Aldrees partners".trr,
                onTap: () {
                  Get.back();
                },
                //backString: "Menu".trr),
                backString: "Back".trr),
            Obx(() {
              return Expanded(
                child: ListView(
                  padding:
                      EdgeInsets.only(top: 15, left: 16, right: 16, bottom: 16),
                  physics: BouncingScrollPhysics(),
                  shrinkWrap: true,
                  children: [
                    Row(
                      children: [
                        tabWidget(
                          title: 'New promotion'.trr,
                          onTap: () {
                            promotionController.isNewPromotion.value = true;
                            promotionController.isHistory.value = false;
                          },
                          isSelected: promotionController.isNewPromotion.value,
                        ),
                        tabWidget(
                          title: 'Promotions history'.trr,
                          onTap: () {
                            promotionController.isNewPromotion.value = false;
                            promotionController.isHistory.value = true;
                          },
                          isSelected: promotionController.isHistory.value,
                        ),
                      ],
                    ),
                    promotionController.isNewPromotion.value == true
                        ? NewPromotionScreen()
                        : PromotionHistoryScreen(
                            promotionController: promotionController,
                          )
                  ],
                ),
              );
            })
          ],
        ),
      ),
    );
  }
}
