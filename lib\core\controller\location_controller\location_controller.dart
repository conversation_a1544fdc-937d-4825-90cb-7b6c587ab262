// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationController extends GetxController {
  RxBool isGasStations = true.obs;
  RxBool isSalesOffice = false.obs;
  RxBool isInstallationCenters = false.obs;
  RxBool isStations = false.obs;
  RxBool isCarService = false.obs;
  RxBool isMosque = false.obs;
  RxBool isFoodResturant = false.obs;
  RxBool isCarRental = false.obs;
  RxBool isBankAtm = false.obs;

  

  RxBool isMap = true.obs;
  RxBool isList = false.obs;
  List<LatLng> latLongList = [
    LatLng(21.**************, 53.***************),
    LatLng(20.***************, 44.**************),
    LatLng(23.***************, 54.***************),
    LatLng(22.***************, 47.***************),
    LatLng(25.***************, 38.***************),
    LatLng(23.***************, 45.**************),
    LatLng(20.***************, 49.**************),
  ];
  List<LatLng> salesOfficeList = [
    LatLng(21.**************, 53.***************),
    LatLng(20.***************, 44.**************),
    LatLng(23.***************, 54.***************),
    LatLng(22.***************, 47.***************),
    LatLng(25.***************, 38.***************),
    LatLng(23.***************, 45.**************),
    LatLng(20.***************, 49.**************),
  ];
  List<LatLng> installationList = [
    LatLng(21.**************, 53.***************),
    LatLng(20.***************, 44.**************),
    LatLng(23.***************, 54.***************),
    LatLng(22.***************, 47.***************),
    LatLng(25.***************, 38.***************),
    LatLng(23.***************, 45.**************),
    LatLng(20.***************, 49.**************),
  ];
  List listData = [
    {
      'data': "Riyadh",
      'list': [
        {
          "title": "Al Reef",
          'subTitle': "RAFB7699، 7699 Riyadh 13314, Saudi Arabia",
        },
        {
          "title": "Asment Exit - 18",
          'subTitle': "REFA7322, 7322 Mahail, 4063",
        },
      ]
    },
    {
      'data': "Al Ahsa",
      'list': [
        {
          "title": "Al Makhaita 2",
          'subTitle':
              "Al Qadisiyah, Al Mubarraz 36422, Saudi Arabia ا,،,لملك سعود",
        },
      ]
    },
    {
      'data': "Duraidah",
      'list': [
        {
          "title": "Al Faizy",
          'subTitle':
              "QBWE7235، 7235 عمر بن الخطاب، 2639، حي النهضة, Buraydah 52388, Saudi Arabia",
        },
      ]
    },
  ];
  RxList itemList = [].obs;
  var searchController = TextEditingController().obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    searchController.value = TextEditingController();
  }
}
