import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
// commented by fuzail 5-12-2025

// import 'package:pointycastle/key_derivators/api.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/UserList.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';

import 'dart:typed_data';
// commented by fuzail 5-12-2025

// import 'package:encrypt/encrypt.dart' as encrypt;
// import 'package:pointycastle/api.dart';
// import 'package:pointycastle/key_derivators/pbkdf2.dart';
// import 'package:pointycastle/digests/sha256.dart';
// import 'package:pointycastle/macs/hmac.dart';



import '../../../../models/profile.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/constants.dart';
import '../../../../utils/text_style.dart';
import '../../../../view/screen/menu_screen/user_management_screen/user_management_screen.dart';
import '../../../../view/widget/common_button.dart';
import '../../../../view/widget/common_space_divider_widget.dart';
import '../../../../view/widget/loading_widget.dart';

class UserManagementController extends GetxController {
  TextEditingController usernameController = TextEditingController();
  TextEditingController useremailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  /* List userDataList = [
    {
      "name": "Mark Watson (You)",
      "status": "Active",
      "vehiclesAssigned": 'N/A',
      "email": "<EMAIL>",
      "id": "37472834",
      "created": "21.04.2023",
      "lastVisit": "Today",
    },
    {
      "name": "Willem Dafoe",
      "status": "Active",
      "vehiclesAssigned": 'N/A',
      "email": "<EMAIL>",
      "id": "37472835",
      "created": "21.04.2023",
      "lastVisit": "Today",
    },
    {
      "name": "Phil Hoffman",
      "status": "Wait for sign up",
      "vehiclesAssigned": '5 Vehicles',
      "email": "<EMAIL>",
      "id": "37472836",
      "created": "21.04.2023",
      "lastVisit": "Today",
    },
  ];*/

  RxList userDataList = <UserList>[].obs;

  RxBool isUserDetail = true.obs;
  RxBool isAssignVehicle = false.obs;
  List statusList = ['Active', "In-Active"];
  List rcvEmailList = ['Yes'.tr, "No".tr];
  List twoFAList = ['Yes'.tr, "No".tr];
  RxString statusValue = 'Active'.obs;
  RxString rcvEmailValue = 'Yes'.tr.obs;
  RxString twoFAValue = 'Yes'.tr.obs;
  RxBool isPassword = true.obs;
  RxList selectedMenuList = [].obs;
  RxList selectedOrgList = [].obs;
  RxList vehiclePlateList = [TextEditingController()].obs;
  RxBool isMenuAccess = true.obs;
  RxBool isOrgLevel = true.obs;
  RxBool isView = true.obs;
  RxList ordersList = [
    {"title": "Create tag orders", "value": true.obs}.obs,
    {"title": "Cancel tag orders", "value": false.obs}.obs,
    {"title": "View tag orders", "value": true.obs}.obs,
  ].obs;
  RxList balanceList = [
    {"title": "Create top-up orders", "value": true.obs}.obs,
    {"title": "Cancel top-up orders", "value": false.obs}.obs,
    {"title": "View top-up orders", "value": true.obs}.obs,
  ].obs;
  RxList purchaseHistoryList = [
    {"title": "View all orders and invoices", "value": false.obs}.obs,
    {"title": "Create request", "value": false.obs}.obs,
    {"title": "Cancel request", "value": false.obs}.obs,
    {"title": "View request", "value": false.obs}.obs,
  ].obs;
  RxList reportsList = [
    {"title": "Fuel consumption", "value": false.obs}.obs,
    {"title": "Monthly service fees report ", "value": false.obs}.obs,
    {"title": "Account statement", "value": true.obs}.obs,
  ].obs;
  RxList companyAffiliateList = [
    {"title": "Create requests", "value": true.obs}.obs,
    {"title": "Approve requests", "value": false.obs}.obs,
    {"title": "Cancel requests", "value": false.obs}.obs,
    {"title": "View requests", "value": true.obs}.obs,
  ].obs;
  RxList tagInstallationList = [
    {"title": "Create requests", "value": true.obs}.obs,
    {"title": "Cancel requests", "value": true.obs}.obs,
    {"title": "View requests", "value": true.obs}.obs,
  ].obs;
  TextEditingController emailController = TextEditingController();
  TextEditingController editEmailController = TextEditingController();
  TextEditingController editNameController = TextEditingController();
  RxList userRoleList = ["Admin", "Manager", "Accountant", "Driver"].obs;
  RxString selectedUserRole = 'Accountant'.obs;
  RxList itemList = [].obs;
  var searchController = TextEditingController().obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    searchController.value = TextEditingController();
    getUserList();
  }

  // getUserList() async {
  //   var client = http.Client();
  //   SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
  //   var userid = sharedUser2.getString('userid');
  //   var user = sharedUser2.getString('user');
  //   Map cardInfo = json.decode(user!);
  //   Profile userData = Profile.fromJson(cardInfo);
  //   try {
  //     var response = await client.post(
  //         Uri.parse(
  //             ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getUserList),
  //         body: {
  //           "custId": userid,
  //         });
  //     print("User List============${response.body}");
  //     List result = jsonDecode(response.body);
  //     userDataList.clear();
  //     for (int i = 0; i < result.length; i++) {
  //       UserList userlist =
  //       UserList.fromMap(result[i] as Map<String, dynamic>);

  //       await Future.delayed(
  //           const Duration(seconds: 0), () => userDataList.add(userlist));
  //     }

  //     return userDataList;

  //   } catch (e) {
  //     log("User List Error $e");
  //     return [];
  //   } finally {
  //     // Then finally destroy the client.
  //     client.close();
  //   }
  // }

  getUserList() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');

    try {
      var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getUserList),
        body: {"custId": userid},
      );

      print("Encrypted Response: ${response.body}");

      Map<String, dynamic> responseBody = jsonDecode(response.body);
      print("responseBody: $responseBody");
      String encryptedData = responseBody['data'];
      print("encryptedData: $encryptedData");

      // Decrypt response
      String decryptedJson = decryptAES(encryptedData);
      print("Decrypted Response: $decryptedJson");

      List result = jsonDecode(decryptedJson);
      List<UserList> userDataList =
          result.map((e) => UserList.fromMap(e)).toList();

      return userDataList;
    } catch (e) {
      print("User List Error: $e");
      return [];
    } finally {
      client.close();
    }
  }



// commented 

// Function to generate AES Key and IV using PBKDF2
  // List<int> deriveKeyAndIV(
  //     String password, List<int> salt, int keyLength, int ivLength) {
  //   print("password Response: $password");
  //   final derivator = PBKDF2KeyDerivator(HMac(SHA256Digest(), 64))
  //     ..init(Pbkdf2Parameters(
  //         Uint8List.fromList(salt), 10000, keyLength + ivLength));
  //   print("derivator Response: $derivator");

  //   final keyIV = derivator.process(Uint8List.fromList(utf8.encode(password)));
  //   print("keyIV Response: $keyIV");

  //   return keyIV;
  // }


/////////////////





// commented by fuzail 5-12-2025

// AES Decryption Method
  String decryptAES(String encryptedText) {
    final password = "A14L12D14R0E0ES"; // Must match C# password
    final salt = [
      0x49,
      0x76,
      0x61,
      0x6e,
      0x20,
      0x4d,
      0x65,
      0x64,
      0x76,
      0x65,
      0x64,
      0x65,
      0x76
    ];

    // commented by fuzail 5-12-2025


    // final keyIV = deriveKeyAndIV(password, salt, 32, 16);
    // print("keyIV Response: $keyIV");
    // final key = encrypt.Key(
    //     Uint8List.fromList(keyIV.sublist(0, 32))); // 32 bytes for key
    // print("key Response: $key.");
    // final iv = encrypt
    //     .IV(Uint8List.fromList(keyIV.sublist(32, 48))); // 16 bytes for IV
    // print("iv Response: $iv");

    // final encrypter =
    //     encrypt.Encrypter(encrypt.AES(key, mode: encrypt.AESMode.cbc));
    // print("encrypter Response: $encrypter");
    // final decrypted = encrypter.decrypt64(encryptedText, iv: iv);
    // print("decrypted Response: $decrypted");
// commented by fuzail 5-12-2025

    return 'decrypted';
  }

  saveUser() async {
    Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    String selectedMenuLists = selectedMenuList.join(",");
    String selectedOrgLists = selectedOrgList.join(",");
    print("===============================================================");
    print(selectedMenuLists);
    print(selectedOrgLists);
    print("===============================================================");
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.saveUser),
          body: {
            "custid": userid,
            "username": usernameController.text,
            "emailid": useremailController.text,
            "password": passwordController.text,
            "actcode": statusValue.value == "Active" ? "A" : "I",
            "rcvemail": rcvEmailValue.value == "Yes" ? "Y" : "N",
            "twofa": twoFAValue.value == "Yes" ? "Y" : "N",
            "menuaccess": selectedMenuLists,
            "orglevel": selectedOrgLists,
            "IsAR": Constants.IsAr_App
          });
      print("===============================================================");
      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.statusCode))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(jsonEncode(response.body))}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["MessageType"]}");

      print(
          "jsonDecode(response.body) .>>>>> ${jsonDecode(response.body)["Message"]}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        print(
            "cancelCompanyAffiliateRequest response.statusCode>>>>>>> ${response.statusCode}");
        usernameController.clear();
        useremailController.clear();
        passwordController.clear();
        if (jsonDecode(response.body)["MessageType"] == "success") {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        await Get.offAll(
                            () => DashBoardManagerScreen(currantIndex: 0));
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["Message"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () async {
                        await Get.offAll(
                            () => DashBoardManagerScreen(currantIndex: 0));
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        }
        //await Get.off(() => UserManagementScreen(), preventDuplicates: false);
      } else {
        print('Failed');
      }
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
