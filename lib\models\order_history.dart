class OrderHistory {
  int LINE;
  String? ORDERID;
  String? ACCTTYPE;
  String? DATEORDERED;
  String? ORDERDATE;
  String? ORDERTYPE;
  String? CUSTID;
  double TOTORDERAMT = 0;
  //double CURRACCTBAL=0;
  double ADDLTOPUP = 0;
  double TOTPAYAMT = 0;
  String? REMARKS;
  String? PAYTYPE;
  String? PAYMODE;
  String? INVNO;
  String? STATUS;
  String? PAID;
  String? STATE;
  String? PAIDON;
  String? CASHIERID;
  String? CASHIERREMARKS;
  String? VOCHNO;
  String? VOCHTRANCODE;
  String? VOCHTRANNO;
  String? SYS_DATE;
  String? SYSUSERID;
  String? ISORDERCLEAR;
  String? PROMORATE;
  String? SERVICETYPE;

  OrderHistory(
      {required this.LINE,
      required this.ORDERID,
      required this.ACCTTYPE,
      required this.DATEORDERED,
      required this.ORDERDATE,
      required this.ORDERTYPE,
      required this.CUSTID,
      //  required this.TOTORDERAMT,
      // required this.CURRACCTBAL,
      //   required this.ADDLTOPUP,
      required this.TOTPAYAMT,
      required this.REMARKS,
      required this.PAYTYPE,
      required this.PAYMODE,
      required this.INVNO,
      required this.STATUS,
      required this.PAID,
      required this.STATE,
      required this.PAIDON,
      required this.CASHIERID,
      required this.CASHIERREMARKS,
      required this.VOCHNO,
      required this.VOCHTRANCODE,
      required this.VOCHTRANNO,
      required this.SYS_DATE,
      required this.SYSUSERID,
      required this.ISORDERCLEAR,
      required this.PROMORATE,
      required this.SERVICETYPE});

  factory OrderHistory.fromJson(Map<String, dynamic> json) {
    return OrderHistory(
        LINE: json['LINE'],
        ORDERID: json['ORDERID'],
        ACCTTYPE: json['ACCTTYPE'],
        DATEORDERED: json['DATEORDERED'],
        ORDERDATE: json['ORDERDATE'],
        ORDERTYPE: json['ORDERTYPE'],
        CUSTID: json['CUSTID'],
        //TOTORDERAMT: json['TOTORDERAMT'],
        //  CURRACCTBAL: json['CURRACCTBAL'],
        //  ADDLTOPUP: json['ADDLTOPUP'],
        //TOTPAYAMT: json['TOTPAYAMT'],
        TOTPAYAMT: json['TOT_AMT'],
        REMARKS: json['REMARKS'],
        PAYTYPE: json['PAYTYPE'],
        PAYMODE: json['PAYMODE'],
        INVNO: json['INVNO'],
        STATUS: json['STATUS'],
        PAID: json['PAID'],
        STATE: json['STATE'],
        PAIDON: json['PAIDON'],
        CASHIERID: json['CASHIERID'],
        CASHIERREMARKS: json['CASHIERREMARKS'],
        VOCHNO: json['VOCHNO'],
        VOCHTRANCODE: json['VOCHTRANCODE'],
        VOCHTRANNO: json['VOCHTRANNO'],
        SYS_DATE: json['SYS_DATE'],
        SYSUSERID: json['SYSUSERID'],
        ISORDERCLEAR: json['ISORDERCLEAR'],
        PROMORATE: json['PROMORATE'],
        SERVICETYPE: json['SERVICETYPE']);
  }
}
