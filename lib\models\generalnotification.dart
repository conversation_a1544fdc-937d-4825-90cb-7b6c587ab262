import 'dart:convert';

class GeneralNotification {
  GeneralNotification({
    required this.custid,
    required this.notifyid,
    required this.notifydate,
    required this.notifytype,
    required this.docNo,
    required this.pagelink,
    required this.notyDesc,
  });

  final String? custid;
  final dynamic? notifyid;
  final String? notifydate;
  final String? notifytype;
  final String? docNo;
  final String? pagelink;
  final String? notyDesc;

  /*factory GeneralNotification.fromJson(Map<String, dynamic> json){
    return GeneralNotification(
      custid: json["CUSTID"],
      notifyid: json["NOTIFYID"],
      notifydate: DateTime.tryParse(json["NOTIFYDATE"] ?? ""),
      notifytype: json["NOTIFYTYPE"],
      docNo: json["DOC_NO"],
      pagelink: json["PAGELINK"],
      notyDesc: json["NOTY_DESC"],
    );
  }*/

  Map<String, dynamic> toMap() {
    return {
      'custid': custid,
      'notifyid': notifyid,
      'notifydate': notifydate,
      'notifytype': notifytype,
      'docNo': docNo,
      'pagelink': pagelink,
      'notyDesc': notyDesc,
    };
  }

  factory GeneralNotification.fromMap(Map<String, dynamic> map) {
    return GeneralNotification(
      custid: map['CUSTID'] ?? '',
      notifyid: map['NOTIFYID'] ?? '',
      notifydate: map['NOTIFYDATE'] ?? '',
      notifytype: map['NOTIFYTYPE'] ?? '',
      docNo: map['DOC_NO'] ?? '',
      pagelink: map['PAGELINK'] ?? '',
      notyDesc: map['NOTY_DESC'] ?? '',
    );
  }
  String toJson() => json.encode(toMap());

  factory GeneralNotification.fromJson(String source) =>
      GeneralNotification.fromMap(json.decode(source));

}
