// ignore_for_file: must_be_immutable, prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:file_picker/file_picker.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/core/controller/menu_controller/profile_controller/data_load_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/screen/auth/digital_coupon/create_dc_screen.dart';

import '../../../../core/controller/menu_controller/profile_controller/company_detail_controller.dart';
import '../../../../utils/images.dart';
import '../../../../utils/text_style.dart';
import '../../../widget/icon_and_image.dart';

class CompanyDetailScreen extends StatefulWidget {
  const CompanyDetailScreen({super.key});

  @override
  State<StatefulWidget> createState() => _CompanyDetailScreen();
}

class _CompanyDetailScreen extends State<CompanyDetailScreen> {
  CompanyDetailController companyDetailController =
      Get.put(CompanyDetailController());
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  Data_Load_Controller dataLoadController = Get.put(Data_Load_Controller());

  String errorString = '';
  FilePickerResult? vatDocx;
  FilePickerResult? idDocx;

  @override
  void initState() {
    super.initState();
    companyDetailController.getstoredCompanyDetails();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Obx(() {
            return Column(
              children: [
                simpleMyAppBar(
                    title: "Company details".trr,
                    onTap: () async {
                      SharedPreferences sharedProfileDetails =
                          await SharedPreferences.getInstance();
                      SharedPreferences sharedProfileDetail =
                          await SharedPreferences.getInstance();
                      await sharedProfileDetails.clear();
                      await sharedProfileDetail.clear();
                      Get.back();
                    },
                    backString: "Back".trr,
                    horizontalSize: 45),
                Expanded(
                  child: Form(
                    key: formKey,
                    child: ListView(
                      shrinkWrap: true,
                      padding:
                          const EdgeInsets.only(top: 24, left: 16, right: 16),
                      children: [
                        buildDisplayRowWidget(
                            title: 'Company type'.trr,
                            value: companyDetailController.compType.toString()),
                        buildDisplayRowWidget(
                            title: 'Registration date'.trr,
                            value: companyDetailController.regDate.toString()),
                        buildDisplayRowWidget(
                            title: 'User ID'.trr,
                            value: companyDetailController.userID.toString()),
                        verticalSpace(32),
                        CommonTextField(
                          controller: companyDetailController.cpNameController,
                          labelText: 'Company Name'.trr + '*',
                          hintText: "Please enter here".trr,
                          validator: (value) {
                            return Validator.validateName(
                                value, "Company Name".trr);
                          },
                        ),
                        // verticalSpace(16),
                        // CommonTextField(
                        //   controller:
                        //       companyDetailController.arabicCpNameController,
                        //   labelText: 'Arabic Company Name'.trr + '*',
                        //   hintText: "Please enter here".trr,
                        //   validator: (value) {
                        //     return Validator.validateName(
                        //         value, "Arabic Company Name".trr);
                        //   },
                        // ),
                        verticalSpace(16),
                        CommonTextField(
                          controller:
                              companyDetailController.designationController,
                          labelText: 'Designation'.trr + '*',
                          hintText: "Admin".trr,
                          validator: (value) {
                            return Validator.validateName(
                                value, "Designation".trr);
                          },
                        ),
                        // verticalSpace(16),
                        // CommonDropdownButtonWidget(
                        //     hint: '',
                        //     labelText: 'Expro'.trr,
                        //     list: companyDetailController.exproList,
                        //     value: companyDetailController
                        //         .selectedExproValue.value,
                        //     onChanged: (value) {
                        //       companyDetailController
                        //           .selectedExproValue.value = value;
                        //     },
                        //     validator: (value) => '',
                        //     fontColor: AppColor.cDarkGreyFont,
                        //     filledColor: AppColor.cFilled),
                        verticalSpace(16),
                        Text(
                          "Salesman".trr + '*',
                          style: pRegular13,
                        ),
                        verticalSpace(10),
                        // CommonHintDropdownWidget(
                        DropdownButtonFormField(
                          items: dataLoadController.salesman.map((data) {
                            return DropdownMenuItem(
                              value: data,
                              child: Text(
                                data,
                                style: pMedium12,
                                textAlign: TextAlign.center,
                              ),
                            );
                          }).toList(),
                          /* value: companyDetailController
                                .selectedSalesmanValue.value,*/
                          onChanged: (value) {
                            companyDetailController.salesManController.text =
                                value.toString();
                            print(
                                "companyDetailControllerselectedSalesmanValue.value +++++++++++++++ ${companyDetailController.salesManController.text}");
                          },
                          validator: (value) {
                            setState(() {
                              if (value == null) {
                                errorString = 'Please select salesman'.trr;
                              } else {
                                errorString = '';
                              }
                            });
                            return null;
                          },
                          style: pRegular14.copyWith(color: AppColor.cLabel),
                          borderRadius: BorderRadius.circular(6),
                          dropdownColor: AppColor.cLightGrey,
                          icon: assetSvdImageWidget(
                              image: DefaultImages.dropDownIcn),
                          decoration: InputDecoration(
                            hintText: 'Salesman'.trr,
                            hintStyle:
                                pRegular14.copyWith(color: AppColor.cHintFont),
                            contentPadding:
                                EdgeInsets.only(left: 16, right: 16),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                              borderSide: BorderSide(
                                color: AppColor.cBorder,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                              borderSide: BorderSide(
                                color: AppColor.cBorder,
                              ),
                            ),
                            errorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                              borderSide: BorderSide(
                                color: AppColor.cBorder,
                              ),
                            ),
                            disabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                              borderSide: BorderSide(
                                color: AppColor.cBorder,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                              borderSide: BorderSide(
                                color: AppColor.cBorder,
                              ),
                            ),
                          ),
                        ),
                        errorString == ''
                            ? SizedBox()
                            : Text(
                                errorString,
                                style: pRegular12.copyWith(
                                    color: AppColor.cRedText),
                              ),
                        /*  fontColor: AppColor.cDarkGreyFont,
                            filledColor: AppColor.cFilled)*/

                        verticalSpace(16),
                        CommonTextField(
                          controller:
                              companyDetailController.contactPersonController,
                          labelText: 'Contact person'.trr + '*',
                          hintText: "Please enter here".trr,
                          validator: (value) {
                            return Validator.validateName(
                                value, "Contact person".trr);
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller:
                              companyDetailController.cpPhoneNoController,
                          labelText: 'Company phone number'.trr + '*',
                          hintText: 'Enter phone number'.trr,
                          keyboardType: TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          maxLength: 12,
                          validator: (value) {
                            return Validator.validateMobile(value);
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller: companyDetailController.cpFaxController,
                          labelText: 'Company fax'.trr,
                          hintText: 'Enter company fax'.trr,
                          keyboardType: TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          validator: (value) {
                            return '';
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller: companyDetailController.regNoController,
                          labelText: 'Commercial Register No'.trr,
                          hintText: 'Enter commercial register no'.trr,
                          keyboardType: TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          validator: (value) {
                            return Validator.validateCommercialRegisterNo(
                                value);
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller: companyDetailController.vatNoController,
                          labelText: 'VAT No'.trr,
                          hintText: 'Enter VAT no'.trr,
                          keyboardType: TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          validator: (value) {
                            return '';
                          },
                        ),
                        verticalSpace(16),
                        Text(
                          "VAT No Document *",
                          style: pRegular12,
                        ),
                        verticalSpace(6),
                        GestureDetector(
                          child: Container(
                            height: 44,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(color: AppColor.cBorder)),
                            padding: EdgeInsets.symmetric(
                              horizontal: 6,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  height: 32,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(6),
                                    color: AppColor.lightBlueColor,
                                  ),
                                  padding: EdgeInsets.only(right: 12, left: 8),
                                  child: Row(
                                    children: [
                                      assetSvdImageWidget(
                                        image: DefaultImages.fileIcn,
                                      ),
                                      horizontalSpace(8),
                                      Text(
                                        "Choose file".trr,
                                        style: pRegular14,
                                      ),
                                    ],
                                  ),
                                ),
                                horizontalSpace(8),
                                Expanded(
                                  child: Text(
                                    companyDetailController.vatDocument.isEmpty
                                        ? "No file chosen".trr
                                        : companyDetailController
                                            .vatDocument.value
                                            .split("/")
                                            .last,
                                    style: pRegular14.copyWith(
                                      color: AppColor.cDarkGreyFont,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          onTap: () async {
                            //refundsController.companyReg.value =
                            //await refundsController.pickImage(ImageSource.gallery);
                            vatDocx = await FilePicker.platform.pickFiles(
                              type: FileType.custom,
                              allowedExtensions: ['jpg', 'pdf', 'doc'],
                              withReadStream: true,
                            );
                            if (vatDocx == null) {
                              print("No file selected");
                            } else {
                              setState(() {
                                for (var element in vatDocx!.files) {
                                  print(element.name);
                                  companyDetailController.vatDocument.value =
                                      element.name;
                                }
                              });
                            }
                          },
                        ),
                        verticalSpace(16),
                        Text(
                          "ID Document *",
                          style: pRegular12,
                        ),
                        verticalSpace(6),
                        GestureDetector(
                          child: Container(
                            height: 44,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(color: AppColor.cBorder)),
                            padding: EdgeInsets.symmetric(
                              horizontal: 6,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  height: 32,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(6),
                                    color: AppColor.lightBlueColor,
                                  ),
                                  padding: EdgeInsets.only(right: 12, left: 8),
                                  child: Row(
                                    children: [
                                      assetSvdImageWidget(
                                        image: DefaultImages.fileIcn,
                                      ),
                                      horizontalSpace(8),
                                      Text(
                                        "Choose file".trr,
                                        style: pRegular14,
                                      ),
                                    ],
                                  ),
                                ),
                                horizontalSpace(8),
                                Expanded(
                                  child: Text(
                                    companyDetailController.idDocument.isEmpty
                                        ? "No file chosen".trr
                                        : companyDetailController
                                            .idDocument.value
                                            .split("/")
                                            .last,
                                    style: pRegular14.copyWith(
                                      color: AppColor.cDarkGreyFont,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          onTap: () async {
                            //refundsController.companyReg.value =
                            //await refundsController.pickImage(ImageSource.gallery);
                            idDocx = await FilePicker.platform.pickFiles(
                              type: FileType.custom,
                              allowedExtensions: ['jpg', 'pdf', 'doc'],
                              withReadStream: true,
                            );
                            if (idDocx == null) {
                              print("No file selected");
                            } else {
                              setState(() {
                                for (var element in idDocx!.files) {
                                  print(element.name);
                                  companyDetailController.idDocument.value =
                                      element.name;
                                }
                              });
                            }
                          },
                        ),
                        verticalSpace(16),
                      ],
                    ),
                  ),
                ),
              ],
            );
          }),
        ),
        bottomNavigationBar: Container(
          color: AppColor.cLightGrey,
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                  child: CommonButton(
                title: 'Cancel'.trr,
                onPressed: () {
                  Get.back();
                },
                textColor: AppColor.cText,
                btnColor: AppColor.cBackGround,
              )),
              horizontalSpace(16),
              Expanded(
                  child: CommonButton(
                title: 'Save'.trr,
                onPressed: () {
                  if (formKey.currentState!.validate()) {
                    if (companyDetailController.vatNoController.text != "") {
                      /*commonToast(
                          'FIRST NAME/MIDDLE NAME/LAST NAME SHOULD BE ARABIC!');*/
                      if (vatDocx == null) {
                        commonToast('Please Upload VAT No Document');
                      } else if (idDocx == null) {
                        commonToast('Please Upload ID Document');
                      } else {
                        PlatformFile invVatdocx = vatDocx!.files.first;
                        PlatformFile invIddocx = idDocx!.files.first;
                        companyDetailController.updateCompanyDetail(
                            invVatdocx, invIddocx);
                      }
                    } else {
                      companyDetailController.updateCompanyDetailNoVat();
                    }
                  }
                },
                textColor: AppColor.cWhiteFont,
                btnColor: AppColor.themeOrangeColor,
              )),
            ],
          ),
        ),
      ),
    );
  }
}
