// ignore_for_file: avoid_print

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:webview_flutter_plus/webview_flutter_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../widget/loading_widget.dart';

class AldreesPortalScreen extends StatefulWidget {
  final String url;
  const AldreesPortalScreen({
    super.key,
    required this.url,
  });

  @override
  State<AldreesPortalScreen> createState() => _AldreesPortalScreenState();
}

class _AldreesPortalScreenState extends State<AldreesPortalScreen> {
  late WebViewController _controler;
  final double _height = 1;
  bool isLoading = true;

  @override
  void initState() {
    _controler = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse('https://waie.aldrees.com'))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (progress) {
            print("progress---> $progress");
            if (progress == 100) {
              setState(() {
                isLoading = false;
              });
            }
          },
        ),
      );
    super.initState();
  }

  Future<bool> _onWillPop() async {
    if (await _controler.canGoBack()) {
      // If there's history, go back to the previous page
      _controler.goBack();
      return false; // Prevent the app from exiting
    } else {
      return true; // Allow the app to exit
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColor.themeBlueColor,
          title: Text(
            "Aldrees Portal".trr,
            style: pBold20.copyWith(color: AppColor.cWhiteFont),
            textAlign: TextAlign.center,
          ),
        ),
        body: isLoading
            ? const Center(
                child: LoadingWidget(),
              )
            : WebViewWidget(
                controller: _controler,
              ),
      ),
    );
  }
}
