// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/bulk_actions_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:quickalert/quickalert.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../../core/controller/menu_controller/purchase_history_controller/order_controller.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/images.dart';
import '../../../widget/common_space_divider_widget.dart';
import '../../../widget/icon_and_image.dart';
import '../company_affiliates_screen/request_history_screen.dart';
import '../user_management_screen/user_management_screen.dart';

class OrderScreen extends StatefulWidget {
  const OrderScreen({super.key});

  @override
  State<OrderScreen> createState() => _OrderScreenState();
}

class _OrderScreenState extends State<OrderScreen> {
  OrderController orderController = Get.put(OrderController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => orderController.orderHistoryList.isNotEmpty
          ? Expanded(
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: Column(
                  children: [
                    ListView.builder(
                      shrinkWrap: true,
                      itemCount: orderController.orderHistoryList.length,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = orderController.orderHistoryList[index];
                        print("DATA ORDER HISTORY==========${data.ORDERID}");
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: orderWidget(
                              code: data.ORDERID.toString(),
                              orderDate: data.ORDERDATE.toString(),
                              orderType: data.ORDERTYPE.toString().trr,
                              serviceType: data.SERVICETYPE.toString().trr,
                              status: data.STATUS.toString().trr,
                              paymentMethod: data.PAYMODE.toString().trr,
                              //paymentMethod2:data['paymentMethod2'].toString().trr,
                              paymentMethod2: "",
                              // price: "",//data['price'],
                              //  quantity: "",//data['quantity'],
                              totalValue: data.TOTPAYAMT
                                  .toString(), //data['totalValue'],
                              color: data.STATUS.toString() == "Pending"
                                  ? AppColor.cLightGrey
                                  : AppColor.cLightGreen,
                              textColor: data.STATUS.toString() == "Pending"
                                  ? AppColor.cDarkGreyFont
                                  : AppColor.cGreen,
                              cancelPOOnTap: () {
                                // await QuickAlert.show(
                                //   context: context,
                                //   type: QuickAlertType.confirm,
                                //   text:
                                //       'You want to cancel this order #${data.ORDERID.toString()}?',
                                //   confirmBtnText: 'Yes',
                                //   cancelBtnText: 'No',
                                //   confirmBtnColor: AppColor.themeOrangeColor,
                                // );
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return AlertDialog(
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12)),
                                      contentPadding: EdgeInsets.all(24),
                                      insetPadding: EdgeInsets.all(16),
                                      content: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              GestureDetector(
                                                  onTap: () {
                                                    Get.back();
                                                  },
                                                  child: assetSvdImageWidget(
                                                      image: DefaultImages
                                                          .cancelIcn)),
                                            ],
                                          ),
                                          verticalSpace(24),
                                          Center(
                                            child: Text("Cancel Order".trr,
                                                style: pBold20,
                                                textAlign: TextAlign.center),
                                          ),
                                          verticalSpace(14),
                                          Center(
                                              child: Text(
                                                  "${"Are you sure you want to cancel this order".trr} ${data.ORDERID.toString()}?"
                                                      .trr,
                                                  style: pRegular13,
                                                  textAlign: TextAlign.center)),
                                          verticalSpace(24),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: CommonButton(
                                                  title: "NO".trr,
                                                  onPressed: () {
                                                    Get.back();
                                                  },
                                                  textColor:
                                                      AppColor.cDarkBlueFont,
                                                  btnColor:
                                                      AppColor.cBackGround,
                                                  bColor:
                                                      AppColor.cDarkBlueFont,
                                                ),
                                              ),
                                              horizontalSpace(16),
                                              Expanded(
                                                child: CommonButton(
                                                  title: "Yes".trr,
                                                  onPressed: () {
                                                    //Get.back();
                                                    Loader.showLoader();
                                                    orderController
                                                        .cancelPurchaseHistory(
                                                            data.ORDERID
                                                                .toString());
                                                    //Get.back();
                                                  },
                                                  textColor:
                                                      AppColor.cWhiteFont,
                                                  btnColor: AppColor.cRedText,
                                                  bColor: AppColor.cTransparent,
                                                  horizontalPadding: 16,
                                                ),
                                              ),
                                            ],
                                          )
                                        ],
                                      ),
                                    );
                                  },
                                );
                              }
                              /*onTap: () {
                              data.STATUS.toString() == "FOR PAYMENT"
                                  ? showModalBottomSheet(
                                      context: context,
                                      shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(16))),
                                      builder: (context) {
                                        return orderActionWidget(
                                          code: data.ORDERID.toString(),
                                          // isShowCancelOrder:
                                          //     data['status'] == "Claimed",
                                          // printOrder: () {},
                                          // downloadOrder: () {},
                                          cancelOrder: () {
                                            orderController
                                                .cancelPurchaseHistory(
                                                    data.ORDERID.toString());
                                          },
                                        );
                                      },
                                    )
                                  : SizedBox();
                            }, */
                              ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            )
          : const Center(child: CircularProgressIndicator()),
    );
  }
}

Widget orderWidget(
    {required String code,
    required String status,
    Color? color,
    Color? textColor,
    required String orderType,
    required String serviceType,
    required String orderDate,
    required String paymentMethod,
    required String paymentMethod2,
    // required String price,
    // required String quantity,
    required String totalValue,
    Function()? cancelPOOnTap,
    Function()? onTap}) {
  return Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(4),
      border: Border.all(color: AppColor.cLightGrey),
    ),
    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.cLightBlueBorder),
                        borderRadius: BorderRadius.circular(4)),
                    padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 13, color: AppColor.cDarkBlueText),
                      ),
                    )),
                horizontalSpace(8),
                newWidget(text: status, color: color, textColor: textColor),
              ],
            ),
            // status == "FOR PAYMENT"
            //     ? GestureDetector(
            //         onTap: onTap,
            //         child: assetSvdImageWidget(
            //             image: DefaultImages.verticleMoreIcn))
            //     : SizedBox()
          ],
        ),
        verticalSpace(18),
        userDataRowWidget(
            title: "Order type".trr,
            value: orderType == "S" ? "Services" : "TopUp"),
        verticalSpace(18),
        userDataRowWidget(
            title: "Service type".trr,
            value: serviceType == "T" ? "Tag" : "Card"),
        verticalSpace(12),
        userDataRowWidget(title: "Order date".trr, value: orderDate),
        verticalSpace(12),
        Row(
          children: [
            SizedBox(
              width: 140,
              child: Text(
                "Payment method".trr,
                style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  text: paymentMethod,
                  style: pRegular14.copyWith(fontSize: 13),
                  children: <TextSpan>[
                    TextSpan(
                        text: paymentMethod2,
                        style: pBold14.copyWith(fontSize: 13)),
                  ],
                ),
                maxLines: 2,
                softWrap: true,
              ),
            ),
          ],
        ),
        /* verticalSpace(12),
        userDataRowWidget(title: "Price".trr, value: price),
        verticalSpace(12),
        userDataRowWidget(title: "Quantity".trr, value: quantity),*/
        verticalSpace(12),
        userDataRowWidget(title: "Total value".trr, value: totalValue),
        verticalSpace(status == "FOR PAYMENT" ? 14 : 0),
        status == "FOR PAYMENT"
            ? CommonIconBorderButton(
                iconData: DefaultImages.cancelRequestIcn,
                title: "Cancel Order".trr,
                onPressed: cancelPOOnTap,
              )
            : SizedBox()
      ],
    ),
  );
}

Widget orderActionWidget({
  required String code,
  bool isShowCancelOrder = false,
  Function()? printOrder,
  Function()? downloadOrder,
  Function()? cancelOrder,
}) {
  return Container(
    decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
    padding: EdgeInsets.all(16),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "${"Order".trr} #$code ",
          style: pBold20,
        ),
        verticalSpace(21),
        bulkActionWidget(
          title: "Are you sure you want to cancel this order?".trr,
          onTap: cancelOrder!,
        ),
        // bulkActionWidget(
        //   title: "Print order".trr,
        //   onTap: printOrder!,
        // ),
        // bulkActionWidget(
        //   title: "Download order".trr,
        //   onTap: downloadOrder!,
        // ),
        // isShowCancelOrder == true
        //     ? SizedBox()
        //     : bulkActionWidget(
        //         title: "Cancel order".trr,
        //         onTap: cancelOrder!,
        //       ),
      ],
    ),
  );
}
