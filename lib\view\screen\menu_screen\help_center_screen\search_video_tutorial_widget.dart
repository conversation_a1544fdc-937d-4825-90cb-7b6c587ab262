// ignore_for_file: prefer_const_constructors, prefer_const_constructors_in_immutables

import 'dart:io';

import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/core/controller/menu_controller/help_center_controller/help_center_controller.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/screen/menu_screen/purchase_history_screen/search_order_widget.dart';
import 'video_tutorials_screen.dart';


class SearchVideoTutorialWidget extends StatefulWidget {
  SearchVideoTutorialWidget({super.key});

  @override
  State<SearchVideoTutorialWidget> createState() => _SearchVideoTutorialWidgetState();
}

class _SearchVideoTutorialWidgetState extends State<SearchVideoTutorialWidget> {
  HelpCenterController helpCenterController = Get.put(HelpCenterController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    helpCenterController.itemList.clear();
  }

  void filterSearchResults(String query) {
    helpCenterController.itemList.value = helpCenterController.videoList
        .where((item) => item['title'].toLowerCase().contains(query.toLowerCase()))
        .toList();
  }
  _launchURL() async {
    if (Platform.isIOS) {
      if (await canLaunch('https://www.youtube.com')) {
        await launch('https://www.youtube.com', forceSafariVC: false);
      } else {
        if (await canLaunch('https://www.youtube.com')) {
          await launch('https://www.youtube.com');
        } else {
          throw 'Could not launch https://www.youtube.com';
        }
      }
    } else {
      const url = 'https://www.youtube.com';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  }
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        height: Get.height - 60,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        padding: EdgeInsets.all(16),
        child: Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              verticalSpace(16),
              Row(
                children: [
                  Expanded(
                    child: CommonTextField(
                      controller: helpCenterController.searchController.value,
                      labelText: '',
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(12),
                        child: assetSvdImageWidget(image: DefaultImages.searchIcn, width: 24, height: 24),
                      ),
                      hintText: 'Search'.trr,
                      onChanged: (value) {
                        if (value.isEmpty) {
                          helpCenterController.itemList.clear();
                          helpCenterController.itemList.refresh();
                        } else {
                          helpCenterController.searchController.refresh();
                          filterSearchResults(value);
                        }
                      },
                    ),
                  ),
                  helpCenterController.searchController.value.text.isEmpty
                      ? SizedBox()
                      : cancelButton(
                        () {
                     helpCenterController.searchController.value.clear();
                     helpCenterController.searchController.refresh();
                     helpCenterController.itemList.clear();
                     helpCenterController.itemList.refresh();
                    },
                  )
                ],
              ),
              verticalSpace(16),
              helpCenterController.itemList.isEmpty
                  ? Expanded(
                  child: Center(
                      child: Text(
                        "No matches".trr,
                        style: pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                      )))
                  : Expanded(
                child: ListView.builder(
                  itemCount: helpCenterController.itemList.length,
                  shrinkWrap: true,
                  physics: BouncingScrollPhysics(),
                  itemBuilder: (context, index) {
                    var data = helpCenterController.itemList[index];
                               return Padding(
                      padding: const EdgeInsets.only(bottom: 24),
                      child: GestureDetector(
                        onTap: _launchURL,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            imageWidget(data['image']),
                            verticalSpace(14),
                            Text(
                              data['title'].toString().trr,
                              style: pSemiBold16,
                            )
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        }),
      ),
    );
  }
}
