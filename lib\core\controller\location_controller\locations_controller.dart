import 'dart:convert';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:waie_app/models/bankatm_station.dart';
import 'package:waie_app/models/car_rental_station.dart';
import 'package:waie_app/models/car_service_station.dart';
import 'package:waie_app/models/foodres_station.dart';
import 'package:waie_app/models/gas_station.dart';
import 'package:waie_app/models/installation_center.dart';
import 'package:waie_app/models/mosque_station.dart';
import 'package:waie_app/models/sales_station.dart';
import 'package:waie_app/models/stationby_fueltype.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:http/http.dart' as http;
import 'dart:math';

import 'package:waie_app/utils/constants.dart';

class LocationsController extends GetxController {
  bool isLoading = true;
  var currentStationList = <dynamic>[].obs;
  var gasStationList = <GasStationModel>[].obs;
  // List<LatLng> gasStats = [];
  double? userLatitude;
  double? userLongitude;
  double radiusInKm = 200.0;
  List<GasStationModel> gasStationListFiltered = [];
  var allStationsList = <GasStationModel>[].obs;
  var allSalesOfficeStationsList = <SalesStationModel>[].obs;
  var salesStationList = <SalesStationModel>[].obs;
  var instCenterList = <InstallationCenterModel>[].obs;

  var allINstallationList = <InstallationCenterModel>[].obs;
  var allMosqueStationsList = <MosqueStation>[].obs;

  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double R = 6371;
    final double dLat = _degToRad(lat2 - lat1);
    final double dLon = _degToRad(lon2 - lon1);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degToRad(lat1)) *
            cos(_degToRad(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return R * c;
  }

  double _degToRad(double deg) => deg * (pi / 180);

  Future<List<GasStationModel>> getGasStationsDefault(
    double userLatitude,
    double userLongitude,
    double radiusInKm,
    bool isDefault,
  ) async {
    // isLoading = false;
    List<GasStationModel> allStations = [];
    List<Map<String, dynamic>> stationWithDistances = [];
    var client = http.Client();
    currentStationList.clear();
    gasStationList.clear();
    allStationsList.clear();
    print('Fetching gas stations...');

    try {
      var response = await client.post(
        Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnLocation,
        ),
        body: {
          "IsAR": Constants.IsAr_App,
        },
      );
      print('Response status: ${response.statusCode}');
      List result = jsonDecode(response.body);
      print('API result: $result');

      // Process the response
      for (int i = 0; i < result.length; i++) {
        GasStationModel gas =
            GasStationModel.fromJson(result[i] as Map<String, dynamic>);

        if (gas.latitude != "" && gas.longitude != "") {
          double stationLatitude = double.parse(gas.latitude);
          double stationLongitude = double.parse(gas.longitude);

          // Calculate distance
          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);

          allStations.add(gas); // Add to all stations list

          // Add station with its distance for sorting
          stationWithDistances.add({
            'station': gas,
            'distance': distance,
          });
        }
      }

      // Sort stations by distance
      stationWithDistances.sort((a, b) =>
          (a['distance'] as double).compareTo(b['distance'] as double));

      // Take the 20 nearest stations
      List<GasStationModel> nearestStations = stationWithDistances
          .take(20)
          .map((entry) => entry['station'] as GasStationModel)
          .toList();

      // Sort allStations by distance (preserve all entries)
      allStations.sort((a, b) {
        double distanceA = calculateDistance(userLatitude, userLongitude,
            double.parse(a.latitude), double.parse(a.longitude));
        double distanceB = calculateDistance(userLatitude, userLongitude,
            double.parse(b.latitude), double.parse(b.longitude));
        return distanceA.compareTo(distanceB);
      });

      // Update reactive lists
      // gasStationList.value = nearestStations;
      // allStationsList.value = allStations; // Sorted full list
      // currentStationList.value = isDefault ? gasStationList : allStationsList;

      gasStationList.value = allStations;
      allStationsList.value = allStations; // Sorted full list
      currentStationList.value = allStationsList;
      print('All stations count: ${allStationsList.length}');
      print('Nearest stations count: ${gasStationList.length}');
      print('Current station list count: ${currentStationList.length}');
      //isLoading = true;

      return gasStationList;
    } catch (e) {
      print("Error fetching gas stations: $e");
      return [];
    } finally {
      client.close();
    }
  }

  Future<List<SalesStationModel>> getSalesOfficeStationsDefault(
    double userLatitude,
    double userLongitude,
    double radiusInKm,
    bool isDefault,
  ) async {
    //isLoading = false;

    List<SalesStationModel> allStations = [];
    List<Map<String, dynamic>> stationWithDistances = [];
    var client = http.Client();
    currentStationList.clear();
    salesStationList.clear();
    allSalesOfficeStationsList.clear();

    print('Fetching sales office stations...');

    try {
      var response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl +
            ApiEndPoints.authEndpoints.getWAIEStnLocation),
        body: {
          "IsAR": Constants.IsAr_App,
        },
      );

      List result = jsonDecode(response.body);
      print('API result: $result');

      for (int i = 0; i < result.length; i++) {
        SalesStationModel station =
            SalesStationModel.fromJson(result[i] as Map<String, dynamic>);

        // Validate latitude and longitude
        if (station.latitude != null &&
            station.latitude.isNotEmpty &&
            double.tryParse(station.latitude) != null &&
            station.longitude != null &&
            station.longitude.isNotEmpty &&
            double.tryParse(station.longitude) != null) {
          double stationLatitude = double.parse(station.latitude);
          double stationLongitude = double.parse(station.longitude);

          allStations.add(station); // Add to all stations list

          // Calculate distance and prepare for sorting
          double distance = calculateDistance(
            userLatitude,
            userLongitude,
            stationLatitude,
            stationLongitude,
          );

          stationWithDistances.add({
            'station': station,
            'distance': distance,
          });
        } else {
          print(
              'Skipping station due to invalid coordinates: Latitude = ${station.latitude}, Longitude = ${station.longitude}');
        }
      }

      // Sort stations by distance
      stationWithDistances.sort((a, b) =>
          (a['distance'] as double).compareTo(b['distance'] as double));

      allStations = stationWithDistances
          .map((entry) => entry['station'] as SalesStationModel)
          .toList();

      currentStationList.value = allStations;
      allSalesOfficeStationsList.value = allStations;

      print(
          'All sales office stations count: ${allSalesOfficeStationsList.length}');
      print('Current station list count: ${currentStationList.length}');
      //isLoading = true;

      return allStations;
    } catch (e) {
      print("Error fetching sales office stations: $e");
      return [];
    } finally {
      client.close();
    }
  }

  Future<List<InstallationCenterModel>> getInstallationCenterStations(
    double userLatitude,
    double userLongitude,
    double radiusInKm,
  ) async {
    //isLoading = false;

    List<InstallationCenterModel> allStations = [];
    List<Map<String, dynamic>> stationWithDistances = [];
    var client = http.Client();
    currentStationList.clear();
    instCenterList.clear();
    allINstallationList.clear();

    print('Fetching installation center stations...');

    try {
      var response = await client.post(
        Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getCenterLocation,
        ),
        body: {
          "IsAR": Constants.IsAr_App,
        },
      );

      List result = jsonDecode(response.body);
      print('API result: $result');

      for (int i = 0; i < result.length; i++) {
        InstallationCenterModel station =
            InstallationCenterModel.fromJson(result[i] as Map<String, dynamic>);

        if (station.latitude != "" && station.longitude != "") {
          double stationLatitude = double.parse(station.latitude);
          double stationLongitude = double.parse(station.longitude);

          allStations.add(station); // Add to all stations list

          // Calculate distance and prepare for sorting
          double distance = calculateDistance(
            userLatitude,
            userLongitude,
            stationLatitude,
            stationLongitude,
          );

          stationWithDistances.add({
            'station': station,
            'distance': distance,
          });
        }
      }

      // Sort stations by distance
      stationWithDistances.sort((a, b) =>
          (a['distance'] as double).compareTo(b['distance'] as double));

      // Extract sorted list of all stations
      allStations = stationWithDistances
          .map((entry) => entry['station'] as InstallationCenterModel)
          .toList();

      // Update reactive lists
      currentStationList.value = allStations; // Sorted full list
      allINstallationList.value = allStations;

      print(
          'All installation center stations count: ${allINstallationList.length}');
      print('Current station list count: ${currentStationList.length}');
      //isLoading = true;

      return allStations;
    } catch (e) {
      print("Error fetching installation center stations: $e");
      return [];
    } finally {
      client.close();
    }
  }

  var mosquestationsList = <MosqueStation>[].obs;

  Future<List<MosqueStation>> getMosqueStations(
    double userLatitude,
    double userLongitude,
    double radiusInKm,
  ) async {
    // isLoading = false;

    List<MosqueStation> allStations = [];
    List<Map<String, dynamic>> stationWithDistances = [];
    var client = http.Client();
    currentStationList.clear();
    mosquestationsList.clear();
    allMosqueStationsList.clear();

    print('Fetching mosque stations within 200km radius...');

    try {
      var response = await client.get(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnMosque),
      );

      List result = jsonDecode(response.body);
      print('mosque res ${response.statusCode}');
      print('mosque res ${response.body}');

      print('API result Mosque: $result');

      for (int i = 0; i < result.length; i++) {
        MosqueStation station =
            MosqueStation.fromJson(result[i] as Map<String, dynamic>);

        if (station.latitude != "" && station.longitude != "") {
          double stationLatitude = double.parse(station.latitude);
          double stationLongitude = double.parse(station.longitude);

          allStations.add(station); // Add to all stations list

          // Calculate distance and prepare for sorting
          double distance = calculateDistance(
            userLatitude,
            userLongitude,
            stationLatitude,
            stationLongitude,
          );

          stationWithDistances.add({
            'station': station,
            'distance': distance,
          });
        }
      }

      // Sort stations by distance
      stationWithDistances.sort((a, b) =>
          (a['distance'] as double).compareTo(b['distance'] as double));

      // Extract sorted list of all stations
      allStations = stationWithDistances
          .map((entry) => entry['station'] as MosqueStation)
          .toList();

      currentStationList.value = allStations; // Sorted full list
      allMosqueStationsList.value = allStations;

      print(
          'All mosque center stations count: ${allMosqueStationsList.length}');
      print('Current station list count: ${currentStationList.length}');
      // isLoading = true;

      return allStations;
    } catch (e) {
      print("Error fetching mosque stations: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }

  var carServiceAllStationsList = <CarServiceStation>[].obs;
  var carServicestationsList = <CarServiceStation>[].obs;

  Future<List<CarServiceStation>> getCarServiceStations(
      double userLatitude, double userLongitude, double radiusInKm) async {
    //isLoading = false;

    List<CarServiceStation> filteredStations = [];
    List<Map<String, dynamic>> stationWithDistances = [];
    var client = http.Client();
    currentStationList.clear();
    carServicestationsList.clear();
    carServiceAllStationsList.clear();

    print('Fetching car service stations');

    try {
      var response = await client.get(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnCarService),
      );

      List result = jsonDecode(response.body);
      print('API response status: ${response.statusCode}');
      print('API response body: ${response.body}');
      print('Parsed result: $result');

      for (int i = 0; i < result.length; i++) {
        CarServiceStation station =
            CarServiceStation.fromJson(result[i] as Map<String, dynamic>);

        if (station.latitude != "" && station.longitude != "") {
          double stationLatitude = double.parse(station.latitude);
          double stationLongitude = double.parse(station.longitude);

          filteredStations.add(station);

          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);

          stationWithDistances.add({
            'station': station,
            'distance': distance,
          });
        }
      }

      stationWithDistances.sort((a, b) =>
          (a['distance'] as double).compareTo(b['distance'] as double));

      filteredStations = stationWithDistances
          .map((entry) => entry['station'] as CarServiceStation)
          .toList();

      carServicestationsList.value = filteredStations;
      carServiceAllStationsList.value = filteredStations;
      currentStationList.value = filteredStations;

      print('Car service stations count: ${carServicestationsList.length}');
      print('Current station list count: ${currentStationList.length}');
      //isLoading = true;

      return filteredStations;
    } catch (e) {
      print("Error fetching car service stations: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }

  var foodResAllStationsList = <FoodResStation>[].obs;
  var foodresStationsList = <FoodResStation>[].obs;

  Future<List<FoodResStation>> getFoodResStations(
      double userLatitude, double userLongitude, double radiusInKm) async {
    // isLoading = false;

    List<FoodResStation> filteredStations = [];
    List<Map<String, dynamic>> stationWithDistances = [];
    var client = http.Client();
    currentStationList.clear();
    foodresStationsList.clear();
    foodResAllStationsList.clear();

    print('Fetching food restaurants....');

    try {
      var response = await client.get(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnFoodRes),
      );

      List result = jsonDecode(response.body);
      print('API response status: ${response.statusCode}');
      print('API response body: ${response.body}');
      print('Parsed result: $result');

      for (int i = 0; i < result.length; i++) {
        FoodResStation station =
            FoodResStation.fromJson(result[i] as Map<String, dynamic>);

        if (station.latitude != "" && station.longitude != "") {
          double stationLatitude = double.parse(station.latitude);
          double stationLongitude = double.parse(station.longitude);

          filteredStations.add(station);

          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);

          stationWithDistances.add({
            'station': station,
            'distance': distance,
          });
        }
      }

      stationWithDistances.sort((a, b) =>
          (a['distance'] as double).compareTo(b['distance'] as double));

      filteredStations = stationWithDistances
          .map((entry) => entry['station'] as FoodResStation)
          .toList();

      foodresStationsList.value = filteredStations;
      foodResAllStationsList.value = filteredStations;
      currentStationList.value = filteredStations;

      print('Food restaurant stations count: ${foodresStationsList.length}');
      print('Current station list count: ${currentStationList.length}');
      // isLoading = true;

      return filteredStations;
    } catch (e) {
      print("Error fetching food restaurants: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }

  var carRentalAllStationsList = <CarRentalStation>[].obs;
  var carRentalstationsList = <CarRentalStation>[].obs;

  Future<List<CarRentalStation>> getCarRentalStations(
      double userLatitude, double userLongitude, double radiusInKm) async {
    // isLoading = false;

    List<CarRentalStation> filteredStations = [];
    List<Map<String, dynamic>> stationWithDistances = [];
    var client = http.Client();
    currentStationList.clear();
    carRentalstationsList.clear();
    carRentalAllStationsList.clear();

    print('Fetching car rental stations...');

    try {
      var response = await client.get(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnCarRental),
      );

      List result = jsonDecode(response.body);
      print('API response status: ${response.statusCode}');
      print('API response body: ${response.body}');
      print('Parsed result: $result');

      for (int i = 0; i < result.length; i++) {
        CarRentalStation station =
            CarRentalStation.fromJson(result[i] as Map<String, dynamic>);

        if (station.latitude != "" && station.longitude != "") {
          double stationLatitude = double.parse(station.latitude);
          double stationLongitude = double.parse(station.longitude);

          filteredStations.add(station);

          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);

          stationWithDistances.add({
            'station': station,
            'distance': distance,
          });
        }
      }

      stationWithDistances.sort((a, b) =>
          (a['distance'] as double).compareTo(b['distance'] as double));

      filteredStations = stationWithDistances
          .map((entry) => entry['station'] as CarRentalStation)
          .toList();

      carRentalstationsList.value = filteredStations;
      carRentalAllStationsList.value = filteredStations;
      currentStationList.value = filteredStations;

      print('Car rental stations count: ${carRentalstationsList.length}');
      print('Current station list count: ${currentStationList.length}');
      // isLoading = true;

      return filteredStations;
    } catch (e) {
      print("Error fetching car rental stations: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }

  var bankAtmstationsList = <BankAtmStation>[].obs;

  var bankAtmAllStationsList = <BankAtmStation>[].obs;

  Future<List<BankAtmStation>> getBankATMStations(
      double userLatitude, double userLongitude, double radiusInKm) async {
    //isLoading = false;

    List<BankAtmStation> filteredStations = [];
    List<Map<String, dynamic>> stationWithDistances = [];
    var client = http.Client();

    currentStationList.clear();
    bankAtmstationsList.clear();
    bankAtmAllStationsList.clear();

    print('Fetching bank and ATM stations...');

    try {
      var response = await client.get(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getStnBankAtm),
      );

      List result = jsonDecode(response.body);
      print('API response status: ${response.statusCode}');
      print('API response body: ${response.body}');
      print('Parsed result: $result');

      for (int i = 0; i < result.length; i++) {
        BankAtmStation station =
            BankAtmStation.fromJson(result[i] as Map<String, dynamic>);

        if (station.latitude != "" && station.longitude != "") {
          double stationLatitude = double.parse(station.latitude);
          double stationLongitude = double.parse(station.longitude);

          filteredStations.add(station);

          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);

          stationWithDistances.add({
            'station': station,
            'distance': distance,
          });
        }
      }

      stationWithDistances.sort((a, b) =>
          (a['distance'] as double).compareTo(b['distance'] as double));

      filteredStations = stationWithDistances
          .map((entry) => entry['station'] as BankAtmStation)
          .toList();

      bankAtmstationsList.value = filteredStations;
      bankAtmAllStationsList.value = filteredStations;
      currentStationList.value = filteredStations;

      print('Bank ATM stations count: ${bankAtmstationsList.length}');
      print('Current station list count: ${currentStationList.length}');
      //isLoading = true;

      return filteredStations;
    } catch (e) {
      print("Error fetching bank and ATM stations: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }

  var fuelTypeStationList = <StationsByFuelType>[].obs;

  var fuelTypeAllStationsList = <StationsByFuelType>[].obs;

  Future<List<StationsByFuelType>> getStationsByFuelType(String fuelType,
      double userLatitude, double userLongitude, double radiusInKm) async {
    //isLoading = false;

    List<StationsByFuelType> filteredStations = [];
    List<Map<String, dynamic>> stationWithDistances = [];
    var client = http.Client();

    currentStationList.clear();
    fuelTypeStationList.clear();
    fuelTypeAllStationsList.clear();

    print('Fetching Fuel Type stations...');

    try {
      var response = await client.get(
        Uri.parse(ApiEndPoints.baseUrl +
            ApiEndPoints.authEndpoints.getStationsByFuelType +
            "?fuelType=$fuelType"),
      );

      var result = jsonDecode(response.body);
      print('API response status: ${response.statusCode}');
      print('API response body: ${response.body}');
      print('Parsed result: $result');

      for (int i = 0; i < result.length; i++) {
        StationsByFuelType station =
            StationsByFuelType.fromJson(result[i] as Map<String, dynamic>);

        if (station.latitude != "" && station.longitude != "") {
          double stationLatitude = double.parse(station.latitude);
          double stationLongitude = double.parse(station.longitude);

          filteredStations.add(station);

          double distance = calculateDistance(
              userLatitude, userLongitude, stationLatitude, stationLongitude);

          stationWithDistances.add({
            'station': station,
            'distance': distance,
          });
        }
      }

      stationWithDistances.sort((a, b) =>
          (a['distance'] as double).compareTo(b['distance'] as double));

      filteredStations = stationWithDistances
          .map((entry) => entry['station'] as StationsByFuelType)
          .toList();

      fuelTypeStationList.value = filteredStations;
      fuelTypeAllStationsList.value = filteredStations;
      currentStationList.value = filteredStations;

      print('Fuel Type stations count: ${fuelTypeStationList.length}');
      print('Current station list count: ${currentStationList.length}');
      // isLoading = true;

      return filteredStations;
    } catch (e) {
      print("Error fetching fuel type stations: ${e.toString()}");
      return [];
    } finally {
      client.close();
    }
  }
}
