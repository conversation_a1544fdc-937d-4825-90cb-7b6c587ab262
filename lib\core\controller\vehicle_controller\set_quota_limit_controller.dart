import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:quickalert/quickalert.dart';

import '../../../utils/colors.dart';
import '../../../utils/constants.dart';
import '../../../utils/text_style.dart';
import '../../../view/widget/common_button.dart';
import '../../../view/widget/common_space_divider_widget.dart';
import '../../../view/widget/loading_widget.dart';

class SetQuotaLimitController extends GetxController {
  VehicleController vehicleController = Get.put(VehicleController());
  GetStorage userStorage = GetStorage('User');
  final vehicle = GetStorage();
  final isPinblock = GetStorage();
  final isDCBlock = GetStorage();
  TextEditingController quotaValueController = TextEditingController();

  setQuotaLimits(serialid, quotaClass, quotaType) async {
    // showDialog(
    //     context: Get.context!,
    //     builder: (context) {
    //       return const Center(
    //         child: CircularProgressIndicator(),
    //       );
    //     });
    Loader.showLoader();
    print("serialid>>>>>>> $serialid");
    print("quotaClass>>>>>>> $quotaClass");
    print("quotaType>>>>>>> $quotaType");
    print("quotaValueController.text>>>>>>> ${quotaValueController.text}");

    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");
    var client = http.Client();

    try {
      print("qouta IN");
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.updateQuotaLimits),
          body: {
            "serialid": serialid,
            "custid": custid, //"000003944"
            "emailid": emailid, //"<EMAIL>",
            "quotaValue": quotaValueController.text,
            "quotaType": quotaType,
            "quotaClass": quotaClass,
          });
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      if (response.statusCode == 200) {
        Loader.hideLoader();
        vehicle.remove('vehicleID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('vehicleSerialID');
        vehicle.remove('complaintJobID');
        isPinblock.remove('isPinActivate');
        isDCBlock.remove('isdcBlock');
        vehicleController.selectedSerialList.clear();
        vehicleController.selectedVehicleList.clear();
        vehicleController.selectedFleetList.clear();
        vehicleController.filterValueList.refresh();
        vehicleController.selectedVehicleList.refresh();
        vehicleController.selectedSerialList.refresh();
        vehicleController.selectedFleetList.refresh();
        quotaValueController.clear();
        // Get.offAll(
        //   () => DashBoardManagerScreen(
        //     currantIndex: 0,
        //   ),
        //   //preventDuplicates: false,
        // );
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.success,
          text: 'Quota limit has been updated!',
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
      } else {
        Loader.hideLoader();
        print('Failed');
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          title: 'Error',
          text: jsonDecode(response.body)["ExceptionMessage"],
        );
        Get.offAll(
          () => DashBoardManagerScreen(
            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
          ),
          //preventDuplicates: false,
        );
        // showDialog(
        //   barrierDismissible: false,
        //   context: Get.context!,
        //   builder: (context) {
        //     return AlertDialog(
        //       insetPadding: const EdgeInsets.all(16),
        //       contentPadding: const EdgeInsets.all(24),
        //       shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12)),
        //       content: Column(
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           Text(
        //             jsonDecode(response.body)["ExceptionMessage"],
        //             style: pBold20,
        //             textAlign: TextAlign.center,
        //           ),
        //           verticalSpace(24),
        //           CommonButton(
        //             title: "OK".tr,
        //             onPressed: () async {
        //               Get.back();
        //             },
        //             btnColor: AppColor.themeOrangeColor,
        //           )
        //         ],
        //       ),
        //     );
        //   },
        // );
      }
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
