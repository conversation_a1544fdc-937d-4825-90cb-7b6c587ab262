// ignore_for_file: prefer_const_constructors, avoid_print, deprecated_member_use, prefer_interpolation_to_compose_strings

import 'dart:convert';
import 'dart:developer';
import 'dart:math';
import 'dart:ui';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:local_auth/local_auth.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:waie_app/core/controller/home_controller/balance_controller.dart';
import 'package:waie_app/core/controller/home_controller/home_controller.dart';
import 'package:waie_app/core/controller/splash_controller/splash_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/regDB.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/screen/home_screen/home_screen_widget.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/new_order_screen.dart';
import 'package:waie_app/view/screen/menu_screen/profile_screen/profile_screen.dart';
import 'package:waie_app/view/screen/menu_screen/promotions_screen/promotions_screen.dart';
import 'package:waie_app/view/screen/notification_screen/notification_screen.dart';
import 'package:waie_app/view/screen/social_responsbility/social_responsibility.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/utils/logger_extention/logger_helper.dart';
import '../../../Services/Notification_Services/notification_services.dart';
import '../../../core/controller/menu_controller/usermenucontroller.dart';
import '../../../main.dart';
import '../../../models/profile.dart';
import '../../../utils/api_endpoints.dart';
import '../../../utils/constant.dart';
import '../../widget/common_button.dart';
import '../../widget/loading_widget.dart';
import '../dashboard_manager/dashboard_manager.dart';
import '../../../core/controller/dashboard_manager_controller/dashboard_manager_controller.dart';
import '../vehicles_screen/tag_installation/book_tag_installation_screen.dart';
import 'package:http/http.dart' as http;
import 'dart:io';
import 'dart:io' show Platform;

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

enum SupportState {
  unknown,
  supported,
  unSupported,
}

double roundNumber(double value, int places) {
  num val = pow(10.0, places);
  return ((value * val).round().toDouble() / val);
}

class _HomeScreenState extends State<HomeScreen> {
  HomeController homeController = Get.put(HomeController());

  DashboardManagerController dashboardManagerController =
      Get.put(DashboardManagerController());

  BalanceController balanceController = Get.put(BalanceController());
  UserMenuController menuController = Get.put(UserMenuController());
  SplashController splashController = Get.put(SplashController());

  String languageCode = myStorage!.getString(AppConstant.LANGUAGE_CODE) ?? 'ar';
  String countryCode = myStorage!.getString(AppConstant.COUNTRY_CODE) ?? 'Ar';
  final LocalAuthentication auth = LocalAuthentication();
  SupportState supportState = SupportState.unknown;
  List<BiometricType>? availableBiometrics;
  static const platform = MethodChannel('flutter.native.helper');
  String deviceId = 'Unknown';
  String? deviceToken;
  DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  RegisterDatabase db = RegisterDatabase();
  // reference the hive register box
  final _isReg = Hive.box('isReg_DB');

  //Locale locale = Locale(languageCode, countryCode);

  Map mapResponse = {};
  Map dataResponse = {};

  //List listResponse = {} as List;

  String userid = "";

  String currentBalance = "0";
  String homeLogo = "";

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _loadHomeImage();
    auth.isDeviceSupported().then(
          (bool isSupported) => setState(
            () => supportState =
                isSupported ? SupportState.supported : SupportState.unSupported,
          ),
        );

    print("===============================================================");
    print("_isReg");
    //check if there is no current register userinfo, then it is the 1st time ever opening the app
    //then let user register login info
    if (_isReg.get("regUser") == null) {
      //db.createUserInfoData();
      // showDialog(
      //   context: context,
      //   builder: (BuildContext context) {
      //     return ActivationDialog(); // passing value here
      //   },
      // );
    } else {
      db.loadData();
    }
    //callOrderInit();
    //newOrderController.fetchPriceTagCards();
    /*  Future.microtask(() {
      fetchSlideImages();
    });*/
    homeController.aldreesNewsList.clear();
    homeController.aldreesNewsList.refresh();

    NotificationServices notificationServices = NotificationServices();
    //notificationServices.requestNotification();
    notificationServices.foreGroundMessage();
    notificationServices.firebaseInit(context);
    notificationServices.setupInteractMessage();
    notificationServices.getDeviceToken().then((value) async {
      deviceToken = value;
      print('Device TOKEN $deviceToken');
      if (Platform.isAndroid) {
        getDeviceId();
      } else {
        getDeviceIdIos();
      }

      //  await saveDeviceToken('Fuzail', value);
    });
    homeController.innerPageController = PageController();
  }

  Future<void> _loadHomeImage() async {
    String path = await splashController.loadImage('MOBLOGOIMGS');
    setState(() {
      homeLogo = path;
      logInfo(homeLogo);
    });
  }

  Future<void> getDeviceIdIos() async {
    IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    print('Running on ${iosInfo.identifierForVendor}');
    deviceId = iosInfo.identifierForVendor!;
    logInfo("ios device id $deviceId");

    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var username = sharedUser2.getString('username');

    logInfo("username get from shared pref $username");
    try {
      logInfo('device id $deviceId');
      saveDeviceToken(
          userId: username.toString(),
          deviceToken: deviceToken!,
          deviceId: deviceId);
    } on PlatformException catch (e) {
      deviceId = "Failed to get device ID: '${e.message}'.";
    }

    // setState(() {
    //   _deviceId = deviceId;
    // });
  }

  Future<void> getDeviceId() async {
    print('come here method channel');
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var username = sharedUser2.getString('username');
    var deviceIdpref = sharedUser2.getString('deviceid');
    var deviceTokenpref = sharedUser2.getString('devicetoken');

    logInfo("username get from shared pref $username");
    logInfo("deviceid get from shared pref $deviceIdpref");
    logInfo("device token get from shared pref $deviceTokenpref");

    try {
      final String result = await platform.invokeMethod('getAndroidId');
      deviceId = result;
      logInfo('device id $deviceId');
      saveDeviceToken(
          userId: username.toString(),
          deviceToken: deviceToken!,
          deviceId: deviceId);
    } on PlatformException catch (e) {
      deviceId = "Failed to get device ID: '${e.message}'.";
    }

    // setState(() {
    //   _deviceId = deviceId;
    // });
  }

  Future<void> checkOldAppTerminate() async {
    var onResponse = await splashController.checkOldAppTerminate();
    print("onResponse");
    print(onResponse);

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    // if (onResponse[0]["RESULT"] == "MANDATORY") {
    //   _launchURL();
    // }
    if (onResponse[0]["RESULT"] == "TERMINATED") {
      var terminateMsg = onResponse[0]["REMARKS"];
      var terminateLink = onResponse[0]["LINK"];
      await showDialog(
        barrierDismissible: true,
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(16),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            titlePadding: EdgeInsets.zero,
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  alignment: FractionalOffset.topRight,
                  child: IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.clear),
                  ),
                ),
                Text(
                  "NOTICE".trr,
                  style: pBold20.copyWith(
                    color: AppColor.cRedText,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // verticalSpace(14),
                // Text("APP TERMINATED".trr,
                //     style: pBold20.copyWith(
                //       color: AppColor.cRedText,
                //     )),
                // verticalSpace(24),
                Text(
                  "$terminateMsg".trr,
                  style: pRegular16,
                  textAlign: TextAlign.center,
                ),
                verticalSpace(24),
                CommonButton(
                  title: "Download now".trr,
                  onPressed: () {
                    if (Platform.isIOS) {
                      _launchNewURL(terminateLink);
                    } else {
                      _launchNewURL(terminateLink);
                    }
                  },
                  btnColor: AppColor.themeOrangeColor,
                )
              ],
            ),
          );
        },
      );
    }
  }

  _launchNewURL(terminateLink) async {
    if (Platform.isIOS) {
      //splashController.updateURLIOS;
      if (await canLaunch("$terminateLink")) {
        await launch("$terminateLink");
      } else {
        throw 'Could not launch ${"$terminateLink"}';
      }
    } else {
      //splashController.updateURLANDROID;
      if (await canLaunch("$terminateLink")) {
        await launch("$terminateLink");
      } else {
        throw 'Could not launch ${"$terminateLink"}';
      }
    }
  }

  Future<void> saveDeviceToken(
      {required String userId,
      required String deviceToken,
      required String deviceId}) async {
    logInfo('save device info');
    logInfo(userId);
    logInfo(deviceToken);
    logInfo(deviceId);
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    String url =
        ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.saveDeviceToken;
    //const url = 'https://devinttest.aldrees.com/api/SAVEDEVICETOKEN/';

    final response = await http.post(
      Uri.parse(url),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: jsonEncode(<String, String>{
        'USERID': userId,
        'DEVICETOKEN': deviceToken,
        'DEVICEID': deviceId,
      }),
    );
    logInfo(
        'body username devicetoken device id ${userId + " " + deviceToken + " " + deviceId}');
    logInfo('response token ${response.body}');
    logInfo('response token ${response.statusCode}');

    if (response.statusCode == 200) {
      logInfo("Device token saved successfully");

      sharedUser2.setString('deviceid', deviceId);
      sharedUser2.setString('devicetoken', deviceToken);
    } else {
      print('Failed to save device token');
    }
  }

  authCheck() async {
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    if (_isReg.get("regUser") == null) {
      final regUser = _isReg.get('regUser');
      print('regUser is $regUser years old.');
      print('username is ${regUser?['username']}');
      print('password is ${regUser?['password']}');
      print('status is ${regUser?['status']}');
      print("userData['USERNAME'] >>> ${userData.auUsers!.username}");
      // if (userData.auUsers!.username == regUser?['username']) {
      //   setState(() {
      //     privacySecurityController.isMatch.value = true;
      //     privacySecurityController.isAuth.value =
      //         regUser?['status'] == "Y" ? true : false;
      //   });
      // }
    }
  }

  Future<void> authenticateWithBiometrics() async {
    try {
      final authenticated = await auth.authenticate(
        localizedReason: 'Authenticate with fingerprint or Face ID',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
          useErrorDialogs: true,
        ),
      );

      if (!mounted) {
        return;
      }

      if (authenticated) {
        // Navigator.push(
        //     context, MaterialPageRoute(builder: (context) => const Home()));

        final regUser = _isReg.get('regUser');
        print('List is $regUser');
        print('username is ${regUser?['username']}');
        print('password is ${regUser?['password']}');
        print('status is ${regUser?['status']}');
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return CheckPasswordDialog(); // passing value here
          },
        );
        // login(regUser?['username'], regUser?['password']);
      }
    } on PlatformException catch (e) {
      print(e);
      return;
    }
  }

  Future<List> fetchSlideImages() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var regType = sharedUser2.getString('regType');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getImageSlide),
          body: {
            "regType": regType,
            "custID": userid,
          });

      print("Image Slide Respose=====" + response.body);
      // Map mapResponse = jsonDecode(response.body);
      List<dynamic> mapResponse = jsonDecode(response.body);
      return mapResponse;
    } catch (e) {
      logInfo("Error in Slide webservice======" + e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<dynamic> fetchStationNewsSlide() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ' + base64Encode(utf8.encode('$username:$password'));

    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getGasStnNews),
          headers: <String, String>{
            'authorization': basicAuth,
            "Access-Control-Allow-Credentials": 'true',
            "Access-Control-Allow-Headers":
                "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS"
          },
          body: {
            "isAr": 'false'
          });
      print("New STN Slide Result=========" + response.body);
      List<dynamic> mapResponse = jsonDecode(response.body);
      return mapResponse;
    } catch (e) {
      logInfo("Error in Slide webservice======" + e.toString());
      return [];
    } finally {
      //     // Then finally destroy the client.
      client.close();
    }
  }

  void _showUserInfoBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      backgroundColor: Colors.transparent,
      builder: (BuildContext bc) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          padding: const EdgeInsets.only(top: 20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: const [
                Color(0xffC5CBD5),
                Color(0xffC5CBD5),
              ],
            ),
          ),
          child: Column(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Info".trr,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Text(
                        "Close".trr,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Flexible(
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(30)),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                    child: Container(
                      width: 327,
                      height: 550, // Adjust height if needed
                      padding: const EdgeInsets.all(20),
                      margin: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 20),
                      decoration: BoxDecoration(
                        color: const Color(0xffF6F6F6).withOpacity(0.15),
                        border: Border.all(
                          width: 1,
                          color: const Color(0xffF6F6F6).withOpacity(0.5),
                        ),
                        borderRadius: BorderRadius.circular(40),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Center(
                            child: CircleAvatar(
                              radius: 40,
                              backgroundColor: Color(0xffFC6423),
                              child: Text(
                                dashboardManagerController.userName
                                        .toString()
                                        .isNotEmpty
                                    ? dashboardManagerController.userName
                                        .toString()[0]
                                        .toUpperCase()
                                    : "?",
                                style: TextStyle(
                                  fontSize: 40,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 15),
                          Center(
                            child: Text(
                              dashboardManagerController.userName.toString(),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.black.withOpacity(0.8),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(height: 10),
                          Divider(
                              color: Color.fromARGB(255, 114, 114, 114)
                                  .withOpacity(0.5)),
                          SizedBox(height: 10),
                          buildUserDetail("Full Name".trr,
                              dashboardManagerController.userName.toString()),
                          Divider(
                              color: Color.fromARGB(255, 114, 114, 114)
                                  .withOpacity(0.5)),
                          SizedBox(height: 10),
                          buildUserDetail("User ID".trr,
                              dashboardManagerController.userID.toString()),
                          Divider(
                              color: Color.fromARGB(255, 114, 114, 114)
                                  .withOpacity(0.5)),
                          SizedBox(height: 10),
                          buildUserDetail("Email".trr,
                              dashboardManagerController.emailID.toString()),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget buildUserDetail(String title, String content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12,
              color: Colors.black.withOpacity(0.5),
            ),
          ),
          SizedBox(height: 5),
          Text(
            content,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black.withOpacity(0.9),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: RefreshIndicator(
        color: AppColor.themeOrangeColor,
        onRefresh: () async {
          await balanceController.fetchBalances();
        },
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: Obx(() {
            print(homeController.stationIndex.value);
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 15),
                  child: Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            GestureDetector(
                              onTap: () {
                                _showUserInfoBottomSheet(context);
                              },
                              child: SizedBox(
                                child: Wrap(
                                  children: [
                                    Container(
                                      height: 48,
                                      width: 48,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                            color: const Color(0xff4EBBD3)),
                                        color: const Color(0xffF5F9F9),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                          child: Text(
                                        dashboardManagerController.userName
                                                .toString()
                                                .isNotEmpty
                                            ? dashboardManagerController
                                                .userName
                                                .toString()[0]
                                                .toUpperCase()
                                            : "?",
                                      )),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 5,
                            ),
                            GestureDetector(
                              onTap: () {
                                _showUserInfoBottomSheet(context);
                              },
                              child: Text(
                                dashboardManagerController.userName.toString(),
                                style: pBold18.copyWith(
                                  color: AppColor.cOrangeFont,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Center(
                        child: homeLogo.isNotEmpty
                            ? Image.network(
                                homeLogo,
                                height: 35,
                                fit: BoxFit.cover,
                              )
                            : Image.asset(
                                'asset/image/image/logotransparent.png',
                                height: 35,
                                fit: BoxFit.cover,
                              ),
                      ),
                      Align(
                        alignment: Get.locale?.languageCode == 'ar'
                            ? Alignment.topLeft
                            : Alignment.topRight,
                        child: GestureDetector(
                          onTap: () {
                            Get.to(() => NotificationScreen());
                          },
                          child: SizedBox(
                              height: 44,
                              width: 44,
                              child: totalNotificationWidget(
                                totalNotification: Constants.notifCount,
                              )),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 16, right: 16, top: 5),
                  child: Column(
                    children: [
                      //     Container(
                      //       height: 140,
                      //       //width: double.infinity,
                      //       padding: EdgeInsets.only(left: 10, top: 10),
                      //       decoration: BoxDecoration(
                      //         //color: Colors.black,
                      //     image: DecorationImage(
                      //   image: AssetImage('asset/image/image/011.jpg'),
                      //   fit: BoxFit.fill,
                      // ),
                      //       ),
                      //       child: Row(
                      //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //         crossAxisAlignment: CrossAxisAlignment.start,
                      //         children: [
                      //           Column(
                      //             crossAxisAlignment: CrossAxisAlignment.start,
                      //             children: [
                      //               GestureDetector(
                      //                 onTap: () {
                      //                   _showUserInfoBottomSheet(context);
                      //                 },
                      //                 child: SizedBox(
                      //                   child: Wrap(
                      //                     children: [
                      //                       Container(
                      //                         height: 48,
                      //                         width: 48,
                      //                         decoration: BoxDecoration(
                      //                           border: Border.all(
                      //                               color: const Color(0xff4EBBD3)),
                      //                           color: const Color(0xffF5F9F9),
                      //                           shape: BoxShape.circle,
                      //                         ),
                      //                         child: Center(
                      //                             child: Text(
                      //                           dashboardManagerController.userName
                      //                                   .toString()
                      //                                   .isNotEmpty
                      //                               ? dashboardManagerController.userName
                      //                                   .toString()[0]
                      //                                   .toUpperCase()
                      //                               : "?",
                      //                         )),
                      //                       ),
                      //                     ],
                      //                   ),
                      //                 ),
                      //               ),
                      //               SizedBox(
                      //                 width: 15,
                      //               ),
                      //               GestureDetector(
                      //                 onTap: () {
                      //                   _showUserInfoBottomSheet(context);
                      //                 },
                      //                 child: Text(
                      //                   dashboardManagerController.userName.toString(),
                      //                   style: pBold18.copyWith(
                      //                     color: AppColor.cOrangeFont,
                      //                     fontSize: 18,
                      //                   ),
                      //                 ),
                      //               ),
                      //             ],
                      //           ),
                      //           Padding(
                      //             padding: const EdgeInsets.only(top: 10),
                      //             child: GestureDetector(
                      //               onTap: () {
                      //                 Get.to(() => NotificationScreen());
                      //               },
                      //               child: SizedBox(
                      //                   height: 44,
                      //                   width: 44,
                      //                   child: totalNotificationWidget(
                      //                     totalNotification: Constants.notifCount,
                      //                   )),
                      //             ),
                      //           ),
                      //         ],
                      //       ),

                      //     ),

                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Center(
                          //   child: Image.asset(
                          //     'asset/image/image/loginnationaldaylogo.jpg',
                          //     height: 35,
                          //     fit: BoxFit.cover,
                          //   ),
                          // ),

                          // Center(
                          //   child: homeLogo.isNotEmpty
                          //       ? Image.network(
                          //           homeLogo,
                          //           height: 35,
                          //           fit: BoxFit.cover,
                          //         )
                          //       : Image.asset(
                          //           'asset/image/image/logotransparent.png',
                          //           height: 35,
                          //           fit: BoxFit.cover,
                          //         ),
                          // ),

                          // profileWidget(
                          //   name: dashboardManagerController.userName.toString(),
                          //   //"Mohamed",
                          //   userId: dashboardManagerController.userID.toString(),
                          //   //'1-222-A23523',
                          //   emailID: dashboardManagerController.emailID.toString(),
                          //   notification: Constants.notifCount,
                          //   notificationTap: () {
                          //     Get.to(() => NotificationScreen());
                          //   },
                          // ),
                          // homeController.isShowProfile.value
                          Constants.isNewUser
                              ? Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(
                                      color: AppColor.cLightGrey,
                                    ),
                                  ),
                                  padding: EdgeInsets.zero,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // verticalSpace(30),
                                      ClipRRect(
                                        borderRadius: BorderRadius.vertical(
                                            top: Radius.circular(6)),
                                        child: FittedBox(
                                            child: StepProgressIndicator(
                                          totalSteps: 6,
                                          currentStep:
                                              homeController.profileStep.value,
                                          selectedColor:
                                              AppColor.themeOrangeColor,
                                          unselectedColor: AppColor.cLightGrey,
                                          padding: 2,
                                          size: 8,
                                          fallbackLength: 500,
                                        )),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: 16, right: 16, left: 16),
                                        child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              Text(
                                                '${(6 - homeController.profileStep.value)} ' +
                                                    "steps to complete".trr,
                                                style: pRegular12,
                                              ),
                                            ]),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Text(
                                          homeController.profileStepList[
                                                  homeController
                                                          .profileStep.value -
                                                      1]['title']
                                              .toString()
                                              .trr,
                                          style: pSemiBold17,
                                        ),
                                      ),
                                      Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          color: AppColor.cLightOrange,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                          border: Border.all(
                                            color: AppColor.cLightGrey,
                                          ),
                                        ),
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              homeController.profileStepList[
                                                      homeController.profileStep
                                                              .value -
                                                          1]['sub_title']
                                                  .toString()
                                                  .trr,
                                              style: pRegular10.copyWith(
                                                  fontSize: 11),
                                            ),
                                            verticalSpace(16),
                                            CommonButton(
                                              title: homeController
                                                  .profileStepList[
                                                      homeController.profileStep
                                                              .value -
                                                          1]['btn_name']
                                                  .toString()
                                                  .trr,
                                              onPressed: () {
                                                if (homeController
                                                        .profileStep.value !=
                                                    5) {
                                                  homeController
                                                      .profileStep.value++;
                                                } else {
                                                  homeController.isShowProfile
                                                      .value = false;
                                                }
                                                // if (homeController.profileStep.value == 4) {
                                                //   Get.to(homeController.profileStepList[homeController.profileStep.value - 1]['page']);
                                                // } else {
                                                //   Get.offAll(homeController.profileStepList[homeController.profileStep.value - 1]['page']);
                                                // }
                                              },
                                              width: 160,
                                              height: 32,
                                              fontSize: 16,
                                              horizontalPadding: 0,
                                              bColor: AppColor.themeOrangeColor,
                                              btnColor:
                                                  AppColor.themeOrangeColor,
                                            )
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : SizedBox(),
                          // if (Constants.TopUpBtn == "Y")
                          //   Padding(
                          //     padding: const EdgeInsets.only(top: 20, bottom: 16),
                          //     child: Text(
                          //       "Top up balance".trr,
                          //       style: pSemiBold17.copyWith(color: AppColor.cLabel),
                          //     ),
                          //   ),
                          SizedBox(
                            height: 5,
                          ),
                          Obx(
                            () => balanceController.balances.isNotEmpty
                                ? Column(
                                    children: [
                                      // ListView.builder(
                                      //     scrollDirection: Axis.vertical,
                                      //     shrinkWrap: true,
                                      //     itemCount: balanceController.balances.length,
                                      //     itemBuilder: (context, index) {
                                      //       final data = balanceController.balances
                                      //           .elementAt(index);
                                      //       //currentBalance = int.parse(data.currentbalance);
                                      //       currentBalance = data.currentbalance;
                                      //       print(
                                      //           "currentBalance >>>>>>> $currentBalance");
                                      //       return Column(
                                      //         crossAxisAlignment:
                                      //             CrossAxisAlignment.start,
                                      //         // Adjust alignment as needed
                                      //         children: [
                                      //           Padding(
                                      //             padding:
                                      //                 const EdgeInsets.only(top: 8.0),
                                      //             // Adjust padding as needed
                                      //             child: Row(
                                      //               children: [
                                      //                 SizedBox(width: 8),
                                      //                 // Adjust based on your alignment needs
                                      //                 balanceWidget(
                                      //                   title: "Current balance".trr,
                                      //                   balance: data.currentbalance ==
                                      //                           "-9999999"
                                      //                       ? "Not Available"
                                      //                       : (data.currentbalance ??
                                      //                           "0.00"),
                                      //                   // balance: "100000000000000",
                                      //                   srs: "SAR".trr,
                                      //                 ),
                                      //               ],
                                      //             ),
                                      //           ),
                                      //           if (Constants.UnalocAmtView == 'Y' &&
                                      //               Constants.ETrans == "Y")
                                      //             Padding(
                                      //               padding:
                                      //                   const EdgeInsets.only(top: 8.0),
                                      //               // Adjust padding as needed
                                      //               child: Row(
                                      //                 children: [
                                      //                   SizedBox(width: 8),
                                      //                   // Adjust based on your alignment needs
                                      //                   balanceWidget(
                                      //                     title: "Unallocated".trr,
                                      //                     balance:
                                      //                         data.unallocatbalance ??
                                      //                             "0.00",
                                      //                     srs: "SAR".trr,
                                      //                   ),
                                      //                 ],
                                      //               ),
                                      //             ),
                                      //           if (Constants.UnalocAmtView == 'Y' &&
                                      //               Constants.isRSRVDView == "Y")
                                      //             Padding(
                                      //               padding:
                                      //                   const EdgeInsets.only(top: 8.0),
                                      //               // Adjust padding as needed
                                      //               child: Row(
                                      //                 children: [
                                      //                   SizedBox(width: 8),
                                      //                   // Adjust based on your alignment needs
                                      //                   balanceWidget(
                                      //                     title: "Reserved".trr,
                                      //                     balance: data.reservebalance ??
                                      //                         "0.00",
                                      //                     srs: "SAR".trr,
                                      //                   ),
                                      //                 ],
                                      //               ),
                                      //             ),
                                      //         ],
                                      //       );
                                      //     }),
                                      SizedBox(
                                        height: 135,
                                        child: Stack(
                                          alignment: Alignment.center,
                                          children: [
                                            PageView.builder(
                                              controller: homeController
                                                  .outerPageController,
                                              scrollDirection: Axis.horizontal,
                                              itemCount: balanceController
                                                  .balances.length,
                                              onPageChanged: (index) {
                                                homeController.currentCardIndex
                                                    .value = index;
                                              },
                                              itemBuilder: (context, index) {
                                                final data = balanceController
                                                    .balances
                                                    .elementAt(index);

                                                List<Widget> cards = [];

                                                cards.add(balanceWidget(
                                                  title: "Current balance".trr,
                                                  balance: data
                                                              .currentbalance ==
                                                          "-9999999"
                                                      ? "Not Available"
                                                      : (data.currentbalance ??
                                                          "0.00"),
                                                  srs: "SAR".trr,
                                                ));

                                                if (Constants.UnalocAmtView ==
                                                        'Y' &&
                                                    Constants.ETrans == "Y") {
                                                  cards.add(balanceWidget(
                                                    title: "Unallocated".trr,
                                                    balance:
                                                        data.unallocatbalance ??
                                                            "0.00",
                                                    srs: "SAR".trr,
                                                  ));
                                                }

                                                if (Constants.UnalocAmtView ==
                                                        'Y' &&
                                                    Constants.isRSRVDView ==
                                                        "Y") {
                                                  cards.add(balanceWidget(
                                                    title: "Reserved".trr,
                                                    balance:
                                                        data.reservebalance ??
                                                            "0.00",
                                                    srs: "SAR".trr,
                                                  ));
                                                }

                                                return Stack(
                                                  alignment: Alignment.center,
                                                  children: [
                                                    PageView.builder(
                                                      controller: homeController
                                                          .innerPageController,
                                                      scrollDirection:
                                                          Axis.horizontal,
                                                      itemCount: cards.length,
                                                      onPageChanged:
                                                          (cardIndex) {
                                                        homeController
                                                            .currentCardIndex
                                                            .value = cardIndex;
                                                      },
                                                      itemBuilder:
                                                          (context, cardIndex) {
                                                        return SizedBox(
                                                          width: 150,
                                                          child:
                                                              cards[cardIndex],
                                                        );
                                                      },
                                                    ),
                                                    Positioned(
                                                      left: 0,
                                                      child: IconButton(
                                                        icon: Icon(Get.locale
                                                                    ?.languageCode ==
                                                                'ar'
                                                            ? Icons
                                                                .arrow_forward
                                                            : Icons.arrow_back),
                                                        onPressed: () =>
                                                            homeController
                                                                .scrollLeft(cards
                                                                    .length),
                                                      ),
                                                    ),
                                                    Positioned(
                                                      right: 0,
                                                      child: IconButton(
                                                        icon: Icon(Get.locale
                                                                    ?.languageCode ==
                                                                'ar'
                                                            ? Icons.arrow_back
                                                            : Icons
                                                                .arrow_forward),
                                                        onPressed: () =>
                                                            homeController
                                                                .scrollRight(
                                                                    cards
                                                                        .length),
                                                      ),
                                                    ),
                                                    Positioned(
                                                      bottom: 12,
                                                      child: Obx(
                                                        () => Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children:
                                                              List.generate(
                                                            cards.length,
                                                            (dotIndex) =>
                                                                Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          4.0),
                                                              child: SvgPicture
                                                                  .asset(
                                                                homeController
                                                                            .currentCardIndex
                                                                            .value ==
                                                                        dotIndex
                                                                    ? 'asset/image/image/dot_indicator_selected.svg'
                                                                    : 'asset/image/image/dot_indicator_unselected.svg',
                                                                width: 12,
                                                                height: 12,
                                                                color: Colors
                                                                    .black45,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                      // if (Constants.TopUpBtn == 'Y') verticalSpace(16),
                                      // if (Constants.TopUpBtn == "Y")
                                      //   CommonButton(
                                      //     title: 'TopUp Balance'.trr,
                                      //     onPressed: () {
                                      //       if (Constants.hasCloseAccountRequest == 'Y') {
                                      //         ScaffoldMessenger.of(context).showSnackBar(
                                      //           SnackBar(
                                      //             backgroundColor: AppColor.cLiteYellow,
                                      //             content: Text(
                                      //               "Your account has pending closing request, it cannot use any facility at this moment.",
                                      //               style: pBold14.copyWith(
                                      //                   color: AppColor.cBlackFont),
                                      //             ),
                                      //           ),
                                      //         );
                                      //       } else if (Constants.custAcctType == "C" &&
                                      //           Constants.custAcctStatus != "A") {
                                      //         Get.to(() => ProfileScreen());
                                      //       } else {
                                      //         Get.offAll(() => DashBoardManagerScreen(
                                      //               currantIndex: 1,
                                      //             ));
                                      //       }
                                      //     },
                                      //     btnColor: AppColor.themeOrangeColor,
                                      //     width: double.infinity,
                                      //   ),
                                      verticalSpace(20),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          // Text(
                                          //   "Tags & Cards".trr,
                                          //   style: pSemiBold17,
                                          // ),

                                          if (Constants.TopUpBtn == "Y")
                                            Builder(
                                              builder: (context) {
                                                return Obx(
                                                  () => balanceController
                                                          .statusCounts
                                                          .isNotEmpty
                                                      ? CommonIconButton(
                                                          title: "TopUp Balance"
                                                              .trr,
                                                          iconData: DefaultImages
                                                              .newBalanceIcn,
                                                          iconColor:
                                                              Colors.white,

                                                          onPressed: () {
                                                            if (Constants
                                                                    .hasCloseAccountRequest ==
                                                                'Y') {
                                                              ScaffoldMessenger
                                                                      .of(context)
                                                                  .showSnackBar(
                                                                SnackBar(
                                                                  backgroundColor:
                                                                      AppColor
                                                                          .cLiteYellow,
                                                                  content: Text(
                                                                    "Your account has pending closing request, it cannot use any facility at this moment.",
                                                                    style: pBold14
                                                                        .copyWith(
                                                                            color:
                                                                                AppColor.cBlackFont),
                                                                  ),
                                                                ),
                                                              );
                                                            } else if (Constants
                                                                        .custAcctType ==
                                                                    "C" &&
                                                                Constants
                                                                        .custAcctStatus !=
                                                                    "A") {
                                                              Get.to(() =>
                                                                  ProfileScreen());
                                                            } else {
                                                              Get.offAll(() =>
                                                                  DashBoardManagerScreen(
                                                                    currantIndex:
                                                                        1,
                                                                  ));
                                                            }
                                                          },
                                                          btnColor:
                                                              // AppColor.cBackGround,
                                                              AppColor
                                                                  .themeOrangeColor,
                                                          // bColor: AppColor
                                                          //     .themeDarkBlueColor,

                                                          width: 155,
                                                          height: 40,
                                                          textColor:
                                                              AppColor.cWhite,
                                                        )
                                                      : const SizedBox(),
                                                );
                                              },
                                            ),
                                          if (Constants.NewOrderBtn == "Y")
                                            Builder(
                                              builder: (context) {
                                                return Obx(
                                                  () => balanceController
                                                          .statusCounts
                                                          .isNotEmpty
                                                      ? CommonIconButton(
                                                          title:
                                                              "New service order"
                                                                  .trr,
                                                          iconData: DefaultImages
                                                              .plusCircleIcn,
                                                          iconColor:
                                                              Colors.white,

                                                          onPressed: () {
                                                            if (Constants
                                                                    .hasCloseAccountRequest ==
                                                                'Y') {
                                                              ScaffoldMessenger
                                                                      .of(context)
                                                                  .showSnackBar(
                                                                SnackBar(
                                                                  backgroundColor:
                                                                      AppColor
                                                                          .cLiteYellow,
                                                                  content: Text(
                                                                    "Your account has pending closing request, it cannot use any facility at this moment.",
                                                                    style: pBold14
                                                                        .copyWith(
                                                                            color:
                                                                                AppColor.cBlackFont),
                                                                  ),
                                                                ),
                                                              );
                                                            } else if (Constants
                                                                        .custAcctType ==
                                                                    "C" &&
                                                                Constants
                                                                        .custAcctStatus !=
                                                                    "A") {
                                                              Get.to(() =>
                                                                  ProfileScreen());
                                                            } else {
                                                              Get.to(() =>
                                                                  NewOrderScreen());
                                                            }
                                                          },
                                                          btnColor:
                                                              // AppColor.cBackGround,
                                                              AppColor
                                                                  .themeOrangeColor,
                                                          // bColor: AppColor
                                                          //     .themeDarkBlueColor,

                                                          width: 155,
                                                          height: 40,
                                                          textColor:
                                                              AppColor.cWhite,
                                                        )
                                                      : const SizedBox(),
                                                );
                                              },
                                            ),
                                        ],
                                      ),
                                      verticalSpace(20),

                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          // CommonButton(
                                          //   title: 'SOCIAL RESPONSIBILITY'.trr,
                                          //   textColor: Color(0xff008000),
                                          //   fontSize: 10,
                                          //   //  image: DefaultImages.socialResLogoImg,
                                          //   onPressed: () async {
                                          //     await Get.to(() =>
                                          //         SocialResponsibilityScreen());
                                          //     // await Get.to(() => AldreesDonationScreen(
                                          //     //     url: Constants.IsAr_App == "true"
                                          //     //         ? AppConstant.aldreesdonationAR
                                          //     //         : AppConstant.aldreesdonationEN));
                                          //   },
                                          //   btnColor: Colors.white,
                                          //   bColor: Color(0xff008000),
                                          //   //AppColor.themeOrangeColor,
                                          //   width: 155,
                                          //   height: 40,

                                          //   //double.infinity,
                                          // ),
                                          GestureDetector(
                                            onTap: () async {
                                              await Get.to(() =>
                                                  SocialResponsibilityScreen());
                                            },
                                            child: Card(
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                              ),
                                              elevation: 8,
                                              shadowColor: AppColor
                                                  .cLightOrangeContainer,
                                              child: Container(
                                                padding: EdgeInsets.only(
                                                    right: 10,
                                                    left: Get.locale
                                                                ?.languageCode ==
                                                            'ar'
                                                        ? 10
                                                        : 0),
                                                width: 160,
                                                height: 48,
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  border: Border.all(
                                                    color: Color(0xff008000),
                                                  ),
                                                ),
                                                child: Row(
                                                  children: [
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              left: 5),
                                                      child: Image.asset(
                                                        DefaultImages
                                                            .socialResLogoImg,
                                                        height: 40,
                                                      ),
                                                    ),
                                                    SizedBox(width: 5),
                                                    Expanded(
                                                      child: Text(
                                                          'SOCIAL RESPONSIBILITY'
                                                              .trr,
                                                          maxLines: 2,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          style: pRegular16
                                                              .copyWith(
                                                                  color: Color(
                                                                      0xff008000),
                                                                  fontSize: 12),
                                                          textAlign: Get.locale
                                                                      ?.languageCode ==
                                                                  'ar'
                                                              ? TextAlign.center
                                                              : TextAlign
                                                                  .start),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),

                                          GestureDetector(
                                            onTap: () async {
                                              await Get.to(
                                                  () => PromotionsScreen());
                                            },
                                            child: Card(
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                              ),
                                              elevation: 8,
                                              shadowColor: AppColor
                                                  .cLightOrangeContainer,
                                              child: Container(
                                                width: 160,
                                                height: 48,
                                                padding: const EdgeInsets.only(
                                                    left: 0, right: 10),
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  border: Border.all(
                                                    color:
                                                        AppColor.cBarBlueLine,
                                                  ),
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceAround,
                                                  children: [
                                                    Image.asset(
                                                      DefaultImages
                                                          .promotionLogoImg,
                                                      height: 40,
                                                    ),
                                                    //SizedBox(width: 10),
                                                    Text(
                                                      'Aldrees partners'.trr,
                                                      maxLines: 2,
                                                      overflow:
                                                          TextOverflow.visible,
                                                      style:
                                                          pRegular16.copyWith(
                                                              color: AppColor
                                                                  .cBarBlueLine,
                                                              fontSize: 13),
                                                      textAlign:
                                                          TextAlign.start,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),

                                          // GestureDetector(
                                          //   onTap: () async {
                                          //     await Get.to(
                                          //         () => PromotionsScreen());
                                          //   },
                                          //   child: Card(
                                          //     shape: RoundedRectangleBorder(
                                          //       borderRadius:
                                          //           BorderRadius.circular(6),
                                          //     ),
                                          //     elevation: 8,
                                          //     shadowColor:
                                          //         AppColor.cLightOrangeContainer,
                                          //     child: Container(
                                          //       width: 155,
                                          //       height: 44,
                                          //       decoration: BoxDecoration(
                                          //           color: Colors.white,
                                          //           borderRadius:
                                          //               BorderRadius.circular(6),
                                          //           border: Border.all(
                                          //             color:
                                          //                 AppColor.cBarBlueLine,
                                          //           )),
                                          //       // padding: EdgeInsets.symmetric(
                                          //       //     horizontal: Get.width * 0.1),

                                          //       child: Row(
                                          //         mainAxisAlignment:
                                          //             MainAxisAlignment
                                          //                 .spaceEvenly,
                                          //         children: [
                                          //           Image.asset(DefaultImages
                                          //               .promotionLogoImg),
                                          //           Text(
                                          //             'Promotions'.trr,
                                          //             style: pRegular16.copyWith(
                                          //                 color: AppColor
                                          //                     .cBarBlueLine,
                                          //                 fontSize: 13),
                                          //             textAlign: TextAlign.center,
                                          //           ),
                                          //         ],
                                          //       ),
                                          //     ),
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    ],
                                  )
                                : const Center(
                                    child: CircularProgressIndicator()),
                          ),

                          verticalSpace(20),
                          Obx(
                            () => balanceController.statusCounts.isNotEmpty
                                ? Column(
                                    children: [
                                      ListView.builder(
                                          scrollDirection: Axis.vertical,
                                          shrinkWrap: true,
                                          itemCount: balanceController
                                              .statusCounts.length,
                                          itemBuilder: (context, index) {
                                            final data = balanceController
                                                .statusCounts
                                                .elementAt(index);
                                            print('data12312312 $data');
                                            print(
                                                'data12312312 ${jsonDecode(jsonEncode(data))}');
                                            return Row(
                                              children: [
                                                tagDashboardWidget(
                                                    title: "Active Tags".trr,
                                                    subTitle:
                                                        data.activeTags ?? "0"),
                                                Gap(4),
                                                tagDashboardWidget(
                                                    title:
                                                        "Tags to Install".trr,
                                                    subTitle:
                                                        data.tagsToInstall ??
                                                            "0"),
                                                Gap(4),
                                                tagDashboardWidget(
                                                    title: "Active Cards".trr,
                                                    subTitle:
                                                        data.activeCards ??
                                                            "0"),
                                              ],
                                            );
                                          }),
                                      // if(Constants.menu_Booking)
                                      if (Constants.BookingBtn == 'Y')
                                        verticalSpace(16),
                                      if (Constants.BookingBtn == 'Y')
                                        CommonButton(
                                            title: "Book tag installation".trr,
                                            onPressed: () {
                                              if (Constants
                                                      .hasCloseAccountRequest ==
                                                  'Y') {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                    backgroundColor:
                                                        AppColor.cLiteYellow,
                                                    content: Text(
                                                      "Your account has pending closing request, it cannot use any facility at this moment.",
                                                      style: pBold14.copyWith(
                                                          color: AppColor
                                                              .cBlackFont),
                                                    ),
                                                  ),
                                                );
                                              } else if (Constants
                                                          .custAcctType ==
                                                      "C" &&
                                                  Constants.custAcctStatus !=
                                                      "A") {
                                                Get.to(() => ProfileScreen());
                                              } else {
                                                Get.to(
                                                    BookTagInstallationScreen());
                                              }
                                            },
                                            btnColor:
                                                AppColor.themeDarkBlueColor,
                                            width: double.infinity),
                                    ],
                                  )
                                : const Center(
                                    child: CircularProgressIndicator()),
                          ),
                          verticalSpace(16),
                          Container(
                            height: 200,
                            width: double.infinity,
                            decoration: BoxDecoration(
                                color: AppColor.cBackGround,
                                borderRadius: BorderRadius.circular(9)),
                            child: FutureBuilder(
                                future: fetchSlideImages(),
                                builder: (context, snapshot) {
                                  switch (snapshot.connectionState) {
                                    case ConnectionState.waiting:
                                      return const Text(
                                        'Loading....',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w100,
                                        ),
                                      );
                                    default:
                                      if (snapshot.hasError) {
                                        return Text('Error: ${snapshot.error}');
                                      } else {
                                        List data = snapshot.data ?? [];

                                        return CarouselSlider(
                                          options: CarouselOptions(
                                            viewportFraction:
                                                1.0, // Use 1.0 to fit the image to the screen width.
                                            autoPlayAnimationDuration:
                                                const Duration(
                                                    milliseconds: 2000),
                                            autoPlay: true,
                                            enlargeCenterPage: true,
                                            height:
                                                200, // Adjust this height as needed
                                          ),
                                          items: data
                                              .map(
                                                (e) => Container(
                                                  width: MediaQuery.of(context)
                                                      .size
                                                      .width, // Make the container fill the screen width
                                                  decoration: BoxDecoration(
                                                    image: DecorationImage(
                                                      image: NetworkImage(
                                                          e["IMG_LOC"]),
                                                      fit: BoxFit
                                                          .cover, // Use BoxFit.cover to ensure the image covers the container.
                                                    ),
                                                  ),
                                                ),
                                              )
                                              .toList(),
                                        );
                                      }
                                  }
                                }),
                          ),
                          verticalSpace(16),
                          // Text(
                          //   "Aldrees news".trr,
                          //   style: pBold20,
                          // ),
                          // verticalSpace(24),
                          Text(
                            "Stations".trr,
                            style: pBold16,
                          ),
                          verticalSpace(16),
                          Obx(
                            () => balanceController.gasStnNews.isNotEmpty
                                ? Container(
                                    padding: EdgeInsets.all(
                                        8.0), // Adjust padding as needed
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        color: AppColor.lightBlueColor),
                                    child: Text(
                                      balanceController.gasStnNews.value.trr,
                                      style: pBold20,
                                    ),
                                  )
                                : const SizedBox(),
                          ),
                          verticalSpace(16),

                          Container(
                            height: 200,
                            width: double.infinity,
                            decoration: BoxDecoration(
                                color: AppColor.cBackGround,
                                borderRadius: BorderRadius.circular(9)),
                            child: FutureBuilder(
                                future: fetchStationNewsSlide(),
                                builder: (context, snapshot) {
                                  switch (snapshot.connectionState) {
                                    case ConnectionState.waiting:
                                      return const Text(
                                        'Loading....',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w100,
                                        ),
                                      );
                                    default:
                                      if (snapshot.hasError) {
                                        return Text('Error: ${snapshot.error}');
                                      } else {
                                        List data = snapshot.data ?? [];

                                        return /*CarouselSlider(
                                          options: CarouselOptions(
                                            viewportFraction:
                                            1.0, // Use 1.0 to fit the image to the screen width.
                                            autoPlayAnimationDuration:
                                            const Duration(milliseconds: 2000),
                                            autoPlay: true,
                                            enlargeCenterPage: true,
                                            height: 200, // Adjust this height as needed
                                          ),
                                          items: data
                                              .map(
                                                (e) => Container(
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width, // Make the container fill the screen width
                                              decoration: BoxDecoration(
                                                image: DecorationImage(
                                                  image: NetworkImage(e["STN_DESC"]),
                                                  fit: BoxFit
                                                      .cover, // Use BoxFit.cover to ensure the image covers the container.
                                                ),
                                              ),
                                            ),
                                          )
                                              .toList(),
                                        );*/
                                            CarouselSlider(
                                          options: CarouselOptions(
                                            viewportFraction:
                                                1.0, // Use 1.0 to occupy the full width of the viewport.
                                            autoPlayAnimationDuration:
                                                const Duration(
                                                    milliseconds: 2000),
                                            autoPlay:
                                                true, // Assuming you might not want auto-scrolling for text and button interactions.
                                            enlargeCenterPage: true,
                                            height:
                                                400, // Adjust this height as needed.
                                          ),
                                          items: data.map((e) {
                                            // print("=================================");
                                            // print(jsonDecode(e.toString()));
                                            // print("=================================");
                                            return Container(
                                              color: AppColor.lightBlueColor,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width, // Make the container fill the screen width.
                                              padding: EdgeInsets.all(
                                                  20), // Add some padding around the content.
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment
                                                    .center, // Center the content vertically.
                                                crossAxisAlignment:
                                                    CrossAxisAlignment
                                                        .start, // Align text to the start (left for LTR languages).
                                                children: [
                                                  Text(
                                                    //"Status: ${e["STN_STATUS"]}", // Adjust the text and style as needed.
                                                    "${e["STN_STATUS"]}", // Adjust the text and style as needed.
                                                    style:
                                                        TextStyle(fontSize: 12),
                                                  ),
                                                  SizedBox(
                                                      height:
                                                          10), // Space between the lines.
                                                  Text(
                                                    // "Description: ${e["STN_DESC"]}",
                                                    "${e["STN_DESC"]}",
                                                    // style: TextStyle(fontSize: 18),
                                                    style: pBold18,
                                                  ),
                                                  SizedBox(height: 10),
                                                  Text(
                                                    //"Place: ${e["PLACE_DESC"]}",
                                                    "${e["PLACE_DESC"]}",
                                                    style:
                                                        TextStyle(fontSize: 12),
                                                  ),
                                                  SizedBox(height: 20),
                                                  /*  ElevatedButton(
                                                      onPressed: () {
                                                        // Navigate to maps or perform the desired action.
                                                        // Example: Navigator.of(context).push(MaterialPageRoute(builder: (context) => MapsScreen(e["STN_COOR"])));
                                                      },
                                                      child: Text("VIEW LOCATION"),
                                                    ),*/
                                                  CommonButton(
                                                    title: "View Location".trr,
                                                    onPressed: () async {
                                                      final Uri url0 =
                                                          Uri.parse(
                                                              e["STN_COOR"]);
                                                      if (!await launchUrl(
                                                          url0)) {
                                                        // You can show an error message if the URL couldn't be launched
                                                        print(
                                                            'Could not launch $url0');
                                                      }
                                                    },
                                                    width: 180,
                                                    //height: 40,
                                                    btnColor: AppColor
                                                        .themeOrangeColor,
                                                    // width: double.infinity
                                                  ),
                                                ],
                                              ),
                                            );
                                          }).toList(),
                                        );
                                      }
                                  }
                                }),
                          ),
                          verticalSpace(16),
                          FutureBuilder<dynamic>(
                              future: homeController.fetchNews(),
                              builder: (context, AsyncSnapshot snapshot) {
                                if (snapshot.connectionState !=
                                    ConnectionState.done) {
                                  return const Center(
                                      child: CircularProgressIndicator());
                                } else {
                                  return ListView.builder(
                                    itemCount:
                                        homeController.aldreesNewsList.length,
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      var data =
                                          homeController.aldreesNewsList[index];
                                      print("selected language =======" +
                                              Constants.selected_language ==
                                          "ar");
                                      return aldreesNewsWidget(
                                        title:
                                            Constants.selected_language == "ar"
                                                ? data.subject_ar
                                                : data.subject,
                                        subTitle:
                                            Constants.selected_language == "ar"
                                                ? data.summary_ar
                                                : data.summary,
                                        onTap: () {
                                          const url =
                                              'https://www.aldrees.com/'; // Replace with your target URL
                                          launch(url);
                                        },
                                      );
                                      // if (Constants.selected_language == "ar") {
                                      //   return aldreesNewsWidget(
                                      //     title: data.subject_ar,
                                      //     subTitle: data.summary_ar,
                                      //     onTap: () {
                                      //       const url =
                                      //           'https://www.aldrees.com/'; // Replace with your target URL
                                      //       launch(url);
                                      //     },
                                      //   );
                                      // } else {
                                      //   return aldreesNewsWidget(
                                      //     title: data.subject,
                                      //     subTitle: data.summary,
                                      //     onTap: () {
                                      //       const url =
                                      //           'https://www.aldrees.com/'; // Replace with your target URL
                                      //       launch(url);
                                      //     },
                                      //   );
                                      // }
                                    },
                                  );
                                }
                              }),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }

  Future<String?> getUserid() async {
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    String? userid = sharedUser.getString('userID');
    return userid;
  }
}

class CheckPasswordDialog extends StatelessWidget {
  const CheckPasswordDialog({super.key});

  @override
  Widget build(BuildContext context) {
    // TextEditingController oldPasswordController = TextEditingController();
    // TextEditingController newPasswordController = TextEditingController();

    HomeController homeController = Get.put(HomeController());
    GlobalKey<FormState> passwordKey = GlobalKey<FormState>();

    return AlertDialog(
      title: Center(
          child: Text(
        'Confirmation'.trr,
        style: pBold18,
      )),
      content: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  'Please enter password to verify your account'.trr,
                  style: pRegular14,
                  textAlign: TextAlign.center,
                ),
              ),
              verticalSpace(10),
              Form(
                key: passwordKey,
                child: CommonTextField(
                    controller: homeController.checkPasswordController,
                    labelText: 'Password'.trr,
                    hintText: "Please enter here".trr,
                    obscuringCharacter: '*',
                    filled: true,
                    fillColor: AppColor.cFilled,
                    obscureText: homeController.isCheckPass.value,
                    validator: (value) {
                      return Validator.validatePassword(value);
                    },
                    suffix: GestureDetector(
                        onTap: () {
                          homeController.isCheckPass.value =
                              !homeController.isCheckPass.value;
                        },
                        child: assetSvdImageWidget(
                            image: homeController.isCheckPass.value
                                ? DefaultImages.eyeOffIcn
                                : DefaultImages.eyeIcn))),
              ),
              verticalSpace(8),
            ],
          )),
      actions: [
        CommonButton(
            title: "Confirm".trr,
            onPressed: () async {
              Loader.showLoader();
              print(homeController.checkPasswordController.text);
              homeController.checkUserPassword();
            },
            btnColor: AppColor.themeOrangeColor),
      ],
    );
  }

  Widget validateTextRow({required String image, required String title}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          assetSvdImageWidget(image: image),
          horizontalSpace(13),
          Expanded(
            child: Text(
              title,
              style: pRegular13,
              maxLines: 3,
            ),
          )
        ],
      ),
    );
  }
}

class ActivationDialog extends StatelessWidget {
  const ActivationDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      contentPadding: EdgeInsets.all(24),
      insetPadding: EdgeInsets.all(16),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: assetSvdImageWidget(image: DefaultImages.faceidblack)),
            ],
          ),
          verticalSpace(24),
          Text('Activate Face ID Login'.trr,
              style: pBold20, textAlign: TextAlign.center),
          verticalSpace(14),
          Center(
              child: Text(
                  'Activate Face ID login to easily access Aldrees App'.trr,
                  style: pRegular13,
                  textAlign: TextAlign.center)),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                child: CommonButton(
                  title: "NO".trr,
                  onPressed: () {
                    Get.back();
                  },
                  textColor: AppColor.cDarkBlueFont,
                  btnColor: AppColor.cBackGround,
                  bColor: AppColor.cDarkBlueFont,
                ),
              ),
              horizontalSpace(16),
              Expanded(
                child: CommonButton(
                  title: "ACTIVATE".trr,
                  onPressed: () {
                    //authenticateWithBiometrics();
                  },
                  textColor: AppColor.cWhiteFont,
                  btnColor: AppColor.cDarkOrangeText,
                  bColor: AppColor.cTransparent,
                  horizontalPadding: 16,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
