// ignore_for_file: must_be_immutable, avoid_print, prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:draggable_bottom_sheet/draggable_bottom_sheet.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/core/controller/vehicle_controller/tag_installation_controller.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/add_pure_dc_vehicle_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/vehicle_controller/vehicle_controller.dart';
import '../../../../utils/validator.dart';
import '../../../widget/common_button.dart';
import '../../location_screen/gase_station_screen.dart';

// class ScheduleInstallationWidget extends StatefulWidget {
//   ScheduleInstallationWidget({super.key});
//
//   @override
//   State<ScheduleInstallationWidget> createState() => _ScheduleInstallationWidgetState();
// }
//
// class _ScheduleInstallationWidgetState extends State<ScheduleInstallationWidget> {
//   TagInstallationController tagInstallationController = Get.find();
//   late GoogleMapController mapController;
//   late CameraPosition cameraPosition;
//
//   final LatLng _center = const LatLng(21.44484793949768, 53.295109691390245);
//
//   void _onMapCreated(GoogleMapController controller) {
//     mapController = controller;
//   }
//
//   Map<MarkerId, Marker> markers = <MarkerId, Marker>{};
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     cameraPosition = CameraPosition(target: _center, zoom: 7.0);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: Get.height - 50,
//       decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
//       child: Stack(
//         alignment: Alignment.bottomCenter,
//         children: [
//           ClipRRect(
//             borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
//             child: GoogleMap(
//               onMapCreated: _onMapCreated,
//               markers: Set<Marker>.of(markers.values),
//               // YOUR MARKS IN MAP
//               initialCameraPosition: cameraPosition,
//               scrollGesturesEnabled: true,
//             ),
//           ),
//           Container(
//             height: Get.height - 150,
//             decoration: BoxDecoration(
//                 color: AppColor.cBackGround, borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
//             padding: EdgeInsets.all(16),
//             child: Obx(() {
//               return SingleChildScrollView(
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     CommonHintDropdownWidget(
//                       hint: "Please select here...",
//                       labelText: 'Select City',
//                       value: tagInstallationController.selectedCity.value,
//                       list: tagInstallationController.cityList,
//                       onChanged: (value) {
//                         tagInstallationController.selectedCity.value = value;
//                       },
//                     ),
//                     verticalSpace(16),
//                     ListView.builder(
//                       shrinkWrap: true,
//                       physics: NeverScrollableScrollPhysics(),
//                       itemCount: tagInstallationController.scheduleDataList.length,
//                       itemBuilder: (context, index) {
//                         var data = tagInstallationController.scheduleDataList[index];
//                         return Obx(() {
//                           return scheduleDetailWidget(
//                             context,
//                             title: data['title'],
//                             address: data['address'],
//                             dateController: data['dateController'].value,
//                             pickDate: () async {
//                               DateTime? pickedDate = await showDatePicker(
//                                 context: context,
//                                 initialDate: DateTime.now(),
//                                 firstDate: DateTime(2000),
//                                 lastDate: DateTime(2101),
//                                 builder: (context, child) {
//                                   return Theme(
//                                     data: Theme.of(context).copyWith(
//                                       colorScheme: ColorScheme.light(
//                                         primary: AppColor.themeOrangeColor,
//                                       ),
//                                       textButtonTheme: TextButtonThemeData(
//                                         style: TextButton.styleFrom(
//                                           foregroundColor: AppColor.themeDarkBlueColor, // button text color
//                                         ),
//                                       ),
//                                     ),
//                                     child: child!,
//                                   );
//                                 },
//                               );
//
//                               if (pickedDate != null) {
//                                 print(pickedDate);
//                                 String formattedDate = DateFormat('MM/dd/yy').format(pickedDate);
//                                 print(formattedDate);
//
//                                 data['dateController'].value.text = formattedDate;
//                               } else {
//                                 print("Date is not selected");
//                               }
//                             },
//                             time: data['time'],
//                             // timeValue: tagInstallationController.selectedTime.value,
//                             timeValue: data['selectedTime'].value,
//                             timeOnChanged: (value) {
//                               // tagInstallationController.selectedTime.value = value.toString();
//                               data['selectedTime'].value = value.toString();
//                             },
//                             plat: data['plat'],
//                             platValue: data['selectedPlat'].value,
//                             platOnChanged: (value) {
//                               data['selectedPlat'].value = value;
//                             },
//                             addVehicle: () {
//                               Get.to(() => AddPureDCVehicleScreen(
//                                     title: "Add vehicle",
//                                   ));
//                             },
//                           );
//                         });
//                       },
//                     ),
//                     verticalSpace(16),
//                     Row(
//                       crossAxisAlignment: CrossAxisAlignment.center,
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         assetSvdImageWidget(image: DefaultImages.trruckIcn),
//                         horizontalSpace(12),
//                         Text(
//                           '${tagInstallationController.scheduleDataList.length} Scheduled',
//                           style: pBold12,
//                         ),
//                         horizontalSpace(24),
//                         Expanded(
//                           child: CommonIconButton(
//                             height: 40,
//                             title: "CONFIRM SCHEDULE",
//                             iconData: DefaultImages.scheduleInstallationIcn,
//                             btnColor: AppColor.themeOrangeColor,
//                             fontSize: 12,
//                             onPressed: () {
//                               Get.back();
//                               showDialog(
//                                 context: context,
//                                 builder: (context) {
//                                   return AlertDialog(
//                                     contentPadding: EdgeInsets.all(16),
//                                     insetPadding: EdgeInsets.all(16),
//                                     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//                                     content: tagInstallationScheduleSuccessWidget(),
//                                   );
//                                 },
//                               );
//                             },
//                           ),
//                         )
//                       ],
//                     )
//                   ],
//                 ),
//               );
//             }),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Container tagInstallationScheduleSuccessWidget() {
//     return Container(
//       decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
//       child: Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.center, children: [
//         GestureDetector(
//           onTap: () {
//             Get.back();
//           },
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.end,
//             children: [assetSvdImageWidget(image: DefaultImages.cancelIcn)],
//           ),
//         ),
//         verticalSpace(24),
//         Text(
//           "Tag installation has been scheduled",
//           style: pSemiBold17,
//         ),
//         verticalSpace(8),
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 40),
//           child: Text("We’ve sent you an email with the appointment details",
//               style: pRegular14, textAlign: TextAlign.center),
//         ),
//         verticalSpace(24),
//         CommonButton(
//           title: 'Okay',
//           onPressed: () {
//             tagInstallationController.upcomimgDataList = tagInstallationController.dummyUpcomimgDataList;
//             tagInstallationController.upcomimgDataList.refresh();
//             Get.back();
//           },
//           btnColor: AppColor.themeOrangeColor,
//         )
//       ]),
//     );
//   }
//
//   scheduleDetailWidget(
//     BuildContext context, {
//     String? title,
//     String? address,
//     TextEditingController? dateController,
//     Function()? pickDate,
//     List? time,
//     String? timeValue,
//     ValueChanged? timeOnChanged,
//     List? plat,
//     String? platValue,
//     ValueChanged? platOnChanged,
//     Function()? addVehicle,
//   }) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 8.0),
//       child: Container(
//         width: Get.width,
//         decoration: BoxDecoration(borderRadius: BorderRadius.circular(6), color: AppColor.cLightGrey),
//         padding: EdgeInsets.all(22),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   title!,
//                   style: pBold24,
//                 ),
//                 assetSvdImageWidget(image: DefaultImages.circleMapPinIcn)
//               ],
//             ),
//             verticalSpace(8),
//             Text(
//               address!,
//               style: pMedium14,
//             ),
//             verticalSpace(16),
//             Row(
//               children: [
//                 Expanded(
//                     child: Text(
//                   "Sun-Thu",
//                   style: pRegular14,
//                 )),
//                 Expanded(
//                     child: Text(
//                   "8AM - 8PM",
//                   style: pMedium14,
//                 )),
//               ],
//             ),
//             Row(
//               children: [
//                 Expanded(
//                     child: Text(
//                   "Fri",
//                   style: pRegular14,
//                 )),
//                 Expanded(
//                     child: Text(
//                   "9AM - 7PM",
//                   style: pMedium14,
//                 )),
//               ],
//             ),
//             verticalSpace(24),
//             Row(
//               children: [
//                 Expanded(
//                   flex: 2,
//                   child: CommonTextField(
//                     controller: dateController,
//                     labelText: '',
//                     suffix: assetSvdImageWidget(image: DefaultImages.calendarIcn),
//                     fillColor: AppColor.cWhite,
//                     filled: true,
//                     readOnly: true,
//                     onTap: pickDate,
//                   ),
//                 ),
//                 horizontalSpace(11),
//                 Expanded(
//                   // flex: 1,
//                   child: Container(
//                     height: 44,
//                     decoration: BoxDecoration(
//                         color: AppColor.cWhite,
//                         borderRadius: BorderRadius.circular(8),
//                         border: Border.all(color: AppColor.cBorder)),
//                     // padding: EdgeInsets.only(left: 10),
//                     child: Center(
//                       child: DropdownButton(
//                         value: timeValue,
//                         items: time!.map((data) {
//                           return DropdownMenuItem(
//                             value: data,
//                             child: Text(
//                               data,
//                               style: pRegular13.copyWith(),
//                               overflow: TextOverflow.ellipsis,
//                             ),
//                           );
//                         }).toList(),
//                         onChanged: timeOnChanged!,
//                         style: pMedium14.copyWith(
//                           color: AppColor.cWhiteFont,
//                         ),
//                         underline: Container(),
//                         dropdownColor: AppColor.cLightGrey,
//                         icon: assetSvdImageWidget(image: DefaultImages.dropDownIcn),
//                       ),
//                     ),
//                   ),
//                 )
//               ],
//             ),
//             verticalSpace(12),
//             CommonHintDropdownWidget(
//                 hint: "Plat #",
//                 labelText: '',
//                 value: platValue,
//                 list: plat,
//                 onChanged: platOnChanged!,
//                 filledColor: AppColor.cWhite),
//             verticalSpace(24),
//             GestureDetector(
//               onTap: addVehicle,
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 children: [
//                   assetSvdImageWidget(
//                       image: DefaultImages.plusIcn,
//                       colorFilter: ColorFilter.mode(AppColor.cOrangeFont, BlendMode.srcIn),
//                       width: 14,
//                       height: 14),
//                   horizontalSpace(8),
//                   Text(
//                     "Add Vehicle",
//                     style: pMedium12.copyWith(color: AppColor.cOrangeFont, fontSize: 13),
//                   )
//                 ],
//               ),
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }

class ScheduleInstallationWidget extends StatefulWidget {
  const ScheduleInstallationWidget({Key? key}) : super(key: key);

  @override
  State<ScheduleInstallationWidget> createState() =>
      _ScheduleInstallationWidgetState();
}

class _ScheduleInstallationWidgetState
    extends State<ScheduleInstallationWidget> {
  TagInstallationController tagInstallationController =
      Get.put(TagInstallationController());
  VehicleController vehicleController = Get.put(VehicleController());

  late GoogleMapController mapController;
  late CameraPosition cameraPosition;

  final LatLng _center = const LatLng(21.44484793949768, 53.295109691390245);

  Map<MarkerId, Marker> markers = <MarkerId, Marker>{};
  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;
    _add();
  }

  Future<void> _add() async {
    for (int i = 0; i < tagInstallationController.latLongList.length; i++) {
      var markerIdVal = "$i";
      final MarkerId markerId = MarkerId(markerIdVal);
      final Marker marker = Marker(
        markerId: markerId,
        position: tagInstallationController.latLongList[i],
        icon: await createMarkerImageFromAsset(
            context, DefaultImages.gasStationMarker),
        onTap: () {
          showModalBottomSheet(
            context: context,
            barrierColor: AppColor.cBlackOpacity,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
            isScrollControlled: true,
            builder: (context) {
              return locationBottomSheetWidget(
                title: "RIYADH",
                subTitle: "ASMENT EXIT - 18",
                openNow: "Yes".trr,
                workingHour: "24/7",
                phoneNo: "966920002667",
                latitude: tagInstallationController.latLongList[i].latitude,
                longitude: tagInstallationController.latLongList[i].longitude,
              );
            },
          );
        },
      );
      setState(() {
        markers[markerId] = marker;
      });
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    cameraPosition = CameraPosition(target: _center, zoom: 7.0);
  }

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: DraggableBottomSheet(
        maxExtent: Get.height / 1.4,
        minExtent: Get.height / 2.3,
        expansionExtent: Get.height / 4,
        useSafeArea: true,
        barrierDismissible: false,
        barrierColor: AppColor.cTransparent,
        curve: Curves.easeIn,
        previewWidget: _backgroundWidget(),
        expandedWidget: _backgroundWidget(),
        backgroundWidget: _backgroundWidget(),
        onDragging: (pos) {},
      ),
    );
  }

  Widget _backgroundWidget() {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.only(right: 16, bottom: 10, left: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    padding: EdgeInsets.only(
                      top: 15,
                      bottom: 15,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        assetSvdImageWidget(
                            image: DefaultImages.backIcn,
                            colorFilter: ColorFilter.mode(
                                AppColor.cDarkBlueFont, BlendMode.srcIn)),
                        horizontalSpace(10),
                        Text(
                          "Back".trr,
                          style: pRegular18.copyWith(
                              color: AppColor.cDarkBlueFont, fontSize: 17),
                          textAlign: TextAlign.start,
                        )
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.center,
                    child: Text(
                      "Tag Installation".trr,
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                assetSvdImageWidget(image: DefaultImages.searchCircleIcn)
              ],
            ),
          ),
          Expanded(
            child: GoogleMap(
              onMapCreated: _onMapCreated,
              markers: Set<Marker>.of(markers.values),
              // YOUR MARKS IN MAP
              initialCameraPosition: cameraPosition,
              scrollGesturesEnabled: true,
            ),
          ),
        ],
      ),
    );
  }

 /* Widget _previewWidget() {
    return Container(
      height: Get.height - 150,
      decoration: BoxDecoration(
          color: AppColor.cBackGround,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      padding: EdgeInsets.all(16),
      child: Obx(() {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
              child: Form(
                key: formKey,
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    CommonDropdownButtonWidget(
                      hint: '',
                      labelText: 'Order ID'.trr,
                      list: tagInstallationController.orderList,
                      value: tagInstallationController.selectedOrder.value,
                      onChanged: (value) {},
                      fontColor: AppColor.cDarkGreyFont,
                      filledColor: AppColor.cFilled,
                    ),
                    verticalSpace(16),
                    CommonTextField(
                      labelText: "Waiting for update".trr + '*',
                      hintText: 'Waiting for update'.trr,
                    ),
                    verticalSpace(16),
                    CommonTextField(
                      labelText: 'Appointed Qty'.trr,
                      hintText: 'Appointed Qty'.trr,
                    ),
                    verticalSpace(16),
                    CommonTextField(
                      labelText: 'Available for Appointment'.trr + '*',
                      hintText: 'Available for Appointment'.trr,
                    ),
                    verticalSpace(16),
                    CommonTextField(
                      labelText: "Mobile number".trr + '*',
                      hintText: 'Enter Mobile number'.trr,
                      keyboardType: TextInputType.number,
                      maxLength: 10,
                      validator: (value) {
                        return Validator.validateMobile(value);
                      },
                    ),
                    CommonDropdownButtonWidget(
                      hint: '',
                      labelText: 'Place'.trr,
                      list: tagInstallationController.placeList,
                      value: tagInstallationController.selectedPlace.value,
                      onChanged: (value) {},
                      fontColor: AppColor.cDarkGreyFont,
                      filledColor: AppColor.cFilled,
                    ),
                    verticalSpace(16),
                    CommonDropdownButtonWidget(
                      hint: '',
                      labelText: 'Center'.trr,
                      list: tagInstallationController.centerList,
                      value: tagInstallationController.selectedCenter.value,
                      onChanged: (value) {},
                      fontColor: AppColor.cDarkGreyFont,
                      filledColor: AppColor.cFilled,
                    ),
                    verticalSpace(16),
                    CommonDropdownButtonWidget(
                      hint: '',
                      labelText: 'Vehicle Type'.trr,
                      list: tagInstallationController.vehicleTypeList,
                      value:
                          tagInstallationController.selectedvehicleType.value,
                      onChanged: (value) {},
                      fontColor: AppColor.cDarkGreyFont,
                      filledColor: AppColor.cFilled,
                    ),
                    verticalSpace(16),
                    CommonDropdownButtonWidget(
                      hint: '',
                      labelText: 'Tank Type'.trr,
                      list: tagInstallationController.tankTypeList,
                      value: tagInstallationController.selectedTankType.value,
                      onChanged: (value) {},
                      fontColor: AppColor.cDarkGreyFont,
                      filledColor: AppColor.cFilled,
                    ),
                    verticalSpace(16),
                    CommonTextField(
                      labelText: "Required Qty".trr + '*',
                      hintText: 'Required Qty'.trr,
                      keyboardType: TextInputType.number,
                      maxLength: 10,
                      validator: (value) {
                        return Validator.validateMobile(value);
                      },
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: CommonButton(
                title: 'View Slots'.trr,
                onPressed: () {
                  if (formKey.currentState!.validate()) {
                    // Call Controller
                  }
                },
                textColor: AppColor.cWhiteFont,
                btnColor: AppColor.themeOrangeColor,
              ),
            ),
            // CommonHintDropdownWidget(
            //   hint: 'Please select here'.trr + "...",
            //   labelText: 'Select City'.trr,
            //   value: tagInstallationController.selectedCity.value,
            //   list: tagInstallationController.cityList,
            //   onChanged: (value) {
            //     tagInstallationController.selectedCity.value = value;
            //   },
            // ),
            // verticalSpace(16),
            // Expanded(
            //   child: SingleChildScrollView(
            //     physics: BouncingScrollPhysics(),
            //     child: Column(
            //         crossAxisAlignment: CrossAxisAlignment.start,
            //         children: [
            //           ListView.builder(
            //             shrinkWrap: true,
            //             physics: NeverScrollableScrollPhysics(),
            //             itemCount:
            //                 tagInstallationController.scheduleDataList.length,
            //             itemBuilder: (context, index) {
            //               var data =
            //                   tagInstallationController.scheduleDataList[index];
            //               return Obx(() {
            //                 return scheduleDetailWidget(
            //                   context,
            //                   title: data['title'],
            //                   address: data['address'],
            //                   dateController: data['dateController'].value,
            //                   pickDate: () async {
            //                     DateTime? pickedDate = await showDatePicker(
            //                       context: context,
            //                       initialDate: DateTime.now(),
            //                       firstDate: DateTime(2000),
            //                       lastDate: DateTime(2101),
            //                       builder: (context, child) {
            //                         return Theme(
            //                           data: Theme.of(context).copyWith(
            //                             colorScheme: ColorScheme.light(
            //                               primary: AppColor.themeOrangeColor,
            //                             ),
            //                             textButtonTheme: TextButtonThemeData(
            //                               style: TextButton.styleFrom(
            //                                 foregroundColor: AppColor
            //                                     .themeDarkBlueColor, // button text color
            //                               ),
            //                             ),
            //                           ),
            //                           child: child!,
            //                         );
            //                       },
            //                     );

            //                     if (pickedDate != null) {
            //                       print(pickedDate);
            //                       String formattedDate =
            //                           DateFormat('MM/dd/yy').format(pickedDate);
            //                       print(formattedDate);

            //                       data['dateController'].value.text =
            //                           formattedDate;
            //                     } else {
            //                       print("Date is not selected");
            //                     }
            //                   },
            //                   time: data['time'],
            //                   // timeValue: tagInstallationController.selectedTime.value,
            //                   timeValue: data['selectedTime'].value,
            //                   timeOnChanged: (value) {
            //                     // tagInstallationController.selectedTime.value = value.toString();
            //                     data['selectedTime'].value = value.toString();
            //                   },
            //                   plat: data['plat'],
            //                   platValue: data['selectedPlat'].value,
            //                   platOnChanged: (value) {
            //                     data['selectedPlat'].value = value;
            //                   },
            //                   addVehicle: () {
            //                     Get.to(() => AddPureDCVehicleScreen(
            //                           title: "Add Vehicle".trr,
            //                         ));
            //                   },
            //                 );
            //               });
            //             },
            //           ),
            //           verticalSpace(16),
            //           Row(
            //             crossAxisAlignment: CrossAxisAlignment.center,
            //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //             children: [
            //               assetSvdImageWidget(image: DefaultImages.trruckIcn),
            //               horizontalSpace(12),
            //               Text(
            //                 '${tagInstallationController.scheduleDataList.length} ' +
            //                     "Scheduled".trr,
            //                 style: pBold12,
            //               ),
            //               horizontalSpace(24),
            //               Expanded(
            //                 child: CommonIconButton(
            //                   height: 40,
            //                   title: "CONFIRM SCHEDULE".trr,
            //                   iconData: DefaultImages.scheduleInstallationIcn,
            //                   btnColor: AppColor.themeOrangeColor,
            //                   fontSize: 12,
            //                   onPressed: () {
            //                     showDialog(
            //                       context: context,
            //                       builder: (context) {
            //                         return AlertDialog(
            //                           contentPadding: EdgeInsets.all(16),
            //                           insetPadding: EdgeInsets.all(16),
            //                           shape: RoundedRectangleBorder(
            //                               borderRadius:
            //                                   BorderRadius.circular(12)),
            //                           content:
            //                               tagInstallationScheduleSuccessWidget(),
            //                         );
            //                       },
            //                     );
            //                   },
            //                 ),
            //               )
            //             ],
            //           )
            //         ]),
            //   ),
            // )
          ],
        );
      }),
    );
  }*/

  Container tagInstallationScheduleSuccessWidget() {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () {
                Get.back();
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [assetSvdImageWidget(image: DefaultImages.cancelIcn)],
              ),
            ),
            verticalSpace(24),
            Text(
              "Your tag installation has been booked.".trr,
              style: pSemiBold17,
            ),
            verticalSpace(8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                  "We'll send you a confirmation email with the details.".trr,
                  style: pRegular14,
                  textAlign: TextAlign.center),
            ),
            verticalSpace(24),
            CommonButton(
              title: 'OK'.trr,
              onPressed: () {
                tagInstallationController.upcomimgDataList =
                    tagInstallationController.dummyUpcomimgDataList;
                tagInstallationController.upcomimgDataList.refresh();
                vehicleController.isMyFleet.value = false;
                vehicleController.isTagTransfer.value = false;
                vehicleController.isScheduleInstallation.value = false;
                vehicleController.isTagInstallation.value = true;
                Get.back();
              },
              btnColor: AppColor.themeOrangeColor,
            )
          ]),
    );
  }

  scheduleDetailWidget(
    BuildContext context, {
    String? title,
    String? address,
    TextEditingController? dateController,
    Function()? pickDate,
    List? time,
    String? timeValue,
    ValueChanged? timeOnChanged,
    List? plat,
    String? platValue,
    ValueChanged? platOnChanged,
    Function()? addVehicle,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        width: Get.width,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6), color: AppColor.cLightGrey),
        padding: EdgeInsets.all(22),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title!,
                  style: pBold24,
                ),
                assetSvdImageWidget(image: DefaultImages.circleMapPinIcn)
              ],
            ),
            verticalSpace(8),
            Text(
              address!,
              style: pMedium14,
            ),
            verticalSpace(16),
            Row(
              children: [
                Expanded(
                    child: Text(
                  "Sun-Thu",
                  style: pRegular14,
                )),
                Expanded(
                    child: Text(
                  "8AM - 8PM",
                  style: pMedium14,
                )),
              ],
            ),
            Row(
              children: [
                Expanded(
                    child: Text(
                  "Fri",
                  style: pRegular14,
                )),
                Expanded(
                    child: Text(
                  "9AM - 7PM",
                  style: pMedium14,
                )),
              ],
            ),
            verticalSpace(24),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: CommonTextField(
                    controller: dateController,
                    labelText: '',
                    suffix:
                        assetSvdImageWidget(image: DefaultImages.calendarIcn),
                    fillColor: AppColor.cWhite,
                    filled: true,
                    readOnly: true,
                    onTap: pickDate,
                  ),
                ),
                horizontalSpace(11),
                Expanded(
                  // flex: 1,
                  child: Container(
                    height: 44,
                    decoration: BoxDecoration(
                        color: AppColor.cWhite,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColor.cBorder)),
                    // padding: EdgeInsets.only(left: 10),
                    child: Center(
                      child: DropdownButton(
                        value: timeValue,
                        items: time!.map((data) {
                          return DropdownMenuItem(
                            value: data,
                            child: Text(
                              data,
                              style: pRegular13.copyWith(),
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }).toList(),
                        onChanged: timeOnChanged!,
                        style: pMedium14.copyWith(
                          color: AppColor.cWhiteFont,
                        ),
                        underline: Container(),
                        dropdownColor: AppColor.cLightGrey,
                        icon: assetSvdImageWidget(
                            image: DefaultImages.dropDownIcn),
                      ),
                    ),
                  ),
                )
              ],
            ),
            verticalSpace(12),
            CommonHintDropdownWidget(
                hint: "Plat".trr + " #",
                labelText: '',
                value: platValue,
                list: plat,
                onChanged: platOnChanged!,
                filledColor: AppColor.cWhite),
            verticalSpace(24),
            GestureDetector(
              onTap: addVehicle,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  assetSvdImageWidget(
                      image: DefaultImages.plusIcn,
                      colorFilter: ColorFilter.mode(
                          AppColor.cOrangeFont, BlendMode.srcIn),
                      width: 14,
                      height: 14),
                  horizontalSpace(8),
                  Text(
                    "Add Vehicle".trr,
                    style: pMedium12.copyWith(
                        color: AppColor.cOrangeFont, fontSize: 13),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
