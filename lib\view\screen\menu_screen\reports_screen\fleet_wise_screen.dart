// ignore_for_file: prefer_const_constructors

import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/fleetwise_controller.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/reports_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../widget/common_appbar_widget.dart';

class FleetWiseScreen extends StatefulWidget {
  final String title;
  const FleetWiseScreen({super.key, required this.title});

  @override
  State<FleetWiseScreen> createState() => _FleetWiseScreenState();
}

class _FleetWiseScreenState extends State<FleetWiseScreen> {
  // ReportController reportController = Get.put((ReportController()));
  FleetWiseController reportFleetwiseController =
      Get.put((FleetWiseController()));

  pickDateFrom() async {
    DateTime? pickDateFrom = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColor.themeOrangeColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor:
                    AppColor.themeDarkBlueColor, // button text color
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (pickDateFrom != null) {
      print(pickDateFrom);
      String formattedDate = DateFormat('dd/MM/yy').format(pickDateFrom);
      print(formattedDate);

      reportFleetwiseController.datePickerFleetFromController.text =
          formattedDate;
      //report
    } else {
      print("Date is not selected");
    }
  }

  pickDateTo() async {
    DateTime? pickDateTo = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColor.themeOrangeColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor:
                    AppColor.themeDarkBlueColor, // button text color
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (pickDateTo != null) {
      print(pickDateTo);
      String formattedDate = DateFormat('dd/MM/yy').format(pickDateTo);
      print(formattedDate);

      reportFleetwiseController.datePickerFleetToController.text =
          formattedDate;
    } else {
      print("Date is not selected");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  /*GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        left: 16,
                        top: 15,
                        bottom: 15,
                      ),
                      color: AppColor.cBackGround,
                      child: assetSvdImageWidget(
                          image: DefaultImages.backIcn,
                          colorFilter: ColorFilter.mode(
                              AppColor.cDarkBlueFont, BlendMode.srcIn)),
                    ),
                  ),*/
                  simpleMyAppBar(
                      title: "".trr,
                      backString: "Back".trr,
                      onTap: () {
                        Get.back();
                      },
                      backColor: AppColor.cBlueFont),
                ],
              ),
            ),
            /* Expanded(
              child: ListView(
                padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                children: [
                  Expanded(
                    flex: 2,
                    child: CommonTextField(
                      controller:
                      reportFleetwiseController.datePickerFleetFromController,
                      labelText: '${"Period From".trr}*',
                      suffix:
                          assetSvdImageWidget(image: DefaultImages.calendarIcn),
                      fillColor: AppColor.cWhite,
                      filled: true,
                      readOnly: true,
                      onTap: pickDateFrom,
                    ),
                  ),
                  Gap(16),
                  Expanded(
                    flex: 2,
                    child: CommonTextField(
                      controller: reportFleetwiseController.datePickerFleetToController,
                      labelText: '${"Period To".trr}*',
                      suffix:
                          assetSvdImageWidget(image: DefaultImages.calendarIcn),
                      fillColor: AppColor.cWhite,
                      filled: true,
                      readOnly: true,
                      onTap: pickDateTo,
                    ),
                  ),
                ],
              ),
            ),*/
            Center(
              child: Text(
                widget.title,
                style: pBold20,
                textAlign: TextAlign.center,
              ),
            ),
            Gap(16),
            ListView(
              padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
              physics: BouncingScrollPhysics(),
              shrinkWrap: true,
              children: [
                CommonTextField(
                  controller:
                      reportFleetwiseController.datePickerFleetFromController,
                  labelText: '${"Period From".trr}*',
                  suffix: assetSvdImageWidget(image: DefaultImages.calendarIcn),
                  fillColor: AppColor.cWhite,
                  filled: true,
                  readOnly: true,
                  onTap: pickDateFrom,
                ),
                Gap(16),
                CommonTextField(
                  controller:
                      reportFleetwiseController.datePickerFleetToController,
                  labelText: '${"Period To".trr}*',
                  suffix: assetSvdImageWidget(image: DefaultImages.calendarIcn),
                  fillColor: AppColor.cWhite,
                  filled: true,
                  readOnly: true,
                  onTap: pickDateTo,
                ),
              ],
            ),
          ],
        ),
      ),
      /*bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: Expanded(
          child: CommonButton(
            title: 'SUBMIT'.trr,
            onPressed: () {
              reportFleetwiseController.reportRequestSubmit();
            },
            textColor: AppColor.cWhiteFont,
            btnColor: AppColor.themeOrangeColor,
          ),
        ),
      ),*/
      bottomNavigationBar: Container(
        color: AppColor.cLightGrey,
        padding: EdgeInsets.all(16),
        child: CommonButton(
          // No need for Expanded here, directly place your button.
          title: 'SUBMIT'.trr,
          onPressed: () {
            reportFleetwiseController.reportRequestSubmit();
          },
          textColor: AppColor.cWhiteFont,
          btnColor: AppColor.themeOrangeColor,
        ),
      ),
    );
  }
}
