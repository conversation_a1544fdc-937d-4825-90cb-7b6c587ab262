import 'dart:convert';
import 'dart:developer' as logs;
import 'dart:math';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:quickalert/quickalert.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/b2b_bank.dart';

import 'package:waie_app/models/balancetopup.dart';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import 'package:waie_app/utils/constant_var.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/logger_extention/logger_helper.dart';
import 'package:waie_app/view/screen/login_manager/mada_alrajhi.dart';
import 'package:waie_app/view/screen/login_manager/mada_topup.dart';
import 'package:waie_app/view/screen/menu_screen/balance_topup_screen/balance_apple_pay_screen.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import '../../../../models/balancetopupnew.dart';
import '../../../../models/checkoutSession.dart';
import '../../../../models/profile.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/locale_string.dart';
import '../../../../utils/text_style.dart';
import '../../../../view/screen/auth/auth_background.dart';
import '../../../../view/screen/dashboard_manager/dashboard_manager.dart';
import '../../../../view/screen/menu_screen/balance_topup_screen/balance_topup_tab_screen.dart';
import '../../../../view/screen/menu_screen/order_screen/order_widget.dart';
import '../../../../view/widget/common_button.dart';
import '../../../../view/widget/common_otp_textfield.dart';
import '../../../../view/widget/common_snak_bar_widget.dart';
import '../../../../view/widget/common_space_divider_widget.dart';
import '../../../../view/widget/loading_widget.dart';
import '../../review_controller/review_controller.dart';

class BalanceTopUpController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  TextEditingController otpText = TextEditingController();
  TextEditingController stcPayOTPText = TextEditingController();
  RxInt currantIndex = 2.obs;
  RxString PayType = "C".obs;
  String VatAmt = "0";
  String UnitPrice = "0";
  String Amount = "0";
  TextEditingController toptupamount = TextEditingController();

  TextEditingController CustIBAN = TextEditingController(
      text: Constants.custB2B_IBAN.isEmpty
          ? "AUTO GENERATED"
          : Constants.custB2B_IBAN);

  final BalanceTopUpTabScreen __balanceTopUpTabScreen =
      const BalanceTopUpTabScreen();

  /*List selectBankList = [
    "RIYADH BANK / بنك الرياض",
    "(NCB) AL AHLI BANK / البنك الأهلي التجاري",
    "(ARB) AL-RAJHI BANK / مصرف الراجحي"
  ];*/
  List selectBankList = [];

  // RxString selectedBank = 'RIYADH BANK / بنك الريا'.obs;
  RxString selectedBank = ''.obs;
  RxBool isShowEmail = false.obs;
  RxString expireMonth = 'MM'.obs;
  RxString expireYear = 'YY'.obs;
  RxString isoCode = 'SA'.obs;
  RxBool isValidate = false.obs;
  RxBool isSubmit = true.obs;
  RxBool isSTCPayValidate = false.obs;
  RxBool isSTCPaySubmit = true.obs;
  RxBool isMokafa = true.obs;
  RxBool isQitaf = false.obs;
  RxBool isANB = false.obs;
  RxBool isALINMA = false.obs;

  //String paytype = "C";
  RxString paytype = ''.obs;
  String prmType = "";
  String phoneNumber = "";
  String stcPayPhoneNumber = "";
  RxString prmPayType = "J".obs;
  var checkoutSessionList = <CheckoutSessionModel>[].obs;
  var checkoutSession = "".obs;
  RxList paymentOptionList = [].obs;
  List<B2BBankListModel> bankList = [];

  void addNewPaymentOption() {
    int i = 0;
    RxBool isPaymentSelected = true.obs;
    RxBool isAppleSelected = false.obs;
    if (Platform.isIOS) {
      currantIndex = 5.obs;
      isAppleSelected = true.obs;
      isPaymentSelected = false.obs;
    }
    print("currantIndex >>>> ${currantIndex}");
    print("isPaymentSelected >>>> ${isPaymentSelected}");
    RxMap<String, Object> newOption1 = {
      "id": 0.obs,
      'value': false.obs,
      "balance": "",
      'title': 'Cash'.tr,
      'label': 'Cash payment instructions'.tr
    }.obs;
    paymentOptionList.insert(i, newOption1);
    i++;
    //  if (Constants.ETrans=="Y")
    {
      RxMap<String, Object> newOption4 = {
        "id": 1.obs,
        'value': false.obs,
        "balance": "",
        'title': 'E. Transfer'.tr,
        'label': 'E. Transfer Note'.tr
      }.obs;

      paymentOptionList.insert(i, newOption4);
      i++;
    }
    //  if (Constants.MadaPayOpt=="Y")
    {
      RxMap<String, Object> newOption2 = {
        "id": 2.obs,
        'value': isPaymentSelected,
        "balance": "",
        'title': 'MADA'.tr,
        'label': 'MADA Payment Note'.tr
      }.obs;
      paymentOptionList.insert(i, newOption2);
      i++;
    }
    //if (Constants.custRegType == "I" && Constants.custAcctType == "C") {
    // if (Constants.promoPayment == "Y") {
    RxMap<String, Object> newOption3 = {
      "id": 3.obs,
      'value': false.obs,
      "balance": "",
      'title': 'Aldrees partners'.tr,
      'label': 'Aldrees partners Note'
    }.obs;

    paymentOptionList.insert(i, newOption3);
    i++;
    //}
    RxMap<String, Object> newOption6 = {
      "id": 5.obs,
      'value': false.obs,
      "balance": "",
      'title': 'Apple Pay'.tr,
      'label': 'Apple Pay Note'.tr
    }.obs;
    if (Platform.isIOS && Constants.IsApplePayEnable == "Y") {
      paymentOptionList.insert(i, newOption6);
      i++;
    }
    RxMap<String, Object> newOption5 = {
      "id": 4.obs,
      'value': isAppleSelected,
      "balance": "",
      'title': 'Stc pay'.tr,
      'label': 'Stc pay Note'
    }.obs;

    paymentOptionList.insert(i, newOption5);
    i++;
  }

  String AmountValue = "0";
  double VatAmount = 0;
  String VatExclusiveAmount = "0";
  //String VatExclusiveAmount = "0";
  Function? updateUi;
  void setUpdateUiCallback(Function updateUiCallback) {
    updateUi = updateUiCallback;
  }

  void clear_Screen() {
    toptupamount.text = "";
    phoneNumber = "";
    otpText.text = "";
    UnitPrice = "";
    VatAmt = "";
    Amount = "";
    AmountValue = "0";
    VatExclusiveAmount = "0";
    VatAmount = 0;
  }

  Future<List<B2BBankListModel>> getB2BSender() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    try {
      var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getB2BSENDER),
      );

      print("getB2BSender=====${response.body}");

      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        B2BBankListModel bank =
            B2BBankListModel.fromJson(result[i] as Map<String, dynamic>);
        bankList.add(bank);
      }

      return bankList;
    } catch (e) {
      logs.log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    print("Constants.custB2B_IBAN");
    print(Constants.custB2B_IBAN);
    print("=================");
    //toptupamount.text="1";
    if (Constants.virtualAccountBTN == 'Y') getB2BSender();
    addNewPaymentOption();
    /*if (Constants.custB2B_BANK == "NCB") {
      selectedBank.value = '(NCB) AL AHLI BANK / البنك الأهلي التجاري';
    } else if (Constants.custB2B_BANK == "ARB") {
      selectedBank.value = '(ARB) AL-RAJHI BANK / مصرف الراجحي';
    } else {
      selectedBank.value = 'RIYADH BANK / بنك الرياض';
    }*/

    if (Constants.custB2B_BANK == "NCB" || Constants.bankNCB == "Y") {
      selectedBank.value = '(NCB) AL AHLI BANK / البنك الأهلي التجاري';
      selectBankList = ["(NCB) AL AHLI BANK / البنك الأهلي التجاري"];
    }
    if (Constants.custB2B_BANK == "ARB" || Constants.bankARB == "Y") {
      selectedBank.value = '(ARB) AL-RAJHI BANK / مصرف الراجحي';
      selectBankList = ["(ARB) AL-RAJHI BANK / مصرف الراجحي"];
    }
    if (Constants.custB2B_BANK == "RYD" || Constants.bankRYD == "Y") {
      selectedBank.value = 'RIYADH BANK / بنك الرياض';
      selectBankList = ["RIYADH BANK / بنك الرياض"];
    }

    if (Constants.virtualAccountBTN == "Y" && Constants.custB2B_IBAN == "") {
      CustIBAN.text = "AUTO GENERATED";
    }
  }

  static const platform = MethodChannel("flutter.native.helper");

  static const MethodChannel _channel = MethodChannel('flutter.native.helper');

  void yourDartFunction() {
    print('Your Dart function is called!');
    // Your Dart function logic here
  }

  void callDartFunctionFromJava() {
    // Call Dart function from Java using the method channel
    _channel.invokeMethod('yourDartFunction', "");
  }

  Future<dynamic> nativeMethodCallHandler(MethodCall methodCall) async {
    print('Native call!');
    switch (methodCall.method) {
      case "methodNameItz":
        print("Return From Java");
        return "This data from flutter.....";
        break;
      default:
        print("Return From Java");
        return "Nothing";
        break;
    }
  }

  prepareApplePay() async {
    print("prepareApplePay >>>>");
    showDialog(
      context: Get.context!,
      builder: (context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    List<BalanceTopUpModelNew> balances = [];
    const chars = '1234567890';
    var rnd = Random();
    var custData = custsData.read('custData');
    var device = "";
    if (Platform.isAndroid || Platform.isWindows) {
      device = "ANDROID";
    } else if (Platform.isIOS) {
      device = "IOS";
    }

    String getRandomString(int length) =>
        String.fromCharCodes(Iterable.generate(
            length, (_) => chars.codeUnitAt(rnd.nextInt(chars.length))));

    var newOrderID = getRandomString(10);
    print('newOrderID >>>> $newOrderID');

    // Create uuid object
    var newUuid = const Uuid();

    // Generate a v4 (random) id
    var depositTo = newUuid.v4(); // -> '110ec58a-a0f2-4ac4-8393-c866d813b8d1'
    var orderID = ""; //newOrderID;

    if (Platform.isAndroid || Platform.isWindows) {
      orderID = "A$newOrderID";
    } else if (Platform.isIOS) {
      orderID = "L$newOrderID";
    }
    print("orderID >>>> $orderID");
    print("depositTo >>>> $depositTo");
    paytype.value = "D";
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.createBalOrder),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
          body: {
            "custid": userid,
            "emailid": userData.auCust?.emailid,
            //"<EMAIL>",
            "regtype": userData.auCust?.regtype,
            //"I",
            "accttype": userData.auCust?.accttype,
            //"C",
            "mobileno": phoneNumber.replaceAll("+", ""),
            //userData.auCust?.mobileno, //"************",
            "paytype": paytype.value,
            "topupamt": toptupamount.text ?? "0",
            // "topupamt": paytype ?? "10",
            "prmtype": prmType,
            //J=Mokafa, S=QITAF, N=ANB
            "depositto": depositTo,
            "payrefno": orderID,
            "device": device,
          });

      print("prepareApplePay1111 >>>> ${paytype.value}");
      if (paytype.value == "D") {
        final jsonData = jsonDecode(response.body);
        print("result");
        print("=======================================");
        print(
            'orderID jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');
        if (jsonData is Map && jsonData.containsKey('ExceptionMessage')) {
          Navigator.of(Get.context!).pop();
          print(
              'jsonDecode(response.body)["ExceptionMessage"] >>>> ${jsonDecode(response.body)["ExceptionMessage"]}');
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["ExceptionMessage"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () {
                        Get.back();
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          var result = jsonDecode(response.body)[0]["RES_BODY"];
          final json = "[$result]";
          List resu = (jsonDecode(json) as List<dynamic>);

          for (int i = 0; i < resu.length; i++) {
            CheckoutSessionModel session =
                CheckoutSessionModel.fromJson(resu[i] as Map<String, dynamic>);
            checkoutSessionList.add(session);
          }
          var sess =
              checkoutSessionList.map((item) => item.session.id).toString();

          print(
              "checkoutSessionList===> ${jsonDecode(jsonEncode(checkoutSessionList))[0]["result"]}");
          print(
              "checkoutSessionList===> ${sess.replaceAll(RegExp(r"\p{P}", unicode: true), "")}");
          checkoutSession.value =
              sess.replaceAll(RegExp(r"\p{P}", unicode: true), "");

          print(checkoutSession.value);
          print("orderID ==== $orderID");
          print("depositTo ==== $depositTo");

          String basicAuth =
              'Basic ${base64Encode(utf8.encode('merchant.${ConstantVar.madaMerchantid}:${ConstantVar.madaAPIkey}'))}';
          var responses = await client.post(
              Uri.parse(
                  "${ConstantVar.madaGateway}/api/rest/version/73/merchant/${ConstantVar.madaMerchantid}/session"),
              headers: <String, String>{
                'authorization': basicAuth,
                "Access-Control-Allow-Credentials": 'true',
                "Access-Control-Allow-Headers":
                    "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS"
              });
          print("createSession==============================");
          print(jsonDecode(responses.body));
          final createSession = jsonDecode(responses.body);
          print(createSession['session']["id"]);
          print("createSession==============================");

          if (jsonDecode(jsonEncode(checkoutSessionList))[0]["result"] ==
              "SUCCESS") {
            Loader.hideLoader();
            // showDialog(
            //   barrierDismissible: false,
            //   context: Get.context!,
            //   builder: (context) {
            //     return AlertDialog(
            //       contentPadding: const EdgeInsets.all(16),
            //       shape: RoundedRectangleBorder(
            //           borderRadius: BorderRadius.circular(12)),
            //       insetPadding: const EdgeInsets.all(25),
            //       content: madaBalanceSuccessWidget(
            //         orderID: orderID,
            //         code: userid,
            //         unitPrice:
            //             double.parse(UnitPrice).toStringAsFixed(2).toString(),
            //         vat: double.parse(VatAmt).toStringAsFixed(2).toString(),
            //         purchaseTotal:
            //             double.parse(Amount).toStringAsFixed(2).toString(),
            //         sessionID: checkoutSession.value,
            //         depositto: depositTo,
            //         orderType: "TOPUP",
            //         custid: userid,
            //       ),
            //     );
            //   },
            // );
            Get.to(() => BalanceApplePayScreen(
                  totalPurchased:
                      double.parse(Amount).toStringAsFixed(2).toString(),
                  sessionID: createSession['session']["id"],
                  orderID: orderID,
                  depositto: depositTo,
                  serviceType: paytype.toString(),
                  qty: "1",
                  orderType: "SERVICE",
                  custid: userid.toString(),
                  unitPrice:
                      double.parse(UnitPrice).toStringAsFixed(2).toString(),
                  vat: double.parse(VatAmt).toStringAsFixed(2).toString(),
                ));
          }
        }
      } else {
        Loader.hideLoader();
        print("TOPUP ========== failed");
        // FailedDialog().totupFailed();
        showErrorMsg("Balance Topup Failed");
      }

      return balances;
    } catch (e) {
      Loader.hideLoader();
      logs.log(e.toString());
      return [];
    }
  }

  sendAlraljhiApplePay(
    token,
    paymentMethod,
    transactionIdentifier,
    totalPurchased,
    orderID,
    serviceType,
    qty,
    orderType,
    custid,
    subTotal,
    vat,
  ) async {
    Loader.showLoader();
    print("sendAlraljhiApplePay==============================");
    print(token);
    print(paymentMethod);
    print(transactionIdentifier);
    print(totalPurchased);
    print(orderID);
    print("sendAlraljhiApplePay=***========");

    print("");

    var client = http.Client();
    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.sendAlrajhiApplePayReq),
          body: {
            "TOKEN": token,
            "PAYMENTMETHOD": jsonEncode(paymentMethod),
            "TRANSIDENTIFIER": transactionIdentifier,
            "TOTALPURCHASED": totalPurchased,
            "ORDERID": orderID
          });
      print("fwregrfdasfsd==============================");
      print(jsonEncode(response.body));

      print(response.body);
      var convertDataToJson = jsonDecode(response.body);
      print(convertDataToJson);
      //List data = convertDataToJson['results'];

      if (convertDataToJson['Success'] == true &&
          convertDataToJson['Code'] == 200) {
        print(convertDataToJson['Success']);
        print(convertDataToJson['Code']);
        await confirmTopup("", totalPurchased, orderID, transactionIdentifier,
            serviceType, qty, "");
        Loader.hideLoader();
        await QuickAlert.show(
            context: Get.context!,
            type: QuickAlertType.success,
            title: convertDataToJson['Message'],
            confirmBtnText: "Continue to Dashboard".trr,
            //confirmBtnColor: AppColor.themeOrangeColor,
            onConfirmBtnTap: () {
              Get.offAll(DashBoardManagerScreen(
                currantIndex: 0,
              ));
            });
      } else {
        Loader.hideLoader();
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          title: convertDataToJson['Message'],
          onCancelBtnTap: () {
            Get.back();
          },
        );
      }

      return null;
    } catch (e) {
      print("e.toString()");
      print(e.toString());
      logs.log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  updateSession(
    token,
    sessionID,
    orderID,
    totalPurchased,
    depositto,
    serviceType,
    qty,
    orderType,
    custid,
    subTotal,
    vat,
  ) async {
    Loader.showLoader();
    var client = http.Client();

    String basicAuth =
        'Basic ${base64Encode(utf8.encode('merchant.${ConstantVar.madaMerchantid}:${ConstantVar.madaAPIkey}'))}';
    var responses = await client.put(
      Uri.parse(
          "${ConstantVar.madaGateway}/api/rest/version/73/merchant/${ConstantVar.madaMerchantid}/session/$sessionID"),
      headers: <String, String>{
        'authorization': basicAuth,
        "Access-Control-Allow-Credentials": 'true',
        "Access-Control-Allow-Headers":
            "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: json.encode({
        "apiOperation": "AUTHENTICATE_PAYER",
        "authentication": {
          "redirectResponseUrl": "https://localhost:8000/",
          "acceptVersions": "3DS2",
          "channel": "PAYER_BROWSER",
          "purpose": "PAYMENT_TRANSACTION"
        },
        "device": {
          "browser": "MOZILLA",
          "browserDetails": {
            "3DSecureChallengeWindowSize": "FULL_SCREEN",
            "acceptHeaders": "application/json",
            "colorDepth": 24,
            "javaEnabled": true,
            "language": "en-US",
            "screenHeight": 640,
            "screenWidth": 480,
            "timeZone": 273
          },
          "ipAddress": "127.0.0.1"
        },
        "sourceOfFunds": {
          "type": "CARD",
          "provided": {
            "card": {
              "devicePayment": {"paymentToken": "$token"}
            }
          }
        },
        "order": {"walletProvider": "APPLE_PAY"}
      }),
    );
    print("updateSession==============================");
    print(jsonDecode(responses.body));
    final updateSession = jsonDecode(responses.body);
    print("updateSession==============================");
    print(updateSession['session']);
    print(updateSession['session']["id"]);
    print(updateSession['session']["updateStatus"]);
    print(updateSession['session']["version"]);
    print("updateSession==============================");

    if (updateSession['session']["updateStatus"] == "SUCCESS") {
      var payResponse = await client.put(
        Uri.parse(
            "${ConstantVar.madaGateway}/api/rest/version/73/merchant/${ConstantVar.madaMerchantid}/order/$orderID/transaction/$orderID"),
        headers: <String, String>{
          'authorization': basicAuth,
          "Access-Control-Allow-Credentials": 'true',
          "Access-Control-Allow-Headers":
              "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: json.encode({
          "apiOperation": "PAY",
          "order": {
            "amount": totalPurchased,
            "currency": "SAR",
            "reference": "$orderID"
          },
          "transaction": {"reference": "$orderID"},
          "session": {"id": "$sessionID"},
          "sourceOfFunds": {"type": "CARD"}
        }),
      );
      print("payResponse==============================");
      print(jsonDecode(payResponse.body));
      final payResponses = jsonDecode(payResponse.body);
      print("payResponse==============================");
      print(payResponses['response']);
      print(payResponses['response']["gatewayCode"]);
      print("payResponse==============================");

      if (payResponses['response']["gatewayCode"] == "APPROVED") {
        // CALL CREATE ORDER_HDR
        //commonToast("نجاح : Success");
        await confirmTopup(sessionID, totalPurchased, orderID, depositto,
            serviceType, qty, payResponses['response']["gatewayCode"]);
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "نجاح : Success",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "Continue to Dashboard".tr,
                    onPressed: () {
                      Get.offAll(DashBoardManagerScreen(
                        currantIndex: 0,
                      ));
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        //commonToast("${payResponses['response']["acquirerMessage"]}");
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${payResponses['response']["acquirerMessage"]}",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    }
  }

  confirmTopup(sessionID, totalPurchased, orderID, depositTo, serviceType, qty,
      gatewayCode) async {
    var client = http.Client();
    var custData = custsData.read('custData');

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.confirmOrder),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
        body: {
          "custid": custData['CUSTID'],
          "depositto": depositTo,
          "transactionid": depositTo,
          "mobileno": custData['MOBILENO'],
          "amount": totalPurchased,
          "OrderTypes": "T",
          "topupamt": totalPurchased ?? "1",
          "qty": qty ?? "1",
          "servicetype": serviceType,
          "paytype": "D",
          "payrefno": orderID,
          "is_mokafa50": "false",
          "IsAR": Constants.IsAr_App,
          "serialid": "",
          "jsonrslt": gatewayCode, //jsonrslt
        });
    print("result");
    print("=======================================");
    print('jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');

    return response;
  }

  prepareSTCPay() async {
    print("paytype.value >>>> ${paytype.value}");
    print("toptupamount.text >>>> ${toptupamount.text}");
    print("STCPayPhoneNumber >>>> $stcPayPhoneNumber");
    print("STCPayPhoneNumber >>>> ${stcPayPhoneNumber.replaceAll("+", "")}");
  }

  newBalOrder() async {
    Loader.showLoader();
    var client = http.Client();
    var device = "";
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    var custData = jsonEncode(custsData.read('custData'));

    print("paytype.value ===============${paytype.value}");
    print("toptupamount.text =============== ${toptupamount.text}");
    print("custsData ===============${jsonEncode(custsData.read('custData'))}");
    if (paytype.value == "Y") {
      print("STCPayPhoneNumber >>>> ${stcPayPhoneNumber.replaceAll("+", "")}");
    }

    if (Platform.isAndroid || Platform.isWindows) {
      device = "A";
    } else if (Platform.isIOS) {
      device = "I";
    }
    print("device ===============$device");

    var stcPayMobileNo = stcPayPhoneNumber.replaceAll("+", "");
    print("stcPayMobileNo ===============$stcPayMobileNo");

    print("==================1213123=====================");
    print("custData ===============$custData");
    print(
        "paytype ===============${paytype.value == "" ? "D" : paytype.value}");
    print("topupamt ===============${toptupamount.text}");
    print("MADAALRAJHI ===============$device");
    print("=======================================");
    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.newBalOrder),
        body: {
          "custdata": custData,
          "paytype": paytype.value == "" ? "D" : paytype.value,
          "topupamt": toptupamount.text ?? "0",
          "MADAALRAJHI": device,
          //"mobileNo": "966506027231",
          "mobileNo": paytype.value == "D"
              ? ""
              : paytype.value == "Y"
                  ? stcPayPhoneNumber.replaceAll("+", "")
                  : "",
        });
    // BalanceTopUpModelNew result =
    //     BalanceTopUpModelNew.fromJson(jsonDecode(response.body));
    final jsonData = response.body;
    logInfo("result FUZAIL GWAPO ${jsonData}");
    print(jsonData);
    print("=======================================");
    print('orderID jsonDecode(response.body) >>>> ${response.body}');
    print("=======================================");
    print(jsonDecode(response.body)["MessageType"]);
    print(jsonDecode(response.body)["message"]);
    if (jsonDecode(response.body)["MessageType"] == "success") {
      if (paytype.value == "D") {
        String urlWithQuotes = '${jsonDecode(response.body)["message"]}';
        String urlWithoutQuotes = urlWithQuotes.replaceAll('"', '');

        print(urlWithoutQuotes);
        var replacement = "false";
        await Get.to(() => MADAtopupScreen(
              url: urlWithoutQuotes,
              replacement: replacement,
              totalAmount: toptupamount.text.toString() ?? "0",
            ));
      } else if (paytype.value == "Y") {
        Loader.hideLoader();
        print("=======================================");
        print("STCPAY");
        print("=======================================");
        print(jsonDecode(response.body)["MessageType"]);
        print(jsonDecode(response.body)["message"]);
        print("=======================================");
        showSTCPAYOTPDialog(
          jsonDecode(response.body)["custID"],
          jsonDecode(response.body)["userID"],
          jsonDecode(response.body)["mobileNo"],
          jsonDecode(response.body)["totalAmount"],
          jsonDecode(response.body)["STCPayOtpReference"],
          jsonDecode(response.body)["STCPayPmtReference"],
        );
      }
    } else {
      Loader.hideLoader();
      print(jsonDecode(response.body)["message"]);
      await QuickAlert.show(
        context: Get.context!,
        type: QuickAlertType.error,
        title: jsonDecode(response.body)['message'].toString(),
      );
    }
  }

  validateSTCPAYSUBMIT(
    custID,
    userID,
    mobileNo,
    totalAmount,
    STCPayOtpReference,
    STCPayPmtReference,
    enteredOTP,
  ) async {
    Loader.showLoader();
    var client = http.Client();

    print("*****validateSTCPAYSUBMIT*****");
    print("custID ===============$custID");
    print("userID ===============$userID");
    print("mobileNo ===============$mobileNo");
    print("totalAmount ===============$totalAmount");
    print("STCPayOtpReference ===============$STCPayOtpReference");
    print("STCPayPmtReference ===============$STCPayPmtReference");
    print("enteredOTP ===============$enteredOTP");

    var response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl +
            ApiEndPoints.authEndpoints.validateSTCPaySubmit),
        body: {
          "custID": custID,
          "userID": userID,
          "mobileNo": mobileNo,
          "totalAmount": totalAmount.toString(),
          "stcOTP": enteredOTP,
          "STCPayOtpReference": STCPayOtpReference,
          "STCPayPmtReference": STCPayPmtReference,
          "IsAr": Constants.IsAr_App,
        });
    final jsonData = response.body;
    print("result");
    print(jsonData);
    print("=======================================");
    print(
        'validateSTCPAYSUBMIT jsonDecode(response.body) >>>> ${response.body}');
    print("=======================================");
    print(jsonDecode(response.body)["MessageType"]);
    print(jsonDecode(response.body)["Message"]);
    if (jsonDecode(response.body)["Action"] == "SUCCESS") {
      Loader.hideLoader();
      print(jsonDecode(response.body)["Message"]);
      // await QuickAlert.show(
      //   context: Get.context!,
      //   type: QuickAlertType.success,
      //   text: jsonDecode(response.body)['Message'].toString(),
      // );
      // await Get.offAll(() => DashBoardManagerScreen(
      //       currantIndex: 0,
      //     ));
      showDialog(
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            contentPadding: const EdgeInsets.all(16),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            insetPadding: const EdgeInsets.all(25),
            content: stcPaySuccessWidget(
              code: jsonDecode(response.body)['ActionParam'].toString(),
              total: totalAmount.toString(),
            ),
          );
        },
      );
    } else {
      Loader.hideLoader();
      print(jsonDecode(response.body)["Message"]);
      await QuickAlert.show(
        context: Get.context!,
        type: QuickAlertType.error,
        title: jsonDecode(response.body)['Message'].toString(),
      );
    }
  }

  Future<List<BalanceTopUpModelNew>> CreateBalOrder() async {
    Loader.showLoader();
    if (paymentOptionList[0]['value'].value) {
      paytype.value = "C";
    } else if (paymentOptionList[1]['value'].value) {
      paytype.value = "E";
    } else if (paymentOptionList[2]['value'].value) {
      paytype.value = "D";
    } else {
      paytype.value = "PRM";
      if (isMokafa.value) {
        paytype.value = "J";
      } else if (isQitaf.value) {
        paytype.value = "S";
      } else if (isANB.value) {
        paytype.value = "N";
      } else if (isALINMA.value) {
        prmType = "L";
      }
      //prmType="J";
    }

    print('paytype.value >>>> ${paytype.value}');
    print(
        "CreateBalOrder PRM=====$prmType  === $prmPayType and paytype ${paytype.value}");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    List<BalanceTopUpModelNew> balances = [];
    const chars = '1234567890';
    var rnd = Random();
    var device = "ANDROID";
    if (Platform.isAndroid || Platform.isWindows) {
      device = "ANDROID";
    } else if (Platform.isIOS) {
      device = "IOS";
    }

    String getRandomString(int length) =>
        String.fromCharCodes(Iterable.generate(
            length, (_) => chars.codeUnitAt(rnd.nextInt(chars.length))));

    var newOrderID = getRandomString(10);
    print('newOrderID >>>> $newOrderID');

    // Create uuid object
    var newUuid = const Uuid();

    // Generate a v4 (random) id
    var depositTo = newUuid.v4(); // -> '110ec58a-a0f2-4ac4-8393-c866d813b8d1'
    var orderID = "A$newOrderID"; //newOrderID;

    if (Platform.isAndroid || Platform.isWindows) {
      orderID = "A$newOrderID";
    } else if (Platform.isIOS) {
      orderID = "I$newOrderID";
    }
    print("orderID >>>> $orderID");
    print("depositTo >>>> $depositTo");
    print("phoneNumber >>>> $phoneNumber");
    print("phoneNumber.replaceAll("
        ", "
        ") >>>> ${phoneNumber.replaceAll("+", "")}");
    Constants.mobileno = phoneNumber.replaceAll("+", "");
    Constants.PRMAmount = toptupamount.text ?? "0";
    print("Constants.mobileno >>>> ${Constants.mobileno}");
    print("Constants.PRMAmount >>>> ${Constants.PRMAmount}");

    print("body======================================");
    print("custid >>>> $userid");
    print("emailid >>>> ${userData.auCust?.emailid}");
    print("regtype >>>> ${userData.auCust?.regtype}");
    print("accttype >>>> ${userData.auCust?.accttype}");
    print("mobileno >>>> ${phoneNumber.replaceAll("+", "")}");
    print("paytype >>>> ${paytype.value}");
    print("topupamt >>>> ${toptupamount.text}");
    print(
        "prmtype >>>> ${paytype.value == "D" ? paytype.value == "C" ? paytype.value == "E" ? "" : "" : "" : prmPayType.value}");
    print("depositto >>>> $depositTo");
    print("payrefno >>>> $orderID");
    print("device >>>> $device");
    print("body======================================");

    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.createBalOrder),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
          body: {
            "custid": userid,
            "emailid": userData.auCust?.emailid,
            //"<EMAIL>",
            "regtype": userData.auCust?.regtype,
            //"I",
            "accttype": userData.auCust?.accttype,
            //"C",
            "mobileno": phoneNumber.replaceAll("+", ""),
            //userData.auCust?.mobileno, //"************",
            "paytype": paytype.value,
            "topupamt": toptupamount.text ?? "0",
            // "topupamt": paytype ?? "10",
            "prmtype": paytype.value == "D"
                ? paytype.value == "C"
                    ? paytype.value == "E"
                        ? ""
                        : ""
                    : ""
                : prmPayType.value,
            //J=Mokafa, S=QITAF, N=ANB
            "depositto": depositTo,
            "payrefno": orderID,
            "device": device,
          });

      print("jsonDecode(response.body) >>>> ${jsonDecode(response.body)}");
      //showOTPDialog();
      if (paytype.value != "D") {
        BalanceTopUpModelNew result =
            BalanceTopUpModelNew.fromJson(jsonDecode(response.body));
        //toptupamount.text="0";
        Loader.hideLoader();
        BalanceTopUpModelNew balanceModel = result;
        print("TOPUP======================================");
        print(result);
        print("result");
        print(response.body);
        print("TOPUP======================================");
        // if (true) {
        if (result.returnMessage?.action == 'EXCEPTION') {
          // if (false) {
          //showErrorMsg(result.returnMessage!.message.toString());

          Loader.hideLoader();
          await QuickAlert.show(
            context: Get.context!,
            type: QuickAlertType.error,
            title: result.returnMessage!.message.toString(),
            onCancelBtnTap: () {
              Get.back();
            },
          );
        } else {
          if (paytype.value == "C" || paytype.value == "E") {
            prmPayType.value = "";
            print('prmPayType.value >>>> ${prmPayType.value}');
            var orderID = balanceModel.reference;
            Loader.hideLoader();
            if (currantIndex.value == 1) {
              await QuickAlert.show(
                  context: Get.context!,
                  type: QuickAlertType.success,
                  title: "Your order has been created successfully!".trr,
                  confirmBtnText: "Continue to Dashboard".trr,
                  //confirmBtnColor: AppColor.themeOrangeColor,
                  onConfirmBtnTap: () {
                    Get.offAll(DashBoardManagerScreen(
                      currantIndex: 0,
                    ));
                    final reviewController = Get.find<ReviewController>();
                    reviewController.triggerReview();
                  });
            } else if (currantIndex.value == 0) {
              showDialog(
                context: Get.context!,
                builder: (context) {
                  return AlertDialog(
                    contentPadding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    insetPadding: const EdgeInsets.all(25),
                    content: cashBalanceSuccessWidget(code: orderID.toString()),
                  );
                },
              );
            }
            clear();
            if (updateUi != null) {
              updateUi!();
            }
          }
          if (prmPayType.value == 'J' ||
              prmPayType.value == 'S' ||
              prmPayType.value == 'N' ||
              prmPayType.value == 'L') {
            if (result.returnMessage?.action == 'EXCEPTION') {
              // if (false) {
              //showErrorMsg(result.returnMessage!.message.toString());

              Loader.hideLoader();
              await QuickAlert.show(
                context: Get.context!,
                type: QuickAlertType.error,
                title: result.returnMessage!.message.toString(),
                onCancelBtnTap: () {
                  Get.back();
                },
              );
            } else {
              print("ANB=======================================");
              print("result.aNBAccessToken >>>>>>> ${result.aNBAccessToken}");
              print("result.aNBOTPToken >>>>>>> ${result.aNBOTPToken}");
              print("result.aNBPayRefNo >>>>>>> ${result.aNBPayRefNo}");
              print(
                  "userData.auCust?.custid >>>>>>> ${userData.auCust?.custid}");
              print(
                  "Constants.mobileno.toString() >>>>>>> ${Constants.mobileno.toString()}");
              print("Constants.PRMAmount >>>>>>> ${Constants.PRMAmount}");
              print("Constants.IsAr_App >>>>>>> ${Constants.IsAr_App}");
              print("prmPayType.value >>>>>>> ${prmPayType.value}");
              print("result.aNBPayRefNo >>>>>>> ${result.aNBPayRefNo}");
              print("ANB=======================================");
              // Constants.mobileno = phoneNumber.replaceAll("+", "");
              // Constants.PRMAmount = toptupamount.text ?? "0";
              if (prmPayType.value == 'J') {
                Constants.prmAccessToken = result.mOKAFAAccessToken;
                Constants.prmOTPToken = result.mOKAFAOTPToken;
                Constants.prmPayrefNo = result.mOKAFAPayRefNo;
              }
              if (prmPayType.value == 'N') {
                Constants.prmAccessToken = result.aNBAccessToken;
                Constants.prmOTPToken = result.aNBOTPToken;
                Constants.prmPayrefNo = result.aNBPayRefNo;
              }
              if (prmPayType.value == 'S') {
                Constants.prmAccessToken = result.qitafDepositTo;
                Constants.prmOTPToken = result.qitafDepositTo;
                Constants.prmPayrefNo = result.qitafPayRefNo;
              }
              if (prmPayType.value == 'L') {
                Constants.prmAccessToken = result.alinmaAccessToken;
                Constants.prmOTPToken = result.alinmaAccessToken;
                Constants.prmPayrefNo = result.alinmaPaymentRef;
              }
              Loader.hideLoader();
              showOTPDialog();
            }
          }
        }
        balances.add(result);
      } else if (paytype.value == "D") {
        final jsonData = jsonDecode(response.body);
        print("result");
        print("=======================================");
        print(
            'orderID jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');
        if (jsonData is Map && jsonData.containsKey('ExceptionMessage')) {
          Navigator.of(Get.context!).pop();
          print(
              'jsonDecode(response.body)["ExceptionMessage"] >>>> ${jsonDecode(response.body)["ExceptionMessage"]}');
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      jsonDecode(response.body)["ExceptionMessage"],
                      style: pBold20,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    CommonButton(
                      title: "OK".tr,
                      onPressed: () {
                        Get.back();
                      },
                      btnColor: AppColor.themeOrangeColor,
                    )
                  ],
                ),
              );
            },
          );
        } else {
          var result = jsonDecode(response.body)[0]["RES_BODY"];
          final json = "[$result]";
          List resu = (jsonDecode(json) as List<dynamic>);

          for (int i = 0; i < resu.length; i++) {
            CheckoutSessionModel session =
                CheckoutSessionModel.fromJson(resu[i] as Map<String, dynamic>);
            checkoutSessionList.add(session);
          }
          var sess =
              checkoutSessionList.map((item) => item.session.id).toString();

          print(
              "checkoutSessionList===> ${jsonDecode(jsonEncode(checkoutSessionList))[0]["result"]}");
          print(
              "checkoutSessionList===> ${sess.replaceAll(RegExp(r"\p{P}", unicode: true), "")}");
          checkoutSession.value =
              sess.replaceAll(RegExp(r"\p{P}", unicode: true), "");

          print(checkoutSession.value);
          print("orderID ==== $orderID");
          print("depositTo ==== $depositTo");
          if (jsonDecode(jsonEncode(checkoutSessionList))[0]["result"] ==
              "SUCCESS") {
            Loader.hideLoader();
            showDialog(
              barrierDismissible: false,
              context: Get.context!,
              builder: (context) {
                return AlertDialog(
                  contentPadding: const EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                  insetPadding: const EdgeInsets.all(25),
                  content: madaBalanceSuccessWidget(
                    orderID: orderID,
                    code: userid,
                    unitPrice:
                        double.parse(UnitPrice).toStringAsFixed(2).toString(),
                    vat: double.parse(VatAmt).toStringAsFixed(2).toString(),
                    purchaseTotal:
                        double.parse(Amount).toStringAsFixed(2).toString(),
                    sessionID: checkoutSession.value,
                    depositto: depositTo,
                    orderType: "TOPUP",
                    custid: userid,
                  ),
                );
              },
            );
          }
        }
      } else {
        Loader.hideLoader();
        print("TOPUP ========== failed");
        // FailedDialog().totupFailed();
        showErrorMsg("Failed to Fetch Data");
      }
//      clear_Screen();

      return balances;
    } catch (e) {
      Loader.hideLoader();
      logs.log(e.toString());
      return [];
    }
  }

  Future<dynamic> ValidatePRMOTP() async {
    Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);

    print("ANB=======================================");
    print("result.aNBAccessToken >>>>>>> ${Constants.prmAccessToken}");
    print("result.aNBOTPToken >>>>>>> ${Constants.prmOTPToken}");
    print("result.aNBPayRefNo >>>>>>> ${Constants.prmPayrefNo}");
    print("userData.auCust?.custid >>>>>>> ${userData.auCust?.custid}");
    print(
        "Constants.mobileno.toString() >>>>>>> ${Constants.mobileno.toString()}");
    print("Constants.PRMAmount >>>>>>> ${Constants.PRMAmount}");
    print("Constants.IsAr_App >>>>>>> ${Constants.IsAr_App}");
    print("prmPayType.value >>>>>>> ${prmPayType.value}");
    print("otpText.text >>>>>>> ${otpText.text}");
    print("ANB=======================================");
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.PRMBalOrder),
          body: {
            "custid": userData.auCust?.custid,
            "mobileno": Constants.mobileno.toString(),
            "userid": userData.auUsers?.emailid,
            "amount": Constants.PRMAmount,
            "accessToken": Constants.prmAccessToken,
            "otptoken": Constants.prmOTPToken,
            "payrefno": Constants.prmPayrefNo,
            "IsAR": Constants.IsAr_App,
            "prmType": prmPayType.value,
            "otp": otpText.text,
          });
      print("PRM OTP Responce==========${response.body}");
      ReturnMessage message = ReturnMessage.fromJson(jsonDecode(response.body));
      if (message.action == 'SUCCESS') {
        Loader.hideLoader();
        await QuickAlert.show(
            context: Get.context!,
            type: QuickAlertType.success,
            title: "Your order has been created successfully!".trr,
            confirmBtnText: "Continue to Dashboard".trr,
            //confirmBtnColor: AppColor.themeOrangeColor,
            onConfirmBtnTap: () {
              Get.offAll(DashBoardManagerScreen(
                currantIndex: 0,
              ));
              final reviewController = Get.find<ReviewController>();
              reviewController.triggerReview();
            });
        // showDialog(
        //   context: Get.context!,
        //   builder: (context) {
        //     return AlertDialog(
        //       contentPadding: const EdgeInsets.all(16),
        //       shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12)),
        //       insetPadding: const EdgeInsets.all(25),
        //       content: currantIndex.value == 0 || currantIndex.value == 1
        //           ? cashBalanceSuccessWidget(
        //               code: userData.auCust!.custid.toString())
        //           : cashBalanceSuccessWidget(
        //               code: userData.auCust!.custid.toString()),
        //     );
        //   },
        // );
        clear();
        if (updateUi != null) {
          updateUi!();
        }
      } else {
        Loader.hideLoader();
        // Get.snackbar('Error', 'Please enter a valid 4-digit OTP',
        //     snackPosition: SnackPosition.BOTTOM);

        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          title: jsonDecode(response.body)["ExceptionMessage"],
          onCancelBtnTap: () {
            Get.back();
          },
        );
        // showDialog(
        //   barrierDismissible: false,
        //   context: Get.context!,
        //   builder: (context) {
        //     return AlertDialog(
        //       insetPadding: const EdgeInsets.all(16),
        //       contentPadding: const EdgeInsets.all(24),
        //       shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12)),
        //       content: Column(
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           Text(
        //             //"${message.message}",
        //             jsonDecode(response.body)["ExceptionMessage"],
        //             //"INVALID OTP PLEASE CHECK THE VALUES AND TRY AGAIN".tr,
        //             style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
        //             textAlign: TextAlign.center,
        //           ),
        //           verticalSpace(24),
        //           CommonButton(
        //             title: "OK".tr,
        //             onPressed: () async {
        //               Get.back();
        //             },
        //             btnColor: AppColor.themeOrangeColor,
        //           )
        //         ],
        //       ),
        //     );
        //   },
        // );
      }
    } catch (e) {
      logs.log(e.toString());
      return [];
    }
  }

  clear() {
    toptupamount.text = "0";
  }

  void showErrorMsg(String msg) {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.all(16),
          contentPadding: const EdgeInsets.all(16),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("ERROR".tr, style: pBold20),
              verticalSpace(24),
              Text(
                // "Balance TopUp Failed.".tr,
                msg,
                style: pRegular16,
                textAlign: TextAlign.center,
              ),
              verticalSpace(24),
              TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text("OK"))
            ],
          ),
        );
      },
    );
  }

  Widget stcPaySuccessWidget({
    required String code,
    required String total,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Center(
            child: Text(
          "Success!".tr,
          style: pBold20,
        )),
        verticalSpace(14),
        Center(
            child: Text(
          "Your order has been successful!.".tr,
          style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          textAlign: TextAlign.center,
        )),
        verticalSpace(8),
        Text.rich(
          TextSpan(
            text:
                'your order has been created' //, you can print the order details/invoice from your TOPUP history.'
                    .tr,
            style: pRegular13,
            children: <TextSpan>[
              TextSpan(
                  text: ". We keep the details of your order"
                      ' #$code '
                      " in Order History",
                  style: pBold12.copyWith(fontSize: 13)),
            ],
          ),
          textAlign: TextAlign.center,
        ),
        verticalSpace(16),
        successSTCPayWidget(
            Total: double.parse(total).toStringAsFixed(2)), // + " SAR".tr
        verticalSpace(16),
        verticalSpace(16),
        buttonRow(
          pdfFun: () {
            if (updateUi != null) {
              updateUi!();
            }
            Get.back();
          },
          dashboardFun: () {
            if (updateUi != null) {
              updateUi!();
            }
            Get.offAll(DashBoardManagerScreen(
              currantIndex: 0,
            ));
          },
        ),
      ],
    );
  }

  Widget cashBalanceSuccessWidget({required String code}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Center(
            child: Text(
          "Success!".tr,
          style: pBold20,
        )),
        verticalSpace(14),
        Center(
            child: Text(
          "Your order has been successful!.".tr,
          style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          textAlign: TextAlign.center,
        )),
        verticalSpace(8),
        Text.rich(
          TextSpan(
            text:
                'your order has been created' //, you can print the order details/invoice from your TOPUP history.'
                    .tr,
            style: pRegular13,
            children: <TextSpan>[
              TextSpan(
                  text: ". We keep the details of your order"
                      ' #$code '
                      " in Order History",
                  style: pBold12.copyWith(fontSize: 13)),
              /*TextSpan(
                text: '${'details in'.tr} ',
              ),*/
              /* TextSpan(
                text: "We keep the details of your order".tr,
              ),*/
              /* TextSpan(
                  text: 'Order History'.tr,
                  style: pBold12.copyWith(
                      fontSize: 13, color: AppColor.cLinkText)),*/
            ],
          ),
          textAlign: TextAlign.center,
        ),
        verticalSpace(16),
        successBalanceWidget(
            //unitPrice: "$UnitPrice SAR",
            unitPrice: UnitPrice, // + " SAR".tr
            Vat: VatAmt, // + " SAR".tr
            Total: double.parse(Amount).toStringAsFixed(2)), // + " SAR".tr
        verticalSpace(16),
        /*Container(
          decoration: BoxDecoration(
              color: AppColor.lightOrangeColor,
              borderRadius: BorderRadius.circular(6)),
          padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 20),
         */ /* child: Text(
              "Order will be automatically confirmed after successful payment"
                  .tr,
              style: pRegular13,
              textAlign: TextAlign.center),*/ /*
        ),*/
        verticalSpace(16),
        buttonRow(
          pdfFun: () {
            if (updateUi != null) {
              updateUi!();
            }
            Get.back();
          },
          dashboardFun: () async {
            if (updateUi != null) {
              updateUi!();
            }
            Get.offAll(DashBoardManagerScreen(
              currantIndex: 0,
            ));
            final reviewController = Get.find<ReviewController>();
            await reviewController.triggerReview();
          },
        ),
      ],
    );
  }

  Widget successSTCPayWidget({String? Total}) {
    return Container(
      decoration: BoxDecoration(
          color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          totalsDataWidget(title: "Total".tr, value: Total!),
        ],
      ),
    );
  }

  Widget successBalanceWidget({String? unitPrice, String? Vat, String? Total}) {
    return Container(
      decoration: BoxDecoration(
          color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          totalsDataWidget(title: "Unit Price".tr, value: unitPrice!),
          verticalSpace(8),
          totalsDataWidget(title: "VAT".tr, value: Vat!),
          verticalSpace(8),
          totalsDataWidget(title: "Total".tr, value: Total!),
        ],
      ),
    );
  }

  Widget totalsDataWidget(
      {required String title,
      required String value,
      double? fontSize,
      Color? fontColor}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: pRegular13.copyWith(
              fontSize: fontSize ?? 13, color: fontColor ?? AppColor.cText),
        ),
        Directionality(
          textDirection: TextDirection.ltr,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              assetSvdImageWidget(
                image: DefaultImages.saudiRiyal,
                width: fontSize == 14
                    ? 13
                    : fontSize == 17
                        ? 16
                        : 13,
                height: fontSize == 14
                    ? 13
                    : fontSize == 17
                        ? 16
                        : 13,
              ),
              Gap(4),
              Text(
                value,
                // double.parse(value).toStringAsFixed(2),
                style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
              ),
            ],
          ),
        ),
        // Text(
        //   value,
        //   // double.parse(value).toStringAsFixed(2),
        //   style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
        // ),
      ],
    );
  }

  /* void showOTPDialog() {
    Get.defaultDialog(
      title: 'Enter OTP',
      content: TextFormField(
        controller: otpText,
        maxLength: 4,
        keyboardType: TextInputType.number,
        decoration: const InputDecoration(
          hintText: 'Enter 4-digit OTP',
        ),
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () {
            String enteredOTP = otpText.text;
            if (enteredOTP.length == 4) {
              print('Entered OTP: $enteredOTP'); // Use the OTP here
              //Get.back();
              ValidatePRMOTP();
              // Close the dialog
            } else {
              // Show an error or inform the user to enter a valid 4-digit OTP
              Get.snackbar('Error', 'Please enter a valid 4-digit OTP',
                  snackPosition: SnackPosition.BOTTOM);
            }
          },
          child: const Text('Submit'),
        ),
      ],
    );
  }*/

  void showSTCPAYOTPDialog(
    custID,
    userID,
    mobileNo,
    totalAmount,
    STCPayOtpReference,
    STCPayPmtReference,
  ) {
    Get.defaultDialog(
      title: 'Enter OTP',
      content: TextFormField(
        controller: stcPayOTPText,
        maxLength: 4,
        keyboardType:
            const TextInputType.numberWithOptions(signed: true, decimal: true),
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        decoration: const InputDecoration(
          hintText: 'Enter 4-digit OTP',
        ),
      ),
      actions: <Widget>[
        // Cancel Button
        TextButton(
          onPressed: () {
            // Just close the dialog
            //  Loader.hideLoader();
            Get.back();
          },
          child: const Text('Cancel'),
        ),
        // Submit Button
        TextButton(
          onPressed: () {
            String enteredOTP = stcPayOTPText.text;
            if (enteredOTP.length == 4) {
              print('Entered OTP: $enteredOTP'); // Use the OTP here
              // You might want to validate the OTP here
              validateSTCPAYSUBMIT(
                custID,
                userID,
                mobileNo,
                totalAmount,
                STCPayOtpReference,
                STCPayPmtReference,
                enteredOTP,
              );
              // Optionally close the dialog if OTP is correct inside ValidatePRMOTP
            }
          },
          child: const Text('Submit'),
        ),
      ],
    );
  }

  // void showOTPDialog() {
  //   Get.defaultDialog(
  //     title: 'Enter OTP',
  //     content: TextFormField(
  //       controller: otpText,
  //       maxLength: 4,
  //       keyboardType:
  //           const TextInputType.numberWithOptions(signed: true, decimal: true),
  //       inputFormatters: [FilteringTextInputFormatter.digitsOnly],
  //       decoration: const InputDecoration(
  //         hintText: 'Enter 4-digit OTP',
  //       ),
  //     ),
  //     actions: <Widget>[
  //       // Cancel Button
  //       TextButton(
  //         onPressed: () {
  //           // Just close the dialog
  //           //  Loader.hideLoader();
  //           Get.back();
  //         },
  //         child: const Text('Cancel'),
  //       ),
  //       // Submit Button
  //       TextButton(
  //         onPressed: () {
  //           String enteredOTP = otpText.text;
  //           if (enteredOTP.length == 4) {
  //             print('Entered OTP: $enteredOTP'); // Use the OTP here
  //             // You might want to validate the OTP here
  //             ValidatePRMOTP();
  //             // Optionally close the dialog if OTP is correct inside ValidatePRMOTP
  //           }
  //         },
  //         child: const Text('Submit'),
  //       ),
  //     ],
  //   );
  // }

  //Added By: RYAN VIAEJDOR - 28052025
  void showOTPDialog() {
    //final otpText = TextEditingController();
    final focusNode = FocusNode();

    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        title: Text(
          'Verification Code'.trr,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Please enter the 4-digit code sent to your device'.trr,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: otpText,
              focusNode: focusNode,
              maxLength: 4,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 24,
                letterSpacing: 4,
              ),
              keyboardType: const TextInputType.numberWithOptions(
                signed: true,
                decimal: true,
              ),
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                counterText: '',
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.grey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: Theme.of(Get.context!).primaryColor),
                ),
                hintText: '••••',
                hintStyle: const TextStyle(
                  letterSpacing: 4,
                  color: Colors.grey,
                ),
              ),
              onChanged: (value) {
                if (value.length == 4) {
                  focusNode.unfocus();
                }
              },
            ),
          ],
        ),
        actionsAlignment: MainAxisAlignment.spaceBetween,
        actions: [
          TextButton(
            onPressed: () {
              otpText.clear();
              Get.back();
            },
            child: Text(
              'CANCEL'.trr,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.themeOrangeColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            onPressed: () {
              final enteredOTP = otpText.text;
              if (enteredOTP.length == 4) {
                ValidatePRMOTP();
              }
            },
            child: Text(
              'VERIFY'.trr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );

    // Auto focus the OTP field when dialog appears
    Future.delayed(const Duration(milliseconds: 100), () {
      focusNode.requestFocus();
    });
  }

  prepareMADA() async {
    // showDialog(
    //   context: Get.context!,
    //   builder: (context) {
    //     return const Center(
    //       child: CircularProgressIndicator(),
    //     );
    //   },
    // );
    var client = http.Client();
    const chars = '1234567890';
    var rnd = Random();
    var custData = custsData.read('custData');
    var device = "";
    if (Platform.isAndroid || Platform.isWindows) {
      device = "ANDROID";
    } else if (Platform.isIOS) {
      device = "IOS";
    }

    String getRandomString(int length) =>
        String.fromCharCodes(Iterable.generate(
            length, (_) => chars.codeUnitAt(rnd.nextInt(chars.length))));

    var newOrderID = getRandomString(10);
    print('newOrderID >>>> $newOrderID');

    // Create uuid object
    var newUuid = const Uuid();

    // Generate a v4 (random) id
    var depositTo = newUuid.v4(); // -> '110ec58a-a0f2-4ac4-8393-c866d813b8d1'
    var orderID = ""; //newOrderID;

    if (Platform.isAndroid || Platform.isWindows) {
      orderID = "A$newOrderID";
    } else if (Platform.isIOS) {
      orderID = "I$newOrderID";
    }
    print("orderID >>>> $orderID");
    print("depositTo >>>> $depositTo");

    showDialog(
      barrierDismissible: false,
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          contentPadding: const EdgeInsets.all(16),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          insetPadding: const EdgeInsets.all(25),
          content: madaBalanceSuccessWidget(
            orderID: orderID,
            code: custData['CUSTID'],
            unitPrice: UnitPrice,
            vat: VatAmt,
            purchaseTotal: Amount,
            sessionID: checkoutSession.value,
            depositto: depositTo,
            orderType: "TOPUP",
            custid: custData['CUSTID'],
          ),
        );
      },
    );
    // }

    return checkoutSessionList;
  }

//   prepareApplePay() async {
//     showDialog(
//       context: Get.context!,
//       builder: (context) {
//         return const Center(
//           child: CircularProgressIndicator(),
//         );
//       },
//     );
//     var client = http.Client();
//     const chars = '1234567890';
//     var rnd = Random();
//     var custData = custsData.read('custData');
//     var device = "";
//     if (Platform.isAndroid || Platform.isWindows) {
//       device = "ANDROID";
//     } else if (Platform.isIOS) {
//       device = "IOS";
//     }

//     String getRandomString(int length) =>
//         String.fromCharCodes(Iterable.generate(
//             length, (_) => chars.codeUnitAt(rnd.nextInt(chars.length))));

//     var newOrderID = getRandomString(10);
//     print('newOrderID >>>> $newOrderID');

//     // Create uuid object
//     var newUuid = const Uuid();

//     // Generate a v4 (random) id
//     var depositTo = newUuid.v4(); // -> '110ec58a-a0f2-4ac4-8393-c866d813b8d1'
//     var orderID = ""; //newOrderID;

//     if (Platform.isAndroid || Platform.isWindows) {
//       orderID = "A$newOrderID";
//     } else if (Platform.isIOS) {
//       orderID = "I$newOrderID";
//     }
//     print("orderID >>>> $orderID");
//     print("depositTo >>>> $depositTo");

//     var response = await client.post(
//         Uri.parse(
//             ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.prepareMADA),
// //          body: {"userId": "000037345","ACCTTYPE" : "C"});
//         body: {
//           "device": device,
//           "custid": custData['CUSTID'],
//           "depositto": depositTo,
//           "amount": toptupamount.text ?? "1",
//           "OrderTypes": "T",
//           "topupamt": toptupamount.text ?? "1",
//           "qty": "1",
//           "servicetype": "T",
//           "paytype": "D",
//           "payrefno": orderID,
//           "is_mokafa50": "false",
//           "IsAR": Constants.IsAr_App,
//           "serialid": "",
//           "reqstr": "",
//         });
//     // BalanceTopUpModelNew result =
//     //     BalanceTopUpModelNew.fromJson(jsonDecode(response.body));
//     print("result");
//     print("=======================================");
//     print(
//         'orderID jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');
//     var result = jsonDecode(response.body)[0]["RES_BODY"];
//     print("result===> ${jsonDecode(jsonEncode(result))}");
//     final json = "[$result]";
//     List resu = (jsonDecode(json) as List<dynamic>);
//     print("resu===> $resu");

//     for (int i = 0; i < resu.length; i++) {
//       CheckoutSessionModel session =
//           CheckoutSessionModel.fromJson(resu[i] as Map<String, dynamic>);
//       checkoutSessionList.add(session);
//     }
//     var sess = checkoutSessionList.map((item) => item.session.id).toString();

//     print(
//         "checkoutSessionList===> ${jsonDecode(jsonEncode(checkoutSessionList))[0]["result"]}");
//     print(
//         "checkoutSessionList===> ${sess.replaceAll(RegExp(r"\p{P}", unicode: true), "")}");
//     checkoutSession.value =
//         sess.replaceAll(RegExp(r"\p{P}", unicode: true), "");

//     print(checkoutSession.value);

//     if (jsonDecode(jsonEncode(checkoutSessionList))[0]["result"] == "SUCCESS") {
//       Navigator.of(Get.context!).pop();
//       showDialog(
//         barrierDismissible: false,
//         context: Get.context!,
//         builder: (context) {
//           return AlertDialog(
//             contentPadding: const EdgeInsets.all(16),
//             shape:
//                 RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//             insetPadding: const EdgeInsets.all(25),
//             content: applePayBalanceSuccessWidget(
//               orderID: orderID,
//               code: custData['CUSTID'],
//               unitPrice: UnitPrice,
//               vat: VatAmt,
//               purchaseTotal: Amount,
//               sessionID: checkoutSession.value,
//               depositto: depositTo,
//               orderType: "TOPUP",
//               custid: custData['CUSTID'],
//             ),
//           );
//         },
//       );
//     }

//     return checkoutSessionList;
//   }
}
