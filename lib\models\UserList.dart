import 'dart:convert';

class UserList {

  int LINE;
  String CUSTID;
  String USERNAME;
  String EMAILID;
  String USRSTCODE;
  String USRSTATUS;
  String RCV_MAILCODE;
  String RCV_MAIL;
  String ISPR_CODE;
  String IS_PRIMARY;
  String ISAUTH_CODE;
  String ISAUTH;

  UserList({
    required this.LINE,
    required this.CUSTID,
    required this.USERNAME,
    required this.EMAILID,
    required this.USRSTCODE,
    required this.USRSTATUS,
    required this.RCV_MAILCODE,
    required this.RCV_MAIL,
    required this.ISPR_CODE,
    required this.IS_PRIMARY,
    required this.ISAUTH_CODE,
    required this.ISAUTH
  });

 /* factory UserList.fromJson(Map<String, dynamic> json) {
    return UserList(
      LINE: json['LINE'],
      CUSTID: json['CUSTID'],
      USERNAME: json['USERNAME'],
      EMAILID: json['EMAILID'],
      USRSTCODE: json['USRSTCODE'],
      USRSTATUS: json['USRSTATUS'],
      RCV_MAILCODE: json['RCV_MAILCODE'],
      RCV_MAIL: json['RCV_MAIL'],
      ISPR_CODE: json['ISPR_CODE'],
      IS_PRIMARY: json['IS_PRIMARY'],
      ISAUTH_CODE: json['ISAUTH_CODE'],
      ISAUTH: json['ISAUTH']
    );
  }*/
  Map<String, dynamic> toMap() {
    return {
      'LINE': LINE,
      'CUSTID': CUSTID,
      'USERNAME': USERNAME,
      'EMAILID': EMAILID,
      'USRSTCODE': USRSTCODE,
      'USRSTATUS': USRSTATUS,
      'RCV_MAILCODE': RCV_MAILCODE,
      'RCV_MAIL': RCV_MAIL,
      'ISPR_CODE': ISPR_CODE,
      'IS_PRIMARY': IS_PRIMARY,
      'ISAUTH_CODE': ISAUTH_CODE,
      'ISAUTH': ISAUTH,
    };
  }
  factory UserList.fromMap(Map<String, dynamic> map) {
    return UserList(
      LINE: map['LINE']?? '',
      CUSTID: map['CUSTID']?? '',
      USERNAME: map['USERNAME']?? '',
      EMAILID: map['EMAILID']?? '',
      USRSTCODE: map['USRSTCODE']?? '',
      USRSTATUS: map['USRSTATUS']?? '',
      RCV_MAILCODE: map['RCV_MAILCODE']?? '',
      RCV_MAIL: map['RCV_MAIL']?? '',
      ISPR_CODE: map['ISPR_CODE']?? '',
      IS_PRIMARY: map['IS_PRIMARY']?? '',
      ISAUTH_CODE: map['ISAUTH_CODE']?? '',
      ISAUTH: map['ISAUTH']?? '',
      //notyDesc: map['NOTY_DESC'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory UserList.fromJson(String source) =>
      UserList.fromMap(json.decode(source));
}