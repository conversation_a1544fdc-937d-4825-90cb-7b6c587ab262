// ignore_for_file: prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../login_with_email_screen.dart';
import 'create_dc_screen.dart';

class FuelConfirmScreen extends StatelessWidget {
  final bool isBack;

  const FuelConfirmScreen({super.key, required this.isBack});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Digital Coupon".trr,
                    style: pBold20,
                  ),
                  CommonBorderButton(
                    title: 'New coupon'.trr,
                    onPressed: () {
                      if (isBack == true) {
                        Get.off(() => CreateDCScreen(
                          isBack: isBack,
                        ));
                      } else {
                        Get.back();
                      }                    },
                    height: 32,
                    width: 120,
                  )
                ],
              ),
              verticalSpace(89),
              assetSvdImageWidget(image: DefaultImages.fualConfirmedIcn),
              verticalSpace(24),
              Text(
                "Fuel topup confirmed".trr,
                textAlign: TextAlign.center,
                style: pBold20,
              ),
              verticalSpace(4),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 25),
                child: Text.rich(
                  TextSpan(
                    text: 'Your vehicle'.trr+' ',
                    style: pRegular14.copyWith(fontSize: 13),
                    children: <TextSpan>[
                      // TextSpan(
                      //   text: ' 3948DJA ',
                      //   style: pBold14.copyWith(
                      //     fontSize: 13,
                      //   ),
                      // ),
                      // TextSpan(text: " "+"has been fuelled with".trr+" "),
                      TextSpan(
                        text: '30 liters',
                        style: pBold14.copyWith(
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(left: 24, right: 24, bottom: 15),
        child: CommonBorderButton(
            title: "Back to Login".trr,
            onPressed: () {
              //Get.offAll(() => LoginManagerScreen());
              Get.offAll(() => LoginWithEmailScreen());
            },
            bColor: AppColor.themeDarkBlueColor),
      ),
    );
  }
}
