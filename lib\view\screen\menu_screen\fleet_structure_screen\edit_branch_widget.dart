
// ignore_for_file: invalid_use_of_protected_member, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_structure_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';

class EditDivisionWidget extends StatefulWidget {
  final String divisionName;
  const EditDivisionWidget({super.key, required this.divisionName});

  @override
  State<EditDivisionWidget> createState() => _EditDivisionWidgetState();
}

class _EditDivisionWidgetState extends State<EditDivisionWidget> {
  FleetStructureController fleetStructureController = Get.find();
@override
  void initState() {
    // TODO: implement initState
    super.initState();
    fleetStructureController.editDivisionNameController.text=widget.divisionName;
  }
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: const BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: const EdgeInsets.all(16),
        child: Column(mainAxisSize: MainAxisSize.min,children: [
          Center(child: Text("Edit Division".trr,style: pSemiBold17,)),
          verticalSpace(24),
          CommonTextField(controller: fleetStructureController.editDivisionNameController,labelText: 'Division Name'.trr,hintText: "Please select here".trr+"...",),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonBorderButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      Get.back();
                    },
                    bColor: AppColor.themeDarkBlueColor,
                    textColor: AppColor.cDarkBlueFont,
                  )),
              horizontalSpace(8),
              Expanded(
                  child: CommonButton(
                    title: 'Confirm'.trr,
                    onPressed: () {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  ))
            ],
          ),
          verticalSpace(16),
        ]),
      ),
    );
  }
}
class EditBranchWidget extends StatelessWidget {
  EditBranchWidget({super.key});
  FleetStructureController fleetStructureController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: const BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: const EdgeInsets.all(16),
        child: Column(mainAxisSize: MainAxisSize.min,children: [
          Center(child: Text("Edit Branch".trr,style: pSemiBold17,)),
          verticalSpace(24),
          CommonTextField(controller: fleetStructureController.branchNameController,labelText: 'Branch Name'.trr,hintText: "Please select here".trr+"...",),
          verticalSpace(24),
          CommonHintDropdownWidget(
            hint: 'Please select here'.trr+"...",
            labelText: 'Subcategory type'.trr,
            list: fleetStructureController.subCategoriesList.value,
            value: fleetStructureController.selectedCategory.value,
            onChanged: (value) {
              fleetStructureController.selectedCategory.value = value;
            },
          ),
          verticalSpace(24),
          Row(
            children: [
              Expanded(
                  child: CommonBorderButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      Get.back();
                    },
                    bColor: AppColor.themeDarkBlueColor,
                    textColor: AppColor.cDarkBlueFont,
                  )),
              horizontalSpace(8),
              Expanded(
                  child: CommonButton(
                    title: 'Confirm'.trr,
                    onPressed: () {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  ))
            ],
          ),
          verticalSpace(16),
        ]),
      ),
    );
  }
}