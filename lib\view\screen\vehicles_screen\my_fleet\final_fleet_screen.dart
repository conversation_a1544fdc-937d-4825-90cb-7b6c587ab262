import 'dart:developer';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/core/controller/splash_controller/splash_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/activate_pinblock_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/change_status_controller.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/new_order_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/activate_pinblock_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/add_pure_dc_vehicle_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/assign_digital_coupon_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/bulk_actions_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/change_status_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/file_complaint_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/fleet_vehicle_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/plate_change_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/set_quota_limits_widget.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_transfer_screen/transfer_tags_widget.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/splash_button_widget.dart';
import 'dart:convert';
import '../../../../core/controller/menu_controller/fleet_structure_controller/edit_vehicle_details_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/fleet_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/overview_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/view_vehicle_details_controller.dart';
import '../../../../core/controller/vehicle_controller/change_plate_controller.dart';
import '../../../../core/controller/vehicle_controller/file_complaint_controller.dart';
import '../../../../core/controller/vehicle_controller/paginated_controller.dart';
import '../../../../core/controller/vehicle_controller/vehicle_controller.dart';
import '../../../../models/newfleet.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/validator.dart';
import '../../../widget/common_snak_bar_widget.dart';
import '../../../widget/loading_widget.dart';
import '../../dashboard_manager/dashboard_manager.dart';

class FinalFleetScreen extends StatefulWidget {
  const FinalFleetScreen({super.key});

  @override
  State<FinalFleetScreen> createState() => _FinalFleetScreenState();
}

class _FinalFleetScreenState extends State<FinalFleetScreen> {
  VehicleController vehicleController = Get.put(VehicleController());
  FileComplaintController fileComplaintController =
      Get.put(FileComplaintController());
  PaginatedController paginatedController = Get.put(PaginatedController());
  OverviewController overviewController = Get.put(OverviewController());
  FleetController fleetController = Get.put(FleetController());
  ActivatePinblockController activatePinblockController =
      Get.put(ActivatePinblockController());
  ChangeStatusController changeStatusController =
      Get.put(ChangeStatusController());
  ChangePlateController changePlateController =
      Get.put(ChangePlateController());
  ViewVehicleDetailsController viewVehicleDetailsController =
      Get.put(ViewVehicleDetailsController());
  EditVehicleDetailsController editVehicleDetailsController =
      Get.put(EditVehicleDetailsController());
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  int _currentPage = 1;
  RxInt countPerPage = 0.obs;
  int _pageSize = 10;
  int fromData = 1;
  int toData = 10;
  int skpPlus = 0;
  int totalCount = 0;
  final List<ServiceObj> _data = [];
  RxInt counts = 0.obs;
  bool _isLoading = false;
  GetStorage userStorage = GetStorage('User');
  RxBool isFilterAccess = false.obs;
  RxString serviceList = 'PL'.obs;
  RxString vehicleList = ''.obs;
  RxString statusList = ''.obs;
  RxString fuelList = ''.obs;
  RxString tp = '10'.obs;
  RxInt skp = 0.obs;
  TextEditingController searchController = TextEditingController();
  final int _pageIndex = 0; // track current page index

  String hidePartOfNumber(String rfid) {
    // Convert the number to a string
    String numberString = rfid.toString();

    // Determine how many digits to hide (in this example, hiding all but the last four digits)
    int digitsToShow = 6;
    int visibleDigits = numberString.length - digitsToShow;

    // Replace the visible digits with asterisks
    String hiddenDigits = 'X' * visibleDigits;

    // Concatenate the visible part of the number with the hidden part
    String maskedNumber = hiddenDigits + numberString.substring(visibleDigits);

    return maskedNumber;
  }

  @override
  void initState() {
    super.initState();
    fetchData();
    _loadHomeImage();
  }

  Future<void> sample() async {
    var searchBy =
        "${searchController.text == "" ? "" : searchController.text},${vehicleList.value},${statusList.value},${fuelList.value},,,,,,,,,${serviceList.value}";
    print("======================================");
    print(searchBy);
    print(skpPlus);
    print(skp.value.toString());
    print(tp.value.toString());
    print("======================================");
  }

  Future<void> fetchData() async {
    _data.clear();
    var searchBy =
        "${searchController.text == "" ? "" : searchController.text},${vehicleList.value},${statusList.value},${fuelList.value},,,,,,,,,${serviceList.value}";
    print("======================================");
    print("fromData $fromData");
    print("toData $toData");
    print(searchBy);
    print("skpPlus $skpPlus");
    print("skp ${skp.value.toString()}");
    print("tp ${tp.value.toString()}");
    print("======================================");
    var client = http.Client();
    var custid = userStorage.read('custid');
    setState(() {
      _isLoading = true;
    });

    // final response = await http.get(Uri.parse(
    //     '${ApiEndpoint.users}?page=$_currentPage')); //&pageSize=$_pageSize

    String username = 'aldrees';
    String password = 'testpass';
    String basicAuth =
        'Basic ${base64Encode(utf8.encode('$username:$password'))}';
    Map body = {
      "SEARCHBY": searchBy != "" ? searchBy.toUpperCase().trim() : "",
      "TOP": tp.value.toString(),
      "SKIP": fromData < 10 ? "0" : skp.value.toString(),
      "CUSTID": custid, //"000003944",
      "SERIALID": ""
    };
    final response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
        body: jsonEncode(body),
        headers: {
          'authorization': basicAuth,
          "Content-Type": "application/json",
        });

    if (response.statusCode == 200) {
      _data.clear();
      searchController.clear();
      serviceList.value = 'PL';
      vehicleList.value = '';
      statusList.value = '';
      fuelList.value = '';
      tp.value = '10';
      skp.value = 0;
      countPerPage.value = 0;
      final jsonData = json.decode(response.body);
      // print("===================================");
      // print(json.decode(response.body));
      // print("===================================");
      // print(jsonData);
      // print("===================================");
      inspect(jsonData);
      final dataList = jsonData["serviceObj"] as List<dynamic>;
      int countList = jsonData["totalCount"] as int;

      final List<ServiceObj> newData =
          dataList.map((item) => ServiceObj.fromJson(item)).toList();

      setState(() {
        _data.addAll(newData);
        counts.value = countList;
        _isLoading = false;
        _data.length == 10 ? counts.value : _data.length;

        if (totalCount == 0) {
          totalCount = _data.length == 10 ? counts.value : _data.length;
        } else {
          totalCount = counts.value;
        }
        if (totalCount < toData) {
          toData = totalCount;
        }

        // print("_data.length");
        // print(countPerPage.value.toString());
        // print(_data.length);
        if (_data.isEmpty) {
          //Loader.showLoader();
          setState(() {
            searchController.clear();
            serviceList.value = 'PL';
            vehicleList.value = '';
            statusList.value = '';
            fuelList.value = '';
            tp.value = '10';
            skp.value = 0;
          });
          //Get.off(() => const PaginatedDataTableView());
          showDialog(
            barrierDismissible: false,
            context: Get.context!,
            builder: (context) {
              return AlertDialog(
                insetPadding: const EdgeInsets.all(16),
                contentPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'No data available in table',
                      style: pSemiBold17,
                      textAlign: TextAlign.center,
                    ),
                    verticalSpace(24),
                    Row(
                      children: [
                        Expanded(
                            child: CommonBorderButton(
                          title: "Dashboard".trr,
                          onPressed: () {
                            Get.offAll(DashBoardManagerScreen(
                              currantIndex: 0,
                            ));
                          },
                          bColor: AppColor.themeDarkBlueColor,
                          textColor: AppColor.cDarkBlueFont,
                        )),
                        horizontalSpace(8),
                        Expanded(
                          child: CommonButton(
                            title: "OK".trr,
                            btnColor: AppColor.themeOrangeColor,
                            onPressed: () {
                              print("===========================");
                              print(searchController.text);
                              print(serviceList.value);
                              print(vehicleList.value);
                              print(statusList.value);
                              print(fuelList.value);
                              print(tp.value);
                              print(skp.value);

                              fromData = 1;
                              toData = 10;
                              print("===========================");
                              //Loader.hideLoader();
                              // Get.off(() => DashBoardManagerScreen(
                              //       currantIndex: 1,
                              //     ));
                              // fetchData();
                              // Get.back();
                              Get.to(() => NewOrderScreen());
                            },
                          ),
                        ),
                      ],
                    ),
                    // CommonButton(
                    //   title: "OK".trr,
                    //   onPressed: () async {
                    //     print("===========================");
                    //     print(searchController.text);
                    //     print(serviceList.value);
                    //     print(vehicleList.value);
                    //     print(statusList.value);
                    //     print(fuelList.value);
                    //     print(tp.value);
                    //     print(skp.value);

                    //     fromData = 1;
                    //     toData = 10;
                    //     print("===========================");
                    //     //Loader.hideLoader();
                    //     // Get.off(() => DashBoardManagerScreen(
                    //     //       currantIndex: 1,
                    //     //     ));
                    //     fetchData();
                    //     Get.back();

                    //     //Get.off(() => const PaginatedDataTableView());
                    //   },
                    //   btnColor: AppColor.themeOrangeColor,
                    // )
                  ],
                ),
              );
            },
          );
        } else {
          if (_pageSize > _data.length) {
            _pageSize = _data.length;
          }
        }
      });
    } else {
      setState(() {
        _isLoading = false;
      });
      throw Exception('Failed to fetch data');
    }
  }

  void _loadMoreData() {
    if (!_isLoading) {
      setState(() {
        _currentPage++;
      });
      fetchData();
    }
  }

  SplashController splashController = Get.put(SplashController());
  String homeLogo = '';
  Future<void> _loadHomeImage() async {
    String path = await splashController.loadImage('MOBLOGOIMGS');
    setState(() {
      homeLogo = path;
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        body: SafeArea(
          child: Obx(
            () => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                verticalSpace(7),
                Center(
                  child: homeLogo.isNotEmpty
                      ? Image.network(
                          homeLogo,
                          height: 35,
                          fit: BoxFit.cover,
                        )
                      : Image.asset(
                          'asset/image/image/logotransparent.png',
                          height: 35,
                          fit: BoxFit.cover,
                        ),
                ),
                Container(
                  padding: const EdgeInsets.only(right: 16, left: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Get.offAll(() => DashBoardManagerScreen(
                                currantIndex: 0,
                              ));
                        },
                        child: Container(
                          padding: const EdgeInsets.only(
                            top: 15,
                            bottom: 16,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              assetSvdImageWidget(
                                  image: DefaultImages.backIcn,
                                  colorFilter: ColorFilter.mode(
                                      AppColor.cDarkBlueFont, BlendMode.srcIn)),
                              horizontalSpace(10),
                              Text(
                                "Back".trr,
                                style: pRegular18.copyWith(
                                    color: AppColor.cDarkBlueFont,
                                    fontSize: 17),
                                textAlign: TextAlign.start,
                              )
                            ],
                          ),
                        ),
                      ),
                      // horizontalSpace(35),
                      Expanded(
                        child: Align(
                          alignment: Alignment.center,
                          child: Text(
                            "My Fleet".trr,
                            style: pBold20,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                verticalSpace(10),
                Padding(
                  padding: const EdgeInsets.only(right: 16, left: 16),
                  child: menuTitleRowWidget(
                    title: "Filters".trr,
                    isSelected: isFilterAccess.value,
                    onTap: () {
                      isFilterAccess.value = !isFilterAccess.value;
                    },
                  ),
                ),
                verticalSpace(10),
                isFilterAccess.value
                    ? Container(
                        padding: const EdgeInsets.only(right: 16, left: 16),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // CommonTextField(
                            //   controller: searchController,
                            //   labelText: '',
                            //   hintText: "Search here".trr,
                            // ),
                            // const Gap(16),
                            Row(
                              children: [
                                Expanded(
                                  child: CommonTextField(
                                    controller: searchController,
                                    labelText: '',
                                    hintText: "Search here".trr,
                                  ),
                                ),
                                const Gap(10),
                                Expanded(
                                  child: DropdownButtonFormField(
                                    isExpanded: true,
                                    items: paginatedController.serviceTypeList
                                        .map((data) {
                                      return DropdownMenuItem(
                                        value: data["TYPECODE"],
                                        child: Text(
                                          data["TYPEDESC"],
                                          style: pMedium12,
                                          textAlign: TextAlign.center,
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      print(value);
                                      // PaginatedController.selectedDiv.value =
                                      //     value.toString();
                                      // reportController.loadBranch(value.toString());
                                      serviceList.value = value.toString();
                                    },
                                    style: pRegular14.copyWith(
                                        color: AppColor.cLabel),
                                    borderRadius: BorderRadius.circular(6),
                                    dropdownColor: AppColor.cLightGrey,
                                    icon: assetSvdImageWidget(
                                        image: DefaultImages.dropDownIcn),
                                    decoration: InputDecoration(
                                      hintText: 'Plate #'.trr,
                                      hintStyle: pRegular14.copyWith(
                                          color: AppColor.cHintFont),
                                      contentPadding: const EdgeInsets.only(
                                          left: 16, right: 16),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      disabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const Gap(10),
                                Expanded(
                                  child: DropdownButtonFormField(
                                    isExpanded: true,
                                    items: paginatedController.statusTypeList
                                        .map((data) {
                                      return DropdownMenuItem(
                                        value: data["TYPECODE"],
                                        child: Text(
                                          data["TYPEDESC"],
                                          style: pMedium12,
                                          textAlign: TextAlign.center,
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      print(value);
                                      // PaginatedController.selectedDiv.value =
                                      //     value.toString();
                                      // reportController.loadBranch(value.toString());
                                      statusList.value = value.toString();
                                    },
                                    style: pRegular14.copyWith(
                                        color: AppColor.cLabel),
                                    borderRadius: BorderRadius.circular(6),
                                    dropdownColor: AppColor.cLightGrey,
                                    icon: assetSvdImageWidget(
                                        image: DefaultImages.dropDownIcn),
                                    decoration: InputDecoration(
                                      hintText: 'All Status'.trr,
                                      hintStyle: pRegular14.copyWith(
                                          color: AppColor.cHintFont),
                                      contentPadding: const EdgeInsets.only(
                                          left: 16, right: 16),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      disabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const Gap(16),
                            Row(
                              children: [
                                Expanded(
                                  child: DropdownButtonFormField(
                                    isExpanded: true,
                                    items: paginatedController.vehicleTypeList
                                        .map((data) {
                                      return DropdownMenuItem(
                                        value: data["TYPECODE"],
                                        child: Text(
                                          data["TYPEDESC"],
                                          style: pMedium12,
                                          textAlign: TextAlign.center,
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      print(value);
                                      // PaginatedController.selectedDiv.value =
                                      //     value.toString();
                                      // reportController.loadBranch(value.toString());
                                      vehicleList.value = value.toString();
                                    },
                                    style: pRegular14.copyWith(
                                        color: AppColor.cLabel),
                                    borderRadius: BorderRadius.circular(6),
                                    dropdownColor: AppColor.cLightGrey,
                                    icon: assetSvdImageWidget(
                                        image: DefaultImages.dropDownIcn),
                                    decoration: InputDecoration(
                                      hintText: 'All Vehicle Type'.trr,
                                      hintStyle: pRegular14.copyWith(
                                          color: AppColor.cHintFont),
                                      contentPadding: const EdgeInsets.only(
                                          left: 16, right: 16),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      disabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const Gap(10),
                                Expanded(
                                  child: DropdownButtonFormField(
                                    isExpanded: true,
                                    items: paginatedController.fuelTypeList
                                        .map((data) {
                                      return DropdownMenuItem(
                                        value: data["TYPECODE"],
                                        child: Text(
                                          data["TYPEDESC"],
                                          style: pMedium12,
                                          textAlign: TextAlign.center,
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      print(value);
                                      // PaginatedController.selectedDiv.value =
                                      //     value.toString();
                                      // reportController.loadBranch(value.toString());
                                      fuelList.value = value.toString();
                                    },
                                    style: pRegular14.copyWith(
                                        color: AppColor.cLabel),
                                    borderRadius: BorderRadius.circular(6),
                                    dropdownColor: AppColor.cLightGrey,
                                    icon: assetSvdImageWidget(
                                        image: DefaultImages.dropDownIcn),
                                    decoration: InputDecoration(
                                      hintText: 'All Fuel Type'.trr,
                                      hintStyle: pRegular14.copyWith(
                                          color: AppColor.cHintFont),
                                      contentPadding: const EdgeInsets.only(
                                          left: 16, right: 16),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      disabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: AppColor.cBorder,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const Gap(24),
                            Row(
                              children: [
                                // Expanded(
                                //     child: CommonButton(
                                //   title: 'Back'.trr,
                                //   onPressed: () {
                                //     Get.back();
                                //     // Get.offAll(
                                //     //   () => DashBoardManagerScreen(
                                //     //     currantIndex: 0,
                                //     //   ),
                                //     //   //preventDuplicates: false,
                                //     // );
                                //   },
                                //   btnColor: AppColor.cBackGround,
                                //   bColor: AppColor.themeDarkBlueColor,
                                //   textColor: AppColor.cDarkBlueFont,
                                // )),
                                // horizontalSpace(16),
                                Expanded(
                                  child: CommonButton(
                                    title: 'Search'.trr,
                                    onPressed: () async {
                                      setState(() {
                                        isFilterAccess.value = false;
                                      });
                                      await fetchData();
                                      //await sample();
                                    },
                                    btnColor: AppColor.themeOrangeColor,
                                    horizontalPadding: 16,
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                      )
                    : const SizedBox(),
                verticalSpace(10),
                Row(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: CommonIconButton(
                            title: 'Previous Page'.trr,
                            textStyle: pRegular14.copyWith(
                                color: _isLoading
                                    ? AppColor.cBlack
                                    : AppColor.cWhite,
                                fontSize: 12),
                            height: 30,
                            width: 40,
                            onPressed: () async {
                              if (fromData < 10 || _isLoading) {
                                null;
                              } else {
                                print("Previous Page");
                                setState(() {
                                  if (fromData > 10) {
                                    if (toData == totalCount) {
                                      var tmp = toData - fromData;
                                      var tmpTotal = (toData - tmp) - 1;
                                      toData = tmpTotal;
                                      fromData = fromData - 10;
                                    } else {
                                      fromData = fromData - 10;
                                      toData = toData - 10;
                                    }
                                  }
                                  if (fromData < 10) {
                                    skpPlus = 0;
                                  } else {
                                    skpPlus = skpPlus - 10;
                                    skp.value = skpPlus;
                                  }
                                  print("------------------------");
                                  print(fromData);
                                  print(toData);
                                  print(skp.value);
                                  print(skpPlus);
                                  print("------------------------");
                                });
                                await fetchData();
                                // Get.to(() => NewUserScreen());
                              }
                            },
                            btnColor: _isLoading
                                ? AppColor.cLightGrey
                                : AppColor.themeOrangeColor,
                            radius: 6),
                      ),
                    ),
                    Expanded(
                      child: Center(
                        child: Directionality(
                          textDirection: TextDirection.ltr,
                          child: Text(
                            //"Showing $fromData to $toData of $totalCount entries",
                            "$toData ${"of".trr} $totalCount",
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: CommonIconButton(
                            title: 'Next Page'.trr,
                            textStyle: pRegular14.copyWith(
                                color: _isLoading
                                    ? AppColor.cBlack
                                    : AppColor.cWhite,
                                fontSize: 12),
                            height: 30,
                            width: 40,
                            onPressed: () async {
                              if (toData == totalCount ||
                                  totalCount < toData ||
                                  _isLoading) {
                                null;
                              } else {
                                setState(() {
                                  print("Next Page");
                                  var tempToSum = toData + 10;
                                  if (tempToSum > totalCount) {
                                    var tmp = toData - totalCount;
                                    var tmpTotal = toData - tmp;
                                    toData = tmpTotal;
                                    fromData = fromData + 10;
                                  } else {
                                    fromData = fromData + 10;
                                    toData = toData + 10;
                                  }
                                  skpPlus = skpPlus + 10;
                                  skp.value = skpPlus;
                                  print("++++++++++++++++++++++++");
                                  print(fromData);
                                  print(toData);
                                  print(skp.value);
                                  print(skpPlus);
                                  print("++++++++++++++++++++++++");
                                });
                                await fetchData();
                                // Get.to(() => NewUserScreen());
                              }
                            },
                            btnColor: _isLoading
                                ? AppColor.cLightGrey
                                : AppColor.themeOrangeColor,
                            radius: 6),
                      ),
                    ),
                  ],
                ),
                verticalSpace(10),
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: SingleChildScrollView(
                            physics: const BouncingScrollPhysics(),
                            child: ListView.builder(
                              itemCount: _data.length,
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                var data = _data[index];
                                print("data.plateno >>>>>>> ${data.plateno}");
                                print(
                                    "data.vehicleid >>>>>>> ${data.vehicleid}");
                                print(
                                    "data.servicestatusDisp >>>>>>> ${data.servicestatusDisp}");
                                return fleetWidget(
                                  //isShowCheck: true,
                                  // onChanged: (value) {
                                  //   setState(() {
                                  //     data.isDC = value ?? false;
                                  //   });
                                  //   if (value == true) {
                                  //     vehicleController.selectedFleetList
                                  //         .add(data.vehicleid);
                                  //     vehicleController.selectedVehicleList
                                  //         .add(data.vehicleid);
                                  //     vehicleController.selectedSerialList
                                  //         .add(data.serialid);
                                  //   } else {
                                  //     vehicleController.selectedFleetList
                                  //         .remove(index);
                                  //     vehicleController.selectedVehicleList
                                  //         .remove(index);
                                  //     vehicleController.selectedSerialList
                                  //         .remove(index);
                                  //     print(
                                  //         "*****************||********************");
                                  //     print(
                                  //         "vehicleController.selectedSerialList remove>>>> ${jsonDecode(jsonEncode(vehicleController.selectedSerialList))}");
                                  //     print(
                                  //         "*****************||********************");
                                  //   }
                                  //   print("*************************************");
                                  //   print(
                                  //       "vehicleController.selectedFleetList.lenght >>>> ${vehicleController.selectedFleetList.length}");
                                  //   print("*************************************");
                                  //   print(
                                  //       "vehicleController.selectedVehicleList >>>> ${jsonDecode(jsonEncode(vehicleController.selectedVehicleList))}");
                                  //   print(
                                  //       "vehicleController.selectedSerialList >>>> ${jsonDecode(jsonEncode(vehicleController.selectedSerialList))}");

                                  //   String vehicleIDList = vehicleController
                                  //       .selectedVehicleList
                                  //       .join(",");
                                  //   String serialIDList = vehicleController
                                  //       .selectedSerialList
                                  //       .join(",");

                                  //   print("*************************************");
                                  //   print("vehicleIDList >>>> $vehicleIDList");
                                  //   print("serialIDList >>>> $serialIDList");
                                  //   vehicle.write('vehicleID', vehicleIDList);
                                  //   vehicle.write('vehicleSerialID', serialIDList);
                                  //   print("*************************************");
                                  //   vehicleController.selectedVehicleList.refresh();
                                  //   vehicleController.selectedSerialList.refresh();
                                  //   vehicleController.selectedFleetList.refresh();
                                  //   vehicleController.filterValueList.refresh();
                                  // },
                                  pinblock: data.pinblock,
                                  pinblockDate: data.pinblockDate,
                                  value: data.isDc,
                                  code: data.plateno,
                                  status: data.servicestatusDisp.toString().trr,
                                  title: data.vehicletypeDisp,
                                  type: data.fueltypeDisp,
                                  driver: data.driver,
                                  quotaTotal: data.quotaDisp,
                                  quotaString: data.quotatypeDisp,
                                  division: data.branchDisp,
                                  serviceStatus: data.servicestatus,
                                  serviceType: data.servicetype,
                                  serialXID: data.servicetype == "C"
                                      ? data.serialid
                                      : hidePartOfNumber(data.serialcode),
                                  insTermDate: data.servicestatus == "T"
                                      ? data.terminatedate
                                      : data.instdate,
                                  textColor:
                                      data.servicestatusDisp == "IN-ACTIVE" ||
                                              data.servicestatusDisp ==
                                                  'In progress'
                                          ? AppColor.cRedText
                                          : data.servicestatusDisp == "NEW"
                                              ? AppColor.cBlackFont
                                              : data.servicestatusDisp ==
                                                      "TERMINATED"
                                                  ? AppColor.cDarkGreyFont
                                                  : AppColor.cDarkBlueFont,
                                  color: data.servicestatusDisp ==
                                              "IN-ACTIVE" ||
                                          data.servicestatusDisp ==
                                              'In progress'
                                      ? AppColor.cLightRedContainer
                                      : data.servicestatusDisp == "NEW"
                                          ? AppColor.cLiteYellow
                                          : data.servicestatusDisp ==
                                                  "TERMINATED"
                                              ? AppColor.cMediumGreyContainer
                                              : AppColor.cLightBlueContainer,
                                  tag: data.servicetype == "D" ||
                                          data.isdc == true
                                      ? DefaultImages.scannerIcn
                                      : data.servicetype == "T"
                                          ? DefaultImages.tagIcn
                                          : DefaultImages.cardIcn,
                                  // tag: data.servicestatusDisp == "IN-ACTIVE" ||
                                  //         data.servicestatusDisp == 'TERMINATED'
                                  //     ? DefaultImages.inactiveIcn
                                  //     : DefaultImages.tagIcn,
                                  viewMore: () {
                                    // GestureDetector(
                                    //   onTap: () {
                                    showModalBottomSheet(
                                      isDismissible: false,
                                      context: Get.context!,
                                      shape: const RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(16))),
                                      backgroundColor: AppColor.cBackGround,
                                      barrierColor: AppColor.cBlackOpacity,
                                      isScrollControlled: true,
                                      builder: (context) {
                                        var isPin =
                                            data.pinblock == "Y" ? true : false;
                                        var isdcBlock =
                                            data.servicetype == "D" ||
                                                    data.isdc == "Y"
                                                ? true
                                                : false;
                                        return Container(
                                          decoration: const BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.vertical(
                                                      top:
                                                          Radius.circular(16))),
                                          padding: const EdgeInsets.all(16),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 10),
                                                child: Row(
                                                  children: [
                                                    GestureDetector(
                                                        onTap: () {
                                                          vehicleController
                                                              .selectedSerialList
                                                              .clear();
                                                          vehicleController
                                                              .selectedVehicleList
                                                              .clear();
                                                          vehicleController
                                                              .selectedFleetList
                                                              .clear();
                                                          vehicleController
                                                              .filterValueList
                                                              .refresh();
                                                          vehicleController
                                                              .selectedVehicleList
                                                              .refresh();
                                                          vehicleController
                                                              .selectedSerialList
                                                              .refresh();
                                                          vehicleController
                                                              .selectedFleetList
                                                              .refresh();
                                                          Get.back();
                                                          // Get.offAll(
                                                          //   () => DashBoardManagerScreen(
                                                          //     currantIndex: 0,
                                                          //   ),
                                                          //   //preventDuplicates: false,
                                                          // );
                                                        },
                                                        child: CircleAvatar(
                                                          radius: 20,
                                                          backgroundColor: AppColor
                                                              .cLightBlueContainer,
                                                          child: Center(
                                                              child: assetSvdImageWidget(
                                                                  image: DefaultImages
                                                                      .backIcn)),
                                                        )),
                                                    Expanded(
                                                      child: Align(
                                                        alignment:
                                                            Alignment.center,
                                                        child: Center(
                                                          child: Text(
                                                            "Options".trr,
                                                            style: pBold20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              verticalSpace(22),
                                              if (isPin == true &&
                                                  isdcBlock == false)
                                                bulkActionWidget(
                                                  title: "UNBLOCK".trr,
                                                  onTap: () {
                                                    activatePinblockController
                                                        .pinblockController
                                                        .text = "N";
                                                    activatePinblockController
                                                        .activatePinblock(
                                                            data.serialid);
                                                    //Get.back();
                                                    // showModalBottomSheet(
                                                    //   isDismissible: false,
                                                    //   context: context,
                                                    //   shape: const RoundedRectangleBorder(
                                                    //       borderRadius:
                                                    //           BorderRadius.vertical(
                                                    //               top: Radius
                                                    //                   .circular(
                                                    //                       16))),
                                                    //   backgroundColor:
                                                    //       AppColor.cBackGround,
                                                    //   barrierColor:
                                                    //       AppColor.cBlackOpacity,
                                                    //   isScrollControlled: true,
                                                    //   builder: (context) {
                                                    //     return ActivatePinblockWidget(
                                                    //       serialid: data.serialid,
                                                    //       isPin: isPin,
                                                    //     );
                                                    //   },
                                                    // );
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                      "BTNVIEWVEHICLE"] ==
                                                  true)
                                                bulkActionWidget(
                                                  title:
                                                      "View Vehicle Details".trr,
                                                  onTap: () {
                                                    Get.back();
                                                    viewVehicleDetailsController
                                                        .getVehicleDetails(
                                                            data.serialid);
                                                    // Loader.showLoader();
                                                    // Future.delayed(
                                                    //     const Duration(
                                                    //         seconds: 3), () {
                                                    //   Loader.hideLoader();
                                                    //   Get.back();
                                                    // });
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                      "BTNSETQUOTALIMIT"] ==
                                                  true)
                                                //      &&
                                                // data.fleetaction[
                                                //         "BTNCANCELCOMPLAIN"] ==
                                                //     false
                                                bulkActionWidget(
                                                  title: "Set quota limits".trr,
                                                  onTap: () {
                                                    Get.back();
                                                    showModalBottomSheet(
                                                      isDismissible: false,
                                                      context: context,
                                                      shape: const RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius.vertical(
                                                                  top: Radius
                                                                      .circular(
                                                                          16))),
                                                      backgroundColor:
                                                          AppColor.cBackGround,
                                                      barrierColor: AppColor
                                                          .cBlackOpacity,
                                                      isScrollControlled: true,
                                                      builder: (context) {
                                                        return SetQuotaLimitsWidget(
                                                          serialID:
                                                              data.serialid,
                                                        );
                                                      },
                                                    );
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                          "BTNCHANGEPLATE"] ==
                                                      true &&
                                                  data.fleetaction[
                                                          "BTNCANCELCOMPLAIN"] ==
                                                      false)
                                                bulkActionWidget(
                                                  title: "Change Plate".trr,
                                                  onTap: () {
                                                    Get.back();
                                                    showModalBottomSheet(
                                                      isDismissible: false,
                                                      context: context,
                                                      shape: const RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius.vertical(
                                                                  top: Radius
                                                                      .circular(
                                                                          16))),
                                                      backgroundColor:
                                                          AppColor.cBackGround,
                                                      barrierColor: AppColor
                                                          .cBlackOpacity,
                                                      isScrollControlled: true,
                                                      builder: (context) {
                                                        return PlateChangeWidget(
                                                          code: data.plateno,
                                                          serialid:
                                                              data.serialid,
                                                        );
                                                      },
                                                    );
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                      "BTNTAGCOMPLAIN"] ==
                                                  true)
                                                bulkActionWidget(
                                                  title: "Complaint".trr,
                                                  onTap: () {
                                                    Get.to(() =>
                                                        FileComplaintScreen(
                                                          code: data.plateno,
                                                          serialid:
                                                              data.serialid,
                                                        ));
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                      "BTNCANCELCOMPLAIN"] ==
                                                  true)
                                                bulkActionWidget(
                                                  title: "Cancel Complaint".trr,
                                                  onTap: () {
                                                    showDialog(
                                                      barrierDismissible: false,
                                                      context: context,
                                                      builder: (context) {
                                                        return AlertDialog(
                                                          insetPadding:
                                                              const EdgeInsets
                                                                  .all(16),
                                                          contentPadding:
                                                              const EdgeInsets
                                                                  .all(24),
                                                          shape: RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          12)),
                                                          content:
                                                              cancelComplaintWidget(
                                                            code: data.plateno,
                                                            onTap: () {
                                                              // final serialid =
                                                              //     vehicle.read('singleVehicleSerialID');
                                                              // print(
                                                              //     "singleVehicleSerialID >>>>> $serialid");

                                                              vehicleController
                                                                  .myFleetList
                                                                  .refresh();
                                                              fileComplaintController
                                                                  .cancelComplaint(
                                                                      data.serialid);
                                                              // Get.back();
                                                              // for (var element in vehicleController.myFleetList) {
                                                              //   if (element['code'] == code) {
                                                              //     element['status'] = 'Active';
                                                              //   }
                                                              // }
                                                              // vehicleController.myFleetList.refresh();
                                                            },
                                                          ),
                                                        );
                                                      },
                                                    );
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                          "BTNCANCELCHANGEPLATE"] ==
                                                      true &&
                                                  data.fleetaction[
                                                          "BTNCANCELCOMPLAIN"] ==
                                                      false)
                                                bulkActionWidget(
                                                  title:
                                                      "Cancel Change Plate".trr,
                                                  onTap: () {
                                                    changePlateController
                                                        .cancelChangePlate(
                                                      data.serialid,
                                                    );
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                      "BTNEDITVEHICLEDETAILS"] ==
                                                  true)
                                                bulkActionWidget(
                                                  title:
                                                      "Edit Vehicle Details".trr,
                                                  onTap: () {
                                                    Get.back();
                                                    editVehicleDetailsController
                                                        .getVehicleDetails(
                                                            data.serialid);
                                                    // Loader.showLoader();
                                                    // Future.delayed(
                                                    //     const Duration(
                                                    //         seconds: 3), () {
                                                    //   Loader.hideLoader();
                                                    //   Get.back();
                                                    // });
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                          "BTNACTIVATESTATS"] ==
                                                      true &&
                                                  data.fleetaction[
                                                          "BTNCANCELCOMPLAIN"] ==
                                                      false)
                                                bulkActionWidget(
                                                  title: "Activate".trr,
                                                  onTap: () {
                                                    changeStatusController
                                                        .changeServiceStatus(
                                                      "A",
                                                      data.vehicleid,
                                                      data.serialid,
                                                    );
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                          "BTNDEACTIVATESTATS"] ==
                                                      true &&
                                                  data.fleetaction[
                                                          "BTNCANCELCOMPLAIN"] ==
                                                      false)
                                                bulkActionWidget(
                                                  title: "Deactivate".trr,
                                                  onTap: () {
                                                    changeStatusController
                                                        .changeServiceStatus(
                                                      "I",
                                                      data.vehicleid,
                                                      data.serialid,
                                                    );
                                                  },
                                                ),
                                              if (data.fleetaction[
                                                          "BTNTERMINATE"] ==
                                                      true &&
                                                  data.fleetaction[
                                                          "BTNCANCELCOMPLAIN"] ==
                                                      false)
                                                bulkActionWidget(
                                                  title: "Terminate".trr,
                                                  onTap: () {
                                                    // changeStatusController
                                                    //     .changeServiceStatus(
                                                    //   "T",
                                                    //   data.vehicleid,
                                                    //   data.serialid,
                                                    // );
                                                    showDialog(
                                                      barrierDismissible: false,
                                                      context: context,
                                                      builder: (context) {
                                                        return AlertDialog(
                                                          insetPadding:
                                                              const EdgeInsets
                                                                  .all(16),
                                                          contentPadding:
                                                              const EdgeInsets
                                                                  .all(24),
                                                          shape: RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          12)),
                                                          content:
                                                              terminateWidget(
                                                            onTap: () {
                                                              print(changeStatusController
                                                                  .passwordController
                                                                  .text);
                                                              print(
                                                                  "serviceStatus +++++++++ T");
                                                              print(
                                                                  "vehicleid +++++++++ ${data.vehicleid}");
                                                              print(
                                                                  "vehicleSerialID +++++++++ ${data.serialid}");

                                                              if (changeStatusController
                                                                      .passwordController
                                                                      .text ==
                                                                  "") {
                                                                commonToast(
                                                                    "Invalid Password");
                                                              } else {
                                                                changeStatusController
                                                                    .changeServiceStatus(
                                                                  "T",
                                                                  data.vehicleid,
                                                                  data.serialid,
                                                                );
                                                              }

                                                              // vehicleController.myFleetList.refresh();
                                                              // fileComplaintController.cancelComplaint(serialid);
                                                            },
                                                          ),
                                                        );
                                                      },
                                                    );
                                                  },
                                                ),
                                              // if (data.servicetypeDisp != "Tag")
                                              //   if (data.servicestatus != "T")
                                              //     bulkActionWidget(
                                              //       title: "Change status".trr,
                                              //       onTap: () {
                                              //         bool btnActivate = data
                                              //                         .fleetaction[
                                              //                     "BTNACTIVATESTATS"] ==
                                              //                 true
                                              //             ? true
                                              //             : false;
                                              //         bool btnDeactivate = data
                                              //                         .fleetaction[
                                              //                     "BTNDEACTIVATESTATS"] ==
                                              //                 true
                                              //             ? true
                                              //             : false;
                                              //         Get.back();
                                              //         showModalBottomSheet(
                                              //           isDismissible: false,
                                              //           context: context,
                                              //           shape: const RoundedRectangleBorder(
                                              //               borderRadius:
                                              //                   BorderRadius.vertical(
                                              //                       top: Radius
                                              //                           .circular(
                                              //                               16))),
                                              //           backgroundColor:
                                              //               AppColor.cBackGround,
                                              //           barrierColor: AppColor
                                              //               .cBlackOpacity,
                                              //           isScrollControlled: true,
                                              //           builder: (context) {
                                              //             return ChangeStatusWidget(
                                              //               serviceStatus: data
                                              //                   .servicestatus,
                                              //               vehicleID:
                                              //                   data.vehicleid,
                                              //               serialID:
                                              //                   data.serialid,
                                              //               btnActivate:
                                              //                   btnActivate,
                                              //               btnDeactivate:
                                              //                   btnDeactivate,
                                              //             );
                                              //           },
                                              //         );
                                              //       },
                                              //     ),
                                              // if (isdcBlock == false)
                                              //   if (data.servicestatus != "N" &&
                                              //       data.servicestatus != "T")
                                              //     bulkActionWidget(
                                              //       title: "Transfer tag".trr,
                                              //       onTap: () {
                                              //         Get.back();
                                              //         showModalBottomSheet(
                                              //           context: context,
                                              //           shape: const RoundedRectangleBorder(
                                              //               borderRadius:
                                              //                   BorderRadius.vertical(
                                              //                       top: Radius
                                              //                           .circular(
                                              //                               16))),
                                              //           backgroundColor:
                                              //               AppColor.cBackGround,
                                              //           barrierColor: AppColor
                                              //               .cBlackOpacity,
                                              //           isScrollControlled: true,
                                              //           builder: (context) {
                                              //             return TransferTagsWidget(
                                              //               serialid:
                                              //                   data.serialid,
                                              //             );
                                              //           },
                                              //         );
                                              //       },
                                              //     ),
                                            ],
                                          ),
                                        );
                                      },
                                    );
                                    //   },
                                    //   child: assetSvdImageWidget(
                                    //       image: DefaultImages.verticleMoreIcn),
                                    // );
                                    //   serviceType == "D"
                                    // ? serviceType == "T"
                                    //     ? DefaultImages.scannerIcn
                                    //     : DefaultImages.scannerIcn
                                    // : DefaultImages.scannerIcn,
                                    // showModalBottomSheet(
                                    //   context: context,
                                    //   shape: RoundedRectangleBorder(
                                    //       borderRadius: BorderRadius.vertical(
                                    //           top: Radius.circular(16))),
                                    //   backgroundColor: AppColor.cBackGround,
                                    //   barrierColor: AppColor.cBlackOpacity,
                                    //   isScrollControlled: true,
                                    //   builder: (context) {
                                    //     return ActionWidget(
                                    //       code: data.plateno,
                                    //       isComplaint:
                                    //           data.servicestatusDisp == "ACTIVE"
                                    //               ? true
                                    //               : false,
                                    //     );
                                    //   },
                                    // );
                                  },
                                  scanTap: () {
                                    showModalBottomSheet(
                                      context: context,
                                      shape: const RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(16))),
                                      backgroundColor: AppColor.cBackGround,
                                      barrierColor: AppColor.cBlackOpacity,
                                      isScrollControlled: true,
                                      builder: (context) {
                                        return AssignDigitalCouponWidget(
                                          code: data.plateno,
                                        );
                                      },
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                      ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  terminateWidget({required Function() onTap}) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  vehicleController.selectedSerialList.clear();
                  vehicleController.selectedVehicleList.clear();
                  vehicleController.selectedFleetList.clear();
                  vehicleController.filterValueList.refresh();
                  vehicleController.selectedVehicleList.refresh();
                  vehicleController.selectedSerialList.refresh();
                  vehicleController.selectedFleetList.refresh();
                  Get.back();
                  // Get.offAll(
                  //   () => DashBoardManagerScreen(
                  //     currantIndex: 0,
                  //   ),
                  //   //preventDuplicates: false,
                  // );
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    assetSvdImageWidget(image: DefaultImages.cancelIcn)
                  ],
                ),
              ),
              verticalSpace(24),
              Text("TERMINATE CONFIRMATION".trr,
                  style: pSemiBold17.copyWith(color: AppColor.cDarkGreyFont),
                  textAlign: TextAlign.center),
              verticalSpace(8),
              Text(
                  "If  terminate the tag  before 6 months from date of installation, it will deduct tag value 100 SAR without vat from account balance."
                      .trr,
                  style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  textAlign: TextAlign.center),
              verticalSpace(24),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonTextField(
                    controller: changeStatusController.passwordController,
                    labelText: 'Please Enter your password'.trr,
                    hintText: "**********",
                    obscureText: changeStatusController.isPassword.value,
                    obscuringCharacter: '*',
                    // validator: (value) {
                    //   return Validator.validateName(value, "Password".trr);
                    // },
                  ),
                  verticalSpace(15),
                  CommonTextField(
                    controller:
                        changeStatusController.confirmPasswordController,
                    labelText: 'Confirm your password'.trr,
                    hintText: "**********",
                    obscureText: changeStatusController.isConfirmPass.value,
                    obscuringCharacter: '*',
                    validator: (value) {
                      return Validator.validateConfirmPassword(value,
                          changeStatusController.passwordController.text);
                    },
                  ),
                ],
              ),
              verticalSpace(24),
              Row(
                children: [
                  Expanded(
                      child: CommonButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.back();
                      // Get.offAll(
                      //   () => DashBoardManagerScreen(
                      //     currantIndex: 0,
                      //   ),
                      //   //preventDuplicates: false,
                      // );
                    },
                    btnColor: AppColor.cBackGround,
                    bColor: AppColor.themeDarkBlueColor,
                    textColor: AppColor.cDarkBlueFont,
                  )),
                  horizontalSpace(16),
                  Expanded(
                    child: CommonButton(
                      title: 'Submit'.trr,
                      onPressed: onTap,
                      btnColor: AppColor.themeOrangeColor,
                      horizontalPadding: 16,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}

Widget fleetWidget({
  required String code,
  String? status,
  Color? textColor,
  Color? color,
  bool? value,
  //bool? isShowCheck,
  //ValueChanged<bool?>? onChanged,
  String? pinblock,
  String? pinblockDate,
  String? title,
  String? tag,
  String? type,
  String? driver,
  String? quotaTotal,
  String? quotaString,
  String? division,
  Function()? viewMore,
  Function()? scanTap,
  String? serviceStatus,
  String? serviceType,
  String? serialXID,
  String? insTermDate,
}) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8.0),
    child: Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
            color: value == true
                ? AppColor.themeDarkBlueColor
                : AppColor.cLightGrey),
        color: AppColor.lightBlueColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  // status == "TERMINATED" || status == "NEW"
                  //     ? SizedBox()
                  //     : isShowCheck == true
                  //         ? SizedBox(
                  //             height: 24,
                  //             width: 24,
                  //             child: Checkbox(
                  //               value: value,
                  //               onChanged: onChanged,
                  //               activeColor: AppColor.themeDarkBlueColor,
                  //               shape: RoundedRectangleBorder(
                  //                   borderRadius: BorderRadius.circular(4),
                  //                   side: BorderSide(color: AppColor.cBorder)),
                  //             ),
                  //           )
                  //         : SizedBox(),
                  // horizontalSpace(isShowCheck == true ? 8 : 0),
                  Container(
                      height: 24,
                      // decoration: BoxDecoration(
                      //     color: status == "NEW"
                      //         ? AppColor.cLightGrey
                      //         : AppColor.cLightBlueContainer,
                      //     border: Border.all(
                      //         color: status == "NEW"
                      //             ? AppColor.cLightGrey
                      //             : AppColor.cLightBlueContainer),
                      //     borderRadius: BorderRadius.circular(4)),
                      padding: const EdgeInsets.symmetric(
                          vertical: 2, horizontal: 6),
                      child: Center(
                        child: Text(
                          code,
                          style: pBold12.copyWith(
                            fontSize: 13,
                          ),
                          // color: status == 'NEW'
                          //     ? AppColor.cDarkGreyFont
                          //     : AppColor.cDarkBlueText),
                        ),
                      )),
                  horizontalSpace(8),
                  statusWidget(
                      text: status,
                      textColor: textColor,
                      color: color,
                      tag: tag),
                  // if (value == true || serviceType == "D") horizontalSpace(8),
                  // if (value == true || serviceType == "D")
                  //   GestureDetector(
                  //     onTap: status == "ACTIVE" ? scanTap : null,
                  //     child: assetSvdImageWidget(
                  //         image: DefaultImages.scannerIcn,
                  //         colorFilter: ColorFilter.mode(
                  //             status == "ACTIVE"
                  //                 ? AppColor.cText
                  //                 : AppColor.cLightBlueFont,
                  //             BlendMode.srcIn)),
                  //   )
                ],
              ),
              // if (serviceStatus != "T")
              GestureDetector(
                  onTap: viewMore,
                  child:
                      assetSvdImageWidget(image: DefaultImages.verticleMoreIcn))
            ],
          ),
          // verticalSpace(8),
          // title == ''
          //     ? const SizedBox()
          //     : Row(
          //         children: [
          //           Text(
          //             title!,
          //             style: pRegular13,
          //           ),
          //           Padding(
          //             padding: const EdgeInsets.symmetric(horizontal: 8.0),
          //             child: assetSvdImageWidget(image: DefaultImages.dotIcn),
          //           ),
          //           Text(
          //             type!,
          //             style: pRegular13,
          //           ),
          //         ],
          //       ),
          // verticalSpace(18),
          // Row(
          //   children: [
          //     SizedBox(
          //         width: 140,
          //         child: Text(
          //           "Service Type".trr,
          //           style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          //         )),
          //     Expanded(
          //       child: Text.rich(
          //         TextSpan(
          //           text: serviceType == "D"
          //               ? "DC"
          //               : serviceType == "T"
          //                   ? "TAG"
          //                   : "SMART CARD",
          //           style: pRegular13,
          //         ),
          //       ),
          //     )
          //     // Text(
          //     //   driver!,
          //     //   style: pRegular13,
          //     // ),
          //   ],
          // ),
          verticalSpace(12),
          Row(
            children: [
              SizedBox(
                  width: 140,
                  child: Text(
                    "Vehicle Type".trr,
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  )),
              Expanded(
                child: Text.rich(
                  TextSpan(
                    text: '$title',
                    style: pRegular13,
                  ),
                ),
              )
              // Text(
              //   driver!,
              //   style: pRegular13,
              // ),
            ],
          ),
          verticalSpace(12),
          Row(
            children: [
              SizedBox(
                  width: 140,
                  child: Text(
                    "Fuel Type".trr,
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  )),
              Expanded(
                child: Text.rich(
                  TextSpan(
                    text: '$type',
                    style: pRegular13,
                  ),
                ),
              )
              // Text(
              //   driver!,
              //   style: pRegular13,
              // ),
            ],
          ),
          verticalSpace(12),
          Row(
            children: [
              SizedBox(
                  width: 140,
                  child: Text(
                    "Driver".trr,
                    style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
                  )),
              Expanded(
                child: Text.rich(
                  TextSpan(
                    text: '$driver ',
                    style: pRegular13,
                  ),
                ),
              )
              // Text(
              //   driver!,
              //   style: pRegular13,
              // ),
            ],
          ),
          // verticalSpace(12),
          // Row(
          //   children: [
          //     SizedBox(
          //         width: 140,
          //         child: Text(
          //           "Quota (used/total)".trr,
          //           style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          //         )),
          //     Expanded(
          //       child: Text.rich(
          //         TextSpan(
          //           text: '$quotaTotal ',
          //           style: pBold14.copyWith(fontSize: 13),
          //           children: <TextSpan>[
          //             TextSpan(text: quotaString, style: pRegular13),
          //           ],
          //         ),
          //       ),
          //     )
          //   ],
          // ),
          // verticalSpace(12),
          // Row(
          //   crossAxisAlignment: CrossAxisAlignment.start,
          //   children: [
          //     SizedBox(
          //         width: 140,
          //         child: Text(
          //           "Division".trr,
          //           style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          //         )),
          //     Text(
          //       division!,
          //       style: pRegular13,
          //     ),
          //   ],
          // ),
          // verticalSpace(12),
          // Row(
          //   crossAxisAlignment: CrossAxisAlignment.start,
          //   children: [
          //     SizedBox(
          //         width: 140,
          //         child: Text(
          //           serviceStatus != "T" //"Division".trr,
          //               ? serviceType == "T"
          //                   ? "Installation Date"
          //                   : "Activation Date"
          //               : "Terminated Date",
          //           style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          //         )),
          //     Text(
          //       insTermDate!,
          //       style: pRegular13,
          //     ),
          //   ],
          // ),
          // verticalSpace(12),
          // Row(
          //   crossAxisAlignment: CrossAxisAlignment.start,
          //   children: [
          //     SizedBox(
          //         width: 140,
          //         child: Text(
          //           serviceType == "C" //"Division".trr,
          //               ? "Card No"
          //               : "RFID",
          //           style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          //         )),
          //     Text(
          //       serialXID!,
          //       style: pRegular13,
          //     ),
          //   ],
          // ),
          // if (pinblock == "Y") verticalSpace(12),
          // if (pinblock == "Y")
          //   Row(
          //     crossAxisAlignment: CrossAxisAlignment.start,
          //     children: [
          //       SizedBox(
          //           width: 140,
          //           child: Text(
          //             "PINBLOCK",
          //             style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          //           )),
          //       Text(
          //         pinblock == "Y" //"Division".trr,
          //             ? "ON"
          //             : "OFF",
          //         style: pRegular13,
          //       ),
          //     ],
          //   ),
          // if (pinblock == "Y") verticalSpace(12),
          // if (pinblock == "Y")
          //   Row(
          //     crossAxisAlignment: CrossAxisAlignment.start,
          //     children: [
          //       SizedBox(
          //           width: 140,
          //           child: Text(
          //             "PINBLOCK Date",
          //             style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          //           )),
          //       Text(
          //         pinblockDate.toString(),
          //         style: pRegular13,
          //       ),
          //     ],
          //   ),
        ],
      ),
    ),
  );
}

Widget statusWidget(
    {String? text,
    String? tag,
    Color? textColor,
    Color? color,
    double? horizontalSpace,
    double? left}) {
  return Container(
      height: 24,
      decoration: BoxDecoration(
          color: color ?? AppColor.cLightGreen,
          borderRadius: BorderRadius.circular(4)),
      padding: EdgeInsets.only(right: 8, left: left ?? 8),
      child: Row(
        children: [
          tag == null
              ? const SizedBox()
              : assetSvdImageWidget(
                  image: tag,
                  colorFilter: ColorFilter.mode(
                      textColor ?? AppColor.cGreen, BlendMode.srcIn)),
          SizedBox(width: horizontalSpace ?? 5),
          Text(
            text!,
            style: pSemiBold12.copyWith(color: textColor ?? AppColor.cGreen),
          ),
        ],
      ));
}

Widget menuTitleRowWidget(
    {required String title, required bool isSelected, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      //color: AppColor.themeOrangeColor,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
            color: AppColor.themeOrangeColor), //AppColor.themeDarkBlueColor
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pBold20.copyWith(
              color: AppColor.themeOrangeColor,
            ),
          ),
          assetSvdImageWidget(
            image: isSelected == true
                ? DefaultImages.arrowUpIcn
                : DefaultImages.dropDownIcn,
            width: 24,
            height: 24,
          )
        ],
      ),
    ),
  );
}

cancelComplaintWidget({required String code, required Function() onTap}) {
  return Container(
    decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: () {
            Get.back();
            // Get.offAll(
            //   () => DashBoardManagerScreen(
            //     currantIndex: 0,
            //   ),
            //   //preventDuplicates: false,
            // );
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [assetSvdImageWidget(image: DefaultImages.cancelIcn)],
          ),
        ),
        verticalSpace(24),
        // Text.rich(
        //     TextSpan(
        //         text: 'Do you want to cancel your complaint for'.trr,
        //         style: pRegular17,
        //         children: [TextSpan(text: '  $code?', style: pSemiBold17)]),
        //     textAlign: TextAlign.center),
        // verticalSpace(8),
        Text("Are you sure you want to Cancel your complaint?".trr,
            style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
            textAlign: TextAlign.center),
        verticalSpace(24),
        Row(
          children: [
            Expanded(
                child: CommonButton(
              title: 'No'.trr,
              onPressed: () {
                Get.back();
                // Get.offAll(
                //   () => DashBoardManagerScreen(
                //     currantIndex: 0,
                //   ),
                //   //preventDuplicates: false,
                // );
              },
              btnColor: AppColor.cBackGround,
              bColor: AppColor.themeDarkBlueColor,
              textColor: AppColor.cDarkBlueFont,
            )),
            horizontalSpace(16),
            Expanded(
              child: CommonButton(
                title: 'Yes'.trr,
                onPressed: onTap,
                btnColor: AppColor.themeOrangeColor,
                horizontalPadding: 16,
              ),
            ),
          ],
        )
      ],
    ),
  );
}
