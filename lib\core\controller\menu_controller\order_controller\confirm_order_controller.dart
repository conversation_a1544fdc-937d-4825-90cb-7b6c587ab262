import 'dart:convert';
import 'dart:math';
import 'dart:io';
import 'dart:developer' as logs;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart';
import 'package:quickalert/quickalert.dart';
import 'package:waie_app/models/balancetopupnew.dart';
import 'package:waie_app/models/pricetagcard.dart';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/models/profile.dart';
import 'package:waie_app/utils/constant_var.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/locale_string.dart';
import 'package:waie_app/utils/prefer.dart';
import 'package:waie_app/utils/regDB.dart';
import 'package:waie_app/view/screen/auth/login_with_email_screen.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/login_manager/mada_alrajhi.dart';
import 'package:waie_app/view/screen/menu_screen/order_screen/apple_pay_screen.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/loading_widget.dart';

import '../../../../models/checkoutSession.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/constants.dart';
import '../../../../utils/text_style.dart';
import '../../../../view/screen/menu_screen/order_screen/order_widget.dart';
import '../../../../view/widget/common_button.dart';
import '../../../../view/widget/common_space_divider_widget.dart';
import '../../review_controller/review_controller.dart';

class ConfirmOrderController extends GetxController {
  GetStorage custsData = GetStorage('custsData');
  var priceTagCards = <PriceTagCardModel>[].obs;
  var checkoutSessionList = <CheckoutSessionModel>[].obs;
  TextEditingController stcPayOTPText = TextEditingController();
  var checkoutSession = "".obs;
  String stcPayPhoneNumber = "";
  RxString bankCode = "ARB".obs;
  GetStorage userStorage = GetStorage('User');
  final vehicle = GetStorage();
  GetStorage usersData = GetStorage('usersData');
  RegisterDatabase db = RegisterDatabase();
  // TextEditingController totalAmountController = TextEditingController();
  // TextEditingController serviceTypeController = TextEditingController();
  Function? updateUi;
  void setUpdateUiCallback(Function updateUiCallback) {
    updateUi = updateUiCallback;
  }

  generateVirtualAccount() async {
    Loader.showLoader();
    var client = http.Client();
    var custData = custsData.read('custData');
    print(bankCode.value);
    print(custData['CUSTID']);
    print(Constants.IsAr_App);

    var response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl +
            ApiEndPoints.authEndpoints.generateVirtualAccount),
        body: {
          "ebank": bankCode.value,
          "custid": custData['CUSTID'],
          "isAR": Constants.IsAr_App,
        });
    print("result");
    print("=======================================");
    print('jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');

    if (jsonDecode(response.body)["MessageType"] == "success") {
      Loader.hideLoader();
      String message = "Virtual Account Generated Successfully";
      String title = "SUCCESS";
      showDialog(
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(16),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(title.tr, style: pBold20),
                verticalSpace(24),
                Text(
                  message.toString(),
                  style: pRegular16,
                  textAlign: TextAlign.center,
                ),
                verticalSpace(24),
                CommonButton(
                  title: 'Continue to Dashboard'.tr,
                  onPressed: () async {
                    await Prefs.clear();
                    await vehicle.erase();
                    await userStorage.erase();
                    await custsData.erase();
                    await usersData.erase();
                    await login(jsonDecode(response.body)["username"],
                        jsonDecode(response.body)["password"]);
                    //Get.offAll(() => DashBoardManagerScreen(currantIndex: 0));
                  },
                  btnColor: AppColor.themeOrangeColor,
                )
              ],
            ),
          );
        },
      );
    } else {
      Loader.hideLoader();
      showDialog(
        barrierDismissible: false,
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(24),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  jsonDecode(response.body)["message"],
                  style: pBold20,
                  textAlign: TextAlign.center,
                ),
                verticalSpace(24),
                CommonButton(
                  title: "OK".tr,
                  onPressed: () async {
                    Get.back();
                  },
                  btnColor: AppColor.themeOrangeColor,
                )
              ],
            ),
          );
        },
      );
    }
  }

  clearStorage() {
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');
    final tagOrderRefund = GetStorage();
    final topupOrderRefund = GetStorage();
    final vehicle = GetStorage();
    final getOTPDataVerify = GetStorage();
    final isPinblock = GetStorage();
    final isDCBlock = GetStorage();
    Prefs.clear();
    //Get.offAll(() => LoginManagerScreen());
    vehicle.erase();
    tagOrderRefund.erase();
    topupOrderRefund.erase();
    getOTPDataVerify.erase();
    isPinblock.erase();
    isDCBlock.erase();
    userStorage.erase();
    custsData.erase();
    usersData.erase();
  }

  login(email, pass) async {
    Loader.showLoader();
    clearStorage();
    GetStorage userStorage = GetStorage('User');
    GetStorage usersData = GetStorage('usersData');
    GetStorage custsData = GetStorage('custsData');

    db.loadData();

    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      print(email);
      print(pass);

      //String url = "https://localhost:44383//api/CUSTLOGIN";

      print(ApiEndPoints.baseUrl);
      String url =
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.signInEmail;
      print(url);
      // String url="https://devapi.aldrees.com//api/CUSTLOGIN";
      var response = await post(Uri.parse(url), headers: <String, String>{
        'authorization': basicAuth,
        "Access-Control-Allow-Origin": "*", // Required for CORS support to work
        "Access-Control-Allow-Credentials":
            "true", // Required for cookies, authorization headers with HTTPS
        "Access-Control-Allow-Headers":
            "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      }, body: {
        'username': email,
        'password': pass
      });

      print(response.statusCode);
      print(response.body);
      // print(basicAuth);
      //Loader.hideLoader();
      print('Loader loads');
      if (response.statusCode == 200) {
        print('Suucessfully Fetch');

        Map cardInfo = json.decode(response.body);
        print("json.decode(response.body) ${json.decode(response.body)}");
        print('1');
        print(cardInfo);
        Profile userData = Profile.fromJson(cardInfo);
        print('2');

        if (userData.returnMessage!.isValidTransaction!) {
          if (userData.returnMessage!.action == 'POPUP') {
            //loginFailWidget(userData.returnMessage!.message);
            commonToast(userData.returnMessage!.message);
            Loader.hideLoader();
          } else {
            String notifUrl =
                ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getNotifCount;
            var notifResponse =
                await post(Uri.parse(notifUrl), headers: <String, String>{
              'authorization': basicAuth,
              "Access-Control-Allow-Credentials": 'true',
              "Access-Control-Allow-Headers":
                  "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "POST, OPTIONS"
            }, body: {
              "custid": userData.auUsers?.custid,
              "isAR": Constants.IsAr_App,
            });
            //print(notifResponse.body);
            print(
                "Constants.notifCount >>>> ${json.decode(notifResponse.body)}");

            var isCount = json.decode(notifResponse.body);

            print("isCount >>>> $isCount");

            Constants.notifCount = isCount;

            print(userData.auUsers?.custid);
            var custData = jsonDecode(jsonEncode(userData.auCust));
            var usrData = jsonDecode(jsonEncode(userData.auUsers));
            print(custData);
            if (custData['IS_VERIFIED'] == 'Y') {
              Constants.custIsVerified == "Y";
            }
            Constants.custAcctStatus = userData.auCust!.acctstatus!;

            print(custData['MOBILENO']);
            print(usrData['CUSTID']);
            print(usrData['EMAILID']);
            print(usrData['USERNAME']);
            print("usrData $usrData");
            print(userData.auUsers!.password);
            SharedPreferences sharedUser =
                await SharedPreferences.getInstance();
            // Map decode_options = jsonDecode(jsonString);
            String user = jsonEncode(cardInfo);
            sharedUser.setString('user', user);
            sharedUser.setString('userid', userData.auCust!.custid!);
            sharedUser.setString('regType', userData.auCust!.regtype);
            sharedUser.setString('username', usrData['USERNAME']);
            userStorage.writeIfNull('custid', userData.auCust!.custid!);
            userStorage.writeIfNull('emailid', userData.auCust!.emailid!);
            userStorage.writeIfNull('accttype', userData.auCust?.accttype);
            usersData.writeIfNull('usrData', usrData);
            custsData.writeIfNull('custData', custData);
            /* Get.to(() => DashBoardManagerScreen(
                currantIndex: 0,
              ));*/
            authController.userAccess();
          }
        } else {
          Loader.hideLoader();
          print("Invalid ID Password");
          //loginFailWidget("Invalid ID or password");
          //commonToast("Invalid ID or password");
          if (userData.returnMessage!.message!
              .toString()
              .contains("ACCTLOCKEDMSG")) {
            commonToast("Your Account is lock, try after 5 minutes");
            Loader.hideLoader();
          } else if (userData.returnMessage!.message!
              .toString()
              .contains("INVALIDPASSWORD")) {
            commonToast("Invalid ID Password");
            Loader.hideLoader();
          } else {
            commonToast(userData.returnMessage!.message!.toString());
            Loader.hideLoader();
          }
        }
        return userData;
      } else {
        Loader.hideLoader();
        print('Login Fail');
        commonToast('Login Fail');
        print(response.statusCode.toString());
        loginFailWidget(response.statusCode.toString());
      }
    } catch (e) {
      print('Error');
      print(e.toString());
    }
  }

  createMADAorder(
      sessionID, totalPurchased, orderID, depositTo, serviceType, qty) async {
    var client = http.Client();
    var custData = custsData.read('custData');

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.createOrderMADA),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
        body: {
          "custid": custData['CUSTID'],
          "depositto": depositTo,
          "transactionid": depositTo,
          "mobileno": custData['MOBILENO'],
          "amount": totalPurchased,
          "OrderTypes": "S",
          "topupamt": totalPurchased ?? "1",
          "qty": qty ?? "1",
          "servicetype": serviceType,
          "paytype": "D",
          "payrefno": orderID,
          "is_mokafa50": "false",
          "IsAR": Constants.IsAr_App,
          "serialid": "",
          "reqstr": "", //jsonrslt
        });
    print("result");
    print("=======================================");
    print('jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');

    return response;
  }

  createMADAtopup(
      sessionID, totalPurchased, orderID, depositTo, serviceType, qty) async {
    var client = http.Client();
    var custData = custsData.read('custData');

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.createOrderMADA),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
        body: {
          "custid": custData['CUSTID'],
          "depositto": depositTo,
          "transactionid": depositTo,
          "mobileno": custData['MOBILENO'],
          "amount": totalPurchased,
          "OrderTypes": "T",
          "topupamt": totalPurchased ?? "1",
          "qty": qty ?? "1",
          "servicetype": serviceType,
          "paytype": "D",
          "payrefno": orderID,
          "is_mokafa50": "false",
          "IsAR": Constants.IsAr_App,
          "serialid": "",
          "reqstr": "",
        });
    print("result");
    print("=======================================");
    print('jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');

    return response;
  }

  createSingleMADAOrder(orderID) async {
    var client = http.Client();
    var custData = custsData.read('custData');

    var response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl +
            ApiEndPoints.authEndpoints.createSingleMADAOrder),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
        body: {
          "custid": custData['CUSTID'],
          "payrefno": orderID,
          "IsAR": Constants.IsAr_App,
        });
    print("createSingleMADAOrder");
    print(custData['CUSTID']);
    print(orderID);
    print(Constants.IsAr_App);
    print("=======================================");
    print('jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');
    print(response.statusCode);
    print(response.body);
  }

  prepareSTCPay(serviceType, qtys, payType, replacement) async {
    Loader.showLoader();
    var client = http.Client();
    var device = "";
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    var custData = jsonEncode(custsData.read('custData'));

    print("prepareSTCPay");
    print("SERVICETYPE ===============$serviceType");
    print("QTY =============== $qtys");
    print("PAYTYPE ===============$payType");
    print("replacement ===============$replacement");
    print("custsData ===============${jsonEncode(custsData.read('custData'))}");

    var stcPayMobileNo = stcPayPhoneNumber.replaceAll("+", "");
    print("stcPayMobileNo ===============$stcPayMobileNo");
  }

  newServiceOrder(
    serviceType,
    qtys,
    payType,
    replacement,
    subTotal,
    vat,
    totalAmount,
  ) async {
    Loader.showLoader();
    var client = http.Client();
    var device = "";
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    var custData = jsonEncode(custsData.read('custData'));

    print("SERVICETYPE ===============$serviceType");
    print("QTY =============== $qtys");
    print("PAYTYPE ===============$payType");
    print("replacement ===============$replacement");
    print("custsData ===============${jsonEncode(custsData.read('custData'))}");

    if (Platform.isAndroid || Platform.isWindows) {
      device = "A";
    } else if (Platform.isIOS) {
      device = "I";
    }

    var stcPayMobileNo = stcPayPhoneNumber.replaceAll("+", "");
    print("stcPayMobileNo ===============$stcPayMobileNo");

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.newServiceOrder),
        body: {
          "custdata": custData,
          "SERVICETYPE": serviceType,
          "QTY": qtys,
          "PAYTYPE": payType,
          "MADAALRAJHI": device,
          "requestSI": Constants.requestSITemp.toString() ?? "",
          "REPLACEMENT": replacement,
          "mobileNo": payType == "D"
              ? ""
              : payType == "Y"
                  ? stcPayPhoneNumber.replaceAll("+", "")
                  : "",
        });
    // BalanceTopUpModelNew result =
    //     BalanceTopUpModelNew.fromJson(jsonDecode(response.body));
    final jsonData = response.body;
    print("result");
    print(jsonData);
    print("=======================================");
    print('orderID jsonDecode(response.body) >>>> ${response.body}');
    print("=======================================");
    print(jsonDecode(response.body)["MessageType"]);
    print(jsonDecode(response.body)["message"]);
    if (jsonDecode(response.body)["MessageType"] == "success") {
      if (payType == "D") {
        String urlWithQuotes = '${jsonDecode(response.body)["message"]}';
        String urlWithoutQuotes = urlWithQuotes.replaceAll('"', '');

        print(urlWithoutQuotes);
        await Get.to(() => MADAAlrajhiScreen(
              url: urlWithoutQuotes,
              replacement: replacement,
              subTotal: subTotal,
              vat: vat,
              totalAmount: totalAmount,
            ));
        Loader.hideLoader();
      } else if (payType == "Y") {
        Loader.hideLoader();
        print("=======================================");
        print("STCPAY");
        print("=======================================");
        print(jsonDecode(response.body)["MessageType"]);
        print(jsonDecode(response.body)["message"]);
        print("=======================================");
        showSTCPAYOTPDialog(
          jsonDecode(response.body)["custID"],
          jsonDecode(response.body)["userID"],
          jsonDecode(response.body)["mobileNo"],
          jsonDecode(response.body)["totalAmount"],
          jsonDecode(response.body)["STCPayOtpReference"],
          jsonDecode(response.body)["STCPayPmtReference"],
        );
      }
      // String urlWithQuotes = '${jsonDecode(response.body)["message"]}';
      // String urlWithoutQuotes = urlWithQuotes.replaceAll('"', '');

      // print(urlWithoutQuotes);
      // await Get.to(() => MADAAlrajhiScreen(
      //       url: urlWithoutQuotes,
      //       type: "service",
      //     ));
    } else {
      Loader.hideLoader();
      print(jsonDecode(response.body)["message"]);
      await QuickAlert.show(
        context: Get.context!,
        type: QuickAlertType.error,
        text: jsonDecode(response.body)['message'].toString(),
      );
    }
  }

  prepareMADA(code, unitPrice, qtys, subTotal, vat, totalAmount, serviceType,
      replacement) async {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
    var client = http.Client();
    const chars = '1234567890';
    var rnd = Random();
    var custData = custsData.read('custData');
    var device = "";
    if (Platform.isAndroid || Platform.isWindows) {
      device = "ANDROID";
    } else if (Platform.isIOS) {
      device = "IOS";
    }

    String getRandomString(int length) =>
        String.fromCharCodes(Iterable.generate(
            length, (_) => chars.codeUnitAt(rnd.nextInt(chars.length))));

    var newOrderID = getRandomString(10);
    print('newOrderID >>>> $newOrderID');

    // Create uuid object
    var newUuid = const Uuid();

    // Generate a v4 (random) id
    var depositTo = newUuid.v4(); // -> '110ec58a-a0f2-4ac4-8393-c866d813b8d1'
    var orderID = ""; //newOrderID;

    if (Platform.isAndroid || Platform.isWindows) {
      orderID = "A$newOrderID";
    } else if (Platform.isIOS) {
      orderID = "I$newOrderID";
    }
    print("orderID >>>> $orderID");
    print("depositTo >>>> $depositTo");

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.prepareMADA),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
        body: {
          "device": device,
          "custid": custData['CUSTID'],
          "depositto": depositTo,
          "amount": totalAmount ?? "1",
          "OrderTypes": "S",
          "topupamt": totalAmount ?? "1",
          "qty": qtys ?? "1",
          "servicetype": serviceType,
          "paytype": "D",
          "payrefno": orderID,
          "is_mokafa50": "false",
          "IsAR": Constants.IsAr_App,
          "serialid": "",
          "reqstr": custData['CUSTID'],
          "REPLACEMENT": replacement,
        });
    // BalanceTopUpModelNew result =
    //     BalanceTopUpModelNew.fromJson(jsonDecode(response.body));
    final jsonData = jsonDecode(response.body);
    print("result");
    print("=======================================");
    print(
        'orderID jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');
    if (jsonData is Map && jsonData.containsKey('ExceptionMessage')) {
      Navigator.of(Get.context!).pop();
      print(
          'jsonDecode(response.body)["ExceptionMessage"] >>>> ${jsonDecode(response.body)["ExceptionMessage"]}');
      showDialog(
        barrierDismissible: false,
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(24),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  jsonDecode(response.body)["ExceptionMessage"],
                  style: pBold20,
                  textAlign: TextAlign.center,
                ),
                verticalSpace(24),
                CommonButton(
                  title: "OK".tr,
                  onPressed: () {
                    Get.back();
                  },
                  btnColor: AppColor.themeOrangeColor,
                )
              ],
            ),
          );
        },
      );
    } else {
      var result = jsonDecode(response.body)[0]["RES_BODY"];
      print("result===> ${jsonDecode(jsonEncode(result))}");
      final json = "[$result]";
      List resu = (jsonDecode(json) as List<dynamic>);
      print("resu===> $resu");

      for (int i = 0; i < resu.length; i++) {
        CheckoutSessionModel session =
            CheckoutSessionModel.fromJson(resu[i] as Map<String, dynamic>);
        checkoutSessionList.add(session);
      }
      var sess = checkoutSessionList.map((item) => item.session.id).toString();

      print(
          "checkoutSessionList===> ${jsonDecode(jsonEncode(checkoutSessionList))[0]["result"]}");
      print(
          "checkoutSessionList===> ${sess.replaceAll(RegExp(r"\p{P}", unicode: true), "")}");
      checkoutSession.value =
          sess.replaceAll(RegExp(r"\p{P}", unicode: true), "");

      print(checkoutSession.value);
      print("orderID ==== $orderID");
      print("depositTo ==== $depositTo");

      if (jsonDecode(jsonEncode(checkoutSessionList))[0]["result"] ==
          "SUCCESS") {
        Navigator.of(Get.context!).pop();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              contentPadding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              insetPadding: const EdgeInsets.all(25),
              content: madaSuccessWidget(
                orderID: orderID,
                code: code,
                unitPrice: unitPrice,
                qty: qtys,
                subTotal: subTotal,
                vat: vat,
                purchaseTotal: totalAmount,
                serviceType: serviceType,
                sessionID: checkoutSession.value,
                depositto: depositTo,
                orderType: "SERVICE",
                custid: custData['CUSTID'],
              ),
            );
          },
        );
      }
    }

    return checkoutSessionList;
  }

  sendAlraljhiApplePay(
    token,
    paymentMethod,
    transactionIdentifier,
    totalPurchased,
    orderID,
    serviceType,
    qty,
    orderType,
    custid,
    subTotal,
    vat,
  ) async {
    Loader.showLoader();
    print("sendAlraljhiApplePay==============================");
    print(token);
    print(paymentMethod);
    print(transactionIdentifier);
    print(totalPurchased);
    print(orderID);
    print("sendAlraljhiApplePay=***========");

    print("");

    var client = http.Client();
    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.sendAlrajhiApplePayReq),
          body: {
            "TOKEN": token,
            "PAYMENTMETHOD": jsonEncode(paymentMethod),
            "TRANSIDENTIFIER": transactionIdentifier,
            "TOTALPURCHASED": totalPurchased,
            "ORDERID": orderID
          });
      print("fwregrfdasfsd==============================");
      print(jsonEncode(response.body));

      print(response.body);
      var convertDataToJson = jsonDecode(response.body);
      print(convertDataToJson);
      //List data = convertDataToJson['results'];

      if (convertDataToJson['Success'] == true &&
          convertDataToJson['Code'] == 200) {
        print(convertDataToJson['Success']);
        print(convertDataToJson['Code']);
        await confirmService("", totalPurchased, orderID, transactionIdentifier,
            serviceType, qty, "");
        Loader.hideLoader();
        await QuickAlert.show(
            context: Get.context!,
            type: QuickAlertType.success,
            text: convertDataToJson['Message'],
            confirmBtnText: "Continue to Dashboard".tr,
            confirmBtnColor: AppColor.themeOrangeColor,
            onConfirmBtnTap: () async {
              Get.offAll(DashBoardManagerScreen(
                currantIndex: 0,
              ));
              final reviewController = Get.find<ReviewController>();
              await reviewController.triggerReview();
            });
      } else {
        Loader.hideLoader();
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          text: convertDataToJson['Message'],
          onCancelBtnTap: () {
            Get.back();
          },
        );
      }

      return null;
    } catch (e) {
      print("e.toString()");
      print(e.toString());
      logs.log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  updateSession(
    token,
    sessionID,
    orderID,
    totalPurchased,
    depositto,
    serviceType,
    qty,
    orderType,
    custid,
    subTotal,
    vat,
  ) async {
    var client = http.Client();

    String basicAuth =
        'Basic ${base64Encode(utf8.encode('merchant.${ConstantVar.madaMerchantid}:${ConstantVar.madaAPIkey}'))}';
    var responses = await client.put(
      Uri.parse(
          "${ConstantVar.madaGateway}/api/rest/version/73/merchant/${ConstantVar.madaMerchantid}/session/$sessionID"),
      headers: <String, String>{
        'authorization': basicAuth,
        "Access-Control-Allow-Credentials": 'true',
        "Access-Control-Allow-Headers":
            "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: json.encode({
        "apiOperation": "AUTHENTICATE_PAYER",
        "authentication": {
          "redirectResponseUrl": "https://localhost:8000/",
          "acceptVersions": "3DS2",
          "channel": "PAYER_BROWSER",
          "purpose": "PAYMENT_TRANSACTION"
        },
        "device": {
          "browser": "MOZILLA",
          "browserDetails": {
            "3DSecureChallengeWindowSize": "FULL_SCREEN",
            "acceptHeaders": "application/json",
            "colorDepth": 24,
            "javaEnabled": true,
            "language": "en-US",
            "screenHeight": 640,
            "screenWidth": 480,
            "timeZone": 273
          },
          "ipAddress": "127.0.0.1"
        },
        "sourceOfFunds": {
          "type": "CARD",
          "provided": {
            "card": {
              "devicePayment": {"paymentToken": "$token"}
            }
          }
        },
        "order": {"walletProvider": "APPLE_PAY"}
      }),
    );
    print("updateSession==============================");
    print(jsonDecode(responses.body));
    final updateSession = jsonDecode(responses.body);
    print("updateSession==============================");
    print(updateSession['session']);
    print(updateSession['session']["id"]);
    print(updateSession['session']["updateStatus"]);
    print(updateSession['session']["version"]);
    print("updateSession==============================");

    if (updateSession['session']["updateStatus"] == "SUCCESS") {
      var payResponse = await client.put(
        Uri.parse(
            "${ConstantVar.madaGateway}/api/rest/version/73/merchant/${ConstantVar.madaMerchantid}/order/$orderID/transaction/$orderID"),
        headers: <String, String>{
          'authorization': basicAuth,
          "Access-Control-Allow-Credentials": 'true',
          "Access-Control-Allow-Headers":
              "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: json.encode({
          "apiOperation": "PAY",
          "order": {
            "amount": totalPurchased,
            "currency": "SAR",
            "reference": "$orderID"
          },
          "transaction": {"reference": "$orderID"},
          "session": {"id": "$sessionID"},
          "sourceOfFunds": {"type": "CARD"}
        }),
      );
      print("payResponse==============================");
      print(jsonDecode(payResponse.body));
      final payResponses = jsonDecode(payResponse.body);
      print("payResponse==============================");
      print(payResponses['response']);
      print(payResponses['response']["gatewayCode"]);
      print("payResponse==============================");

      if (payResponses['response']["gatewayCode"] == "APPROVED") {
        // CALL CREATE ORDER_HDR
        //commonToast("نجاح : Success");
        await confirmService(sessionID, totalPurchased, orderID, depositto,
            serviceType, qty, payResponses['response']["gatewayCode"]);
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "نجاح : Success",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "Continue to Dashboard".tr,
                    onPressed: () async {
                      Get.offAll(DashBoardManagerScreen(
                        currantIndex: 0,
                      ));
                      final reviewController = Get.find<ReviewController>();
                      await reviewController.triggerReview();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        //commonToast("${payResponses['response']["acquirerMessage"]}");
        Loader.hideLoader();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${payResponses['response']["acquirerMessage"]}",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    }
  }

  confirmService(sessionID, totalPurchased, orderID, depositTo, serviceType,
      qty, gatewayCode) async {
    var client = http.Client();
    var custData = custsData.read('custData');

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.confirmOrder),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
        body: {
          "custid": custData['CUSTID'],
          "depositto": depositTo,
          "transactionid": depositTo,
          "mobileno": custData['MOBILENO'],
          "amount": totalPurchased,
          "OrderTypes": "S",
          "topupamt": totalPurchased ?? "1",
          "qty": qty ?? "1",
          "servicetype": serviceType,
          "paytype": "D",
          "payrefno": orderID,
          "is_mokafa50": "false",
          "IsAR": Constants.IsAr_App,
          "serialid": "",
          "jsonrslt": gatewayCode, //jsonrslt
        });
    print("result");
    print("=======================================");
    print('jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');

    return response;
  }

  prepareApplePay(
      code, unitPrice, qtys, subTotal, vat, totalAmount, serviceType) async {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
    var client = http.Client();
    const chars = '1234567890';
    var rnd = Random();
    var custData = custsData.read('custData');
    var device = "";
    if (Platform.isAndroid || Platform.isWindows) {
      device = "ANDROID";
    } else if (Platform.isIOS) {
      device = "IOS";
    }

    String getRandomString(int length) =>
        String.fromCharCodes(Iterable.generate(
            length, (_) => chars.codeUnitAt(rnd.nextInt(chars.length))));

    var newOrderID = getRandomString(10);
    print('newOrderID >>>> $newOrderID');

    // Create uuid object
    var newUuid = const Uuid();

    // Generate a v4 (random) id
    var depositTo = newUuid.v4(); // -> '110ec58a-a0f2-4ac4-8393-c866d813b8d1'
    var orderID = ""; //newOrderID;

    if (Platform.isAndroid || Platform.isWindows) {
      orderID = "A$newOrderID";
    } else if (Platform.isIOS) {
      orderID = "L$newOrderID";
    }
    print("orderID >>>> $orderID");
    print("depositTo >>>> $depositTo");

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.prepareMADA),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
        body: {
          "device": device,
          "custid": custData['CUSTID'],
          "depositto": depositTo,
          "amount": totalAmount ?? "1",
          "OrderTypes": "S",
          "topupamt": totalAmount ?? "1",
          "qty": qtys ?? "1",
          "servicetype": serviceType,
          "paytype": "D",
          "payrefno": orderID,
          "is_mokafa50": "false",
          "IsAR": Constants.IsAr_App,
          "serialid": "",
          "reqstr": "",
        });
    // BalanceTopUpModelNew result =
    //     BalanceTopUpModelNew.fromJson(jsonDecode(response.body));
    final jsonData = jsonDecode(response.body);
    print("result");
    print("=======================================");
    print(
        'orderID jsonDecode(response.body) >>>> ${jsonDecode(response.body)}');
    if (jsonData is Map && jsonData.containsKey('ExceptionMessage')) {
      Navigator.of(Get.context!).pop();
      print(
          'jsonDecode(response.body)["ExceptionMessage"] >>>> ${jsonDecode(response.body)["ExceptionMessage"]}');
      showDialog(
        barrierDismissible: false,
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(24),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  jsonDecode(response.body)["ExceptionMessage"],
                  style: pBold20,
                  textAlign: TextAlign.center,
                ),
                verticalSpace(24),
                CommonButton(
                  title: "OK".tr,
                  onPressed: () {
                    Get.back();
                  },
                  btnColor: AppColor.themeOrangeColor,
                )
              ],
            ),
          );
        },
      );
    } else {
      var result = jsonDecode(response.body)[0]["RES_BODY"];
      print("result===> ${jsonDecode(jsonEncode(result))}");
      final json = "[$result]";
      List resu = (jsonDecode(json) as List<dynamic>);
      print("resu===> $resu");

      for (int i = 0; i < resu.length; i++) {
        CheckoutSessionModel session =
            CheckoutSessionModel.fromJson(resu[i] as Map<String, dynamic>);
        checkoutSessionList.add(session);
      }
      var sess = checkoutSessionList.map((item) => item.session.id).toString();

      print(
          "checkoutSessionList===> ${jsonDecode(jsonEncode(checkoutSessionList))[0]["result"]}");
      print(
          "checkoutSessionList===> ${sess.replaceAll(RegExp(r"\p{P}", unicode: true), "")}");
      checkoutSession.value =
          sess.replaceAll(RegExp(r"\p{P}", unicode: true), "");

      print(checkoutSession.value);
      print("orderID ==== $orderID");
      print("depositTo ==== $depositTo");

      String basicAuth =
          'Basic ${base64Encode(utf8.encode('merchant.${ConstantVar.madaMerchantid}:${ConstantVar.madaAPIkey}'))}';
      var responses = await client.post(
          Uri.parse(
              "${ConstantVar.madaGateway}/api/rest/version/73/merchant/${ConstantVar.madaMerchantid}/session"),
          headers: <String, String>{
            'authorization': basicAuth,
            "Access-Control-Allow-Credentials": 'true',
            "Access-Control-Allow-Headers":
                "Origin,Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,locale",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS"
          });
      print("createSession==============================");
      print(jsonDecode(responses.body));
      final createSession = jsonDecode(responses.body);
      print(createSession['session']["id"]);
      print("createSession==============================");

      if (jsonDecode(jsonEncode(checkoutSessionList))[0]["result"] ==
          "SUCCESS") {
        Navigator.of(Get.context!).pop();
        Get.to(
          () => ApplePayScreen(
            unitPrice: unitPrice,
            totalPurchased: totalAmount,
            sessionID: createSession['session']["id"],
            orderID: orderID,
            depositto: depositTo,
            serviceType: serviceType,
            qty: qtys,
            orderType: "SERVICE",
            custid: custData['CUSTID'],
            subTotal: subTotal,
            vat: vat,
          ),
        );
      }
    }

    return checkoutSessionList;
  }

  Future<List<PriceTagCardModel>> fetchPriceTagCards() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var custid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);

    List<PriceTagCardModel> prices = [];

    var response = await client.post(
        Uri.parse(
            ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getPriceTagCard),
        body: {"custid": custid, "accttype": userData.auCust?.accttype});

    List result = jsonDecode(response.body);

    for (int i = 0; i < result.length; i++) {
      PriceTagCardModel price =
          PriceTagCardModel.fromMap(result[i] as Map<String, dynamic>);
      prices.add(price);
    }
    priceTagCards.value = prices;

    return priceTagCards;
  }

  @override
  void onInit() {
    super.onInit();
    fetchPriceTagCards();
  }

  void showErrorMsg(String msg) {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.all(16),
          contentPadding: const EdgeInsets.all(16),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("ERROR".tr, style: pBold20),
              verticalSpace(24),
              Text(
                // "Balance TopUp Failed.".tr,
                msg,
                style: pRegular16,
                textAlign: TextAlign.center,
              ),
              verticalSpace(24),
              TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text("OK"))
            ],
          ),
        );
      },
    );
  }

  validateSTCPAYSUBMIT(
    custID,
    userID,
    mobileNo,
    totalAmount,
    STCPayOtpReference,
    STCPayPmtReference,
    enteredOTP,
  ) async {
    Loader.showLoader();
    var client = http.Client();

    print("*****validateSTCPAYSUBMIT*****");
    print("custID ===============$custID");
    print("userID ===============$userID");
    print("mobileNo ===============$mobileNo");
    print("totalAmount ===============$totalAmount");
    print("STCPayOtpReference ===============$STCPayOtpReference");
    print("STCPayPmtReference ===============$STCPayPmtReference");
    print("enteredOTP ===============$enteredOTP");

    var response = await client.post(
        Uri.parse(ApiEndPoints.baseUrl +
            ApiEndPoints.authEndpoints.validateSTCPaySubmit),
        body: {
          "custID": custID,
          "userID": userID,
          "mobileNo": mobileNo,
          "totalAmount": totalAmount.toString(),
          "stcOTP": enteredOTP,
          "STCPayOtpReference": STCPayOtpReference,
          "STCPayPmtReference": STCPayPmtReference,
          "IsAr": Constants.IsAr_App,
        });
    final jsonData = response.body;
    print("result");
    print(jsonData);
    print("=======================================");
    print(
        'validateSTCPAYSUBMIT jsonDecode(response.body) >>>> ${response.body}');
    print("=======================================");
    print(jsonDecode(response.body)["MessageType"]);
    print(jsonDecode(response.body)["Message"]);
    if (jsonDecode(response.body)["Action"] == "SUCCESS") {
      Loader.hideLoader();
      print(jsonDecode(response.body)["Message"]);
      // await QuickAlert.show(
      //   context: Get.context!,
      //   type: QuickAlertType.success,
      //   text: jsonDecode(response.body)['Message'].toString(),
      // );
      // await Get.offAll(() => DashBoardManagerScreen(
      //       currantIndex: 0,
      //     ));
      showDialog(
        context: Get.context!,
        builder: (context) {
          return AlertDialog(
            contentPadding: const EdgeInsets.all(16),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            insetPadding: const EdgeInsets.all(25),
            content: stcPaySuccessWidget(
              code: jsonDecode(response.body)['ActionParam'].toString(),
              total: totalAmount.toString(),
            ),
          );
        },
      );
    } else {
      Loader.hideLoader();
      print(jsonDecode(response.body)["Message"]);
      await QuickAlert.show(
        context: Get.context!,
        type: QuickAlertType.error,
        text: jsonDecode(response.body)['Message'].toString(),
      );
    }
  }

  void showSTCPAYOTPDialog(
    custID,
    userID,
    mobileNo,
    totalAmount,
    STCPayOtpReference,
    STCPayPmtReference,
  ) {
    Get.defaultDialog(
      title: 'Enter OTP',
      content: TextFormField(
        controller: stcPayOTPText,
        maxLength: 4,
        keyboardType:
            const TextInputType.numberWithOptions(signed: true, decimal: true),
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        decoration: const InputDecoration(
          hintText: 'Enter 4-digit OTP',
        ),
      ),
      actions: <Widget>[
        // Cancel Button
        TextButton(
          onPressed: () {
            // Just close the dialog
            //  Loader.hideLoader();
            Get.back();
          },
          child: const Text('Cancel'),
        ),
        // Submit Button
        TextButton(
          onPressed: () {
            String enteredOTP = stcPayOTPText.text;
            if (enteredOTP.length == 4) {
              print('Entered OTP: $enteredOTP'); // Use the OTP here
              // You might want to validate the OTP here
              validateSTCPAYSUBMIT(
                custID,
                userID,
                mobileNo,
                totalAmount,
                STCPayOtpReference,
                STCPayPmtReference,
                enteredOTP,
              );
              // Optionally close the dialog if OTP is correct inside ValidatePRMOTP
            }
          },
          child: const Text('Submit'),
        ),
      ],
    );
  }

  Widget stcPaySuccessWidget({
    required String code,
    required String total,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Center(
            child: Text(
          "Success!".tr,
          style: pBold20,
        )),
        verticalSpace(14),
        Center(
            child: Text(
          "Your order has been successful!.".tr,
          style: pRegular13.copyWith(color: AppColor.cDarkGreyFont),
          textAlign: TextAlign.center,
        )),
        verticalSpace(8),
        Text.rich(
          TextSpan(
            text:
                'your order has been created' //, you can print the order details/invoice from your TOPUP history.'
                    .tr,
            style: pRegular13,
            children: <TextSpan>[
              TextSpan(
                  text: ". We keep the details of your order"
                      ' #$code '
                      " in Order History",
                  style: pBold12.copyWith(fontSize: 13)),
            ],
          ),
          textAlign: TextAlign.center,
        ),
        verticalSpace(16),
        successSTCPayWidget(
            Total: double.parse(total).toStringAsFixed(2)), // + " SAR".tr
        verticalSpace(16),
        verticalSpace(16),
        buttonRow(
          pdfFun: () {
            if (updateUi != null) {
              updateUi!();
            }
            Get.back();
          },
          dashboardFun: () {
            if (updateUi != null) {
              updateUi!();
            }
            // Get.offAll(DashBoardManagerScreen(
            //   currantIndex: 0,
            // ));
            Get.back();
            Get.back();
            Get.back();
            Get.back();
          },
        ),
      ],
    );
  }

  Widget successSTCPayWidget({String? Total}) {
    return Container(
      decoration: BoxDecoration(
          color: AppColor.cLightGrey, borderRadius: BorderRadius.circular(6)),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          totalsDataWidget(title: "Total".tr, value: Total!),
        ],
      ),
    );
  }

  Widget totalsDataWidget(
      {required String title,
      required String value,
      double? fontSize,
      Color? fontColor}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: pRegular13.copyWith(
              fontSize: fontSize ?? 13, color: fontColor ?? AppColor.cText),
        ),

        Directionality(
          textDirection: TextDirection.ltr,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              assetSvdImageWidget(
                image: DefaultImages.saudiRiyal,
                width: fontSize == 14
                    ? 13
                    : fontSize == 17
                        ? 16
                        : 13,
                height: fontSize == 14
                    ? 13
                    : fontSize == 17
                        ? 16
                        : 13,
              ),
              Gap(4),
              Text(
                value,
                // double.parse(value).toStringAsFixed(2),
                style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
              ),
            ],
          ),
        )
        // Text(
        //   value,
        //   // double.parse(value).toStringAsFixed(2),
        //   style: pSemiBold14.copyWith(fontSize: fontSize ?? 14),
        // ),
      ],
    );
  }
}
