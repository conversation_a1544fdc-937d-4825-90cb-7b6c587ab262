// ignore_for_file: prefer_const_constructors

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/listview_screen.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/graphview_screen.dart';
import 'package:waie_app/view/screen/vehicles_screen/tag_installation/tag_installation_screen.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/reports_controller.dart';

class ChangeReportScreen extends StatelessWidget {
  final String title;
  final ReportController reportController;

  const ChangeReportScreen({Key? key, required this.title, required this.reportController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        left: 16,
                        top: 15,
                        bottom: 15,
                      ),
                      color: AppColor.cBackGround,
                      child: assetSvdImageWidget(
                          image: DefaultImages.backIcn,
                          colorFilter: ColorFilter.mode(AppColor.cDarkBlueFont, BlendMode.srcIn)),
                    ),
                  ),
                  horizontalSpace(45),
                  Text(
                    title,
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            Obx(
              () {
                return Expanded(
                  child: ListView(
                    padding: EdgeInsets.only(top: 15, left: 16, right: 16, bottom: 16),
                    physics: BouncingScrollPhysics(),
                    shrinkWrap: true,
                    children: [
                      Row(
                        children: [
                          tabWidget(
                            title: 'List view'.trr,
                            onTap: () {
                              reportController.isListview.value = true;
                              reportController.isGraphview.value = false;
                            },
                            isSelected: reportController.isListview.value,
                          ),
                          tabWidget(
                            title: 'Graph view'.trr,
                            onTap: () {
                              reportController.isListview.value = false;
                              reportController.isGraphview.value = true;
                            },
                            isSelected: reportController.isGraphview.value,
                          ),
                        ],
                      ),
                      verticalSpace(24),
                      reportController.isListview.value == true ? ListViewScreen() : GraphViewScreen(),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
