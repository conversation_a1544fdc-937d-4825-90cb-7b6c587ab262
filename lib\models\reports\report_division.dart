import 'dart:convert';

class Report_Division {
  final String TYPECODE;
  final String TYPEDESC;

  Report_Division({
    required this.TYPECODE,
    required this.TYPEDESC,
  });

  Map<String, dynamic> toMap() {
    return {
      'TYPECODE': TYPECODE,
      'TYPEDESC': TYPEDESC,
    };
  }

  factory Report_Division.fromMap(Map<String, dynamic> map) {
    return Report_Division(
      TYPECODE: map['TYPECODE'] ?? '',
      TYPEDESC: map['TYPEDESC'] ?? '',
    );
  }
  String toJson() => json.encode(toMap());

  factory Report_Division.fromJson(String source) =>
      Report_Division.fromMap(json.decode(source));
}