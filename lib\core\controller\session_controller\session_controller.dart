import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../view/screen/login_manager/login_manager_with_email_screen.dart';

class SessionController extends GetxController with WidgetsBindingObserver {
  DateTime? _backgroundTime;

  @override
  void onInit() {
    WidgetsBinding.instance.addObserver(this);
    super.onInit();
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    log("session background time ${_backgroundTime.toString()}");
    if (state == AppLifecycleState.paused) {
      _backgroundTime = DateTime.now();
    } else if (state == AppLifecycleState.resumed) {
      if (_backgroundTime != null) {
        final difference = DateTime.now().difference(_backgroundTime!);
        if (difference.inMinutes > 5) {
          Get.offAll(() => const LoginManagerWithEmailScreen());

          WidgetsBinding.instance.addPostFrameCallback((_) {
            String titleText = Get.locale.toString() == 'ar_AR'
                ? "تم انهاء الجلسة"
                : 'Session Expired';

            String contentText = Get.locale.toString() == 'ar_AR'
                ? "يرجى تسجيل الدخول مرة أخرى"
                : 'Please log in again.';

            String okButtonText =
                Get.locale.toString() == 'ar_AR' ? "المتابعه" : 'OK';

            Get.dialog(
              AlertDialog(
                title: Text(titleText),
                content: Text(contentText),
                actions: [
                  TextButton(
                    onPressed: () {
                      _backgroundTime = null;
                      Get.back();
                    },
                    child: Text(okButtonText),
                  ),
                ],
              ),
              barrierDismissible: false,
            );
          });
        }
      }
    }
  }
}
