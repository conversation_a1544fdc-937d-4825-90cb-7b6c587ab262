import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:convert';
import 'dart:developer';

import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/donations.dart';
import 'package:waie_app/models/load_data.dart';
import 'package:http/http.dart' as http;
import 'package:waie_app/models/promotions.dart';
import 'package:waie_app/view/widget/loading_widget.dart';
import '../../../../utils/api_endpoints.dart';
import '../../../../utils/constants.dart';
import '../../../../utils/text_style.dart';
import '../../../../view/widget/common_space_divider_widget.dart';

class DonationController extends GetxController {
  RxBool isNewPromotion = true.obs;
  RxBool isHistory = false.obs;
  var donationList = <Donations>[].obs;

  TextEditingController PromoFieldValue = TextEditingController();
  TextEditingController tawuniyaTopupController = TextEditingController();
  TextEditingController walaTopupController = TextEditingController();
  TextEditingController stcTopupController = TextEditingController();
  TextEditingController yggCODETopupController = TextEditingController();
  TextEditingController yggPINTopupController = TextEditingController();

  List historyList = [
    {
      "title": "Tawuniya Promotions",
      "service": "Smart card",
      "activationCode": "B0BE20F410CC",
      "activationDate": "04.21.2023",
      "amount": '15',
    },
    {
      "title": "WALA Plus Promotions",
      "service": "Code",
      "activationCode": "F04N5H405FKG",
      "activationDate": "04.19.2023",
      "amount": '20',
    },
    {
      "title": "Tawuniya Promotions",
      "service": "Code",
      "activationCode": "GL30FIG931930",
      "activationDate": "03.03.2023",
      "amount": '10',
    },
  ];

  Future<List<Donations>> socialResponsibilityList() async {
    var client1 = http.Client();
    donationList.clear();
    try {
      var donationResponse = await client1.get(Uri.parse(
          ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getDonations));

      print("donationResponse==============${donationResponse.statusCode}");
      print(
          "responsedonationResponse===> ${jsonDecode(donationResponse.body)}");

      List donationResult = jsonDecode(donationResponse.body);

      for (int i = 0; i < donationResult.length; i++) {
        Donations loadData =
            Donations.fromMap(donationResult[i] as Map<String, dynamic>);
        donationList.add(loadData);
        print("promoResult ===============${loadData.IMG_LOC}");
      }

      return donationList;
    } catch (e) {
      log(e.toString());
      print('Failed to load data');
      throw Exception('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client1.close();
    }
  }
}
