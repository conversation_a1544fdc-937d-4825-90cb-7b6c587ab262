// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:waie_app/main.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constant.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

Widget simpleAppBar({
  required String title,
  required Function() onTap,
  required String backString,
  Color? backColor,
}) {
  String languageCode = myStorage!.getString(AppConstant.LANGUAGE_CODE) ?? 'ar';
  return Container(
    padding: EdgeInsets.only(right: 16),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          flex: 2,
          child: Align(
            alignment: Alignment.centerLeft,
            child: GestureDetector(
              onTap: onTap,
              child: Container(
                padding: EdgeInsets.only(
                  left: 16,
                  top: 15,
                  bottom: 15,
                ),
                color: AppColor.cBackGround,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    assetSvdImageWidget(
                        image: DefaultImages.backIcn,
                        colorFilter: ColorFilter.mode(
                            backColor ?? AppColor.cDarkBlueFont,
                            BlendMode.srcIn)),
                    horizontalSpace(10),
                    Text(
                      backString,
                      style: pRegular18.copyWith(
                          color: backColor ?? AppColor.cDarkBlueFont,
                          fontSize: 16.5),
                      textAlign: TextAlign.start,
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
        // horizontalSpace(35),
        Expanded(
          flex: 3,
          child: Container(
            // color: Colors.green,
            child: Align(
              // alignment: Alignment.center - Alignment(0.2, 0),
              alignment: languageCode == 'ar'
                  ? Alignment.centerRight
                  : Alignment.centerLeft,
              widthFactor: 100,
              child: Text(
                title,
                style: pBold20,
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

Widget simpleMyAppBar({
  required String title,
  required Function() onTap,
  required String backString,
  double? horizontalSize,
  Color? backColor,
}) {
  return Container(
    padding: EdgeInsets.only(right: 16),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.only(
              left: 16,
              top: 15,
              bottom: 15,
            ),
            //color: AppColor.cBackGround,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                assetSvdImageWidget(
                    image: DefaultImages.backIcn,
                    colorFilter: ColorFilter.mode(
                        backColor ?? AppColor.cDarkBlueFont, BlendMode.srcIn)),
                horizontalSpace(10),
                Text(
                  backString,
                  style: pRegular18.copyWith(
                      color: backColor ?? AppColor.cDarkBlueFont, fontSize: 17),
                  textAlign: TextAlign.start,
                )
              ],
            ),
          ),
        ),
        horizontalSpace(horizontalSize ?? 35),
        Text(
          title,
          style: pBold20,
          textAlign: TextAlign.center,
        ),
      ],
    ),
  );
}
