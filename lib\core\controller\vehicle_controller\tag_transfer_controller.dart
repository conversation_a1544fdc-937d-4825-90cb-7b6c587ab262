import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/models/tag_transfer_company_detail.dart';
import 'package:waie_app/models/tag_transfer_history.dart';
import 'package:waie_app/models/tag_transfer_vehicle_detail.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../utils/constants.dart';
import '../../../view/widget/loading_widget.dart';

class TagTransferController extends GetxController {
  RxList tagTransferHistoryLists = [].obs;
  VehicleController vehicleController = Get.put(VehicleController());
  TextEditingController companyIdController = TextEditingController();
  TextEditingController commentController = TextEditingController();
  TextEditingController searchController = TextEditingController();
  GetStorage userStorage = GetStorage('User');
  GetStorage custsData = GetStorage('custsData');
  final vehicle = GetStorage();
  final isPinblock = GetStorage();
  final isDCBlock = GetStorage();
  List searchList = ['Company', 'Date'];
  RxString searchValue = 'Company'.obs;
  RxList tagTransferDataList = [
    {
      "value": false.obs,
      "title": "2FK/012/25/5853849",
      "status": "Pending",
      "totalTag": '3',
      "companyName": "Microsoft",
      "date": "06.04.2023",
    }.obs,
    {
      "value": false.obs,
      "title": "2FK/012/25/5853849",
      "status": "Completed",
      "totalTag": '3',
      "companyName": "Microsoft",
      "date": "06.04.2023",
    }.obs,
  ].obs;
  List incomingTagDataList = [
    {
      "id": '1',
      "plat": "0797DDA",
      "make": 'Chevrolet',
      "model": 'Equinox',
    },
    {
      "id": '2',
      "plat": "1013ANA",
      "make": 'Chevrolet',
      "model": 'Express',
    },
    {
      "id": '3',
      "plat": "111ZBD",
      "make": 'Chevrolet',
      "model": 'Suburban',
    },
  ];

  Future<dynamic> fetchTagTransferHistory() async {
    var custid = userStorage.read('custid');
    var client = http.Client();

    try {
      print("PASOK1");
      var reasonResponse = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getLoadTransferHistory),
          body: {
            "custid": custid, //custid
            "top": "0",
            "skip": "0",
            "IsAR": Constants.IsAr_App,
          });
      print("PASOK2");
      List result = jsonDecode(reasonResponse.body);
      print("===============================================================");
      print("result >>>>> $result");
      print("reasonResponseBody >>>>> ${reasonResponse.body}");
      print("===============================================================");

      for (int i = 0; i < result.length; i++) {
        TagTransferHistoryModel vehicleInfo =
            TagTransferHistoryModel.fromMap(result[i] as Map<String, dynamic>);
        tagTransferHistoryLists.add(vehicleInfo);
      }

      return tagTransferHistoryLists;
    } catch (e) {
      log(e.toString());
      print("e.toString() >>>>> ${e.toString()}");
      print('Failed to load data');
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  tagTransferRequest(vehicleserialIDList) async {
    // showDialog(
    //     context: Get.context!,
    //     builder: (context) {
    //       return const Center(
    //         child: CircularProgressIndicator(),
    //       );
    //     });
    Loader.showLoader();
    print(
        "TagTransferController vehicleserialIDList>>>>>>> $vehicleserialIDList");

    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = custsData.read('custData');
    print("custData>>>>>>> $custData");
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");
    String custOthersList =
        "${companyIdController.text},${commentController.text}";
    print("===============================================================");
    print("custOthersList1 >>>>> $custOthersList");
    print("===============================================================");
    var client = http.Client();

    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.tagTransferProcessRequest),
          body: {
            "serialid": vehicleserialIDList,
            "emailid": custData['EMAILID'],
            "customerID": custData['CUSTID'], //custid
            "bankamt": custData['BANKAMT'],
            "bank": custData['BANK'],
            "bankrefno": custData['BANKREFNO'],
            "b2biban": custData['B2B_IBAN'],
            "others": custOthersList,
          });
      print("===============================================================");
      print("jsonDecode(response.body >>>>> ${jsonDecode(response.body)}");
      print("===============================================================");
      var result = jsonDecode(response.body);
      if (result == null || result == "") {
        Loader.hideLoader();
        clear();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.end,
                  //   children: [
                  //     GestureDetector(
                  //         onTap: () {
                  //           Get.back();
                  //         },
                  //         child: assetSvdImageWidget(
                  //             image: DefaultImages.cancelIcn)),
                  //   ],
                  // ),
                  // verticalSpace(24),
                  Text(
                    "Your tag transfer request has been successfully submitted"
                        .tr,
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  // verticalSpace(24),
                  // Text(
                  //   "Give us 48 hours to review your request. We’ll send you an email once it’s reviewed.".tr,
                  //   style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                  //   textAlign: TextAlign.center,
                  // ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      vehicle.remove('vehicleID');
                      vehicle.remove('vehicleSerialID');
                      vehicle.remove('vehicleSerialID');
                      vehicle.remove('complaintJobID');
                      isPinblock.remove('isPinActivate');
                      isDCBlock.remove('isdcBlock');
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.offAll(
                        () => DashBoardManagerScreen(
                          currantIndex: 0,
                        ),
                        //preventDuplicates: false,
                      );
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        Loader.hideLoader();
        clear();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.end,
                  //   children: [
                  //     GestureDetector(
                  //         onTap: () {
                  //           Get.back();
                  //         },
                  //         child: assetSvdImageWidget(
                  //             image: DefaultImages.cancelIcn)),
                  //   ],
                  // ),
                  // verticalSpace(24),
                  Text(
                    result,
                    style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center,
                  ),
                  // verticalSpace(24),
                  // Text(
                  //   "Give us 48 hours to review your request. We’ll send you an email once it’s reviewed.".tr,
                  //   style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                  //   textAlign: TextAlign.center,
                  // ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK".tr,
                    onPressed: () {
                      vehicle.remove('vehicleID');
                      vehicle.remove('vehicleSerialID');
                      vehicle.remove('vehicleSerialID');
                      vehicle.remove('complaintJobID');
                      isPinblock.remove('isPinActivate');
                      isDCBlock.remove('isdcBlock');
                      vehicleController.selectedSerialList.clear();
                      vehicleController.selectedVehicleList.clear();
                      vehicleController.selectedFleetList.clear();
                      vehicleController.filterValueList.refresh();
                      vehicleController.selectedVehicleList.refresh();
                      vehicleController.selectedSerialList.refresh();
                      vehicleController.selectedFleetList.refresh();
                      Get.offAll(
                        () => DashBoardManagerScreen(
                          currantIndex: 0,
                        ),
                        //preventDuplicates: false,
                      );
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  @override
  void onInit() {
    super.onInit();
    print('TagTransferController');
    if (tagTransferHistoryLists.isEmpty) {
      fetchTagTransferHistory();
    }
  }

  clear() {
    companyIdController.clear();
    commentController.clear();
  }
}
