// ignore_for_file: invalid_use_of_protected_member, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_structure_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';

import '../../../../utils/validator.dart';

class GroupProcessEditWidget extends StatefulWidget {
  String name;
  String typecode;
  String typeid;
  String typedesc;
  String parentid;
  GroupProcessEditWidget(
      {super.key,
      required this.name,
      required this.typecode,
      required this.typeid,
      required this.typedesc,
      required this.parentid});

  @override
  State<GroupProcessEditWidget> createState() => _GroupProcessEditWidgetState();
}

class _GroupProcessEditWidgetState extends State<GroupProcessEditWidget> {
  FleetStructureController fleetStructureController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fleetStructureController.editGroupProcessNameController.text =
        widget.typedesc;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Text(
                  "Edit ${widget.name}".trr,
                  style: pSemiBold17,
                ),
              ),
              verticalSpace(24),
              CommonTextField(
                controller:
                    fleetStructureController.editGroupProcessNameController,
                labelText: '${widget.name} Name'.trr,
                hintText: "Please select here".trr + "...",
                validator: (value) {
                  return Validator.validateName(
                      value, "${widget.name} name".trr);
                },
              ),
              verticalSpace(24),
              Row(
                children: [
                  Expanded(
                      child: CommonBorderButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      Get.back();
                    },
                    bColor: AppColor.themeDarkBlueColor,
                    textColor: AppColor.cDarkBlueFont,
                  )),
                  horizontalSpace(8),
                  Expanded(
                      child: CommonButton(
                    title: 'Confirm'.trr,
                    onPressed: () async {
                      if (_formKey.currentState!.validate()) {
                        if (widget.typeid == "CUSTDIVISION") {
                          fleetStructureController.divisionList.refresh();
                        } else if (widget.typeid == "CUSTBRANCH") {
                          fleetStructureController.branchList.refresh();
                        } else if (widget.typeid == "CUSTDEPT") {
                          fleetStructureController.departmentList.refresh();
                        } else {
                          fleetStructureController.operationList.refresh();
                        }
                        await fleetStructureController.groupingProcessEdit(
                          widget.typecode,
                          widget.typeid,
                          widget.typedesc,
                          widget.parentid,
                        );
                      }
                    },
                    btnColor: AppColor.themeOrangeColor,
                  ))
                ],
              ),
              verticalSpace(16),
            ],
          ),
        ),
      ),
    );
  }
}
