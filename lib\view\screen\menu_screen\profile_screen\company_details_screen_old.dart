// ignore_for_file: must_be_immutable, prefer_const_constructors, prefer_interpolation_to_compose_strings

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_drop_down_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/screen/auth/digital_coupon/create_dc_screen.dart';

import '../../../../core/controller/menu_controller/profile_controller/company_detail_controller.dart';

class CompanyDetailScreen extends StatelessWidget {
  CompanyDetailScreen({super.key});

  CompanyDetailController companyDetailController =
      Get.put(CompanyDetailController());
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: Obx(() {
            return Column(
              children: [
                simpleMyAppBar(
                    title: "Company details".trr,
                    onTap: () {
                      Get.back();
                    },
                    backString: "Back".trr,
                    horizontalSize: 45),
                Expanded(
                  child: Form(
                    key: formKey,
                    child: ListView(
                      shrinkWrap: true,
                      padding:
                          const EdgeInsets.only(top: 24, left: 16, right: 16),
                      children: [
                        buildDisplayRowWidget(
                            title: 'Company type'.trr, value: "Private"),
                        buildDisplayRowWidget(
                            title: 'Registration date'.trr, value: "11.03.2023"),
                        buildDisplayRowWidget(
                            title: 'User ID'.trr, value: "1-222-A23523"),
                        verticalSpace(32),
                        CommonTextField(
                          controller: companyDetailController.cpNameController,
                          labelText: 'Company Name'.trr + '*',
                          hintText: "Please enter here".trr,
                          validator: (value) {
                            return Validator.validateName(
                                value, "Company Name".trr);
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller:
                              companyDetailController.arabicCpNameController,
                          labelText: 'Arabic Company Name'.trr + '*',
                          hintText: "Please enter here".trr,
                          validator: (value) {
                            return Validator.validateName(
                                value, "Arabic Company Name".trr);
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller:
                              companyDetailController.designationController,
                          labelText: 'Designation'.trr + '*',
                          hintText: "Admin".trr,
                          validator: (value) {
                            return Validator.validateName(
                                value, "Designation".trr);
                          },
                        ),
                        // verticalSpace(16),
                        // CommonDropdownButtonWidget(
                        //     hint: '',
                        //     labelText: 'Expro'.trr,
                        //     list: companyDetailController.exproList,
                        //     value: companyDetailController
                        //         .selectedExproValue.value,
                        //     onChanged: (value) {
                        //       companyDetailController
                        //           .selectedExproValue.value = value;
                        //     },
                        //     validator: (value) => '',
                        //     fontColor: AppColor.cDarkGreyFont,
                        //     filledColor: AppColor.cFilled),
                        verticalSpace(16),
                        CommonHintDropdownWidget(
                            hint: 'Please select here'.trr + "...",
                            labelText: 'Sales contact'.trr,
                            list: companyDetailController.salesmanList,
                            value: companyDetailController
                                .selectedSalesmanValue.value,
                            onChanged: (value) {
                              companyDetailController
                                  .selectedSalesmanValue.value = value;
                            },
                            fontColor: AppColor.cDarkGreyFont,
                            filledColor: AppColor.cFilled),
                        verticalSpace(16),
                        CommonTextField(
                          controller:
                              companyDetailController.contactPersonController,
                          labelText: 'Contact person'.trr + '*',
                          hintText: "Please enter here".trr,
                          validator: (value) {
                            return Validator.validateName(
                                value, "Contact person".trr);
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller:
                              companyDetailController.cpPhoneNoController,
                          labelText: 'Company phone number'.trr + '*',
                          hintText: 'Enter phone number'.trr,
                          keyboardType: TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          maxLength: 10,
                          validator: (value) {
                            return Validator.validateMobile(value);
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller: companyDetailController.cpFaxController,
                          labelText: 'Company fax'.trr,
                          hintText: 'Enter company fax'.trr,
                          keyboardType: TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          validator: (value) {
                            return '';
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller: companyDetailController.regNoController,
                          labelText: 'Commercial Register No'.trr,
                          hintText: 'Enter commercial register no'.trr,
                          keyboardType: TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          validator: (value) {
                            return '';
                          },
                        ),
                        verticalSpace(16),
                        CommonTextField(
                          controller: companyDetailController.vatNoController,
                          labelText: 'VAT No'.trr,
                          hintText: 'Enter VAT no'.trr,
                          keyboardType: TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          validator: (value) {
                            return '';
                          },
                        ),
                        verticalSpace(16),
                      ],
                    ),
                  ),
                ),
              ],
            );
          }),
        ),
        bottomNavigationBar: Container(
          color: AppColor.cLightGrey,
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                  child: CommonButton(
                title: 'Cancel'.trr,
                onPressed: () {
                  Get.back();
                },
                textColor: AppColor.cText,
                btnColor: AppColor.cBackGround,
              )),
              horizontalSpace(16),
              Expanded(
                  child: CommonButton(
                title: 'Save'.trr,
                onPressed: () {
                  if (formKey.currentState!.validate()) {
                    Get.back();
                  }
                },
                textColor: AppColor.cWhiteFont,
                btnColor: AppColor.themeOrangeColor,
              )),
            ],
          ),
        ),
      ),
    );
  }
}
