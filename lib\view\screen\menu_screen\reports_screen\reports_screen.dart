// ignore_for_file: must_be_immutable, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/reports_controller/reports_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/customer_statment_screen.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/fleet_wise_screen.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/fuel_consumption_screen.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/monthly_quota_screen.dart';
import 'package:waie_app/view/screen/menu_screen/reports_screen/station_list_screen.dart';
import 'package:waie_app/view/widget/common_appbar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import 'change_report_screen.dart';
import 'monthly_trans_statment_screen.dart';

class ReportsScreen extends StatelessWidget {
  ReportsScreen({super.key});
  ReportController reportController = Get.put((ReportController()));

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.cBackGround,
      body: SafeArea(
        child: Column(
          children: [
            simpleAppBar(
                title: "Reports".trr,
                onTap: () {
                  Get.back();
                },
                // backString: "Menu".trr),
                backString: "Back".trr),
            Expanded(
              child: ListView(
                padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                children: [
                  //  if(Constants.custAcctType=='D' ||(Constants.custAcctType=='C' && Constants.custRegType!='I'))
                  if (Constants.ReportMnthlyTrStmnt == "Y") Gap(15),
                  if (Constants.ReportMnthlyTrStmnt == "Y")
                    GestureDetector(
                      onTap: () {
                        Get.to(MonthlyTransStatmentScreen(
                          title: "Monthly Transaction Statement".trr,
                        ));
                      },
                      child:
                          reportWidget(title: "Monthly Transaction Statement".trr),
                    ),
                  //  if(Constants.custAcctType=='D' ||(Constants.custAcctType=='C' && Constants.custRegType!='I'))
                  if (Constants.ReportFuelCmsp == "Y") Gap(15),
                  if (Constants.ReportFuelCmsp == "Y")
                    GestureDetector(
                      onTap: () {
                        Get.to(FuelConsumptionScreen(
                          title: "Fuel Consumption By Customer".trr,
                        ));
                      },
                      child:
                          reportWidget(title: "Fuel Consumption By Customer".trr),
                    ),
                  // if(Constants.custAcctType=='D' ||(Constants.custAcctType=='C' && Constants.custRegType!='I'))
                  if (Constants.ReportFleetStmnt == "Y") Gap(15),
                  // if(Constants.custAcctType=='D' ||(Constants.custAcctType=='C' && Constants.custRegType!='I'))
                  if (Constants.ReportFleetStmnt == "Y")
                    GestureDetector(
                      onTap: () {
                        Get.to(FleetWiseScreen(
                          title: "Fleet Wise Fuel Usage".trr,
                        ));
                      },
                      child: reportWidget(title: "Fleet Wise Fuel Usage".trr),
                    ),
                  if (Constants.ReportSTNList == "Y") Gap(15),
                  //  if(Constants.custAcctType=='D' ||(Constants.custAcctType=='C' && Constants.custRegType=='I'))
                  if (Constants.ReportSTNList == "Y")
                    GestureDetector(
                      onTap: () {
                        Get.to(StationListScreen(
                          title: "Station Info List".trr,
                        ));
                      },
                      child: reportWidget(title: "Station Info List".trr),
                    ),
                  if (Constants.ReportCustomerStmnt == "Y") Gap(15),
                  if (Constants.ReportCustomerStmnt == "Y")
                    GestureDetector(
                      onTap: () {
                        Get.to(CustomerStatmentScreen(
                          title: "Customer Statement".trr,
                        ));
                      },
                      child: reportWidget(title: "Customer Statement".trr),
                    ),
                  //  if(Constants.custAcctType=='D' ||(Constants.custAcctType=='C' && Constants.custRegType!='I'))
                  if (Constants.ReportMnthlyQtaSum == "Y") Gap(15),
                  if (Constants.ReportMnthlyQtaSum == "Y")
                    //  if(Constants.custAcctType=='D' ||(Constants.custAcctType=='C' && Constants.custRegType!='I'))
                    GestureDetector(
                      onTap: () {
                        Get.to(MonthlyQuotaScreen(
                          title: "Monthly Quota Variance Summary".trr,
                        ));
                      },
                      child:
                          reportWidget(title: "Monthly Quota Variance Summary".trr),
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget subscriptionWidget() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        decoration: BoxDecoration(
          color: AppColor.cLightGrey,
          borderRadius: BorderRadius.circular(6),
        ),
        padding: EdgeInsets.symmetric(vertical: 40, horizontal: 16),
        child: Column(
          children: [
            assetSvdImageWidget(image: DefaultImages.lightReportIcn),
            verticalSpace(10),
            Text(
              "Lorem Ipsum Report".trr,
              style: pBold20.copyWith(color: AppColor.cText.withOpacity(0.5)),
            ),
            // verticalSpace(16),
            // Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 35),
            //   child: Text.rich(
            //     TextSpan(
            //       text: "To revisit when the content is ready".trr,
            //       style: pRegular14,
            //       children: [
            //         // TextSpan(
            //         //   text: 'Learn more',
            //         //   style: pBold16.copyWith(
            //         //       color: AppColor.cDarkBlueFont, fontFamily: 'Roboto'),
            //         // )
            //       ],
            //     ),
            //     textAlign: TextAlign.center,
            //   ),
            // )
          ],
        ),
      ),
    );
  }

  /*Widget reportWidget({required String title}) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Container(
          decoration: BoxDecoration(
            color: AppColor.cLightGrey,
            borderRadius: BorderRadius.circular(6),
          ),
          padding: EdgeInsets.symmetric(vertical: 40, horizontal: 16),
          child: Column(
            children: [
              assetSvdImageWidget(image: DefaultImages.reportIcn),
              verticalSpace(10),
              Text(
                title,
                style: pBold20,
              )
            ],
          ),
        ),
      ),
    );
  }*/
  Widget reportWidget({required String title}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        decoration: BoxDecoration(
          color: AppColor.cLightGrey,
          borderRadius: BorderRadius.circular(6),
        ),
        padding: EdgeInsets.symmetric(vertical: 40, horizontal: 16),
        child: Column(
          children: [
            assetSvdImageWidget(image: DefaultImages.reportIcn),
            verticalSpace(10),
            Text(
              title,
              style: pBold20,
            )
          ],
        ),
      ),
    );
  }
}
