buildscript {
   ext.kotlin_version = '1.9.10'
   // ext.kotlin_version = '1.2.10'
    repositories {
       /* google()
        mavenCentral()*/
        google()
        jcenter()
        mavenCentral()
        //maven { url "https://jitpack.io" }
        //maven()
    }

    dependencies {
        //classpath 'com.android.tools.build:gradle:7.0.3'
        //classpath 'com.android.tools.build:gradle:7.0.0'
        //classpath 'com.android.tools.build:gradle:4.1.0'
        //classpath 'com.android.tools.build:gradle:6.7.1'
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
       /* google()
        mavenCentral()*/
        //maven()
        google()
        jcenter()
        mavenCentral()
        //maven { url "https://jitpack.io" }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
