import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/auth/auth_controller.dart';
import 'package:waie_app/core/controller/signup_controller/otp_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/auth/auth_background.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:pinput/pinput.dart';

class NewOTPScreen extends StatefulWidget {
  const NewOTPScreen({super.key});

  @override
  State<NewOTPScreen> createState() => _NewOTPScreenState();
}

class _NewOTPScreenState extends State<NewOTPScreen> {
  AuthController authController = Get.put(AuthController());
  OTPController otpController = Get.put(OTPController());
  final pinController = TextEditingController();
  final focusNode = FocusNode();
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    pinController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const focusedBorderColor = Color.fromRGBO(0, 157, 223, 1);
    const fillColor = Color.fromRGBO(243, 246, 249, 0);
    const borderColor = Color.fromRGBO(1, 93, 133, 1);

    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: const TextStyle(
        fontSize: 22,
        color: Color.fromRGBO(30, 60, 87, 1),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(19),
        border: Border.all(color: borderColor),
      ),
    );

    /// Optionally you can use form to validate the Pinput
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        backgroundColor: AppColor.cBackGround,
        body: SafeArea(
          child: AuthBackGroundWidget(
            widget: ListView(
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              children: [
                verticalSpace(Get.height * 0.08),
                Text(
                  "Confirm your phone number".trr,
                  style: pBold28,
                  textAlign: TextAlign.center,
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 25),
                  child: Text(
                    "A four-digit OTP is sent to Phone Number Please enter it below."
                        .trr
                        .trr,
                    style: pRegular17,
                    textAlign: TextAlign.center,
                  ),
                ),
                Form(
                  key: formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      verticalSpace(24),
                      Directionality(
                        // Specify direction if desired
                        textDirection: TextDirection.ltr,
                        child: Pinput(
                          controller: pinController,
                          focusNode: focusNode,
                          androidSmsAutofillMethod:
                              AndroidSmsAutofillMethod.smsUserConsentApi,
                          listenForMultipleSmsOnAndroid: true,
                          defaultPinTheme: defaultPinTheme,
                          separatorBuilder: (index) => const SizedBox(width: 8),
                          // validator: (value) {
                          //   return value == '2222' ? null : 'Pin is incorrect';
                          // },
                          // onClipboardFound: (value) {
                          //   debugPrint('onClipboardFound: $value');
                          //   pinController.setText(value);
                          // },
                          hapticFeedbackType: HapticFeedbackType.lightImpact,
                          onCompleted: (pin) {
                            debugPrint('onCompleted: $pin');
                          },
                          onChanged: (value) {
                            debugPrint('onChanged: $value');
                          },
                          cursor: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Container(
                                margin: const EdgeInsets.only(bottom: 9),
                                width: 22,
                                height: 1,
                                color: focusedBorderColor,
                              ),
                            ],
                          ),
                          focusedPinTheme: defaultPinTheme.copyWith(
                            decoration: defaultPinTheme.decoration!.copyWith(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: focusedBorderColor),
                            ),
                          ),
                          submittedPinTheme: defaultPinTheme.copyWith(
                            decoration: defaultPinTheme.decoration!.copyWith(
                              color: fillColor,
                              borderRadius: BorderRadius.circular(19),
                              border: Border.all(color: focusedBorderColor),
                            ),
                          ),
                          // errorPinTheme: defaultPinTheme.copyBorderWith(
                          //   border: Border.all(color: Colors.redAccent),
                          // ),
                        ),
                      ),
                      verticalSpace(16),
                      CommonButton(
                        title: 'Login'.trr,
                        onPressed: () {
                          print(pinController.value.text);

                          authController.verificationCode.value =
                              pinController.value.text;

                          if (pinController.value == '') {
                            commonToast("Enter OTP".trr);
                          } else {
                            //  otpController.sample(code);
                            authController.OTPValidate();
                          }
                          focusNode.unfocus();
                          formKey.currentState!.validate();
                        },
                      ),
                    ],
                  ),
                ),
                verticalSpace(20),
                Text.rich(
                  TextSpan(
                    text: "${'Didn`t get the code?'.trr} ",
                    style: pRegular14,
                    children: <TextSpan>[
                      TextSpan(
                        text: 'Resend code again'.trr,
                        style: pBold14.copyWith(color: AppColor.cBlueFont),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            authController.resetMobileOTP();
                          },
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
