// ignore_for_file: prefer_const_constructors, must_be_immutable
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/bank_list_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/submit_order_refund_service_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/utils/validator.dart';
import 'package:waie_app/view/widget/common_snak_bar_widget.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';
import 'package:file_picker/file_picker.dart';

import '../../../../core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import '../../../widget/common_button.dart';
import '../../../widget/common_drop_down_widget.dart';
import '../company_affiliates_screen/new_affiliate_screen.dart';

class BankAcDetailWidget extends StatefulWidget {
  bool isDebit;
  BankAcDetailWidget({super.key, required this.isDebit});

  @override
  State<BankAcDetailWidget> createState() => _BankAcDetailWidgetState();
}

class _BankAcDetailWidgetState extends State<BankAcDetailWidget> {
  FilePickerResult? companyRegistration;
  FilePickerResult? bankLetter;

  String errorString = '';
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  BankListController bankListController = Get.put(BankListController());
  SubmitOrderRefundServiceController submitOrderRefundServiceController =
      Get.put(SubmitOrderRefundServiceController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Enter your bank account details".trr,
              style: pBold20,
            ),
            verticalSpace(24),
            CommonTextField(
              controller:
                  submitOrderRefundServiceController.accountHolderController,
              labelText: "Name of account holder".trr,
              hintText: "Enter a name".trr,
              validator: (value) {
                return Validator.validateRequired(value);
              },
            ),
            verticalSpace(16),
            Text(
              "Company registration document".trr,
              style: pRegular12,
            ),
            verticalSpace(6),
            GestureDetector(
              child: Container(
                height: 44,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: AppColor.cBorder)),
                padding: EdgeInsets.symmetric(
                  horizontal: 6,
                ),
                child: Row(
                  children: [
                    Container(
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: AppColor.lightBlueColor,
                      ),
                      padding: EdgeInsets.only(right: 12, left: 8),
                      child: Row(
                        children: [
                          assetSvdImageWidget(
                            image: DefaultImages.fileIcn,
                          ),
                          horizontalSpace(8),
                          Text(
                            "Choose file".trr,
                            style: pRegular14,
                          ),
                        ],
                      ),
                    ),
                    horizontalSpace(8),
                    Expanded(
                      child: Text(
                        submitOrderRefundServiceController.companyReg.isEmpty
                            ? "No file chosen".trr
                            : submitOrderRefundServiceController
                                .companyReg.value
                                .split("/")
                                .last,
                        style: pRegular14.copyWith(
                          color: AppColor.cDarkGreyFont,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              onTap: () async {
                //refundsController.companyReg.value =
                //await refundsController.pickImage(ImageSource.gallery);
                companyRegistration = await FilePicker.platform.pickFiles(
                  type: FileType.custom,
                  allowedExtensions: ['jpg', 'pdf', 'doc'],
                  withReadStream: true,
                );
                if (companyRegistration == null) {
                  print("No file selected");
                } else {
                  setState(() {
                    for (var element in companyRegistration!.files) {
                      print(element.name);
                         log("company register letter ${element.name}");
                      submitOrderRefundServiceController.companyReg.value =
                          element.name;
                    }
                  });
                }
              },
            ),
            verticalSpace(16),
            Text(
              "Bank letter".trr,
              style: pRegular12,
            ),
            verticalSpace(6),
            GestureDetector(
              child: Container(
                height: 44,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: AppColor.cBorder)),
                padding: EdgeInsets.symmetric(
                  horizontal: 6,
                ),
                child: Row(
                  children: [
                    Container(
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: AppColor.lightBlueColor,
                      ),
                      padding: EdgeInsets.only(right: 12, left: 8),
                      child: Row(
                        children: [
                          assetSvdImageWidget(
                            image: DefaultImages.fileIcn,
                          ),
                          horizontalSpace(8),
                          Text(
                            "Choose file".trr,
                            style: pRegular14,
                          ),
                        ],
                      ),
                    ),
                    horizontalSpace(8),
                    Expanded(
                      child: Text(
                        submitOrderRefundServiceController.bankLetter.isEmpty
                            ? "No file chosen".trr
                            : submitOrderRefundServiceController
                                .bankLetter.value
                                .split("/")
                                .last,
                        style: pRegular14.copyWith(
                          color: AppColor.cDarkGreyFont,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  ],
                ),
              ),
              onTap: () async {
                bankLetter = await FilePicker.platform.pickFiles(
                  type: FileType.custom,
                  allowedExtensions: ['jpg', 'pdf', 'doc'],
                  withReadStream: true,
                );
                if (bankLetter == null) {
                  print("No file selected");
                } else {
                  setState(() {
                    for (var element in bankLetter!.files) {
                      log("bank letter ${element.name}");
                      submitOrderRefundServiceController.bankLetter.value =
                          element.name;
                    }
                  });
                }
              },
            ),
            verticalSpace(16),
            // CommonDropdownButtonWidget(
            //   labelText: "Select Bank".trr,
            //   list: refundsController.selectBankList,
            //   value: refundsController.selectedBank.value,
            //   onChanged: (value) {
            //     refundsController.selectedBank.value = value;
            //   },
            //   isExpanded: true,
            // ),
            Text(
              '${"Select Bank ".trr}*',
              style: pRegular13,
            ),
            verticalSpace(10),
            // CommonHintDropdownWidget(
            DropdownButtonFormField(
              items: bankListController.bankLists.map((data) {
                return DropdownMenuItem(
                  value: data.typecode,
                  child: Text(
                    data.typedesc,
                    style: pMedium12,
                    textAlign: TextAlign.center,
                  ),
                );
              }).toList(),
              /*value: bankListController
                                  .bankController.value,*/
              onChanged: (value) {
                bankListController.bankController.text = value.toString();
                print(
                    "bankListController.value +++++++++++++++ ${bankListController.bankController.text}");
                submitOrderRefundServiceController.bankController.text =
                    value.toString();
                print(
                    "submitOrderRefundServiceController.bankController.text +++++++++++++++ ${submitOrderRefundServiceController.bankController.text}");
              },
              validator: (value) {
                setState(() {
                  if (value == null) {
                    errorString = 'Please select bank'.trr;
                  } else {
                    errorString = '';
                  }
                });
                return null;
              },
              style: pRegular14.copyWith(color: AppColor.cLabel),
              borderRadius: BorderRadius.circular(6),
              dropdownColor: AppColor.cLightGrey,
              icon: assetSvdImageWidget(image: DefaultImages.dropDownIcn),
              decoration: InputDecoration(
                hintText: 'Banks'.trr,
                hintStyle: pRegular14.copyWith(color: AppColor.cHintFont),
                contentPadding: EdgeInsets.only(left: 16, right: 16),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(
                    color: AppColor.cBorder,
                  ),
                ),
              ),
            ),
            errorString == ''
                ? SizedBox()
                : Text(
                    errorString,
                    style: pRegular12.copyWith(color: AppColor.cRedText),
                  ),
            verticalSpace(16),
            CommonDropdownButtonWidget(
              labelText: "IBAN number".trr,
              list: submitOrderRefundServiceController.ibanList,
              value: submitOrderRefundServiceController.selectedIBAN.value,
              onChanged: (value) {
                submitOrderRefundServiceController.selectedIBAN.value = value;
                print(
                    "submitOrderRefundServiceController.selectedIBAN.value +++++++++++++++ ${submitOrderRefundServiceController.selectedIBAN.value}");
              },
              isExpanded: true,
            ),
            verticalSpace(16),
            CommonTextField(
              controller: submitOrderRefundServiceController.iBanNoController,
              labelText: '',
              hintText: '',
              validator: (value) {
                return Validator.validateRequired(value);
              },
            ),
            verticalSpace(16),
            CommonTextField(
              controller:
                  submitOrderRefundServiceController.reasonRefundController,
              labelText: "Reason for refund".trr,
              hintText: "Please tell us why you've refunded these orders".trr,
              maxLines: 5,
              validator: (value) {
                return Validator.validateRequired(value);
              },
            ),
            // verticalSpace(24),
            // Container(
            //   padding: EdgeInsets.symmetric(vertical: 7, horizontal: 16),
            //   decoration: BoxDecoration(
            //       color: AppColor.lightOrangeColor,
            //       borderRadius: BorderRadius.circular(6)),
            //   child: Text(
            //       "!  ${"Refunded orders don't count towards your bulk discount on your next order.".trr}",
            //       style: pRegular13.copyWith(color: AppColor.cDarkOrangeText)),
            // ),
            verticalSpace(24),
            CommonButton(
              title: "Submit".trr,
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  if (widget.isDebit == true) {
                    submitOrderRefundServiceController.rblRefundOPT.value = "Q";
                  }
                  if (bankLetter == null) {
                    commonToast('Please Upload Bank Letter');
                  } else if (companyRegistration == null) {
                    commonToast('Please Upload Company Registration');
                  } else {
                    PlatformFile bankletter = bankLetter!.files.first;
                    PlatformFile companyregistration =
                        companyRegistration!.files.first;
                    // var bankletter = bankLetter;
                    // var companyregistration = companyRegistration;
                    submitOrderRefundServiceController.submitOrderRefundFormQ(
                        bankletter, companyregistration);
                  }
                }
                // showDialog(
                //   context: context,
                //   builder: (context) {
                //     return AlertDialog(
                //       shape: RoundedRectangleBorder(
                //           borderRadius: BorderRadius.circular(12)),
                //       contentPadding: EdgeInsets.all(24),
                //       content: successDialogWidget(
                //           title: "Your refund request".trr, () {
                //         Get.back();
                //       }, isBorderBtn: true),
                //     );
                //   },
                // );
              },
              btnColor: AppColor.themeOrangeColor,
            ),
            verticalSpace(24),
          ],
        ),
      );
    });
  }
}
