// ignore_for_file: prefer_const_constructors

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_card_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_current_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/order_refund_reserved_controller.dart';
import 'package:waie_app/core/controller/menu_controller/refunds_controller/refunds_controller.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/company_affiliates_screen/request_history_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import '../user_management_screen/user_management_screen.dart';
import 'no_refund_found_screen.dart';

class OrderRefundReservedScreen extends StatefulWidget {
  const OrderRefundReservedScreen({super.key});

  @override
  State<OrderRefundReservedScreen> createState() =>
      _OrderRefundReservedScreenState();
}

class _OrderRefundReservedScreenState extends State<OrderRefundReservedScreen> {
  @override
  void initState() {
    super.initState();

    for (var item in orderRefundReservedController.refundableReservedTopups) {
      item.isvalue = refundsController.selectedReservedList.contains(item);
    }
  }

  final topupOrderRefund = GetStorage();

  RefundsController refundsController = Get.find();

//added by fuzail 05-15-2025
  final tagOrderRefund = GetStorage();

  OrderRefundReservedController orderRefundReservedController =
      Get.put(OrderRefundReservedController());

  @override
  Widget build(BuildContext context) {
    print("OLD CHKRES ${topupOrderRefund.read('chkRES')}");
    topupOrderRefund.remove('chkRES');
    topupOrderRefund.write('chkRES', "true");
    print("NEW CHKRES ${topupOrderRefund.read('chkRES')}");

    print("OLD STYPE ${tagOrderRefund.read('orderType')}");
    tagOrderRefund.remove('orderType');
    tagOrderRefund.write('orderType', "T");
    print("NEW STYPE ${tagOrderRefund.read('orderType')}");

    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () => orderRefundReservedController
                      .refundableReservedTopups.isEmpty
                  ? NoRefundFoundScreen()
                  : ListView.builder(
                      itemCount: orderRefundReservedController
                          .refundableReservedTopups.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var data = orderRefundReservedController
                            .refundableReservedTopups[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              bool newValue = !(data.isvalue ?? false);

                              setState(() {
                                data.isvalue = newValue;
                              });

                              if (newValue == true) {
                                // added by fuzail 5-15-2025
                                refundsController.selectedReservedList
                                    .add(data);
                                refundsController.selectedTopupList
                                    .clear(); // clear topup list from reserved card selection

                                refundsController.selectedOrderRefundList
                                    .add(data);
                                refundsController
                                    .selectedOrderRefundReservedOrderIDList
                                    .add(data.orderid);

                                orderRefundReservedController.topupTotaltAmt +=
                                    data.addltopup;
                                orderRefundReservedController.topupTotaltVAT +=
                                    data.vat;

                                print(
                                    "orderRefundReservedController.topupTotaltAmt ${orderRefundReservedController.topupTotaltAmt.toStringAsFixed(2)}");
                                print(
                                    "orderRefundReservedController.topupTotaltVAT ${orderRefundReservedController.topupTotaltVAT.toStringAsFixed(2)}");
                              } else {
                                refundsController.selectedReservedList
                                    .remove(data);
                                refundsController.selectedTopupList.clear();

                                refundsController.selectedOrderRefundList
                                    .removeAt(index);
                                refundsController
                                    .selectedOrderRefundReservedOrderIDList
                                    .removeAt(index);

                                orderRefundReservedController.topupTotaltAmt -=
                                    data.addltopup;
                                orderRefundReservedController.topupTotaltVAT -=
                                    data.vat;

                                print(
                                    "orderRefundReservedController.topupTotaltAmt ${orderRefundReservedController.topupTotaltAmt.toStringAsFixed(2)}");
                                print(
                                    "orderRefundReservedController.topupTotaltVAT ${orderRefundReservedController.topupTotaltVAT.toStringAsFixed(2)}");
                              }

                              refundsController.selectedOrderRefundList
                                  .refresh();
                              refundsController
                                  .selectedOrderRefundReservedOrderIDList
                                  .refresh();

                              print("*************************************");
                              print(
                                  "refundsController.selectedOrderRefundCurrentOrderIDList.length >>>> ${refundsController.selectedOrderRefundCurrentOrderIDList.length}");
                              print("*************************************");

                              String orderRefundReservedOrderIDList =
                                  refundsController
                                      .selectedOrderRefundReservedOrderIDList
                                      .join(",");

                              print("*************************************");
                              print(
                                  "orderRefundReservedOrderIDList >>>> $orderRefundReservedOrderIDList");

                              tagOrderRefund.write('orderRefundTagSerialIDList',
                                  orderRefundReservedOrderIDList);

                              topupOrderRefund.write(
                                  'tAmt',
                                  orderRefundReservedController.topupTotaltAmt
                                      .toStringAsFixed(2));
                              topupOrderRefund.write(
                                  'tVAT',
                                  orderRefundReservedController.topupTotaltVAT
                                      .toStringAsFixed(2));

                              print("*************************************");
                              log("tagOrderRefund.write('orderRefundTagSerialIDList' >>>> ${tagOrderRefund.read('orderRefundTagSerialIDList')}");
                              print(
                                  "topupOrderRefund.write('tAmt' >>>> ${topupOrderRefund.read('tAmt')}");
                              print(
                                  "topupOrderRefund.write('tVAT' >>>> ${topupOrderRefund.read('tVAT')}");
                            },
                            child: refundCurrentTopupDataWidget(
                              isShowCheckBox: true,
                              onChanged:
                                  null, // disable direct checkbox tap handling
                              value: data.isvalue,
                              code: data.orderid,
                              invNo: data.invno,
                              orderDate: data.orderdate,
                              paidDate: data.paidon,
                              amount: data.addltopup.toStringAsFixed(2),
                              vat: data.vat.toStringAsFixed(2),
                            ),
                          ),
                        );

//commented code original read if any problem
//                         return Padding(
//                           padding: const EdgeInsets.only(bottom: 8.0),
//                           child: refundCurrentTopupDataWidget(
//                             isShowCheckBox: true,
//                             onChanged: (value) {
//                               setState(() {
//                                 data.isvalue = value ?? false;
//                                 print("data.isvalue ${data.isvalue}");
//                                 print("value $value");
//                               });
//                               if (value == true) {
//                                 //added by fuzail 5-15-2025
//                                 refundsController.selectedReservedList
//                                     .add(data); // Added by fuzail
//                                 refundsController.selectedTopupList
//                                     .clear(); // clear the tags list from smart card selection

//                                 // refundsController.selectedOrderRefundCardList
//                                 //     .add(data); // Added by fuzail
//                                 // refundsController.selectedOrderRefundTagList
//                                 //     .clear(); // clear the topups list from reserved selection

//                                 // //////////////////
//                                 // ///

//                                 ////////////////
//                                 // Added by fuzail 5-13-2025

//                                 // if (!refundsController.selectedReservedList
//                                 //     .contains(data)) {
//                                 //   refundsController.selectedReservedList
//                                 //       .add(data);
//                                 // }

//                                 // // Clear topup state (checkboxes and selection)
//                                 // for (var item
//                                 //     in Get.find<OrderRefundCurrentController>()
//                                 //         .refundableCurrentTopups) {
//                                 //   item.isvalue = false;
//                                 // }
//                                 // refundsController.selectedTopupList.clear();

//                                 // ended by fuzail 5-13-2025

//                                 refundsController.selectedOrderRefundList
//                                     .add(data);
//                                 refundsController
//                                     .selectedOrderRefundReservedOrderIDList
//                                     .add(data.orderid);

//                                 orderRefundReservedController.topupTotaltAmt +=
//                                     data.addltopup;
//                                 orderRefundReservedController.topupTotaltVAT +=
//                                     data.vat;
//                                 print(
//                                     "orderRefundReservedController.topupTotaltAmt ${orderRefundReservedController.topupTotaltAmt.toStringAsFixed(2)}");
//                                 print(
//                                     "orderRefundReservedController.topupTotaltAmt ADD ${orderRefundReservedController.topupTotaltAmt.toStringAsFixed(2)}");
//                                 print(
//                                     "orderRefundReservedController.topupTotaltVAT ${orderRefundReservedController.topupTotaltVAT.toStringAsFixed(2)}");
//                                 print(
//                                     "orderRefundReservedController.topupTotaltVAT ADD ${orderRefundReservedController.topupTotaltVAT.toStringAsFixed(2)}");
//                               } else {
//                                 //added by fuzail 5-15-2025
//                                 refundsController.selectedReservedList
//                                     .remove(data); // added by fuzail
//                                 refundsController.selectedTopupList
//                                     .clear(); // clear the topup list from reserved card selection

//                                 // refundsController.selectedOrderRefundCardList
//                                 //     .remove(data); // added by fuzail
//                                 // refundsController.selectedOrderRefundTagList
//                                 //     .clear(); // clear the tags list from smart card selection
//                                 // refundsController.selectedOrderRefundCardList
//                                 //     .remove(data); // added by fuzail
//                                 // refundsController.selectedOrderRefundTagList
//                                 //     .clear(); // clear the topups list from reserved selection
//                                 //////////////////
//                                 ///
//                                 //added by fuzail 5-13-2025

//                                 // refundsController.selectedReservedList
//                                 //     .remove(data);
//                                 //ended by fuzail 5-13-2025

//                                 refundsController.selectedOrderRefundList
//                                     .removeAt(index);
//                                 refundsController
//                                     .selectedOrderRefundReservedOrderIDList
//                                     .removeAt(index);

//                                 orderRefundReservedController.topupTotaltAmt -=
//                                     data.addltopup;
//                                 orderRefundReservedController.topupTotaltVAT -=
//                                     data.vat;
//                                 print(
//                                     "orderRefundReservedController.topupTotaltAmt ${orderRefundReservedController.topupTotaltAmt.toStringAsFixed(2)}");
//                                 print(
//                                     "orderRefundReservedController.topupTotaltAmt MINUS ${orderRefundReservedController.topupTotaltAmt.toStringAsFixed(2)}");
//                                 print(
//                                     "orderRefundReservedController.topupTotaltVAT ${orderRefundReservedController.topupTotaltVAT.toStringAsFixed(2)}");
//                                 print(
//                                     "orderRefundReservedController.topupTotaltVAT MINUS ${orderRefundReservedController.topupTotaltVAT.toStringAsFixed(2)}");
//                               }
//                               refundsController.selectedOrderRefundList
//                                   .refresh();
//                               refundsController
//                                   .selectedOrderRefundReservedOrderIDList
//                                   .refresh();

//                               print("*************************************");
//                               print(
//                                   "refundsController.selectedOrderRefundCurrentOrderIDList.lenght >>>> ${refundsController.selectedOrderRefundCurrentOrderIDList.length}");
//                               print("*************************************");

//                               String orderRefundReservedOrderIDList =
//                                   refundsController
//                                       .selectedOrderRefundReservedOrderIDList
//                                       .join(",");

//                               print("*************************************");
//                               print(
//                                   "orderRefundReservedOrderIDList >>>> $orderRefundReservedOrderIDList");
// //commented by fuzail

//                               // topupOrderRefund.write(
//                               //     'orderRefundReservedOrderIDList',
//                               //     orderRefundReservedOrderIDList);

//                               //added by fuzail 5-15-2025

//                               tagOrderRefund.write('orderRefundTagSerialIDList',
//                                   orderRefundReservedOrderIDList);

//                               topupOrderRefund.write(
//                                   'tAmt',
//                                   orderRefundReservedController.topupTotaltAmt
//                                       .toStringAsFixed(2));
//                               topupOrderRefund.write(
//                                   'tVAT',
//                                   orderRefundReservedController.topupTotaltVAT
//                                       .toStringAsFixed(2));
//                               print("*************************************");
//                               log("tagOrderRefund.write('orderRefundTagSerialIDList' >>>> ${tagOrderRefund.read('orderRefundTagSerialIDList')}");
//                               print(
//                                   "topupOrderRefund.write('tAmt' >>>> ${topupOrderRefund.read('tAmt')}");
//                               print(
//                                   "topupOrderRefund.write('tVAT' >>>> ${topupOrderRefund.read('tVAT')}");
//                             },
//                             value: data.isvalue,
//                             code: data.orderid,
//                             invNo: data.invno,
//                             orderDate: data.orderdate,
//                             paidDate: data.paidon,
//                             amount: data.addltopup.toStringAsFixed(2),
//                             vat: data.vat.toStringAsFixed(2),
//                           ),
//                         );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget refundCurrentTopupDataWidget({
  required String code,
  String? status,
  Color? color,
  Color? textColor,
  bool? value,
  ValueChanged<bool?>? onChanged,
  required String invNo,
  required String orderDate,
  required String paidDate,
  required String amount,
  required String vat,
  bool? isShowButton = false,
  bool? isShowCheckBox = false,
  Function()? cancelReqOnTap,
}) {
  return Container(
    decoration: BoxDecoration(
      color: AppColor.lightBlueColor,
      borderRadius: BorderRadius.circular(4),
      border: Border.all(
          color: value == true ? AppColor.cDarkBlueFont : AppColor.cLightGrey),
    ),
    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                isShowCheckBox == false
                    ? SizedBox()
                    : SizedBox(
                        height: 24,
                        width: 24,
                        child: Checkbox(
                          value: value,
                          onChanged: (on){},
                          activeColor: AppColor.themeDarkBlueColor,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                              side: BorderSide(color: AppColor.cBorder)),
                        ),
                      ),
                horizontalSpace(isShowCheckBox == false ? 0 : 8),
                Container(
                    height: 24,
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.cLightBlueBorder),
                        borderRadius: BorderRadius.circular(4)),
                    padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                    child: Center(
                      child: Text(
                        code,
                        style: pBold12.copyWith(
                            fontSize: 13, color: AppColor.cDarkBlueText),
                      ),
                    )),
              ],
            ),
            // assetSvdImageWidget(image: DefaultImages.verticleMoreIcn)
          ],
        ),
        verticalSpace(18),
        userDataRowWidget(
          title: "Invoice #".trr,
          value: invNo,
        ),
        verticalSpace(12),
        userDataRowWidget(title: "Order date".trr, value: orderDate),
        verticalSpace(12),
        userDataRowWidget(title: "Paid date".trr, value: paidDate),
        verticalSpace(12),
        userTotalValueWidget(title: "Amount".trr, value: amount),
        verticalSpace(12),
        userTotalValueWidget(title: "VAT".trr, value: vat),
        verticalSpace(isShowButton == true ? 14 : 0),
        isShowButton == true
            ? CommonIconBorderButton(
                iconData: DefaultImages.cancelRequestIcn,
                title: "Cancel request".trr,
                onPressed: cancelReqOnTap,
              )
            : SizedBox()
      ],
    ),
  );
}
