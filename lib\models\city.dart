import 'dart:convert';

class CityModel {
  final bool disabled;
  final String group;
  final bool selected;
  final String text;
  final String value;
  CityModel({
    required this.disabled,
    required this.group,
    required this.selected,
    required this.text,
    required this.value,
  });

  Map<String, dynamic> toMap() {
    return {
      'Disabled': disabled,
      'Group': group,
      'Selected': selected,
      'Text': text,
      'Value': value,
    };
  }

  factory CityModel.fromMap(Map<String, dynamic> map) {
    return CityModel(
      disabled: map['Disabled'] ?? '',
      group: map['Group'] ?? '',
      selected: map['Selected'] ?? '',
      text: map['Text'] ?? '',
      value: map['Value'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory CityModel.fromJson(String source) =>
      CityModel.fromMap(json.decode(source));
}
