import 'dart:convert';

Userlogin userloginFromJson(String str) => Userlogin.fromJson(json.decode(str));

String userloginToJson(Userlogin data) => json.encode(data.toJson());

class Userlogin {
  dynamic accStatus;
  dynamic regActivated;
  String readOnly;
  String childCustId;
  String isPrimay;
  bool completeOnboarding;
  bool chkTerm;
  bool isCaptchaCodeValid;
  dynamic city;
  AuUsers auUsers;
  Map<String, String?> auCust;
  ReturnMessage returnMessage;

  Userlogin({
    required this.accStatus,
    required this.regActivated,
    required this.readOnly,
    required this.childCustId,
    required this.isPrimay,
    required this.completeOnboarding,
    required this.chkTerm,
    required this.isCaptchaCodeValid,
    required this.city,
    required this.auUsers,
    required this.auCust,
    required this.returnMessage,
  });

  factory Userlogin.fromJson(Map<String, dynamic> json) => Userlogin(
        accStatus: json["AccStatus"],
        regActivated: json["RegActivated"],
        readOnly: json["ReadOnly"],
        childCustId: json["ChildCustId"],
        isPrimay: json["IsPrimay"],
        completeOnboarding: json["CompleteOnboarding"],
        chkTerm: json["ChkTerm"],
        isCaptchaCodeValid: json["isCaptchaCodeValid"],
        city: json["City"],
        auUsers: AuUsers.fromJson(json["AuUsers"]),
        auCust: Map.from(json["AuCust"])
            .map((k, v) => MapEntry<String, String?>(k, v)),
        returnMessage: ReturnMessage.fromJson(json["ReturnMessage"]),
      );

  Map<String, dynamic> toJson() => {
        "AccStatus": accStatus,
        "RegActivated": regActivated,
        "ReadOnly": readOnly,
        "ChildCustId": childCustId,
        "IsPrimay": isPrimay,
        "CompleteOnboarding": completeOnboarding,
        "ChkTerm": chkTerm,
        "isCaptchaCodeValid": isCaptchaCodeValid,
        "City": city,
        "AuUsers": auUsers.toJson(),
        "AuCust":
            Map.from(auCust).map((k, v) => MapEntry<String, dynamic>(k, v)),
        "ReturnMessage": returnMessage.toJson(),
      };
}

class AuUsers {
  String custid;
  String emailid;
  dynamic oldemailid;
  String password;
  String loginstatus;
  dynamic loginctr;
  String lockexpire;
  String resetpass;
  String activecode;
  String primary;
  String role;
  dynamic rcvEmail;
  String username;
  dynamic authFlag;

  AuUsers({
    required this.custid,
    required this.emailid,
    required this.oldemailid,
    required this.password,
    required this.loginstatus,
    required this.loginctr,
    required this.lockexpire,
    required this.resetpass,
    required this.activecode,
    required this.primary,
    required this.role,
    required this.rcvEmail,
    required this.username,
    required this.authFlag,
  });

  factory AuUsers.fromJson(Map<String, dynamic> json) => AuUsers(
        custid: json["CUSTID"] ?? "",
        emailid: json["EMAILID"] ?? "",
        oldemailid: json["OLDEMAILID"] ?? "",
        password: json["PASSWORD"] ?? "",
        loginstatus: json["LOGINSTATUS"] ?? "",
        loginctr: json["LOGINCTR"] ?? "",
        lockexpire: json["LOCKEXPIRE"] ?? "",
        resetpass: json["RESETPASS"] ?? "",
        activecode: json["ACTIVECODE"] ?? "",
        primary: json["PRIMARY"] ?? "",
        role: json["ROLE"] ?? "",
        rcvEmail: json["RCV_EMAIL"] ?? "",
        username: json["USERNAME"] ?? "",
        authFlag: json["AUTH_FLAG"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "CUSTID": custid,
        "EMAILID": emailid,
        "OLDEMAILID": oldemailid,
        "PASSWORD": password,
        "LOGINSTATUS": loginstatus,
        "LOGINCTR": loginctr,
        "LOCKEXPIRE": lockexpire,
        "RESETPASS": resetpass,
        "ACTIVECODE": activecode,
        "PRIMARY": primary,
        "ROLE": role,
        "RCV_EMAIL": rcvEmail,
        "USERNAME": username,
        "AUTH_FLAG": authFlag,
      };
}

class ReturnMessage {
  dynamic title;
  dynamic message;
  dynamic subMessage;
  dynamic messageType;
  dynamic responseAction;
  String redirect;
  String actionParam;
  String action;
  bool isValidTransaction;
  dynamic data;

  ReturnMessage({
    required this.title,
    required this.message,
    required this.subMessage,
    required this.messageType,
    required this.responseAction,
    required this.redirect,
    required this.actionParam,
    required this.action,
    required this.isValidTransaction,
    required this.data,
  });

  factory ReturnMessage.fromJson(Map<String, dynamic> json) => ReturnMessage(
        title: json["Title"] ?? "",
        message: json["Message"] ?? "",
        subMessage: json["SubMessage"] ?? "",
        messageType: json["MessageType"] ?? "",
        responseAction: json["ResponseAction"] ?? "",
        redirect: json["Redirect"] ?? "",
        actionParam: json["ActionParam"] ?? "",
        action: json["Action"] ?? "",
        isValidTransaction: json["isValidTransaction"],
        data: json["Data"],
      );

  Map<String, dynamic> toJson() => {
        "Title": title,
        "Message": message,
        "SubMessage": subMessage,
        "MessageType": messageType,
        "ResponseAction": responseAction,
        "Redirect": redirect,
        "ActionParam": actionParam,
        "Action": action,
        "isValidTransaction": isValidTransaction,
        "Data": data,
      };
}
