import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationsScreen extends StatefulWidget {
  const LocationsScreen({Key? key}) : super(key: key);

  @override
  State<LocationsScreen> createState() => _LocationsScreenState();
}

class _LocationsScreenState extends State<LocationsScreen> {
  late GoogleMapController mapController;

  final LatLng _initialPosition = const LatLng(24.7136, 46.6753);
  LatLng? selectedStation;

  final List<String> categories = [
    "Aldrees Gas Stations",
    "Sales Office",
    "Installation Center"
  ];
  final List<LatLng> stations = [
    LatLng(24.7221, 46.6706),
    LatLng(24.7282, 46.6821),
    LatLng(24.7158, 46.6554),
  ];
  final List<String> stationNames = ["Station 1", "Station 2", "Station 3"];

  final List<String> filterOptions = [
    "Car Service",
    "Mosque",
    "Food Restaurant",
    "Car Rental",
    "Bank ATM"
  ];

  int selectedIndex = 0; // Default selected index
  bool isSearchActive = false; // Toggle for search mode
  String distanceText = ""; // Distance to display
  LatLng? midpoint; // Midpoint for the distance text
  Offset? distanceLabelOffset;

  List<String> filteredStations = []; // Filtered list for search
  Timer? _debounce;
  @override
  void initState() {
    super.initState();
    filteredStations = List.from(stationNames); // Initialize with full list
  }

  @override
  void dispose() {
    _debounce?.cancel(); // Cancel debounce on dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: _initialPosition,
              zoom: 14.0,
            ),
            onMapCreated: (GoogleMapController controller) {
              mapController = controller;
            },
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            markers: _getMarkers(),
            polylines: selectedStation != null
                ? {
                    Polyline(
                      polylineId: const PolylineId('route'),
                      points: [_initialPosition, selectedStation!],
                      color: Colors.blue,
                      width: 5,
                    )
                  }
                : {},
          ),
          if (distanceLabelOffset != null && distanceText.isNotEmpty)
            Positioned(
              left: distanceLabelOffset!.dx - 50, // Adjust for text width
              top: distanceLabelOffset!.dy - 20, // Adjust for text height
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 5,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  distanceText,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ),
            ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            isSearchActive = false;
                          });
                        },
                        child: const Text("Default"),
                      ),
                      Expanded(
                        child: isSearchActive
                            ? TextField(
                                onChanged: (query) => _onSearchChanged(query),
                                decoration: InputDecoration(
                                  hintText: 'Search stations...',
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(7),
                                    borderSide:
                                        const BorderSide(color: Colors.grey),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(7),
                                    borderSide:
                                        const BorderSide(color: Colors.blue),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 10),
                                ),
                              )
                            : InkWell(
                                onTap: () {
                                  setState(() {
                                    isSearchActive = true;
                                  });
                                },
                                child: Container(
                                  height: 50,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 19, vertical: 10),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(7),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withOpacity(0.2),
                                        offset: const Offset(2.0, 2.0),
                                        blurRadius: 12.0,
                                        spreadRadius: 2.5,
                                      ),
                                    ],
                                  ),
                                  child: const Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      "Search",
                                      style: TextStyle(
                                          color: Colors.black54, fontSize: 16),
                                    ),
                                  ),
                                ),
                              ),
                      ),
                      const SizedBox(height: 10),

                      // Station List
                      Expanded(
                        child: ListView.builder(
                          itemCount: filteredStations.length,
                          itemBuilder: (context, index) {
                            return ListTile(
                              leading:
                                  const Icon(Icons.place, color: Colors.grey),
                              title: Text(filteredStations[index]),
                              onTap: () {
                                showMessage(context,
                                    "Selected ${filteredStations[index]}");
                              },
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            isSearchActive = false;
                          });
                        },
                        child: const Text("All"),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  if (!isSearchActive)
                    ..._buildCategoryList()
                  else
                    ..._buildStationList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Get markers for the map
  Set<Marker> _getMarkers() {
    final markers = <Marker>{
      Marker(
        markerId: const MarkerId('currentLocation'),
        position: _initialPosition,
        infoWindow: const InfoWindow(
          title: 'Current Location',
        ),
      ),
    };
    if (selectedStation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('selectedStation'),
          position: selectedStation!,
          infoWindow: InfoWindow(
            title: 'Selected Station',
          ),
        ),
      );
    }
    return markers;
  }

  void _onSearchChanged(String query) {
    // Cancel any existing debounce timer
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    // Start a new debounce timer
    _debounce = Timer(const Duration(milliseconds: 300), () {
      _filterStations(query);
    });
  }

  void _filterStations(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredStations = List.from(stationNames); // Reset to full list
      } else {
        filteredStations = stationNames
            .where((station) =>
                station.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  // Build the category list
  List<Widget> _buildCategoryList() {
    return [
      ListView.builder(
        shrinkWrap: true,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          return ListTile(
            leading: Icon(
              Icons.location_on,
              color: selectedIndex == index ? Colors.blue : Colors.grey,
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  categories[index],
                  style: TextStyle(
                    color: selectedIndex == index ? Colors.blue : Colors.black,
                    fontWeight: selectedIndex == index
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
                // Show filter button only for "Aldrees Gas Stations"
                if (index == 0 && selectedIndex == index)
                  ElevatedButton(
                    onPressed: () {
                      showFilterDialog(context);
                    },
                    child: const Text("Filter"),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      textStyle: const TextStyle(fontSize: 12),
                    ),
                  ),
              ],
            ),
            onTap: () {
              setState(() {
                selectedIndex = index;
              });
              showMessage(context, "Fetching data for ${categories[index]}");
            },
          );
        },
      ),
    ];
  }

  // Build the station list
  List<Widget> _buildStationList() {
    return [
      ListView.builder(
        shrinkWrap: true,
        itemCount: stations.length,
        itemBuilder: (context, index) {
          return ListTile(
            leading: const Icon(Icons.place, color: Colors.grey),
            title: Text(stationNames[index]),
            onTap: () {
              setState(() {
                selectedStation = stations[index];
              });
              final distance = _calculateDistance(
                _initialPosition.latitude,
                _initialPosition.longitude,
                stations[index].latitude,
                stations[index].longitude,
              );
              showMessage(
                  context, "Distance: ${distance.toStringAsFixed(2)} km");
            },
          );
        },
      ),
    ];
  }

  // Filter dialog
  void showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Filter Options"),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: filterOptions.map((option) {
              return ListTile(
                title: Text(option),
                onTap: () {
                  Navigator.of(context).pop();
                  showMessage(context, "Filter applied: $option");
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text("Close"),
            ),
          ],
        );
      },
    );
  }

  // Distance calculation
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const earthRadius = 6371;
    final dLat = _degreesToRadians(lat2 - lat1);
    final dLon = _degreesToRadians(lon2 - lon1);
    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }

  // Show message
  void showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text(message)));
  }
}
