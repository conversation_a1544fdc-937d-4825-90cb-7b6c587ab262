import 'dart:convert';

List<RefundableService> RefundableServiceFromJson(String str) =>
    List<RefundableService>.from(
        json.decode(str).map((x) => RefundableService.fromJson(x)));

String RefundableServiceToJson(List<RefundableService> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class RefundableService {
  int line;
  String custid;
  String serialid;
  String serialcode;
  String servicetype;
  String servicestatus;
  dynamic country;
  dynamic terminatedate;
  String servicestatusDisp;
  dynamic servicepw;
  String passwordDisp;
  dynamic pwflag;
  dynamic serviceactivedate;
  dynamic plateno;
  dynamic driver;
  dynamic fueltype;
  String quotatype;
  String isdc;
  dynamic quotaplanid;
  double quotavalue;
  dynamic quotabal;
  dynamic dcMobileno;
  double dcQuotavalue;
  dynamic dcStations;
  double dcQuotarem;
  double dcQuotares;
  double dcQuotavalue2;
  double dcQuotarem2;
  double dcQuotares2;
  dynamic dcType;
  String fillingdays;
  dynamic subStations;
  String fillingdaysDisp;
  String reforderid;
  String refjobid;
  dynamic instby;
  dynamic instdate;
  dynamic instremarks;
  String division;
  String divisionname;
  String branch;
  String branchname;
  String deptno;
  String deptname;
  String operation;
  String operationname;
  String quotaclass;
  String quotaclassDisp;
  String isoffline;
  String isofflineDisp;
  double offlinelimit;
  dynamic replacement;
  dynamic stations;
  dynamic pendingplate;
  double scamt;
  String tankNo;
  dynamic transferdate;
  dynamic uniquefield;
  dynamic vehicleid;
  dynamic complaintjobid;
  String stationPush;
  dynamic refundreqid;
  double unliValue;
  String districts;
  dynamic holdingid;
  String? dcFlag;
  String pinblock;
  String vehlicType;
  String vehlicTypeDisp;
  dynamic pinblockDate;
  String vehicletype;
  String vehicletypeDisp;
  dynamic fueltypeDisp;
  String quotatypeDisp;
  String quotaDisp;
  String paid;
  String paidon;
  dynamic invno;
  double vatAmt;
  double scRefund;
  double? rate;
  double rateDisc;
  dynamic replacerfid;
  dynamic replacefor;
  double vat;
  String orderdate;
  double vatPerc;
  String divisionDisp;
  String branchDisp;
  String departmentDisp;
  String operationDisp;
  String divOnhold;
  String braOnhold;
  String dptOnhold;
  bool isvalue;

  RefundableService({
    required this.line,
    required this.custid,
    required this.serialid,
    required this.serialcode,
    required this.servicetype,
    required this.servicestatus,
    required this.country,
    required this.terminatedate,
    required this.servicestatusDisp,
    required this.servicepw,
    required this.passwordDisp,
    required this.pwflag,
    required this.serviceactivedate,
    required this.plateno,
    required this.driver,
    required this.fueltype,
    required this.quotatype,
    required this.isdc,
    required this.quotaplanid,
    required this.quotavalue,
    required this.quotabal,
    required this.dcMobileno,
    required this.dcQuotavalue,
    required this.dcStations,
    required this.dcQuotarem,
    required this.dcQuotares,
    required this.dcQuotavalue2,
    required this.dcQuotarem2,
    required this.dcQuotares2,
    required this.dcType,
    required this.fillingdays,
    required this.subStations,
    required this.fillingdaysDisp,
    required this.reforderid,
    required this.refjobid,
    required this.instby,
    required this.instdate,
    required this.instremarks,
    required this.division,
    required this.divisionname,
    required this.branch,
    required this.branchname,
    required this.deptno,
    required this.deptname,
    required this.operation,
    required this.operationname,
    required this.quotaclass,
    required this.quotaclassDisp,
    required this.isoffline,
    required this.isofflineDisp,
    required this.offlinelimit,
    required this.replacement,
    required this.stations,
    required this.pendingplate,
    required this.scamt,
    required this.tankNo,
    required this.transferdate,
    required this.uniquefield,
    required this.vehicleid,
    required this.complaintjobid,
    required this.stationPush,
    required this.refundreqid,
    required this.unliValue,
    required this.districts,
    required this.holdingid,
    required this.dcFlag,
    required this.pinblock,
    required this.vehlicType,
    required this.vehlicTypeDisp,
    required this.pinblockDate,
    required this.vehicletype,
    required this.vehicletypeDisp,
    required this.fueltypeDisp,
    required this.quotatypeDisp,
    required this.quotaDisp,
    required this.paid,
    required this.paidon,
    required this.invno,
    required this.vatAmt,
    required this.scRefund,
    required this.rate,
    required this.rateDisc,
    required this.replacerfid,
    required this.replacefor,
    required this.vat,
    required this.orderdate,
    required this.vatPerc,
    required this.divisionDisp,
    required this.branchDisp,
    required this.departmentDisp,
    required this.operationDisp,
    required this.divOnhold,
    required this.braOnhold,
    required this.dptOnhold,
    required this.isvalue,
  });

  factory RefundableService.fromJson(Map<String, dynamic> json) =>
      RefundableService(
        line: json["LINE"] ?? 0,
        custid: json["CUSTID"] ?? "",
        serialid: json["SERIALID"] ?? "",
        serialcode: json["SERIALCODE"] ?? "",
        servicetype: json["SERVICETYPE"] ?? "",
        servicestatus: json["SERVICESTATUS"] ?? "",
        country: json["COUNTRY"] ?? "",
        terminatedate: json["TERMINATEDATE"] ?? "",
        servicestatusDisp: json["SERVICESTATUS_DISP"] ?? "",
        servicepw: json["SERVICEPW"] ?? "",
        passwordDisp: json["PASSWORD_DISP"] ?? "",
        pwflag: json["PWFLAG"] ?? "",
        serviceactivedate: json["SERVICEACTIVEDATE"] ?? "",
        plateno: json["PLATENO"] ?? "",
        driver: json["DRIVER"] ?? "",
        fueltype: json["FUELTYPE"] ?? "",
        quotatype: json["QUOTATYPE"] ?? "",
        isdc: json["ISDC"] ?? "",
        quotaplanid: json["QUOTAPLANID"] ?? "",
        quotavalue: json["QUOTAVALUE"] ?? 0,
        quotabal: json["QUOTABAL"] ?? "",
        dcMobileno: json["DC_MOBILENO"] ?? "",
        dcQuotavalue: json["DC_QUOTAVALUE"] ?? 0,
        dcStations: json["DC_STATIONS"] ?? "",
        dcQuotarem: json["DC_QUOTAREM"] ?? 0,
        dcQuotares: json["DC_QUOTARES"] ?? 0,
        dcQuotavalue2: json["DC_QUOTAVALUE2"] ?? 0,
        dcQuotarem2: json["DC_QUOTAREM2"] ?? 0,
        dcQuotares2: json["DC_QUOTARES2"] ?? 0,
        dcType: json["DC_TYPE"] ?? "",
        fillingdays: json["FILLINGDAYS"] ?? "",
        subStations: json["SUB_STATIONS"] ?? "",
        fillingdaysDisp: json["FILLINGDAYS_DISP"] ?? "",
        reforderid: json["REFORDERID"] ?? "",
        refjobid: json["REFJOBID"] ?? "",
        instby: json["INSTBY"] ?? "",
        instdate: json["INSTDATE"] ?? "",
        instremarks: json["INSTREMARKS"] ?? "",
        division: json["DIVISION"] ?? "",
        divisionname: json["DIVISIONNAME"] ?? "",
        branch: json["BRANCH"] ?? "",
        branchname: json["BRANCHNAME"] ?? "",
        deptno: json["DEPTNO"] ?? "",
        deptname: json["DEPTNAME"] ?? "",
        operation: json["OPERATION"] ?? "",
        operationname: json["OPERATIONNAME"] ?? "",
        quotaclass: json["QUOTACLASS"] ?? "",
        quotaclassDisp: json["QUOTACLASS_DISP"] ?? "",
        isoffline: json["ISOFFLINE"] ?? "",
        isofflineDisp: json["ISOFFLINE_DISP"] ?? "",
        offlinelimit: json["OFFLINELIMIT"] ?? 0,
        replacement: json["REPLACEMENT"] ?? "",
        stations: json["STATIONS"] ?? "",
        pendingplate: json["PENDINGPLATE"] ?? "",
        scamt: json["SCAMT"] ?? 0,
        tankNo: json["TANK_NO"] ?? "",
        transferdate: json["TRANSFERDATE"] ?? "",
        uniquefield: json["UNIQUEFIELD"] ?? "",
        vehicleid: json["VEHICLEID"] ?? "",
        complaintjobid: json["COMPLAINTJOBID"] ?? "",
        stationPush: json["STATION_PUSH"] ?? "",
        refundreqid: json["REFUNDREQID"] ?? "",
        unliValue: json["UNLI_VALUE"] ?? 0,
        districts: json["DISTRICTS"] ?? "",
        holdingid: json["HOLDINGID"] ?? "",
        dcFlag: json["DC_FLAG"] ?? "",
        pinblock: json["PINBLOCK"] ?? "",
        vehlicType: json["VEHLIC_TYPE"] ?? "",
        vehlicTypeDisp: json["VEHLIC_TYPE_DISP"] ?? "",
        pinblockDate: json["PINBLOCK_DATE"] ?? "",
        vehicletype: json["VEHICLETYPE"] ?? "",
        vehicletypeDisp: json["VEHICLETYPE_DISP"] ?? "",
        fueltypeDisp: json["FUELTYPE_DISP"] ?? "",
        quotatypeDisp: json["QUOTATYPE_DISP"] ?? "",
        quotaDisp: json["QUOTA_DISP"] ?? "",
        paid: json["PAID"] ?? "",
        paidon: json["PAIDON"] ?? "",
        invno: json["INVNO"] ?? "",
        vatAmt: json["VAT_AMT"]?.toDouble() ?? "",
        scRefund: json["SC_REFUND"] ?? 0,
        rate: json["RATE"] ?? 0,
        rateDisc: json["RATE_DISC"] ?? 0,
        replacerfid: json["REPLACERFID"] ?? "",
        replacefor: json["REPLACEFOR"] ?? "",
        vat: json["VAT"]?.toDouble() ?? "",
        orderdate: json["ORDERDATE"] ?? "",
        vatPerc: json["VAT_PERC"] ?? 0,
        divisionDisp: json["DIVISION_DISP"] ?? "",
        branchDisp: json["BRANCH_DISP"] ?? "",
        departmentDisp: json["DEPARTMENT_DISP"] ?? "",
        operationDisp: json["OPERATION_DISP"] ?? "",
        divOnhold: json["DIV_ONHOLD"] ?? "",
        braOnhold: json["BRA_ONHOLD"] ?? "",
        dptOnhold: json["DPT_ONHOLD"] ?? "",
        isvalue: json["ISVALUE"] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "LINE": line,
        "CUSTID": custid,
        "SERIALID": serialid,
        "SERIALCODE": serialcode,
        "SERVICETYPE": servicetype,
        "SERVICESTATUS": servicestatus,
        "COUNTRY": country,
        "TERMINATEDATE": terminatedate,
        "SERVICESTATUS_DISP": servicestatusDisp,
        "SERVICEPW": servicepw,
        "PASSWORD_DISP": passwordDisp,
        "PWFLAG": pwflag,
        "SERVICEACTIVEDATE": serviceactivedate,
        "PLATENO": plateno,
        "DRIVER": driver,
        "FUELTYPE": fueltype,
        "QUOTATYPE": quotatype,
        "ISDC": isdc,
        "QUOTAPLANID": quotaplanid,
        "QUOTAVALUE": quotavalue,
        "QUOTABAL": quotabal,
        "DC_MOBILENO": dcMobileno,
        "DC_QUOTAVALUE": dcQuotavalue,
        "DC_STATIONS": dcStations,
        "DC_QUOTAREM": dcQuotarem,
        "DC_QUOTARES": dcQuotares,
        "DC_QUOTAVALUE2": dcQuotavalue2,
        "DC_QUOTAREM2": dcQuotarem2,
        "DC_QUOTARES2": dcQuotares2,
        "DC_TYPE": dcType,
        "FILLINGDAYS": fillingdays,
        "SUB_STATIONS": subStations,
        "FILLINGDAYS_DISP": fillingdaysDisp,
        "REFORDERID": reforderid,
        "REFJOBID": refjobid,
        "INSTBY": instby,
        "INSTDATE": instdate,
        "INSTREMARKS": instremarks,
        "DIVISION": division,
        "DIVISIONNAME": divisionname,
        "BRANCH": branch,
        "BRANCHNAME": branchname,
        "DEPTNO": deptno,
        "DEPTNAME": deptname,
        "OPERATION": operation,
        "OPERATIONNAME": operationname,
        "QUOTACLASS": quotaclass,
        "QUOTACLASS_DISP": quotaclassDisp,
        "ISOFFLINE": isoffline,
        "ISOFFLINE_DISP": isofflineDisp,
        "OFFLINELIMIT": offlinelimit,
        "REPLACEMENT": replacement,
        "STATIONS": stations,
        "PENDINGPLATE": pendingplate,
        "SCAMT": scamt,
        "TANK_NO": tankNo,
        "TRANSFERDATE": transferdate,
        "UNIQUEFIELD": uniquefield,
        "VEHICLEID": vehicleid,
        "COMPLAINTJOBID": complaintjobid,
        "STATION_PUSH": stationPush,
        "REFUNDREQID": refundreqid,
        "UNLI_VALUE": unliValue,
        "DISTRICTS": districts,
        "HOLDINGID": holdingid,
        "DC_FLAG": dcFlag,
        "PINBLOCK": pinblock,
        "VEHLIC_TYPE": vehlicType,
        "VEHLIC_TYPE_DISP": vehlicTypeDisp,
        "PINBLOCK_DATE": pinblockDate,
        "VEHICLETYPE": vehicletype,
        "VEHICLETYPE_DISP": vehicletypeDisp,
        "FUELTYPE_DISP": fueltypeDisp,
        "QUOTATYPE_DISP": quotatypeDisp,
        "QUOTA_DISP": quotaDisp,
        "PAID": paid,
        "PAIDON": paidon,
        "INVNO": invno,
        "VAT_AMT": vatAmt,
        "SC_REFUND": scRefund,
        "RATE": rate,
        "RATE_DISC": rateDisc,
        "REPLACERFID": replacerfid,
        "REPLACEFOR": replacefor,
        "VAT": vat,
        "ORDERDATE": orderdate,
        "VAT_PERC": vatPerc,
        "DIVISION_DISP": divisionDisp,
        "BRANCH_DISP": branchDisp,
        "DEPARTMENT_DISP": departmentDisp,
        "OPERATION_DISP": operationDisp,
        "DIV_ONHOLD": divOnhold,
        "BRA_ONHOLD": braOnhold,
        "DPT_ONHOLD": dptOnhold,
        "ISVALUE": isvalue,
      };
}
