import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:html/parser.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/balance.dart';
import 'package:waie_app/models/org_level.dart';
import 'package:waie_app/models/profile.dart';
import 'package:waie_app/models/servicecount.dart';
import 'package:waie_app/models/slides.dart';
import 'package:waie_app/models/station_news.dart';
import 'package:waie_app/utils/api_endpoints.dart';

import '../../../models/menu_access.dart';
import '../../../models/menu_access.dart';
import '../../../models/menu_access.dart';
import '../../../models/usemenulist.dart';
import '../../../models/usemenulist.dart';
import '../../../models/usemenulist.dart';
import '../../../utils/constants.dart';

class BalanceController extends GetxController {
  List<MenuAccess> userMenuList = [];
  List<OrgLevel> orgLevelMenuList = [];
  RxList multipleSelected = [].obs;
  RxBool isSelected = false.obs;
  RxList balances = [].obs;
  RxList statusCounts = [].obs;

  //var userMenuList = <UserMenulist>[].obs;
  RxString gasStnNews = ''.obs;
  RxList gasStnSlide = [].obs;
  RxList stationImageList = [].obs;

  @override
  void onInit() {
    super.onInit();
    print('BalanceController');
    getDatas();
  }

  getDatas() async {
    await fetchGasStnCount();
    await fetchBalances();
    print("balances ${jsonDecode(jsonEncode(balances))}");
    await fetchServiceCount();
    print("statusCounts ${jsonDecode(jsonEncode(statusCounts))}");
    await getUserMenu();
    //await fetchStationNewsSlide();
  }

  Future<dynamic> fetchGasStnCount() async {
    String isAr = Constants.selected_language == "ar" ? 'true' : 'false';
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getGasStnCount),
          body: {"IsAr": isAr});
      print("GASSTN RESULT=======${response.body}");
      String result = jsonDecode(response.body);
      await Future.delayed(
          const Duration(seconds: 0), () => gasStnNews.value = result);
      return result;
    } catch (e) {
      log("GasStnCount Error$e");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<dynamic> fetchBalances() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    print("userData.auCust?.accttype  : " + userData.auCust?.accttype);
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getBalances),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
          body: {
            "userId": userid.toString(),
            "ACCTTYPE": userData.auCust?.accttype
          });
      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        BalanceModel balance =
            BalanceModel.fromMap(result[i] as Map<String, dynamic>);
        Constants.custBalance = balance.currentbalance;
        print("Balance Amount ================== ${balance.currentbalance}");
        print("Balance Amount ================== ${balance.unallocatbalance}");
        log("Balance Amount check fuzail ${balance.reservebalance}");
        Constants.custResrvBal = double.parse(balance.reservebalance);
        await Future.delayed(
            const Duration(seconds: 0), () => balances.add(balance));
      }

      return balances;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<dynamic> fetchSlideImages() async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);

    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getImageSlide),
          body: {"regType": userData.auCust?.regtype});
      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        Slides slides = Slides.fromMap(result[i] as Map<String, dynamic>);
        await Future.delayed(
            const Duration(seconds: 0), () => stationImageList.add(slides));
      }

      return stationImageList;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
  // Future<dynamic> fetchStationNewsSlide() async {
  //   var client = http.Client();
  //   SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
  //   var userid = sharedUser2.getString('userid');
  //   var user = sharedUser2.getString('user');
  //   Map cardInfo = json.decode(user!);
  //   Profile userData = Profile.fromJson(cardInfo);
  //
  //   try {
  //     var response = await client.post(
  //         Uri.parse(
  //             ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getGasStnNews),
  //         body: {"isAr": false});
  //     List result = jsonDecode(response.body);
  //
  //     for (int i = 0; i < result.length; i++) {
  //       Station_News news_slides = Station_News.fromMap(result[i] as Map<String, dynamic>);
  //       await Future.delayed(
  //           const Duration(seconds: 0), () => gasStnSlide.add(news_slides));
  //     }
  //
  //     return gasStnSlide;
  //   } catch (e) {
  //     log(e.toString());
  //     return [];
  //   } finally {
  //     // Then finally destroy the client.
  //     client.close();
  //   }
  // }

  Future<dynamic> fetchServiceCount() async {
    var client = http.Client();
    SharedPreferences sharedUser = await SharedPreferences.getInstance();
    var userid = sharedUser.getString('userid');
    print("USER ID============ $userid");
    try {
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getServiceCount),
          body: {"userId": userid});
      //body: {"userId": "000038345"});
      List result = jsonDecode(response.body);

      for (int i = 0; i < result.length; i++) {
        ServiceCountModel count =
            ServiceCountModel.fromMap(result[i] as Map<String, dynamic>);
        print("activeTags ================== ${count.activeTags}");
        print("activeCards ================== ${count.activeCards}");
        print("tagsToInstall ================== ${count.tagsToInstall}");
        statusCounts.add(count);
      }

      return statusCounts;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  Future<dynamic> getUserMenu() async {
    // Loader.showLoader();
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    var user = sharedUser2.getString('user');
    Map cardInfo = json.decode(user!);
    Profile userData = Profile.fromJson(cardInfo);
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getUserMenu),
          body: {
            "custId": userid,
            "ACCTTYPE": userData.auCust?.accttype,
            "email": userData.auCust?.emailid,
            //"IsAr": Constants.IsAr_App
            "IsAr": "false"
          });
      print("User Menu List============${response.body}");
      // List <UserMenulist>result2 = UserMenulist.fromJson(jsonDecode(response.body)) as List<UserMenulist>;
      //  Loader.hideLoader();
      Iterable list = jsonDecode(response.body);
      List<dynamic> orderHistory = List<UserMenulist>.from(
          list.map((model) => UserMenulist.fromJson(model)));
      for (UserMenulist orderHis in orderHistory) {
        if (orderHis.menuCode == "FINA0") {
          Constants.menu_Finance = true;
          List<SubMenu> SubMenuList = orderHis.menu;
          for (SubMenu subMenu in SubMenuList) {
            if (subMenu.controlName == "Balance") {}
            if (subMenu.controlName == "Service Order") {
              Constants.subMenu_Order = true;
            }
            if (subMenu.controlName == "Purchase History") {
              Constants.subMenu_PurchaseHistory = true;
            }
            if (subMenu.controlName == "Refund") {
              Constants.subMenu_Refund = true;
            }
            if (subMenu.controlName == "Refund Request") {
              Constants.subMenu_Refund = true;
            }
            if (subMenu.controlName == "Company Affiliate") {
              Constants.subMenu_CompanyAffiliates = true;
            }
          }
          /* Constants.menu_Finance = true;
          Constants.subMenu_Order = true;
          Constants.subMenu_PurchaseHistory = true;
          Constants.subMenu_Refund = true;
          Constants.subMenu_CompanyAffiliates = true;*/
        }
        if (orderHis.menuCode == "FLEE0") {
          Constants.menu_Fleet = true;
          List<SubMenu> SubMenuList = orderHis.menu;
          for (SubMenu subMenu in SubMenuList) {
            if (subMenu.controlName == "Fleet Structure") {
              Constants.subMenu_FleetStruc = true;
            }
          }
          Constants.subMenu_FleetStruc = true;
        }
        if (orderHis.menuCode == "REPO0") {
          Constants.menu_Reports = true;
          List<SubMenu> SubMenuList = orderHis.menu;
          for (SubMenu subMenu in SubMenuList) {
            if (subMenu.controlName == "Station Info List") {
              Constants.ReportSTNList = "Y";
            }
            if (subMenu.controlName == "Customer Statement") {
              Constants.ReportCustomerStmnt = "Y";
            }
            if (subMenu.controlName == "Monthly Quota Variance Summary") {
              Constants.ReportMnthlyQtaSum = "Y";
            }
            if (subMenu.controlName == "Fleet Wise Fuel Usage") {
              Constants.ReportFleetStmnt = "Y";
            }
            if (subMenu.controlName == "Fuel Consumption By Customer") {
              Constants.ReportFuelCmsp = "Y";
            }
            if (subMenu.controlName == "Monthly Transaction Statement") {
              Constants.ReportMnthlyTrStmnt = "Y";
            }
          }
        }
        if (orderHis.menuCode == "PROF0") {
          Constants.menu_profile = true;
          List<SubMenu> SubMenuList = orderHis.menu;
          for (SubMenu subMenu in SubMenuList) {
            if (subMenu.controlName == "Users") {
              Constants.subMenu_Users = true;
            }
            if (subMenu.controlName == "Subscriptions") {
              Constants.subMenu_Subscription = true;
            }
            if (subMenu.controlName == "My Company") {
              Constants.subMenu_MyCompany = true;
            }
          }
          /* Constants.subMenu_MyCompany = true;
          Constants.subMenu_Users = true;
          Constants.subMenu_Subscription = true;*/
        }
      }
      var responseMenuAccess = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getMenuNodes),
          body: {
            "custid": userid,
            "accttype": userData.auCust?.accttype,
            "emailid": userData.auCust?.emailid,
            "isAr": Constants.IsAr_App
          });
      List resultMenuAccess = jsonDecode(responseMenuAccess.body);

      for (int i = 0; i < resultMenuAccess.length; i++) {
        print(
            "===============================================================");
        print(i);
        print(resultMenuAccess[i]);
        print(
            "===============================================================");
        MenuAccess userMenuLists =
            MenuAccess.fromJson(resultMenuAccess[i] as Map<String, dynamic>);
        userMenuList.add(userMenuLists);
      }
      print(
          "=============================resultMenuAccess==================================");
      // print(
      //     "userMenuListuserMenuListuserMenuListuserMenuList >>>>> ${jsonDecode(jsonEncode(userMenuList))}");
      var responseOrgLevel = await client.post(
          Uri.parse(ApiEndPoints.baseUrl +
              ApiEndPoints.authEndpoints.getOrgLevelNodes),
          body: {
            "custid": userid,
            "isAr": Constants.IsAr_App,
          });
      List resultOrgLevel = jsonDecode(responseOrgLevel.body);
      print(jsonDecode(responseOrgLevel.body));

      for (int i = 0; i < resultOrgLevel.length; i++) {
        print(
            "===============================================================");
        print(i);
        print(resultOrgLevel[i]);
        print(
            "===============================================================");
        OrgLevel orgLevelMenuLists =
            OrgLevel.fromJson(resultOrgLevel[i] as Map<String, dynamic>);
        orgLevelMenuList.add(orgLevelMenuLists);
      }
      print(
          "=============================orgLevelMenuLists==================================");
      // print(
      //     "orgLevelMenuListorgLevelMenuListorgLevelMenuListorgLevelMenuList >>>>> ${jsonDecode(jsonEncode(orgLevelMenuList))}");

      /* Constants.userMenuListCon.clear();
      Constants.userMenuListCon.assignAll(userMenuList);*/

      //Constants.menu_Finance=true;
      return "";
    } catch (e) {
      log("User Menu List Error $e");
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
